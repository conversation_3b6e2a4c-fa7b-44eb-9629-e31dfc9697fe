{"ast": null, "code": "/* eslint-env browser */\nmodule.exports = typeof self == 'object' ? self.FormData : window.FormData;", "map": {"version": 3, "names": ["module", "exports", "self", "FormData", "window"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/node_modules/axios/node_modules/form-data/lib/browser.js"], "sourcesContent": ["/* eslint-env browser */\nmodule.exports = typeof self == 'object' ? self.FormData : window.FormData;\n"], "mappings": "AAAA;AACAA,MAAM,CAACC,OAAO,GAAG,OAAOC,IAAI,IAAI,QAAQ,GAAGA,IAAI,CAACC,QAAQ,GAAGC,MAAM,CAACD,QAAQ"}, "metadata": {}, "sourceType": "script"}