{"ast": null, "code": "import { axios } from 'boot/axios';\nimport { serializeError } from 'serialize-error';\nimport { createProblemDetails, isProblemDetails } from 'utils/problem-details';\nclass ApiBase {\n  get(url, config) {\n    return new Promise((resolve, reject) => {\n      return axios.get(url, config).then(r => checkResult(r, resolve, reject)).catch(e => handleError(e, reject));\n    });\n  }\n  post(url) {\n    let payload = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let config = arguments.length > 2 ? arguments[2] : undefined;\n    return new Promise((resolve, reject) => {\n      return axios.post(url, payload, config).then(r => checkResult(r, resolve, reject)).catch(e => handleError(e, reject));\n    });\n  }\n  put(url) {\n    let payload = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let config = arguments.length > 2 ? arguments[2] : undefined;\n    return new Promise((resolve, reject) => {\n      return axios.put(url, payload, config).then(r => checkResult(r, resolve, reject)).catch(e => handleError(e, reject));\n    });\n  }\n  delete(url, config) {\n    return new Promise((resolve, reject) => {\n      return axios.delete(url, config).then(r => checkResult(r, resolve, reject)).catch(e => handleError(e, reject));\n    });\n  }\n}\nfunction checkResult(response, resolve, reject) {\n  if (!isSuccess(response)) {\n    if (isAxiosError(response)) {\n      if (response.response) {\n        if (isProblemDetails(response.response.data)) {\n          return reject(response.response.data);\n        }\n        if (typeof response.response.data === 'string') {\n          return reject(createProblemDetails(response.response.data));\n        }\n      }\n    } else {\n      if (response) {\n        if (isError(response)) {\n          const error = serializeError(response);\n          return reject(createProblemDetails(error.name || 'Error', error.message));\n        }\n        if (response.data && isProblemDetails(response.data)) {\n          return reject(response.data);\n        }\n        if (typeof response.data === 'string') {\n          return reject(createProblemDetails(response.data));\n        }\n      }\n      if (response && response.data) {\n        return reject(response.data);\n      }\n      return reject(response.data);\n    }\n  }\n  return resolve(response.data);\n}\nexport function handleError(e, reject) {\n  console.error(e);\n  if (e.response) {\n    if (isProblemDetails(e.response.data)) {\n      return reject(e.response.data);\n    }\n    if (typeof e.response.data === 'string') {\n      return reject(createProblemDetails(e.response.data));\n    }\n  }\n  if (e.response && e.response.data) {\n    const data = e.response.data;\n    if (typeof data.error === 'string' && typeof data.reason === 'string') {\n      return reject(createProblemDetails(data.error, data.reason));\n    }\n    return reject(e.response.data);\n  }\n  return reject(e.message);\n}\nfunction isSuccess(response) {\n  return response.status >= 200 && response.status < 300;\n}\nfunction isAxiosError(response) {\n  return 'isAxiosError' in response && response.isAxiosError;\n}\nfunction isError(response) {\n  return response instanceof Error;\n}\nexport const api = new ApiBase();", "map": {"version": 3, "names": ["axios", "serializeError", "createProblemDetails", "isProblemDetails", "ApiBase", "get", "url", "config", "Promise", "resolve", "reject", "then", "r", "checkResult", "catch", "e", "handleError", "post", "payload", "put", "delete", "response", "isSuccess", "isAxiosError", "data", "isError", "error", "name", "message", "console", "reason", "status", "Error", "api"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/api/api-base.ts"], "sourcesContent": ["import { axios } from 'boot/axios';\r\nimport { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';\r\nimport { serializeError } from 'serialize-error';\r\nimport { createProblemDetails, isProblemDetails } from 'utils/problem-details';\r\n\r\nclass ApiBase {\r\n  get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {\r\n    return new Promise<T>((resolve, reject) => {\r\n      return axios\r\n        .get<T>(url, config)\r\n        .then(r => checkResult(r, resolve, reject))\r\n        .catch((e: AxiosError) => handleError(e, reject));\r\n    });\r\n  }\r\n  post<T>(url: string, payload: any = {}, config?: AxiosRequestConfig): Promise<T> {\r\n    return new Promise<T>((resolve, reject) => {\r\n      return axios\r\n        .post<T>(url, payload, config)\r\n        .then(r => checkResult(r, resolve, reject))\r\n        .catch((e: AxiosError) => handleError(e, reject));\r\n    });\r\n  }\r\n  put<T>(url: string, payload: any = {}, config?: AxiosRequestConfig): Promise<T> {\r\n    return new Promise<T>((resolve, reject) => {\r\n      return axios\r\n        .put<T>(url, payload, config)\r\n        .then(r => checkResult(r, resolve, reject))\r\n        .catch((e: AxiosError) => handleError(e, reject));\r\n    });\r\n  }\r\n  delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {\r\n    return new Promise<T>((resolve, reject) => {\r\n      return axios\r\n        .delete<T>(url, config)\r\n        .then(r => checkResult(r, resolve, reject))\r\n        .catch((e: AxiosError) => handleError(e, reject));\r\n    });\r\n  }\r\n}\r\n\r\nfunction checkResult<T>(\r\n  response: AxiosResponse<T> | AxiosError | Error,\r\n  resolve: (data: T) => void,\r\n  reject: (reason?: any) => void\r\n): T | void {\r\n  if (!isSuccess(response)) {\r\n    if (isAxiosError(response)) {\r\n      if (response.response) {\r\n        if (isProblemDetails(response.response.data)) {\r\n          return reject(response.response.data);\r\n        }\r\n\r\n        if (typeof response.response.data === 'string') {\r\n          return reject(createProblemDetails(response.response.data));\r\n        }\r\n      }\r\n    } else {\r\n      if (response) {\r\n        if (isError(response)) {\r\n          const error = serializeError(response);\r\n          return reject(createProblemDetails(error.name || 'Error', error.message));\r\n        }\r\n\r\n        if (response.data && isProblemDetails(response.data)) {\r\n          return reject(response.data);\r\n        }\r\n\r\n        if (typeof response.data === 'string') {\r\n          return reject(createProblemDetails(response.data));\r\n        }\r\n      }\r\n\r\n      if (response && response.data) {\r\n        return reject(response.data);\r\n      }\r\n\r\n      return reject(response.data);\r\n    }\r\n  }\r\n\r\n  return resolve((response as AxiosResponse).data);\r\n}\r\n\r\nexport function handleError(e: AxiosError, reject: (reason?: any) => void) {\r\n  console.error(e);\r\n\r\n  if (e.response) {\r\n    if (isProblemDetails(e.response.data)) {\r\n      return reject(e.response.data);\r\n    }\r\n\r\n    if (typeof e.response.data === 'string') {\r\n      return reject(createProblemDetails(e.response.data));\r\n    }\r\n  }\r\n\r\n  if (e.response && e.response.data) {\r\n    const data = e.response.data as any;\r\n    if (typeof data.error === 'string' && typeof data.reason === 'string') {\r\n      return reject(createProblemDetails(data.error, data.reason));\r\n    }\r\n\r\n    return reject(e.response.data);\r\n  }\r\n\r\n  return reject(e.message);\r\n}\r\n\r\nfunction isSuccess(response: AxiosResponse<any> | AxiosError | Error) {\r\n  return (response as AxiosResponse).status >= 200 && (response as AxiosResponse).status < 300;\r\n}\r\n\r\nfunction isAxiosError(response: AxiosResponse<any> | AxiosError | Error): response is AxiosError {\r\n  return 'isAxiosError' in response && response.isAxiosError;\r\n}\r\n\r\nfunction isError(response: any): response is Error {\r\n  return response instanceof Error;\r\n}\r\n\r\nexport const api = new ApiBase();\r\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,YAAY;AAElC,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,oBAAoB,EAAEC,gBAAgB,QAAQ,uBAAuB;AAE9E,MAAMC,OAAO,CAAC;EACZC,GAAG,CAAIC,GAAW,EAAEC,MAA2B,EAAc;IAC3D,OAAO,IAAIC,OAAO,CAAI,CAACC,OAAO,EAAEC,MAAM,KAAK;MACzC,OAAOV,KAAK,CACTK,GAAG,CAAIC,GAAG,EAAEC,MAAM,CAAC,CACnBI,IAAI,CAACC,CAAC,IAAIC,WAAW,CAACD,CAAC,EAAEH,OAAO,EAAEC,MAAM,CAAC,CAAC,CAC1CI,KAAK,CAAEC,CAAa,IAAKC,WAAW,CAACD,CAAC,EAAEL,MAAM,CAAC,CAAC;IACrD,CAAC,CAAC;EACJ;EACAO,IAAI,CAAIX,GAAW,EAA8D;IAAA,IAA5DY,OAAY,uEAAG,CAAC,CAAC;IAAA,IAAEX,MAA2B;IACjE,OAAO,IAAIC,OAAO,CAAI,CAACC,OAAO,EAAEC,MAAM,KAAK;MACzC,OAAOV,KAAK,CACTiB,IAAI,CAAIX,GAAG,EAAEY,OAAO,EAAEX,MAAM,CAAC,CAC7BI,IAAI,CAACC,CAAC,IAAIC,WAAW,CAACD,CAAC,EAAEH,OAAO,EAAEC,MAAM,CAAC,CAAC,CAC1CI,KAAK,CAAEC,CAAa,IAAKC,WAAW,CAACD,CAAC,EAAEL,MAAM,CAAC,CAAC;IACrD,CAAC,CAAC;EACJ;EACAS,GAAG,CAAIb,GAAW,EAA8D;IAAA,IAA5DY,OAAY,uEAAG,CAAC,CAAC;IAAA,IAAEX,MAA2B;IAChE,OAAO,IAAIC,OAAO,CAAI,CAACC,OAAO,EAAEC,MAAM,KAAK;MACzC,OAAOV,KAAK,CACTmB,GAAG,CAAIb,GAAG,EAAEY,OAAO,EAAEX,MAAM,CAAC,CAC5BI,IAAI,CAACC,CAAC,IAAIC,WAAW,CAACD,CAAC,EAAEH,OAAO,EAAEC,MAAM,CAAC,CAAC,CAC1CI,KAAK,CAAEC,CAAa,IAAKC,WAAW,CAACD,CAAC,EAAEL,MAAM,CAAC,CAAC;IACrD,CAAC,CAAC;EACJ;EACAU,MAAM,CAAId,GAAW,EAAEC,MAA2B,EAAc;IAC9D,OAAO,IAAIC,OAAO,CAAI,CAACC,OAAO,EAAEC,MAAM,KAAK;MACzC,OAAOV,KAAK,CACToB,MAAM,CAAId,GAAG,EAAEC,MAAM,CAAC,CACtBI,IAAI,CAACC,CAAC,IAAIC,WAAW,CAACD,CAAC,EAAEH,OAAO,EAAEC,MAAM,CAAC,CAAC,CAC1CI,KAAK,CAAEC,CAAa,IAAKC,WAAW,CAACD,CAAC,EAAEL,MAAM,CAAC,CAAC;IACrD,CAAC,CAAC;EACJ;AACF;AAEA,SAASG,WAAW,CAClBQ,QAA+C,EAC/CZ,OAA0B,EAC1BC,MAA8B,EACpB;EACV,IAAI,CAACY,SAAS,CAACD,QAAQ,CAAC,EAAE;IACxB,IAAIE,YAAY,CAACF,QAAQ,CAAC,EAAE;MAC1B,IAAIA,QAAQ,CAACA,QAAQ,EAAE;QACrB,IAAIlB,gBAAgB,CAACkB,QAAQ,CAACA,QAAQ,CAACG,IAAI,CAAC,EAAE;UAC5C,OAAOd,MAAM,CAACW,QAAQ,CAACA,QAAQ,CAACG,IAAI,CAAC;QACvC;QAEA,IAAI,OAAOH,QAAQ,CAACA,QAAQ,CAACG,IAAI,KAAK,QAAQ,EAAE;UAC9C,OAAOd,MAAM,CAACR,oBAAoB,CAACmB,QAAQ,CAACA,QAAQ,CAACG,IAAI,CAAC,CAAC;QAC7D;MACF;IACF,CAAC,MAAM;MACL,IAAIH,QAAQ,EAAE;QACZ,IAAII,OAAO,CAACJ,QAAQ,CAAC,EAAE;UACrB,MAAMK,KAAK,GAAGzB,cAAc,CAACoB,QAAQ,CAAC;UACtC,OAAOX,MAAM,CAACR,oBAAoB,CAACwB,KAAK,CAACC,IAAI,IAAI,OAAO,EAAED,KAAK,CAACE,OAAO,CAAC,CAAC;QAC3E;QAEA,IAAIP,QAAQ,CAACG,IAAI,IAAIrB,gBAAgB,CAACkB,QAAQ,CAACG,IAAI,CAAC,EAAE;UACpD,OAAOd,MAAM,CAACW,QAAQ,CAACG,IAAI,CAAC;QAC9B;QAEA,IAAI,OAAOH,QAAQ,CAACG,IAAI,KAAK,QAAQ,EAAE;UACrC,OAAOd,MAAM,CAACR,oBAAoB,CAACmB,QAAQ,CAACG,IAAI,CAAC,CAAC;QACpD;MACF;MAEA,IAAIH,QAAQ,IAAIA,QAAQ,CAACG,IAAI,EAAE;QAC7B,OAAOd,MAAM,CAACW,QAAQ,CAACG,IAAI,CAAC;MAC9B;MAEA,OAAOd,MAAM,CAACW,QAAQ,CAACG,IAAI,CAAC;IAC9B;EACF;EAEA,OAAOf,OAAO,CAAEY,QAAQ,CAAmBG,IAAI,CAAC;AAClD;AAEA,OAAO,SAASR,WAAW,CAACD,CAAa,EAAEL,MAA8B,EAAE;EACzEmB,OAAO,CAACH,KAAK,CAACX,CAAC,CAAC;EAEhB,IAAIA,CAAC,CAACM,QAAQ,EAAE;IACd,IAAIlB,gBAAgB,CAACY,CAAC,CAACM,QAAQ,CAACG,IAAI,CAAC,EAAE;MACrC,OAAOd,MAAM,CAACK,CAAC,CAACM,QAAQ,CAACG,IAAI,CAAC;IAChC;IAEA,IAAI,OAAOT,CAAC,CAACM,QAAQ,CAACG,IAAI,KAAK,QAAQ,EAAE;MACvC,OAAOd,MAAM,CAACR,oBAAoB,CAACa,CAAC,CAACM,QAAQ,CAACG,IAAI,CAAC,CAAC;IACtD;EACF;EAEA,IAAIT,CAAC,CAACM,QAAQ,IAAIN,CAAC,CAACM,QAAQ,CAACG,IAAI,EAAE;IACjC,MAAMA,IAAI,GAAGT,CAAC,CAACM,QAAQ,CAACG,IAAW;IACnC,IAAI,OAAOA,IAAI,CAACE,KAAK,KAAK,QAAQ,IAAI,OAAOF,IAAI,CAACM,MAAM,KAAK,QAAQ,EAAE;MACrE,OAAOpB,MAAM,CAACR,oBAAoB,CAACsB,IAAI,CAACE,KAAK,EAAEF,IAAI,CAACM,MAAM,CAAC,CAAC;IAC9D;IAEA,OAAOpB,MAAM,CAACK,CAAC,CAACM,QAAQ,CAACG,IAAI,CAAC;EAChC;EAEA,OAAOd,MAAM,CAACK,CAAC,CAACa,OAAO,CAAC;AAC1B;AAEA,SAASN,SAAS,CAACD,QAAiD,EAAE;EACpE,OAAQA,QAAQ,CAAmBU,MAAM,IAAI,GAAG,IAAKV,QAAQ,CAAmBU,MAAM,GAAG,GAAG;AAC9F;AAEA,SAASR,YAAY,CAACF,QAAiD,EAA0B;EAC/F,OAAO,cAAc,IAAIA,QAAQ,IAAIA,QAAQ,CAACE,YAAY;AAC5D;AAEA,SAASE,OAAO,CAACJ,QAAa,EAAqB;EACjD,OAAOA,QAAQ,YAAYW,KAAK;AAClC;AAEA,OAAO,MAAMC,GAAG,GAAG,IAAI7B,OAAO,EAAE"}, "metadata": {}, "sourceType": "module"}