{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\nimport { sortBy } from 'utils/sort';\nconst initialState = {\n  customers: []\n};\nexport const customersSlice = createSlice({\n  name: 'customers',\n  initialState,\n  reducers: {\n    setCustomers(state, action) {\n      state.customers = action.payload;\n    }\n  }\n});\nexport const {\n  setCustomers\n} = customersSlice.actions;\nexport const selectCustomers = state => state.customers.customers.map(z => ({\n  ...z\n})).sort(sortBy('name'));\nexport default customersSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "sortBy", "initialState", "customers", "customersSlice", "name", "reducers", "setCustomers", "state", "action", "payload", "actions", "selectCustomers", "map", "z", "sort", "reducer"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/customers/customers-slice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\r\nimport { Customer } from 'api/models/customers';\r\nimport { RootState } from 'app/store';\r\nimport { sortBy } from 'utils/sort';\r\n\r\nexport interface CustomersState {\r\n  customers: Customer[];\r\n}\r\n\r\nconst initialState: CustomersState = {\r\n  customers: []\r\n};\r\n\r\nexport const customersSlice = createSlice({\r\n  name: 'customers',\r\n  initialState,\r\n  reducers: {\r\n    setCustomers(state, action: PayloadAction<Customer[]>) {\r\n      state.customers = action.payload;\r\n    }\r\n  }\r\n});\r\n\r\nexport const { setCustomers } = customersSlice.actions;\r\n\r\nexport const selectCustomers = (state: RootState) => state.customers.customers.map(z => ({...z})).sort(sortBy('name'));\r\n\r\nexport default customersSlice.reducer;\r\n"], "mappings": "AAAA,SAASA,WAAW,QAAuB,kBAAkB;AAG7D,SAASC,MAAM,QAAQ,YAAY;AAMnC,MAAMC,YAA4B,GAAG;EACnCC,SAAS,EAAE;AACb,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGJ,WAAW,CAAC;EACxCK,IAAI,EAAE,WAAW;EACjBH,YAAY;EACZI,QAAQ,EAAE;IACRC,YAAY,CAACC,KAAK,EAAEC,MAAiC,EAAE;MACrDD,KAAK,CAACL,SAAS,GAAGM,MAAM,CAACC,OAAO;IAClC;EACF;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEH;AAAa,CAAC,GAAGH,cAAc,CAACO,OAAO;AAEtD,OAAO,MAAMC,eAAe,GAAIJ,KAAgB,IAAKA,KAAK,CAACL,SAAS,CAACA,SAAS,CAACU,GAAG,CAACC,CAAC,KAAK;EAAC,GAAGA;AAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAACd,MAAM,CAAC,MAAM,CAAC,CAAC;AAEtH,eAAeG,cAAc,CAACY,OAAO"}, "metadata": {}, "sourceType": "module"}