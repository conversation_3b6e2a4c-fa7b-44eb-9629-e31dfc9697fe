{"ast": null, "code": "import * as React from 'react';\nimport warning from 'warning';\nimport { ManagerReferenceNodeSetterContext } from './Manager';\nimport { safeInvoke, unwrapArray, setRef } from './utils';\nexport function Reference(_ref) {\n  var children = _ref.children,\n    innerRef = _ref.innerRef;\n  var setReferenceNode = React.useContext(ManagerReferenceNodeSetterContext);\n  var refHandler = React.useCallback(function (node) {\n    setRef(innerRef, node);\n    safeInvoke(setReferenceNode, node);\n  }, [innerRef, setReferenceNode]); // ran on unmount\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n\n  React.useEffect(function () {\n    return function () {\n      return setRef(innerRef, null);\n    };\n  }, []);\n  React.useEffect(function () {\n    warning(Boolean(setReferenceNode), '`Reference` should not be used outside of a `Manager` component.');\n  }, [setReferenceNode]);\n  return unwrapArray(children)({\n    ref: refHand<PERSON>\n  });\n}", "map": {"version": 3, "names": ["React", "warning", "ManagerReferenceNodeSetterContext", "safeInvoke", "unwrapArray", "setRef", "Reference", "_ref", "children", "innerRef", "setReferenceNode", "useContext", "ref<PERSON><PERSON><PERSON>", "useCallback", "node", "useEffect", "Boolean", "ref"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/node_modules/react-popper/lib/esm/Reference.js"], "sourcesContent": ["import * as React from 'react';\nimport warning from 'warning';\nimport { ManagerReferenceNodeSetterContext } from './Manager';\nimport { safeInvoke, unwrapArray, setRef } from './utils';\nexport function Reference(_ref) {\n  var children = _ref.children,\n      innerRef = _ref.innerRef;\n  var setReferenceNode = React.useContext(ManagerReferenceNodeSetterContext);\n  var refHandler = React.useCallback(function (node) {\n    setRef(innerRef, node);\n    safeInvoke(setReferenceNode, node);\n  }, [innerRef, setReferenceNode]); // ran on unmount\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n\n  React.useEffect(function () {\n    return function () {\n      return setRef(innerRef, null);\n    };\n  }, []);\n  React.useEffect(function () {\n    warning(Boolean(setReferenceNode), '`Reference` should not be used outside of a `Manager` component.');\n  }, [setReferenceNode]);\n  return unwrapArray(children)({\n    ref: refHand<PERSON>\n  });\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,SAAS;AAC7B,SAASC,iCAAiC,QAAQ,WAAW;AAC7D,SAASC,UAAU,EAAEC,WAAW,EAAEC,MAAM,QAAQ,SAAS;AACzD,OAAO,SAASC,SAAS,CAACC,IAAI,EAAE;EAC9B,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IACxBC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;EAC5B,IAAIC,gBAAgB,GAAGV,KAAK,CAACW,UAAU,CAACT,iCAAiC,CAAC;EAC1E,IAAIU,UAAU,GAAGZ,KAAK,CAACa,WAAW,CAAC,UAAUC,IAAI,EAAE;IACjDT,MAAM,CAACI,QAAQ,EAAEK,IAAI,CAAC;IACtBX,UAAU,CAACO,gBAAgB,EAAEI,IAAI,CAAC;EACpC,CAAC,EAAE,CAACL,QAAQ,EAAEC,gBAAgB,CAAC,CAAC,CAAC,CAAC;EAClC;;EAEAV,KAAK,CAACe,SAAS,CAAC,YAAY;IAC1B,OAAO,YAAY;MACjB,OAAOV,MAAM,CAACI,QAAQ,EAAE,IAAI,CAAC;IAC/B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACNT,KAAK,CAACe,SAAS,CAAC,YAAY;IAC1Bd,OAAO,CAACe,OAAO,CAACN,gBAAgB,CAAC,EAAE,kEAAkE,CAAC;EACxG,CAAC,EAAE,CAACA,gBAAgB,CAAC,CAAC;EACtB,OAAON,WAAW,CAACI,QAAQ,CAAC,CAAC;IAC3BS,GAAG,EAAEL;EACP,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module"}