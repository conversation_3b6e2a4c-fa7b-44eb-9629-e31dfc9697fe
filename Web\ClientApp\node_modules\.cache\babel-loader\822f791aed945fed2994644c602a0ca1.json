{"ast": null, "code": "'use strict';\n\nimport utils from './../utils.js';\nimport platform from '../platform/index.js';\nexport default platform.isStandardBrowserEnv ?\n// Standard browser envs support document.cookie\nfunction standardBrowserEnv() {\n  return {\n    write: function write(name, value, expires, path, domain, secure) {\n      const cookie = [];\n      cookie.push(name + '=' + encodeURIComponent(value));\n      if (utils.isNumber(expires)) {\n        cookie.push('expires=' + new Date(expires).toGMTString());\n      }\n      if (utils.isString(path)) {\n        cookie.push('path=' + path);\n      }\n      if (utils.isString(domain)) {\n        cookie.push('domain=' + domain);\n      }\n      if (secure === true) {\n        cookie.push('secure');\n      }\n      document.cookie = cookie.join('; ');\n    },\n    read: function read(name) {\n      const match = document.cookie.match(new RegExp('(^|;\\\\s*)(' + name + ')=([^;]*)'));\n      return match ? decodeURIComponent(match[3]) : null;\n    },\n    remove: function remove(name) {\n      this.write(name, '', Date.now() - 86400000);\n    }\n  };\n}() :\n// Non standard browser env (web workers, react-native) lack needed support.\nfunction nonStandardBrowserEnv() {\n  return {\n    write: function write() {},\n    read: function read() {\n      return null;\n    },\n    remove: function remove() {}\n  };\n}();", "map": {"version": 3, "names": ["utils", "platform", "isStandardBrowserEnv", "standardBrowserEnv", "write", "name", "value", "expires", "path", "domain", "secure", "cookie", "push", "encodeURIComponent", "isNumber", "Date", "toGMTString", "isString", "document", "join", "read", "match", "RegExp", "decodeURIComponent", "remove", "now", "nonStandardBrowserEnv"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/node_modules/axios/lib/helpers/cookies.js"], "sourcesContent": ["'use strict';\n\nimport utils from './../utils.js';\nimport platform from '../platform/index.js';\n\nexport default platform.isStandardBrowserEnv ?\n\n// Standard browser envs support document.cookie\n  (function standardBrowserEnv() {\n    return {\n      write: function write(name, value, expires, path, domain, secure) {\n        const cookie = [];\n        cookie.push(name + '=' + encodeURIComponent(value));\n\n        if (utils.isNumber(expires)) {\n          cookie.push('expires=' + new Date(expires).toGMTString());\n        }\n\n        if (utils.isString(path)) {\n          cookie.push('path=' + path);\n        }\n\n        if (utils.isString(domain)) {\n          cookie.push('domain=' + domain);\n        }\n\n        if (secure === true) {\n          cookie.push('secure');\n        }\n\n        document.cookie = cookie.join('; ');\n      },\n\n      read: function read(name) {\n        const match = document.cookie.match(new RegExp('(^|;\\\\s*)(' + name + ')=([^;]*)'));\n        return (match ? decodeURIComponent(match[3]) : null);\n      },\n\n      remove: function remove(name) {\n        this.write(name, '', Date.now() - 86400000);\n      }\n    };\n  })() :\n\n// Non standard browser env (web workers, react-native) lack needed support.\n  (function nonStandardBrowserEnv() {\n    return {\n      write: function write() {},\n      read: function read() { return null; },\n      remove: function remove() {}\n    };\n  })();\n"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,MAAM,eAAe;AACjC,OAAOC,QAAQ,MAAM,sBAAsB;AAE3C,eAAeA,QAAQ,CAACC,oBAAoB;AAE5C;AACG,SAASC,kBAAkB,GAAG;EAC7B,OAAO;IACLC,KAAK,EAAE,SAASA,KAAK,CAACC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAE;MAChE,MAAMC,MAAM,GAAG,EAAE;MACjBA,MAAM,CAACC,IAAI,CAACP,IAAI,GAAG,GAAG,GAAGQ,kBAAkB,CAACP,KAAK,CAAC,CAAC;MAEnD,IAAIN,KAAK,CAACc,QAAQ,CAACP,OAAO,CAAC,EAAE;QAC3BI,MAAM,CAACC,IAAI,CAAC,UAAU,GAAG,IAAIG,IAAI,CAACR,OAAO,CAAC,CAACS,WAAW,EAAE,CAAC;MAC3D;MAEA,IAAIhB,KAAK,CAACiB,QAAQ,CAACT,IAAI,CAAC,EAAE;QACxBG,MAAM,CAACC,IAAI,CAAC,OAAO,GAAGJ,IAAI,CAAC;MAC7B;MAEA,IAAIR,KAAK,CAACiB,QAAQ,CAACR,MAAM,CAAC,EAAE;QAC1BE,MAAM,CAACC,IAAI,CAAC,SAAS,GAAGH,MAAM,CAAC;MACjC;MAEA,IAAIC,MAAM,KAAK,IAAI,EAAE;QACnBC,MAAM,CAACC,IAAI,CAAC,QAAQ,CAAC;MACvB;MAEAM,QAAQ,CAACP,MAAM,GAAGA,MAAM,CAACQ,IAAI,CAAC,IAAI,CAAC;IACrC,CAAC;IAEDC,IAAI,EAAE,SAASA,IAAI,CAACf,IAAI,EAAE;MACxB,MAAMgB,KAAK,GAAGH,QAAQ,CAACP,MAAM,CAACU,KAAK,CAAC,IAAIC,MAAM,CAAC,YAAY,GAAGjB,IAAI,GAAG,WAAW,CAAC,CAAC;MAClF,OAAQgB,KAAK,GAAGE,kBAAkB,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;IACrD,CAAC;IAEDG,MAAM,EAAE,SAASA,MAAM,CAACnB,IAAI,EAAE;MAC5B,IAAI,CAACD,KAAK,CAACC,IAAI,EAAE,EAAE,EAAEU,IAAI,CAACU,GAAG,EAAE,GAAG,QAAQ,CAAC;IAC7C;EACF,CAAC;AACH,CAAC,EAAG;AAEN;AACG,SAASC,qBAAqB,GAAG;EAChC,OAAO;IACLtB,KAAK,EAAE,SAASA,KAAK,GAAG,CAAC,CAAC;IAC1BgB,IAAI,EAAE,SAASA,IAAI,GAAG;MAAE,OAAO,IAAI;IAAE,CAAC;IACtCI,MAAM,EAAE,SAASA,MAAM,GAAG,CAAC;EAC7B,CAAC;AACH,CAAC,EAAG"}, "metadata": {}, "sourceType": "module"}