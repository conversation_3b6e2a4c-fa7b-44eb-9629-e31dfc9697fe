{"ast": null, "code": "require('../modules/es.global-this');\nmodule.exports = require('../internals/global');", "map": {"version": 3, "names": ["require", "module", "exports"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/node_modules/core-js-pure/es/global-this.js"], "sourcesContent": ["require('../modules/es.global-this');\n\nmodule.exports = require('../internals/global');\n"], "mappings": "AAAAA,OAAO,CAAC,2BAA2B,CAAC;AAEpCC,MAAM,CAACC,OAAO,GAAGF,OAAO,CAAC,qBAAqB,CAAC"}, "metadata": {}, "sourceType": "script"}