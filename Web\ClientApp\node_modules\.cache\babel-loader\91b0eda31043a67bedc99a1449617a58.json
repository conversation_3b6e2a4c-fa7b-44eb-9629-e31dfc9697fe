{"ast": null, "code": "/**\n * React Router DOM v6.4.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport * as React from 'react';\nimport { UNSAFE_enhanceManualRouteObjects, Router, useHref, useResolvedPath, useLocation, UNSAFE_DataRouterStateContext, useNavigate, createPath, UNSAFE_NavigationContext, UNSAFE_RouteContext, useMatches, useNavigation, UNSAFE_DataRouterContext } from 'react-router';\nexport { AbortedDeferredError, Await, MemoryRouter, Navigate, NavigationType, Outlet, Route, Router, RouterProvider, Routes, UNSAFE_DataRouterContext, UNSAFE_DataRouterStateContext, UNSAFE_DataStaticRouterContext, UNSAFE_LocationContext, UNSAFE_NavigationContext, UNSAFE_RouteContext, UNSAF<PERSON>_enhanceManualRouteObjects, createMemoryRouter, createPath, createRoutesFromChildren, createRoutesFromElements, defer, generatePath, isRouteErrorResponse, json, matchPath, matchRoutes, parsePath, redirect, renderMatches, resolvePath, useActionData, useAsyncError, useAsyncValue, useHref, useInRouterContext, useLoaderData, useLocation, useMatch, useMatches, useNavigate, useNavigation, useNavigationType, useOutlet, useOutletContext, useParams, useResolvedPath, useRevalidator, useRouteError, useRouteLoaderData, useRoutes } from 'react-router';\nimport { createRouter, createBrowserHistory, createHashHistory, invariant, joinPaths } from '@remix-run/router';\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nconst defaultMethod = \"get\";\nconst defaultEncType = \"application/x-www-form-urlencoded\";\nfunction isHtmlElement(object) {\n  return object != null && typeof object.tagName === \"string\";\n}\nfunction isButtonElement(object) {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"button\";\n}\nfunction isFormElement(object) {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"form\";\n}\nfunction isInputElement(object) {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"input\";\n}\nfunction isModifiedEvent(event) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\nfunction shouldProcessLinkClick(event, target) {\n  return event.button === 0 && (\n  // Ignore everything but left clicks\n  !target || target === \"_self\") &&\n  // Let browser handle \"target=_blank\" etc.\n  !isModifiedEvent(event) // Ignore clicks with modifier keys\n  ;\n}\n/**\n * Creates a URLSearchParams object using the given initializer.\n *\n * This is identical to `new URLSearchParams(init)` except it also\n * supports arrays as values in the object form of the initializer\n * instead of just strings. This is convenient when you need multiple\n * values for a given key, but don't want to use an array initializer.\n *\n * For example, instead of:\n *\n *   let searchParams = new URLSearchParams([\n *     ['sort', 'name'],\n *     ['sort', 'price']\n *   ]);\n *\n * you can do:\n *\n *   let searchParams = createSearchParams({\n *     sort: ['name', 'price']\n *   });\n */\n\nfunction createSearchParams(init) {\n  if (init === void 0) {\n    init = \"\";\n  }\n  return new URLSearchParams(typeof init === \"string\" || Array.isArray(init) || init instanceof URLSearchParams ? init : Object.keys(init).reduce((memo, key) => {\n    let value = init[key];\n    return memo.concat(Array.isArray(value) ? value.map(v => [key, v]) : [[key, value]]);\n  }, []));\n}\nfunction getSearchParamsForLocation(locationSearch, defaultSearchParams) {\n  let searchParams = createSearchParams(locationSearch);\n  for (let key of defaultSearchParams.keys()) {\n    if (!searchParams.has(key)) {\n      defaultSearchParams.getAll(key).forEach(value => {\n        searchParams.append(key, value);\n      });\n    }\n  }\n  return searchParams;\n}\nfunction getFormSubmissionInfo(target, defaultAction, options) {\n  let method;\n  let action;\n  let encType;\n  let formData;\n  if (isFormElement(target)) {\n    let submissionTrigger = options.submissionTrigger;\n    method = options.method || target.getAttribute(\"method\") || defaultMethod;\n    action = options.action || target.getAttribute(\"action\") || defaultAction;\n    encType = options.encType || target.getAttribute(\"enctype\") || defaultEncType;\n    formData = new FormData(target);\n    if (submissionTrigger && submissionTrigger.name) {\n      formData.append(submissionTrigger.name, submissionTrigger.value);\n    }\n  } else if (isButtonElement(target) || isInputElement(target) && (target.type === \"submit\" || target.type === \"image\")) {\n    let form = target.form;\n    if (form == null) {\n      throw new Error(\"Cannot submit a <button> or <input type=\\\"submit\\\"> without a <form>\");\n    } // <button>/<input type=\"submit\"> may override attributes of <form>\n\n    method = options.method || target.getAttribute(\"formmethod\") || form.getAttribute(\"method\") || defaultMethod;\n    action = options.action || target.getAttribute(\"formaction\") || form.getAttribute(\"action\") || defaultAction;\n    encType = options.encType || target.getAttribute(\"formenctype\") || form.getAttribute(\"enctype\") || defaultEncType;\n    formData = new FormData(form); // Include name + value from a <button>, appending in case the button name\n    // matches an existing input name\n\n    if (target.name) {\n      formData.append(target.name, target.value);\n    }\n  } else if (isHtmlElement(target)) {\n    throw new Error(\"Cannot submit element that is not <form>, <button>, or \" + \"<input type=\\\"submit|image\\\">\");\n  } else {\n    method = options.method || defaultMethod;\n    action = options.action || defaultAction;\n    encType = options.encType || defaultEncType;\n    if (target instanceof FormData) {\n      formData = target;\n    } else {\n      formData = new FormData();\n      if (target instanceof URLSearchParams) {\n        for (let [name, value] of target) {\n          formData.append(name, value);\n        }\n      } else if (target != null) {\n        for (let name of Object.keys(target)) {\n          formData.append(name, target[name]);\n        }\n      }\n    }\n  }\n  let {\n    protocol,\n    host\n  } = window.location;\n  let url = new URL(action, protocol + \"//\" + host);\n  return {\n    url,\n    method,\n    encType,\n    formData\n  };\n}\nconst _excluded = [\"onClick\", \"relative\", \"reloadDocument\", \"replace\", \"state\", \"target\", \"to\", \"preventScrollReset\"],\n  _excluded2 = [\"aria-current\", \"caseSensitive\", \"className\", \"end\", \"style\", \"to\", \"children\"],\n  _excluded3 = [\"reloadDocument\", \"replace\", \"method\", \"action\", \"onSubmit\", \"fetcherKey\", \"routeId\", \"relative\"];\n//#region Routers\n////////////////////////////////////////////////////////////////////////////////\n\nfunction createBrowserRouter(routes, opts) {\n  var _window;\n  return createRouter({\n    basename: opts == null ? void 0 : opts.basename,\n    history: createBrowserHistory({\n      window: opts == null ? void 0 : opts.window\n    }),\n    hydrationData: (opts == null ? void 0 : opts.hydrationData) || ((_window = window) == null ? void 0 : _window.__staticRouterHydrationData),\n    routes: UNSAFE_enhanceManualRouteObjects(routes)\n  }).initialize();\n}\nfunction createHashRouter(routes, opts) {\n  var _window2;\n  return createRouter({\n    basename: opts == null ? void 0 : opts.basename,\n    history: createHashHistory({\n      window: opts == null ? void 0 : opts.window\n    }),\n    hydrationData: (opts == null ? void 0 : opts.hydrationData) || ((_window2 = window) == null ? void 0 : _window2.__staticRouterHydrationData),\n    routes: UNSAFE_enhanceManualRouteObjects(routes)\n  }).initialize();\n}\n/**\n * A `<Router>` for use in web browsers. Provides the cleanest URLs.\n */\n\nfunction BrowserRouter(_ref) {\n  let {\n    basename,\n    children,\n    window\n  } = _ref;\n  let historyRef = React.useRef();\n  if (historyRef.current == null) {\n    historyRef.current = createBrowserHistory({\n      window,\n      v5Compat: true\n    });\n  }\n  let history = historyRef.current;\n  let [state, setState] = React.useState({\n    action: history.action,\n    location: history.location\n  });\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n  return /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history\n  });\n}\n/**\n * A `<Router>` for use in web browsers. Stores the location in the hash\n * portion of the URL so it is not sent to the server.\n */\n\nfunction HashRouter(_ref2) {\n  let {\n    basename,\n    children,\n    window\n  } = _ref2;\n  let historyRef = React.useRef();\n  if (historyRef.current == null) {\n    historyRef.current = createHashHistory({\n      window,\n      v5Compat: true\n    });\n  }\n  let history = historyRef.current;\n  let [state, setState] = React.useState({\n    action: history.action,\n    location: history.location\n  });\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n  return /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history\n  });\n}\n/**\n * A `<Router>` that accepts a pre-instantiated history object. It's important\n * to note that using your own history object is highly discouraged and may add\n * two versions of the history library to your bundles unless you use the same\n * version of the history library that React Router uses internally.\n */\n\nfunction HistoryRouter(_ref3) {\n  let {\n    basename,\n    children,\n    history\n  } = _ref3;\n  const [state, setState] = React.useState({\n    action: history.action,\n    location: history.location\n  });\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n  return /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history\n  });\n}\nif (process.env.NODE_ENV !== \"production\") {\n  HistoryRouter.displayName = \"unstable_HistoryRouter\";\n}\n/**\n * The public API for rendering a history-aware <a>.\n */\n\nconst Link = /*#__PURE__*/React.forwardRef(function LinkWithRef(_ref4, ref) {\n  let {\n      onClick,\n      relative,\n      reloadDocument,\n      replace,\n      state,\n      target,\n      to,\n      preventScrollReset\n    } = _ref4,\n    rest = _objectWithoutPropertiesLoose(_ref4, _excluded);\n  let href = useHref(to, {\n    relative\n  });\n  let internalOnClick = useLinkClickHandler(to, {\n    replace,\n    state,\n    target,\n    preventScrollReset,\n    relative\n  });\n  function handleClick(event) {\n    if (onClick) onClick(event);\n    if (!event.defaultPrevented) {\n      internalOnClick(event);\n    }\n  }\n  return /*#__PURE__*/(\n    // eslint-disable-next-line jsx-a11y/anchor-has-content\n    React.createElement(\"a\", _extends({}, rest, {\n      href: href,\n      onClick: reloadDocument ? onClick : handleClick,\n      ref: ref,\n      target: target\n    }))\n  );\n});\nif (process.env.NODE_ENV !== \"production\") {\n  Link.displayName = \"Link\";\n}\n/**\n * A <Link> wrapper that knows if it's \"active\" or not.\n */\n\nconst NavLink = /*#__PURE__*/React.forwardRef(function NavLinkWithRef(_ref5, ref) {\n  let {\n      \"aria-current\": ariaCurrentProp = \"page\",\n      caseSensitive = false,\n      className: classNameProp = \"\",\n      end = false,\n      style: styleProp,\n      to,\n      children\n    } = _ref5,\n    rest = _objectWithoutPropertiesLoose(_ref5, _excluded2);\n  let path = useResolvedPath(to, {\n    relative: rest.relative\n  });\n  let location = useLocation();\n  let routerState = React.useContext(UNSAFE_DataRouterStateContext);\n  let toPathname = path.pathname;\n  let locationPathname = location.pathname;\n  let nextLocationPathname = routerState && routerState.navigation && routerState.navigation.location ? routerState.navigation.location.pathname : null;\n  if (!caseSensitive) {\n    locationPathname = locationPathname.toLowerCase();\n    nextLocationPathname = nextLocationPathname ? nextLocationPathname.toLowerCase() : null;\n    toPathname = toPathname.toLowerCase();\n  }\n  let isActive = locationPathname === toPathname || !end && locationPathname.startsWith(toPathname) && locationPathname.charAt(toPathname.length) === \"/\";\n  let isPending = nextLocationPathname != null && (nextLocationPathname === toPathname || !end && nextLocationPathname.startsWith(toPathname) && nextLocationPathname.charAt(toPathname.length) === \"/\");\n  let ariaCurrent = isActive ? ariaCurrentProp : undefined;\n  let className;\n  if (typeof classNameProp === \"function\") {\n    className = classNameProp({\n      isActive,\n      isPending\n    });\n  } else {\n    // If the className prop is not a function, we use a default `active`\n    // class for <NavLink />s that are active. In v5 `active` was the default\n    // value for `activeClassName`, but we are removing that API and can still\n    // use the old default behavior for a cleaner upgrade path and keep the\n    // simple styling rules working as they currently do.\n    className = [classNameProp, isActive ? \"active\" : null, isPending ? \"pending\" : null].filter(Boolean).join(\" \");\n  }\n  let style = typeof styleProp === \"function\" ? styleProp({\n    isActive,\n    isPending\n  }) : styleProp;\n  return /*#__PURE__*/React.createElement(Link, _extends({}, rest, {\n    \"aria-current\": ariaCurrent,\n    className: className,\n    ref: ref,\n    style: style,\n    to: to\n  }), typeof children === \"function\" ? children({\n    isActive,\n    isPending\n  }) : children);\n});\nif (process.env.NODE_ENV !== \"production\") {\n  NavLink.displayName = \"NavLink\";\n}\n/**\n * A `@remix-run/router`-aware `<form>`. It behaves like a normal form except\n * that the interaction with the server is with `fetch` instead of new document\n * requests, allowing components to add nicer UX to the page as the form is\n * submitted and returns with data.\n */\n\nconst Form = /*#__PURE__*/React.forwardRef((props, ref) => {\n  return /*#__PURE__*/React.createElement(FormImpl, _extends({}, props, {\n    ref: ref\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") {\n  Form.displayName = \"Form\";\n}\nconst FormImpl = /*#__PURE__*/React.forwardRef((_ref6, forwardedRef) => {\n  let {\n      reloadDocument,\n      replace,\n      method = defaultMethod,\n      action,\n      onSubmit,\n      fetcherKey,\n      routeId,\n      relative\n    } = _ref6,\n    props = _objectWithoutPropertiesLoose(_ref6, _excluded3);\n  let submit = useSubmitImpl(fetcherKey, routeId);\n  let formMethod = method.toLowerCase() === \"get\" ? \"get\" : \"post\";\n  let formAction = useFormAction(action, {\n    relative\n  });\n  let submitHandler = event => {\n    onSubmit && onSubmit(event);\n    if (event.defaultPrevented) return;\n    event.preventDefault();\n    let submitter = event.nativeEvent.submitter;\n    submit(submitter || event.currentTarget, {\n      method,\n      replace,\n      relative\n    });\n  };\n  return /*#__PURE__*/React.createElement(\"form\", _extends({\n    ref: forwardedRef,\n    method: formMethod,\n    action: formAction,\n    onSubmit: reloadDocument ? onSubmit : submitHandler\n  }, props));\n});\nif (process.env.NODE_ENV !== \"production\") {\n  Form.displayName = \"Form\";\n}\n/**\n * This component will emulate the browser's scroll restoration on location\n * changes.\n */\n\nfunction ScrollRestoration(_ref7) {\n  let {\n    getKey,\n    storageKey\n  } = _ref7;\n  useScrollRestoration({\n    getKey,\n    storageKey\n  });\n  return null;\n}\nif (process.env.NODE_ENV !== \"production\") {\n  ScrollRestoration.displayName = \"ScrollRestoration\";\n} //#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region Hooks\n////////////////////////////////////////////////////////////////////////////////\n\nvar DataRouterHook;\n(function (DataRouterHook) {\n  DataRouterHook[\"UseScrollRestoration\"] = \"useScrollRestoration\";\n  DataRouterHook[\"UseSubmitImpl\"] = \"useSubmitImpl\";\n  DataRouterHook[\"UseFetcher\"] = \"useFetcher\";\n})(DataRouterHook || (DataRouterHook = {}));\nvar DataRouterStateHook;\n(function (DataRouterStateHook) {\n  DataRouterStateHook[\"UseFetchers\"] = \"useFetchers\";\n  DataRouterStateHook[\"UseScrollRestoration\"] = \"useScrollRestoration\";\n})(DataRouterStateHook || (DataRouterStateHook = {}));\nfunction getDataRouterConsoleError(hookName) {\n  return hookName + \" must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.\";\n}\nfunction useDataRouterContext(hookName) {\n  let ctx = React.useContext(UNSAFE_DataRouterContext);\n  !ctx ? process.env.NODE_ENV !== \"production\" ? invariant(false, getDataRouterConsoleError(hookName)) : invariant(false) : void 0;\n  return ctx;\n}\nfunction useDataRouterState(hookName) {\n  let state = React.useContext(UNSAFE_DataRouterStateContext);\n  !state ? process.env.NODE_ENV !== \"production\" ? invariant(false, getDataRouterConsoleError(hookName)) : invariant(false) : void 0;\n  return state;\n}\n/**\n * Handles the click behavior for router `<Link>` components. This is useful if\n * you need to create custom `<Link>` components with the same click behavior we\n * use in our exported `<Link>`.\n */\n\nfunction useLinkClickHandler(to, _temp) {\n  let {\n    target,\n    replace: replaceProp,\n    state,\n    preventScrollReset,\n    relative\n  } = _temp === void 0 ? {} : _temp;\n  let navigate = useNavigate();\n  let location = useLocation();\n  let path = useResolvedPath(to, {\n    relative\n  });\n  return React.useCallback(event => {\n    if (shouldProcessLinkClick(event, target)) {\n      event.preventDefault(); // If the URL hasn't changed, a regular <a> will do a replace instead of\n      // a push, so do the same here unless the replace prop is explicitly set\n\n      let replace = replaceProp !== undefined ? replaceProp : createPath(location) === createPath(path);\n      navigate(to, {\n        replace,\n        state,\n        preventScrollReset,\n        relative\n      });\n    }\n  }, [location, navigate, path, replaceProp, state, target, to, preventScrollReset, relative]);\n}\n/**\n * A convenient wrapper for reading and writing search parameters via the\n * URLSearchParams interface.\n */\n\nfunction useSearchParams(defaultInit) {\n  process.env.NODE_ENV !== \"production\" ? warning(typeof URLSearchParams !== \"undefined\", \"You cannot use the `useSearchParams` hook in a browser that does not \" + \"support the URLSearchParams API. If you need to support Internet \" + \"Explorer 11, we recommend you load a polyfill such as \" + \"https://github.com/ungap/url-search-params\\n\\n\" + \"If you're unsure how to load polyfills, we recommend you check out \" + \"https://polyfill.io/v3/ which provides some recommendations about how \" + \"to load polyfills only for users that need them, instead of for every \" + \"user.\") : void 0;\n  let defaultSearchParamsRef = React.useRef(createSearchParams(defaultInit));\n  let location = useLocation();\n  let searchParams = React.useMemo(() => getSearchParamsForLocation(location.search, defaultSearchParamsRef.current), [location.search]);\n  let navigate = useNavigate();\n  let setSearchParams = React.useCallback((nextInit, navigateOptions) => {\n    const newSearchParams = createSearchParams(typeof nextInit === \"function\" ? nextInit(searchParams) : nextInit);\n    navigate(\"?\" + newSearchParams, navigateOptions);\n  }, [navigate, searchParams]);\n  return [searchParams, setSearchParams];\n}\n/**\n * Returns a function that may be used to programmatically submit a form (or\n * some arbitrary data) to the server.\n */\n\nfunction useSubmit() {\n  return useSubmitImpl();\n}\nfunction useSubmitImpl(fetcherKey, routeId) {\n  let {\n    router\n  } = useDataRouterContext(DataRouterHook.UseSubmitImpl);\n  let defaultAction = useFormAction();\n  return React.useCallback(function (target, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    if (typeof document === \"undefined\") {\n      throw new Error(\"You are calling submit during the server render. \" + \"Try calling submit within a `useEffect` or callback instead.\");\n    }\n    let {\n      method,\n      encType,\n      formData,\n      url\n    } = getFormSubmissionInfo(target, defaultAction, options);\n    let href = url.pathname + url.search;\n    let opts = {\n      replace: options.replace,\n      formData,\n      formMethod: method,\n      formEncType: encType\n    };\n    if (fetcherKey) {\n      !(routeId != null) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"No routeId available for useFetcher()\") : invariant(false) : void 0;\n      router.fetch(fetcherKey, routeId, href, opts);\n    } else {\n      router.navigate(href, opts);\n    }\n  }, [defaultAction, router, fetcherKey, routeId]);\n}\nfunction useFormAction(action, _temp2) {\n  let {\n    relative\n  } = _temp2 === void 0 ? {} : _temp2;\n  let {\n    basename\n  } = React.useContext(UNSAFE_NavigationContext);\n  let routeContext = React.useContext(UNSAFE_RouteContext);\n  !routeContext ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"useFormAction must be used inside a RouteContext\") : invariant(false) : void 0;\n  let [match] = routeContext.matches.slice(-1);\n  let resolvedAction = action != null ? action : \".\"; // Shallow clone path so we can modify it below, otherwise we modify the\n  // object referenced by useMemo inside useResolvedPath\n\n  let path = _extends({}, useResolvedPath(resolvedAction, {\n    relative\n  })); // Previously we set the default action to \".\". The problem with this is that\n  // `useResolvedPath(\".\")` excludes search params and the hash of the resolved\n  // URL. This is the intended behavior of when \".\" is specifically provided as\n  // the form action, but inconsistent w/ browsers when the action is omitted.\n  // https://github.com/remix-run/remix/issues/927\n\n  let location = useLocation();\n  if (action == null) {\n    // Safe to write to these directly here since if action was undefined, we\n    // would have called useResolvedPath(\".\") which will never include a search\n    // or hash\n    path.search = location.search;\n    path.hash = location.hash; // When grabbing search params from the URL, remove the automatically\n    // inserted ?index param so we match the useResolvedPath search behavior\n    // which would not include ?index\n\n    if (match.route.index) {\n      let params = new URLSearchParams(path.search);\n      params.delete(\"index\");\n      path.search = params.toString() ? \"?\" + params.toString() : \"\";\n    }\n  }\n  if ((!action || action === \".\") && match.route.index) {\n    path.search = path.search ? path.search.replace(/^\\?/, \"?index&\") : \"?index\";\n  } // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the form action.  If this is a root navigation, then just use\n  // the raw basename which allows the basename to have full control over the\n  // presence of a trailing slash on root actions\n\n  if (basename !== \"/\") {\n    path.pathname = path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n  return createPath(path);\n}\nfunction createFetcherForm(fetcherKey, routeId) {\n  let FetcherForm = /*#__PURE__*/React.forwardRef((props, ref) => {\n    return /*#__PURE__*/React.createElement(FormImpl, _extends({}, props, {\n      ref: ref,\n      fetcherKey: fetcherKey,\n      routeId: routeId\n    }));\n  });\n  if (process.env.NODE_ENV !== \"production\") {\n    FetcherForm.displayName = \"fetcher.Form\";\n  }\n  return FetcherForm;\n}\nlet fetcherId = 0;\n/**\n * Interacts with route loaders and actions without causing a navigation. Great\n * for any interaction that stays on the same page.\n */\n\nfunction useFetcher() {\n  var _route$matches;\n  let {\n    router\n  } = useDataRouterContext(DataRouterHook.UseFetcher);\n  let route = React.useContext(UNSAFE_RouteContext);\n  !route ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"useFetcher must be used inside a RouteContext\") : invariant(false) : void 0;\n  let routeId = (_route$matches = route.matches[route.matches.length - 1]) == null ? void 0 : _route$matches.route.id;\n  !(routeId != null) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"useFetcher can only be used on routes that contain a unique \\\"id\\\"\") : invariant(false) : void 0;\n  let [fetcherKey] = React.useState(() => String(++fetcherId));\n  let [Form] = React.useState(() => {\n    !routeId ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"No routeId available for fetcher.Form()\") : invariant(false) : void 0;\n    return createFetcherForm(fetcherKey, routeId);\n  });\n  let [load] = React.useState(() => href => {\n    !router ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"No router available for fetcher.load()\") : invariant(false) : void 0;\n    !routeId ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"No routeId available for fetcher.load()\") : invariant(false) : void 0;\n    router.fetch(fetcherKey, routeId, href);\n  });\n  let submit = useSubmitImpl(fetcherKey, routeId);\n  let fetcher = router.getFetcher(fetcherKey);\n  let fetcherWithComponents = React.useMemo(() => _extends({\n    Form,\n    submit,\n    load\n  }, fetcher), [fetcher, Form, submit, load]);\n  React.useEffect(() => {\n    // Is this busted when the React team gets real weird and calls effects\n    // twice on mount?  We really just need to garbage collect here when this\n    // fetcher is no longer around.\n    return () => {\n      if (!router) {\n        console.warn(\"No fetcher available to clean up from useFetcher()\");\n        return;\n      }\n      router.deleteFetcher(fetcherKey);\n    };\n  }, [router, fetcherKey]);\n  return fetcherWithComponents;\n}\n/**\n * Provides all fetchers currently on the page. Useful for layouts and parent\n * routes that need to provide pending/optimistic UI regarding the fetch.\n */\n\nfunction useFetchers() {\n  let state = useDataRouterState(DataRouterStateHook.UseFetchers);\n  return [...state.fetchers.values()];\n}\nconst SCROLL_RESTORATION_STORAGE_KEY = \"react-router-scroll-positions\";\nlet savedScrollPositions = {};\n/**\n * When rendered inside a RouterProvider, will restore scroll positions on navigations\n */\n\nfunction useScrollRestoration(_temp3) {\n  let {\n    getKey,\n    storageKey\n  } = _temp3 === void 0 ? {} : _temp3;\n  let {\n    router\n  } = useDataRouterContext(DataRouterHook.UseScrollRestoration);\n  let {\n    restoreScrollPosition,\n    preventScrollReset\n  } = useDataRouterState(DataRouterStateHook.UseScrollRestoration);\n  let location = useLocation();\n  let matches = useMatches();\n  let navigation = useNavigation(); // Trigger manual scroll restoration while we're active\n\n  React.useEffect(() => {\n    window.history.scrollRestoration = \"manual\";\n    return () => {\n      window.history.scrollRestoration = \"auto\";\n    };\n  }, []); // Save positions on unload\n\n  useBeforeUnload(React.useCallback(() => {\n    if (navigation.state === \"idle\") {\n      let key = (getKey ? getKey(location, matches) : null) || location.key;\n      savedScrollPositions[key] = window.scrollY;\n    }\n    sessionStorage.setItem(storageKey || SCROLL_RESTORATION_STORAGE_KEY, JSON.stringify(savedScrollPositions));\n    window.history.scrollRestoration = \"auto\";\n  }, [storageKey, getKey, navigation.state, location, matches])); // Read in any saved scroll locations\n\n  React.useLayoutEffect(() => {\n    try {\n      let sessionPositions = sessionStorage.getItem(storageKey || SCROLL_RESTORATION_STORAGE_KEY);\n      if (sessionPositions) {\n        savedScrollPositions = JSON.parse(sessionPositions);\n      }\n    } catch (e) {// no-op, use default empty object\n    }\n  }, [storageKey]); // Enable scroll restoration in the router\n\n  React.useLayoutEffect(() => {\n    let disableScrollRestoration = router == null ? void 0 : router.enableScrollRestoration(savedScrollPositions, () => window.scrollY, getKey);\n    return () => disableScrollRestoration && disableScrollRestoration();\n  }, [router, getKey]); // Restore scrolling when state.restoreScrollPosition changes\n\n  React.useLayoutEffect(() => {\n    // Explicit false means don't do anything (used for submissions)\n    if (restoreScrollPosition === false) {\n      return;\n    } // been here before, scroll to it\n\n    if (typeof restoreScrollPosition === \"number\") {\n      window.scrollTo(0, restoreScrollPosition);\n      return;\n    } // try to scroll to the hash\n\n    if (location.hash) {\n      let el = document.getElementById(location.hash.slice(1));\n      if (el) {\n        el.scrollIntoView();\n        return;\n      }\n    } // Opt out of scroll reset if this link requested it\n\n    if (preventScrollReset === true) {\n      return;\n    } // otherwise go to the top on new locations\n\n    window.scrollTo(0, 0);\n  }, [location, restoreScrollPosition, preventScrollReset]);\n}\nfunction useBeforeUnload(callback) {\n  React.useEffect(() => {\n    window.addEventListener(\"beforeunload\", callback);\n    return () => {\n      window.removeEventListener(\"beforeunload\", callback);\n    };\n  }, [callback]);\n} //#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region Utils\n////////////////////////////////////////////////////////////////////////////////\n\nfunction warning(cond, message) {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n    try {\n      // Welcome to debugging React Router!\n      //\n      // This error is thrown as a convenience so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message); // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n} //#endregion\n\nexport { BrowserRouter, Form, HashRouter, Link, NavLink, ScrollRestoration, createBrowserRouter, createHashRouter, createSearchParams, HistoryRouter as unstable_HistoryRouter, useFetcher, useFetchers, useFormAction, useLinkClickHandler, useSearchParams, useSubmit };", "map": {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGO,MAAMA,aAAa,GAAG,KAAtB;AACP,MAAMC,cAAc,GAAG,mCAAvB;AAEM,SAAUC,aAAV,CAAwBC,MAAxB,EAAmC;EACvC,OAAOA,MAAM,IAAI,IAAV,IAAkB,OAAOA,MAAM,CAACC,OAAd,KAA0B,QAAnD;AACD;AAEK,SAAUC,eAAV,CAA0BF,MAA1B,EAAqC;EACzC,OAAOD,aAAa,CAACC,MAAD,CAAb,IAAyBA,MAAM,CAACC,OAAP,CAAeE,WAAf,OAAiC,QAAjE;AACD;AAEK,SAAUC,aAAV,CAAwBJ,MAAxB,EAAmC;EACvC,OAAOD,aAAa,CAACC,MAAD,CAAb,IAAyBA,MAAM,CAACC,OAAP,CAAeE,WAAf,OAAiC,MAAjE;AACD;AAEK,SAAUE,cAAV,CAAyBL,MAAzB,EAAoC;EACxC,OAAOD,aAAa,CAACC,MAAD,CAAb,IAAyBA,MAAM,CAACC,OAAP,CAAeE,WAAf,OAAiC,OAAjE;AACD;AAOD,SAASG,eAAT,CAAyBC,KAAzB,EAAiD;EAC/C,OAAO,CAAC,EAAEA,KAAK,CAACC,OAAN,IAAiBD,KAAK,CAACE,MAAvB,IAAiCF,KAAK,CAACG,OAAvC,IAAkDH,KAAK,CAACI,QAA1D,CAAR;AACD;AAEe,gCACdJ,KADc,EAEdK,MAFc,EAEC;EAEf,OACEL,KAAK,CAACM,MAAN,KAAiB,CAAjB;EAAA;EACC,CAACD,MAAD,IAAWA,MAAM,KAAK,OADvB,CACmC;EAAA;EACnC,CAACN,eAAe,CAACC,KAAD,CAHlB;EAAA;AAKD;AAUD;;;;;;;;;;;;;;;;;;;;AAoBG;;AACa,4BACdO,IADc,EACgB;EAAA,IAA9BA,IAA8B;IAA9BA,IAA8B,GAAF,EAAE;EAAA;EAE9B,OAAO,IAAIC,eAAJ,CACL,OAAOD,IAAP,KAAgB,QAAhB,IACAE,KAAK,CAACC,OAAN,CAAcH,IAAd,CADA,IAEAA,IAAI,YAAYC,eAFhB,GAGID,IAHJ,GAIII,MAAM,CAACC,IAAP,CAAYL,IAAZ,EAAkBM,MAAlB,CAAyB,CAACC,IAAD,EAAOC,GAAP,KAAc;IACrC,IAAIC,KAAK,GAAGT,IAAI,CAACQ,GAAD,CAAhB;IACA,OAAOD,IAAI,CAACG,MAAL,CACLR,KAAK,CAACC,OAAN,CAAcM,KAAd,IAAuBA,KAAK,CAACE,GAAN,CAAWC,CAAD,IAAO,CAACJ,GAAD,EAAMI,CAAN,CAAjB,CAAvB,GAAoD,CAAC,CAACJ,GAAD,EAAMC,KAAN,CAAD,CAD/C,CAAP;GAFF,EAKG,EALH,CALC,CAAP;AAYD;AAEe,oCACdI,cADc,EAEdC,mBAFc,EAEsB;EAEpC,IAAIC,YAAY,GAAGC,kBAAkB,CAACH,cAAD,CAArC;EAEA,KAAK,IAAIL,GAAT,IAAgBM,mBAAmB,CAACT,IAApB,EAAhB,EAA4C;IAC1C,IAAI,CAACU,YAAY,CAACE,GAAb,CAAiBT,GAAjB,CAAL,EAA4B;MAC1BM,mBAAmB,CAACI,MAApB,CAA2BV,GAA3B,EAAgCW,OAAhC,CAAyCV,KAAD,IAAU;QAChDM,YAAY,CAACK,MAAb,CAAoBZ,GAApB,EAAyBC,KAAzB;OADF;IAGD;EACF;EAED,OAAOM,YAAP;AACD;SAuCeM,sBACdvB,QAQAwB,eACAC,SAAsB;EAOtB,IAAIC,MAAJ;EACA,IAAIC,MAAJ;EACA,IAAIC,OAAJ;EACA,IAAIC,QAAJ;EAEA,IAAIrC,aAAa,CAACQ,MAAD,CAAjB,EAA2B;IACzB,IAAI8B,iBAAiB,GACnBL,OACD,CAACK,iBAFF;IAIAJ,MAAM,GAAGD,OAAO,CAACC,MAAR,IAAkB1B,MAAM,CAAC+B,YAAP,CAAoB,QAApB,CAAlB,IAAmD9C,aAA5D;IACA0C,MAAM,GAAGF,OAAO,CAACE,MAAR,IAAkB3B,MAAM,CAAC+B,YAAP,CAAoB,QAApB,CAAlB,IAAmDP,aAA5D;IACAI,OAAO,GACLH,OAAO,CAACG,OAAR,IAAmB5B,MAAM,CAAC+B,YAAP,CAAoB,SAApB,CAAnB,IAAqD7C,cADvD;IAGA2C,QAAQ,GAAG,IAAIG,QAAJ,CAAahC,MAAb,CAAX;IAEA,IAAI8B,iBAAiB,IAAIA,iBAAiB,CAACG,IAA3C,EAAiD;MAC/CJ,QAAQ,CAACP,MAAT,CAAgBQ,iBAAiB,CAACG,IAAlC,EAAwCH,iBAAiB,CAACnB,KAA1D;IACD;GAdH,MAeO,IACLrB,eAAe,CAACU,MAAD,CAAf,IACCP,cAAc,CAACO,MAAD,CAAd,KACEA,MAAM,CAACkC,IAAP,KAAgB,QAAhB,IAA4BlC,MAAM,CAACkC,IAAP,KAAgB,OAD9C,CAFI,EAIL;IACA,IAAIC,IAAI,GAAGnC,MAAM,CAACmC,IAAlB;IAEA,IAAIA,IAAI,IAAI,IAAZ,EAAkB;MAChB,MAAM,IAAIC,KAAJ,CAAN;IAGD,CAPD;;IAWAV,MAAM,GACJD,OAAO,CAACC,MAAR,IACA1B,MAAM,CAAC+B,YAAP,CAAoB,YAApB,CADA,IAEAI,IAAI,CAACJ,YAAL,CAAkB,QAAlB,CAFA,IAGA9C,aAJF;IAKA0C,MAAM,GACJF,OAAO,CAACE,MAAR,IACA3B,MAAM,CAAC+B,YAAP,CAAoB,YAApB,CADA,IAEAI,IAAI,CAACJ,YAAL,CAAkB,QAAlB,CAFA,IAGAP,aAJF;IAKAI,OAAO,GACLH,OAAO,CAACG,OAAR,IACA5B,MAAM,CAAC+B,YAAP,CAAoB,aAApB,CADA,IAEAI,IAAI,CAACJ,YAAL,CAAkB,SAAlB,CAFA,IAGA7C,cAJF;IAMA2C,QAAQ,GAAG,IAAIG,QAAJ,CAAaG,IAAb,CAAX,CA3BA;IA8BA;;IACA,IAAInC,MAAM,CAACiC,IAAX,EAAiB;MACfJ,QAAQ,CAACP,MAAT,CAAgBtB,MAAM,CAACiC,IAAvB,EAA6BjC,MAAM,CAACW,KAApC;IACD;EACF,CAtCM,MAsCA,IAAIxB,aAAa,CAACa,MAAD,CAAjB,EAA2B;IAChC,MAAM,IAAIoC,KAAJ,CACJ,2FADI,CAAN;EAID,CALM,MAKA;IACLV,MAAM,GAAGD,OAAO,CAACC,MAAR,IAAkBzC,aAA3B;IACA0C,MAAM,GAAGF,OAAO,CAACE,MAAR,IAAkBH,aAA3B;IACAI,OAAO,GAAGH,OAAO,CAACG,OAAR,IAAmB1C,cAA7B;IAEA,IAAIc,MAAM,YAAYgC,QAAtB,EAAgC;MAC9BH,QAAQ,GAAG7B,MAAX;IACD,CAFD,MAEO;MACL6B,QAAQ,GAAG,IAAIG,QAAJ,EAAX;MAEA,IAAIhC,MAAM,YAAYG,eAAtB,EAAuC;QACrC,KAAK,IAAI,CAAC8B,IAAD,EAAOtB,KAAP,CAAT,IAA0BX,MAA1B,EAAkC;UAChC6B,QAAQ,CAACP,MAAT,CAAgBW,IAAhB,EAAsBtB,KAAtB;QACD;MACF,CAJD,MAIO,IAAIX,MAAM,IAAI,IAAd,EAAoB;QACzB,KAAK,IAAIiC,IAAT,IAAiB3B,MAAM,CAACC,IAAP,CAAYP,MAAZ,CAAjB,EAAsC;UACpC6B,QAAQ,CAACP,MAAT,CAAgBW,IAAhB,EAAsBjC,MAAM,CAACiC,IAAD,CAA5B;QACD;MACF;IACF;EACF;EAED,IAAI;IAAEI,QAAF;IAAYC;GAASC,SAAM,CAACC,QAAhC;EACA,IAAIC,GAAG,GAAG,IAAIC,GAAJ,CAAQf,MAAR,EAAmBU,QAAnB,GAAgCC,WAAhC,CAAV;EAEA,OAAO;IAAEG,GAAF;IAAOf,MAAP;IAAeE,OAAf;IAAwBC;GAA/B;AACD;;;;ACvDD;AACA;;AAEgB,6BACdc,MADc,EAEdC,IAFc,EAMb;EAAA;EAED,OAAOC,YAAY,CAAC;IAClBC,QAAQ,EAAEF,IAAF,IAAEA,oBAAI,CAAEE,QADE;IAElBC,OAAO,EAAEC,oBAAoB,CAAC;MAAET,MAAM,EAAEK,IAAF,IAAEA,oBAAI,CAAEL;IAAhB,CAAD,CAFX;IAGlBU,aAAa,EAAE,KAAI,QAAJ,gBAAI,CAAEA,aAAN,MAAuBV,iBAAvB,KAAuB,wBAAQW,2BAA/B,CAHG;IAIlBP,MAAM,EAAEQ,gCAAyB,CAACR,MAAD;GAJhB,CAAZ,CAKJS,UALI,EAAP;AAMD;AAEe,0BACdT,MADc,EAEdC,IAFc,EAMb;EAAA;EAED,OAAOC,YAAY,CAAC;IAClBC,QAAQ,EAAEF,IAAF,IAAEA,oBAAI,CAAEE,QADE;IAElBC,OAAO,EAAEM,iBAAiB,CAAC;MAAEd,MAAM,EAAEK,IAAF,IAAEA,oBAAI,CAAEL;IAAhB,CAAD,CAFR;IAGlBU,aAAa,EAAE,KAAI,QAAJ,gBAAI,CAAEA,aAAN,MAAuBV,kBAAvB,KAAuB,yBAAQW,2BAA/B,CAHG;IAIlBP,MAAM,EAAEQ,gCAAyB,CAACR,MAAD;GAJhB,CAAZ,CAKJS,UALI,EAAP;AAMD;AAaD;;AAEG;;AACG,SAAUE,aAAV,CAIeC;EAAA,IAJS;IAC5BT,QAD4B;IAE5BU,QAF4B;IAG5BjB;GACmB;EACnB,IAAIkB,UAAU,GAAGC,KAAK,CAACC,MAAN,EAAjB;EACA,IAAIF,UAAU,CAACG,OAAX,IAAsB,IAA1B,EAAgC;IAC9BH,UAAU,CAACG,OAAX,GAAqBZ,oBAAoB,CAAC;MAAET,MAAF;MAAUsB,QAAQ,EAAE;IAApB,CAAD,CAAzC;EACD;EAED,IAAId,OAAO,GAAGU,UAAU,CAACG,OAAzB;EACA,IAAI,CAACE,KAAD,EAAQC,QAAR,IAAoBL,KAAK,CAACM,QAAN,CAAe;IACrCrC,MAAM,EAAEoB,OAAO,CAACpB,MADqB;IAErCa,QAAQ,EAAEO,OAAO,CAACP;EAFmB,CAAf,CAAxB;EAKAkB,KAAK,CAACO,eAAN,CAAsB,MAAMlB,OAAO,CAACmB,MAAR,CAAeH,QAAf,CAA5B,EAAsD,CAAChB,OAAD,CAAtD;EAEA,oBACEW,oBAACS,MAAD,EAAO;IACLrB,QAAQ,EAAEA,QADL;IAELU,QAAQ,EAAEA,QAFL;IAGLhB,QAAQ,EAAEsB,KAAK,CAACtB,QAHX;IAIL4B,cAAc,EAAEN,KAAK,CAACnC,MAJjB;IAKL0C,SAAS,EAAEtB;EALN,CAAP,CADF;AASD;AAQD;;;AAGG;;AACG,SAAUuB,UAAV,CAAoEC;EAAA,IAA/C;IAAEzB,QAAF;IAAYU,QAAZ;IAAsBjB;GAAyB;EACxE,IAAIkB,UAAU,GAAGC,KAAK,CAACC,MAAN,EAAjB;EACA,IAAIF,UAAU,CAACG,OAAX,IAAsB,IAA1B,EAAgC;IAC9BH,UAAU,CAACG,OAAX,GAAqBP,iBAAiB,CAAC;MAAEd,MAAF;MAAUsB,QAAQ,EAAE;IAApB,CAAD,CAAtC;EACD;EAED,IAAId,OAAO,GAAGU,UAAU,CAACG,OAAzB;EACA,IAAI,CAACE,KAAD,EAAQC,QAAR,IAAoBL,KAAK,CAACM,QAAN,CAAe;IACrCrC,MAAM,EAAEoB,OAAO,CAACpB,MADqB;IAErCa,QAAQ,EAAEO,OAAO,CAACP;EAFmB,CAAf,CAAxB;EAKAkB,KAAK,CAACO,eAAN,CAAsB,MAAMlB,OAAO,CAACmB,MAAR,CAAeH,QAAf,CAA5B,EAAsD,CAAChB,OAAD,CAAtD;EAEA,oBACEW,oBAACS,MAAD,EAAO;IACLrB,QAAQ,EAAEA,QADL;IAELU,QAAQ,EAAEA,QAFL;IAGLhB,QAAQ,EAAEsB,KAAK,CAACtB,QAHX;IAIL4B,cAAc,EAAEN,KAAK,CAACnC,MAJjB;IAKL0C,SAAS,EAAEtB;EALN,CAAP,CADF;AASD;AAQD;;;;;AAKG;;AACH,SAASyB,aAAT,CAA0EC;EAAA,IAAnD;IAAE3B,QAAF;IAAYU,QAAZ;IAAsBT;GAA6B;EACxE,MAAM,CAACe,KAAD,EAAQC,QAAR,IAAoBL,KAAK,CAACM,QAAN,CAAe;IACvCrC,MAAM,EAAEoB,OAAO,CAACpB,MADuB;IAEvCa,QAAQ,EAAEO,OAAO,CAACP;EAFqB,CAAf,CAA1B;EAKAkB,KAAK,CAACO,eAAN,CAAsB,MAAMlB,OAAO,CAACmB,MAAR,CAAeH,QAAf,CAA5B,EAAsD,CAAChB,OAAD,CAAtD;EAEA,oBACEW,oBAACS,MAAD,EAAO;IACLrB,QAAQ,EAAEA,QADL;IAELU,QAAQ,EAAEA,QAFL;IAGLhB,QAAQ,EAAEsB,KAAK,CAACtB,QAHX;IAIL4B,cAAc,EAAEN,KAAK,CAACnC,MAJjB;IAKL0C,SAAS,EAAEtB;EALN,CAAP,CADF;AASD;AAED,IAAa2B;EACXF,aAAa,CAACG,WAAd,GAA4B,wBAA5B;AACD;AAcD;;AAEG;;AACI,MAAMC,IAAI,gBAAGlB,KAAK,CAACmB,UAAN,CAClB,SAASC,WAAT,CAYEC,UAZF,EAYK;EAAA,IAXH;MACEC,OADF;MAEEC,QAFF;MAGEC,cAHF;MAIEC,OAJF;MAKErB,KALF;MAME9D,MANF;MAOEoF,EAPF;MAQEC;KAGC;IAFEC,IAEF;EAEH,IAAIC,IAAI,GAAGC,OAAO,CAACJ,EAAD,EAAK;IAAEH;EAAF,CAAL,CAAlB;EACA,IAAIQ,eAAe,GAAGC,mBAAmB,CAACN,EAAD,EAAK;IAC5CD,OAD4C;IAE5CrB,KAF4C;IAG5C9D,MAH4C;IAI5CqF,kBAJ4C;IAK5CJ;EAL4C,CAAL,CAAzC;EAOA,SAASU,WAAT,CACEhG,KADF,EACwD;IAEtD,IAAIqF,OAAJ,EAAaA,OAAO,CAACrF,KAAD,CAAP;IACb,IAAI,CAACA,KAAK,CAACiG,gBAAX,EAA6B;MAC3BH,eAAe,CAAC9F,KAAD,CAAf;IACD;EACF;EAED;IACE;IACA+D,KACM,cADN,CACM,GADN,eACM4B,IADN;MAEEC,IAAI,EAAEA,IAFR;MAGEP,OAAO,EAAEE,cAAc,GAAGF,OAAH,GAAaW,WAHtC;MAIEZ,GAAG,EAAEA,GAJP;MAKE/E,MAAM,EAAEA;IALV;EAAA;AAQH,CA1CiB;AA6CpB,IAAa0E;EACXE,IAAI,CAACD,WAAL,GAAmB,MAAnB;AACD;AAuBD;;AAEG;;AACI,MAAMkB,OAAO,gBAAGnC,KAAK,CAACmB,UAAN,CACrB,SAASiB,cAAT,CAWEf,UAXF,EAWK;EAAA,IAVH;MACE,cAAgBgB,iBAAe,GAAG,MADpC;MAEEC,aAAa,GAAG,KAFlB;MAGEC,SAAS,EAAEC,aAAa,GAAG,EAH7B;MAIEC,GAAG,GAAG,KAJR;MAKEC,KAAK,EAAEC,SALT;MAMEjB,EANF;MAOE5B;KAGC;IAFE8B,IAEF;EAEH,IAAIgB,IAAI,GAAGC,eAAe,CAACnB,EAAD,EAAK;IAAEH,QAAQ,EAAEK,IAAI,CAACL;EAAjB,CAAL,CAA1B;EACA,IAAIzC,QAAQ,GAAGgE,WAAW,EAA1B;EACA,IAAIC,WAAW,GAAG/C,KAAK,CAACgD,UAAN,CAAiBC,6BAAjB,CAAlB;EAEA,IAAIC,UAAU,GAAGN,IAAI,CAACO,QAAtB;EACA,IAAIC,gBAAgB,GAAGtE,QAAQ,CAACqE,QAAhC;EACA,IAAIE,oBAAoB,GACtBN,WAAW,IAAIA,WAAW,CAACO,UAA3B,IAAyCP,WAAW,CAACO,UAAZ,CAAuBxE,QAAhE,GACIiE,WAAW,CAACO,UAAZ,CAAuBxE,QAAvB,CAAgCqE,QADpC,GAEI,IAHN;EAKA,IAAI,CAACb,aAAL,EAAoB;IAClBc,gBAAgB,GAAGA,gBAAgB,CAACvH,WAAjB,EAAnB;IACAwH,oBAAoB,GAAGA,oBAAoB,GACvCA,oBAAoB,CAACxH,WAArB,EADuC,GAEvC,IAFJ;IAGAqH,UAAU,GAAGA,UAAU,CAACrH,WAAX,EAAb;EACD;EAED,IAAI0H,QAAQ,GACVH,gBAAgB,KAAKF,UAArB,IACC,CAACT,GAAD,IACCW,gBAAgB,CAACI,UAAjB,CAA4BN,UAA5B,CADD,IAECE,gBAAgB,CAACK,MAAjB,CAAwBP,UAAU,CAACQ,MAAnC,MAA+C,GAJnD;EAMA,IAAIC,SAAS,GACXN,oBAAoB,IAAI,IAAxB,KACCA,oBAAoB,KAAKH,UAAzB,IACE,CAACT,GAAD,IACCY,oBAAoB,CAACG,UAArB,CAAgCN,UAAhC,CADD,IAECG,oBAAoB,CAACI,MAArB,CAA4BP,UAAU,CAACQ,MAAvC,MAAmD,GAJvD,CADF;EAOA,IAAIE,WAAW,GAAGL,QAAQ,GAAGlB,eAAH,GAAqBwB,SAA/C;EAEA,IAAItB,SAAJ;EACA,IAAI,OAAOC,aAAP,KAAyB,UAA7B,EAAyC;IACvCD,SAAS,GAAGC,aAAa,CAAC;MAAEe,QAAF;MAAYI;IAAZ,CAAD,CAAzB;EACD,CAFD,MAEO;IACL;IACA;IACA;IACA;IACA;IACApB,SAAS,GAAG,CACVC,aADU,EAEVe,QAAQ,GAAG,QAAH,GAAc,IAFZ,EAGVI,SAAS,GAAG,SAAH,GAAe,IAHd,EAKTG,MALS,CAKFC,OALE,CAMTC,KANS,CAMJ,GANI,CAAZ;EAOD;EAED,IAAItB,KAAK,GACP,OAAOC,SAAP,KAAqB,UAArB,GACIA,SAAS,CAAC;IAAEY,QAAF;IAAYI;GAAb,CADb,GAEIhB,SAHN;EAKA,oBACE3C,KAAC,cAAD,CAACkB,IAAD,eACMU,IADN;IAEgB,2BAFhB;IAGEW,SAAS,EAAEA,SAHb;IAIElB,GAAG,EAAEA,GAJP;IAKEqB,KAAK,EAAEA,KALT;IAMEhB,EAAE,EAAEA;EANN,IAQG,OAAO5B,QAAP,KAAoB,UAApB,GACGA,QAAQ,CAAC;IAAEyD,QAAF;IAAYI;GAAb,CADX,GAEG7D,QAVN,CADF;AAcD,CArFoB;AAwFvB,IAAakB;EACXmB,OAAO,CAAClB,WAAR,GAAsB,SAAtB;AACD;AAwCD;;;;;AAKG;;AACI,MAAMgD,IAAI,gBAAGjE,KAAK,CAACmB,UAAN,CAClB,CAAC+C,KAAD,EAAQ7C,GAAR,KAAe;EACb,oBAAOrB,oBAACmE,QAAD,eAAcD,KAAd;IAAqB7C,GAAG,EAAEA;GAAjC;AACD,CAHiB;AAMpB,IAAaL;EACXiD,IAAI,CAAChD,WAAL,GAAmB,MAAnB;AACD;AAeD,MAAMkD,QAAQ,gBAAGnE,KAAK,CAACmB,UAAN,CACf,QAYEiD,YAZF,KAaI;EAAA,IAZF;MACE5C,cADF;MAEEC,OAFF;MAGEzD,MAAM,GAAGzC,aAHX;MAIE0C,MAJF;MAKEoG,QALF;MAMEC,UANF;MAOEC,OAPF;MAQEhD;KAIA;IAHG2C,KAGH;EACF,IAAIM,MAAM,GAAGC,aAAa,CAACH,UAAD,EAAaC,OAAb,CAA1B;EACA,IAAIG,UAAU,GACZ1G,MAAM,CAACnC,WAAP,OAAyB,KAAzB,GAAiC,KAAjC,GAAyC,MAD3C;EAEA,IAAI8I,UAAU,GAAGC,aAAa,CAAC3G,MAAD,EAAS;IAAEsD;EAAF,CAAT,CAA9B;EACA,IAAIsD,aAAa,GAA6C5I,KAAD,IAAU;IACrEoI,QAAQ,IAAIA,QAAQ,CAACpI,KAAD,CAApB;IACA,IAAIA,KAAK,CAACiG,gBAAV,EAA4B;IAC5BjG,KAAK,CAAC6I,cAAN;IAEA,IAAIC,SAAS,GAAI9I,KAAoC,CAAC+I,WAArC,CACdD,SADH;IAGAP,MAAM,CAACO,SAAS,IAAI9I,KAAK,CAACgJ,aAApB,EAAmC;MAAEjH,MAAF;MAAUyD,OAAV;MAAmBF;IAAnB,CAAnC,CAAN;GARF;EAWA,oBACEvB;IACEqB,GAAG,EAAE+C,YADP;IAEEpG,MAAM,EAAE0G,UAFV;IAGEzG,MAAM,EAAE0G,UAHV;IAIEN,QAAQ,EAAE7C,cAAc,GAAG6C,QAAH,GAAcQ;EAJxC,GAKMX,KALN,CADF;AASD,CAvCc,CAAjB;AA0CA,IAAalD;EACXiD,IAAI,CAAChD,WAAL,GAAmB,MAAnB;AACD;AAOD;;;AAGG;;SACaiE,kBAGSC;EAAA,IAHS;IAChCC,MADgC;IAEhCC;GACuB;EACvBC,oBAAoB,CAAC;IAAEF,MAAF;IAAUC;EAAV,CAAD,CAApB;EACA,OAAO,IAAP;AACD;AAED,IAAarE;EACXkE,iBAAiB,CAACjE,WAAlB,GAAgC,mBAAhC;AACD;AAGD;AACA;AACA;;AAEA,IAAKsE,cAAL;AAAA,WAAKA,cAAL,EAAmB;EACjBA;EACAA;EACAA;AACD,CAJD,EAAKA,cAAc,KAAdA,cAAc,GAIlB,EAJkB,CAAnB;AAMA,IAAKC,mBAAL;AAAA,WAAKA,mBAAL,EAAwB;EACtBA;EACAA;AACD,CAHD,EAAKA,mBAAmB,KAAnBA,mBAAmB,GAGvB,EAHuB,CAAxB;AAKA,SAASC,yBAAT,CACEC,QADF,EACgD;EAE9C,OAAUA,QAAV;AACD;AAED,SAASC,oBAAT,CAA8BD,QAA9B,EAAsD;EACpD,IAAIE,GAAG,GAAG5F,KAAK,CAACgD,UAAN,CAAiB6C,wBAAjB,CAAV;EACA,CAAUD,GAAV,oDAAS,CAAMH,gCAAyB,CAACC,QAAD,CAA/B,CAAT,YAAS,CAAT;EACA,OAAOE,GAAP;AACD;AAED,SAASE,kBAAT,CAA4BJ,QAA5B,EAAyD;EACvD,IAAItF,KAAK,GAAGJ,KAAK,CAACgD,UAAN,CAAiBC,6BAAjB,CAAZ;EACA,CAAU7C,KAAV,oDAAS,CAAQqF,gCAAyB,CAACC,QAAD,CAAjC,CAAT,YAAS,CAAT;EACA,OAAOtF,KAAP;AACD;AAED;;;;AAIG;;SACa4B,oBACdN,IAaMqE;EAAA,IAZN;IACEzJ,MADF;IAEEmF,OAAO,EAAEuE,WAFX;IAGE5F,KAHF;IAIEuB,kBAJF;IAKEJ;EALF,CAYM,sBAAF,EAAE;EAEN,IAAI0E,QAAQ,GAAGC,WAAW,EAA1B;EACA,IAAIpH,QAAQ,GAAGgE,WAAW,EAA1B;EACA,IAAIF,IAAI,GAAGC,eAAe,CAACnB,EAAD,EAAK;IAAEH;EAAF,CAAL,CAA1B;EAEA,OAAOvB,KAAK,CAACmG,WAAN,CACJlK,KAAD,IAA2C;IACzC,IAAImK,sBAAsB,CAACnK,KAAD,EAAQK,MAAR,CAA1B,EAA2C;MACzCL,KAAK,CAAC6I,cAAN,GADyC;MAIzC;;MACA,IAAIrD,OAAO,GACTuE,WAAW,KAAKnC,SAAhB,GACImC,WADJ,GAEIK,UAAU,CAACvH,QAAD,CAAV,KAAyBuH,UAAU,CAACzD,IAAD,CAHzC;MAKAqD,QAAQ,CAACvE,EAAD,EAAK;QAAED,OAAF;QAAWrB,KAAX;QAAkBuB,kBAAlB;QAAsCJ;MAAtC,CAAL,CAAR;IACD;GAbE,EAeL,CACEzC,QADF,EAEEmH,QAFF,EAGErD,IAHF,EAIEoD,WAJF,EAKE5F,KALF,EAME9D,MANF,EAOEoF,EAPF,EAQEC,kBARF,EASEJ,QATF,CAfK,CAAP;AA2BD;AAED;;;AAGG;;AACG,SAAU+E,eAAV,CACJC,WADI,EAC6B;EAEjCvF,+CAAO,CACL,OAAOvE,eAAP,KAA2B,WADtB,EAEL,meAFK,CAAP;EAYA,IAAI+J,sBAAsB,GAAGxG,KAAK,CAACC,MAAN,CAAazC,kBAAkB,CAAC+I,WAAD,CAA/B,CAA7B;EAEA,IAAIzH,QAAQ,GAAGgE,WAAW,EAA1B;EACA,IAAIvF,YAAY,GAAGyC,KAAK,CAACyG,OAAN,CACjB,MACEC,0BAA0B,CACxB5H,QAAQ,CAAC6H,MADe,EAExBH,sBAAsB,CAACtG,OAFC,CAFX,EAMjB,CAACpB,QAAQ,CAAC6H,MAAV,CANiB,CAAnB;EASA,IAAIV,QAAQ,GAAGC,WAAW,EAA1B;EACA,IAAIU,eAAe,GAAG5G,KAAK,CAACmG,WAAN,CACpB,CAACU,QAAD,EAAWC,eAAX,KAA8B;IAC5B,MAAMC,eAAe,GAAGvJ,kBAAkB,CACxC,OAAOqJ,QAAP,KAAoB,UAApB,GAAiCA,QAAQ,CAACtJ,YAAD,CAAzC,GAA0DsJ,QADlB,CAA1C;IAGAZ,QAAQ,CAAC,MAAMc,eAAP,EAAwBD,eAAxB,CAAR;EACD,CANmB,EAOpB,CAACb,QAAD,EAAW1I,YAAX,CAPoB,CAAtB;EAUA,OAAO,CAACA,YAAD,EAAeqJ,eAAf,CAAP;AACD;AAyCD;;;AAGG;;SACaI,YAAS;EACvB,OAAOvC,aAAa,EAApB;AACD;AAED,SAASA,aAAT,CAAuBH,UAAvB,EAA4CC,OAA5C,EAA4D;EAC1D,IAAI;IAAE0C;EAAF,IAAatB,oBAAoB,CAACJ,cAAc,CAAC2B,aAAhB,CAArC;EACA,IAAIpJ,aAAa,GAAG8G,aAAa,EAAjC;EAEA,OAAO5E,KAAK,CAACmG,WAAN,CACL,UAAC7J,MAAD,EAASyB,OAAT,EAAyB;IAAA,IAAhBA,OAAgB;MAAhBA,OAAgB,GAAN,EAAM;IAAA;IACvB,IAAI,OAAOoJ,QAAP,KAAoB,WAAxB,EAAqC;MACnC,MAAM,IAAIzI,KAAJ,CACJ,sDACE,8DAFE,CAAN;IAID;IAED,IAAI;MAAEV,MAAF;MAAUE,OAAV;MAAmBC,QAAnB;MAA6BY;IAA7B,IAAqClB,qBAAqB,CAC5DvB,MAD4D,EAE5DwB,aAF4D,EAG5DC,OAH4D,CAA9D;IAMA,IAAI8D,IAAI,GAAG9C,GAAG,CAACoE,QAAJ,GAAepE,GAAG,CAAC4H,MAA9B;IACA,IAAIzH,IAAI,GAAG;MACTuC,OAAO,EAAE1D,OAAO,CAAC0D,OADR;MAETtD,QAFS;MAGTuG,UAAU,EAAE1G,MAHH;MAIToJ,WAAW,EAAElJ;KAJf;IAMA,IAAIoG,UAAJ,EAAgB;MACd,EAAUC,OAAO,IAAI,IAArB,qDAAS,QAAkB,uCAAlB,CAAT,YAAS,CAAT;MACA0C,MAAM,CAACI,KAAP,CAAa/C,UAAb,EAAyBC,OAAzB,EAAkC1C,IAAlC,EAAwC3C,IAAxC;IACD,CAHD,MAGO;MACL+H,MAAM,CAAChB,QAAP,CAAgBpE,IAAhB,EAAsB3C,IAAtB;IACD;GA3BE,EA6BL,CAACpB,aAAD,EAAgBmJ,MAAhB,EAAwB3C,UAAxB,EAAoCC,OAApC,CA7BK,CAAP;AA+BD;AAEK,SAAUK,aAAV,CACJ3G,MADI,EAEiDqJ;EAAA,IAArD;IAAE/F;EAAF,CAAqD,uBAAF,EAAE;EAErD,IAAI;IAAEnC;EAAF,IAAeY,KAAK,CAACgD,UAAN,CAAiBuE,wBAAjB,CAAnB;EACA,IAAIC,YAAY,GAAGxH,KAAK,CAACgD,UAAN,CAAiByE,mBAAjB,CAAnB;EACA,CAAUD,YAAV,oDAAS,QAAe,kDAAf,CAAT,YAAS,CAAT;EAEA,IAAI,CAACE,KAAD,IAAUF,YAAY,CAACG,OAAb,CAAqBC,KAArB,CAA2B,CAAC,CAA5B,CAAd;EACA,IAAIC,cAAc,GAAG5J,MAAH,WAAGA,MAAH,GAAa,GAA/B,CAPqD;EASrD;;EACA,IAAI2E,IAAI,gBAAQC,eAAe,CAACgF,cAAD,EAAiB;IAAEtG;GAAnB,CAAvB,CAAR,CAVqD;EAarD;EACA;EACA;EACA;;EACA,IAAIzC,QAAQ,GAAGgE,WAAW,EAA1B;EACA,IAAI7E,MAAM,IAAI,IAAd,EAAoB;IAClB;IACA;IACA;IACA2E,IAAI,CAAC+D,MAAL,GAAc7H,QAAQ,CAAC6H,MAAvB;IACA/D,IAAI,CAACkF,IAAL,GAAYhJ,QAAQ,CAACgJ,IAArB,CALkB;IAQlB;IACA;;IACA,IAAIJ,KAAK,CAACK,KAAN,CAAYC,KAAhB,EAAuB;MACrB,IAAIC,MAAM,GAAG,IAAIxL,eAAJ,CAAoBmG,IAAI,CAAC+D,MAAzB,CAAb;MACAsB,MAAM,CAACC,MAAP,CAAc,OAAd;MACAtF,IAAI,CAAC+D,MAAL,GAAcsB,MAAM,CAACE,QAAP,EAAwBF,eAAM,CAACE,QAAP,EAAxB,GAA8C,EAA5D;IACD;EACF;EAED,IAAI,CAAC,CAAClK,MAAD,IAAWA,MAAM,KAAK,GAAvB,KAA+ByJ,KAAK,CAACK,KAAN,CAAYC,KAA/C,EAAsD;IACpDpF,IAAI,CAAC+D,MAAL,GAAc/D,IAAI,CAAC+D,MAAL,GACV/D,IAAI,CAAC+D,MAAL,CAAYlF,OAAZ,CAAoB,KAApB,EAA2B,SAA3B,CADU,GAEV,QAFJ;EAGD,CAvCoD;EA0CrD;EACA;EACA;;EACA,IAAIrC,QAAQ,KAAK,GAAjB,EAAsB;IACpBwD,IAAI,CAACO,QAAL,GACEP,IAAI,CAACO,QAAL,KAAkB,GAAlB,GAAwB/D,QAAxB,GAAmCgJ,SAAS,CAAC,CAAChJ,QAAD,EAAWwD,IAAI,CAACO,QAAhB,CAAD,CAD9C;EAED;EAED,OAAOkD,UAAU,CAACzD,IAAD,CAAjB;AACD;AAED,SAASyF,iBAAT,CAA2B/D,UAA3B,EAA+CC,OAA/C,EAA8D;EAC5D,IAAI+D,WAAW,gBAAGtI,KAAK,CAACmB,UAAN,CAChB,CAAC+C,KAAD,EAAQ7C,GAAR,KAAe;IACb,oBACErB,KAAC,cAAD,CAACmE,QAAD,eACMD,KADN;MAEE7C,GAAG,EAAEA,GAFP;MAGEiD,UAAU,EAAEA,UAHd;MAIEC,OAAO,EAAEA;KALb;EAQD,CAVe,CAAlB;EAYA,IAAavD;IACXsH,WAAW,CAACrH,WAAZ,GAA0B,cAA1B;EACD;EACD,OAAOqH,WAAP;AACD;AAED,IAAIC,SAAS,GAAG,CAAhB;AAYA;;;AAGG;;SACaC,aAAU;EAAA;EACxB,IAAI;IAAEvB;EAAF,IAAatB,oBAAoB,CAACJ,cAAc,CAACkD,UAAhB,CAArC;EAEA,IAAIV,KAAK,GAAG/H,KAAK,CAACgD,UAAN,CAAiByE,mBAAjB,CAAZ;EACA,CAAUM,KAAV,oDAAS,CAAT,mEAAS,CAAT;EAEA,IAAIxD,OAAO,GAAGwD,uBAAK,CAACJ,OAAN,CAAcI,KAAK,CAACJ,OAAN,CAAcjE,MAAd,GAAuB,CAArC,CAAH,qBAAGgF,cAAyCX,MAAzC,CAA+CY,EAA7D;EACA,EACEpE,OAAO,IAAI,IADb,qDAAS,CAAT,wFAAS,CAAT;EAKA,IAAI,CAACD,UAAD,CAAetE,QAAK,CAACM,QAAN,CAAe,MAAMsI,MAAM,CAAC,EAAEL,SAAH,CAA3B,CAAnB;EACA,IAAI,CAACtE,IAAD,IAASjE,KAAK,CAACM,QAAN,CAAe,MAAK;IAC/B,CAAUiE,OAAV,oDAAS,CAAT,6DAAS,CAAT;IACA,OAAO8D,iBAAiB,CAAC/D,UAAD,EAAaC,OAAb,CAAxB;EACD,CAHY,CAAb;EAIA,IAAI,CAACsE,IAAD,CAAS7I,QAAK,CAACM,QAAN,CAAe,MAAOuB,IAAD,IAAiB;IACjD,CAAUoF,MAAV,oDAAS,QAAS,wCAAT,CAAT,YAAS,CAAT;IACA,CAAU1C,OAAV,oDAAS,QAAU,yCAAV,CAAT,YAAS,CAAT;IACA0C,MAAM,CAACI,KAAP,CAAa/C,UAAb,EAAyBC,OAAzB,EAAkC1C,IAAlC;EACD,CAJY,CAAb;EAKA,IAAI2C,MAAM,GAAGC,aAAa,CAACH,UAAD,EAAaC,OAAb,CAA1B;EAEA,IAAIuE,OAAO,GAAG7B,MAAM,CAAC8B,UAAP,CAAyBzE,UAAzB,CAAd;EAEA,IAAI0E,qBAAqB,GAAGhJ,KAAK,CAACyG,OAAN,CAC1B;IACExC,IADF;IAEEO,MAFF;IAGEqE;EAHF,GAIKC,OAJL,CAD0B,EAO1B,CAACA,OAAD,EAAU7E,IAAV,EAAgBO,MAAhB,EAAwBqE,IAAxB,CAP0B,CAA5B;EAUA7I,KAAK,CAACiJ,SAAN,CAAgB,MAAK;IACnB;IACA;IACA;IACA,OAAO,MAAK;MACV,IAAI,CAAChC,MAAL,EAAa;QACXiC,OAAO,CAACC,IAAR;QACA;MACD;MACDlC,MAAM,CAACmC,aAAP,CAAqB9E,UAArB;KALF;EAOD,CAXD,EAWG,CAAC2C,MAAD,EAAS3C,UAAT,CAXH;EAaA,OAAO0E,qBAAP;AACD;AAED;;;AAGG;;SACaK,cAAW;EACzB,IAAIjJ,KAAK,GAAG0F,kBAAkB,CAACN,mBAAmB,CAAC8D,WAArB,CAA9B;EACA,OAAO,CAAC,GAAGlJ,KAAK,CAACmJ,QAAN,CAAeC,MAAf,EAAJ,CAAP;AACD;AAED,MAAMC,8BAA8B,GAAG,+BAAvC;AACA,IAAIC,oBAAoB,GAA2B,EAAnD;AAEA;;AAEG;;AACH,SAASpE,oBAAT,CAMMqE;EAAA,IANwB;IAC5BvE,MAD4B;IAE5BC;EAF4B,CAMxB,uBAAF,EAAE;EACJ,IAAI;IAAE4B;EAAF,IAAatB,oBAAoB,CAACJ,cAAc,CAACqE,oBAAhB,CAArC;EACA,IAAI;IAAEC,qBAAF;IAAyBlI;EAAzB,IAAgDmE,kBAAkB,CACpEN,mBAAmB,CAACoE,oBADgD,CAAtE;EAGA,IAAI9K,QAAQ,GAAGgE,WAAW,EAA1B;EACA,IAAI6E,OAAO,GAAGmC,UAAU,EAAxB;EACA,IAAIxG,UAAU,GAAGyG,aAAa,EAA9B,CAPI;;EAUJ/J,KAAK,CAACiJ,SAAN,CAAgB,MAAK;IACnBpK,MAAM,CAACQ,OAAP,CAAe2K,iBAAf,GAAmC,QAAnC;IACA,OAAO,MAAK;MACVnL,MAAM,CAACQ,OAAP,CAAe2K,iBAAf,GAAmC,MAAnC;KADF;GAFF,EAKG,EALH,EAVI;;EAkBJC,eAAe,CACbjK,KAAK,CAACmG,WAAN,CAAkB,MAAK;IACrB,IAAI7C,UAAU,CAAClD,KAAX,KAAqB,MAAzB,EAAiC;MAC/B,IAAIpD,GAAG,GAAG,CAACoI,MAAM,GAAGA,MAAM,CAACtG,QAAD,EAAW6I,OAAX,CAAT,GAA+B,IAAtC,KAA+C7I,QAAQ,CAAC9B,GAAlE;MACA0M,oBAAoB,CAAC1M,GAAD,CAApB,GAA4B6B,MAAM,CAACqL,OAAnC;IACD;IACDC,cAAc,CAACC,OAAf,CACE/E,UAAU,IAAIoE,8BADhB,EAEEY,IAAI,CAACC,SAAL,CAAeZ,oBAAf,CAFF;IAIA7K,MAAM,CAACQ,OAAP,CAAe2K,iBAAf,GAAmC,MAAnC;EACD,CAVD,EAUG,CAAC3E,UAAD,EAAaD,MAAb,EAAqB9B,UAAU,CAAClD,KAAhC,EAAuCtB,QAAvC,EAAiD6I,OAAjD,CAVH,CADa,CAAf,CAlBI;;EAiCJ3H,KAAK,CAACO,eAAN,CAAsB,MAAK;IACzB,IAAI;MACF,IAAIgK,gBAAgB,GAAGJ,cAAc,CAACK,OAAf,CACrBnF,UAAU,IAAIoE,8BADO,CAAvB;MAGA,IAAIc,gBAAJ,EAAsB;QACpBb,oBAAoB,GAAGW,IAAI,CAACI,KAAL,CAAWF,gBAAX,CAAvB;MACD;IACF,CAPD,CAOE,OAAOG,CAAP,EAAU;IAAA;EAGb,CAXD,EAWG,CAACrF,UAAD,CAXH,EAjCI;;EA+CJrF,KAAK,CAACO,eAAN,CAAsB,MAAK;IACzB,IAAIoK,wBAAwB,GAAG1D,MAAH,IAAGA,sBAAM,CAAE2D,uBAAR,CAC7BlB,oBAD6B,EAE7B,MAAM7K,MAAM,CAACqL,OAFgB,EAG7B9E,MAH6B,CAA/B;IAKA,OAAO,MAAMuF,wBAAwB,IAAIA,wBAAwB,EAAjE;EACD,CAPD,EAOG,CAAC1D,MAAD,EAAS7B,MAAT,CAPH,EA/CI;;EAyDJpF,KAAK,CAACO,eAAN,CAAsB,MAAK;IACzB;IACA,IAAIsJ,qBAAqB,KAAK,KAA9B,EAAqC;MACnC;IACD,CAJwB;;IAOzB,IAAI,OAAOA,qBAAP,KAAiC,QAArC,EAA+C;MAC7ChL,MAAM,CAACgM,QAAP,CAAgB,CAAhB,EAAmBhB,qBAAnB;MACA;IACD,CAVwB;;IAazB,IAAI/K,QAAQ,CAACgJ,IAAb,EAAmB;MACjB,IAAIgD,EAAE,GAAG3D,QAAQ,CAAC4D,cAAT,CAAwBjM,QAAQ,CAACgJ,IAAT,CAAcF,KAAd,CAAoB,CAApB,CAAxB,CAAT;MACA,IAAIkD,EAAJ,EAAQ;QACNA,EAAE,CAACE,cAAH;QACA;MACD;IACF,CAnBwB;;IAsBzB,IAAIrJ,kBAAkB,KAAK,IAA3B,EAAiC;MAC/B;IACD,CAxBwB;;IA2BzB9C,MAAM,CAACgM,QAAP,CAAgB,CAAhB,EAAmB,CAAnB;EACD,CA5BD,EA4BG,CAAC/L,QAAD,EAAW+K,qBAAX,EAAkClI,kBAAlC,CA5BH;AA6BD;AAED,SAASsI,eAAT,CAAyBgB,QAAzB,EAA4C;EAC1CjL,KAAK,CAACiJ,SAAN,CAAgB,MAAK;IACnBpK,MAAM,CAACqM,gBAAP,CAAwB,cAAxB,EAAwCD,QAAxC;IACA,OAAO,MAAK;MACVpM,MAAM,CAACsM,mBAAP,CAA2B,cAA3B,EAA2CF,QAA3C;KADF;GAFF,EAKG,CAACA,QAAD,CALH;AAMD;AAID;AACA;AACA;;AAEA,SAASG,OAAT,CAAiBC,IAAjB,EAAgCC,OAAhC,EAA+C;EAC7C,IAAI,CAACD,IAAL,EAAW;IACT;IACA,IAAI,OAAOnC,OAAP,KAAmB,WAAvB,EAAoCA,OAAO,CAACC,IAAR,CAAamC,OAAb;IAEpC,IAAI;MACF;MACA;MACA;MACA;MACA;MACA,MAAM,IAAI5M,KAAJ,CAAU4M,OAAV,CAAN,CANE;IAQH,CARD,CAQE,OAAOZ,CAAP,EAAU;EACb;AACF", "names": ["defaultMethod", "defaultEncType", "isHtmlElement", "object", "tagName", "isButtonElement", "toLowerCase", "isFormElement", "isInputElement", "isModifiedEvent", "event", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "target", "button", "init", "URLSearchParams", "Array", "isArray", "Object", "keys", "reduce", "memo", "key", "value", "concat", "map", "v", "locationSearch", "defaultSearchParams", "searchParams", "createSearchParams", "has", "getAll", "for<PERSON>ach", "append", "getFormSubmissionInfo", "defaultAction", "options", "method", "action", "encType", "formData", "submissionTrigger", "getAttribute", "FormData", "name", "type", "form", "Error", "protocol", "host", "window", "location", "url", "URL", "routes", "opts", "createRouter", "basename", "history", "createBrowserHistory", "hydrationData", "__staticRouterHydrationData", "enhanceManualRouteObjects", "initialize", "createHashHistory", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "children", "historyRef", "React", "useRef", "current", "v5Compat", "state", "setState", "useState", "useLayoutEffect", "listen", "Router", "navigationType", "navigator", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref2", "HistoryRouter", "_ref3", "process", "displayName", "Link", "forwardRef", "LinkWithRef", "ref", "onClick", "relative", "reloadDocument", "replace", "to", "preventScrollReset", "rest", "href", "useHref", "internalOnClick", "useLinkClickHandler", "handleClick", "defaultPrevented", "NavLink", "NavLinkWithRef", "ariaCurrentProp", "caseSensitive", "className", "classNameProp", "end", "style", "styleProp", "path", "useResolvedPath", "useLocation", "routerState", "useContext", "DataRouterStateContext", "toPathname", "pathname", "locationPathname", "nextLocationPathname", "navigation", "isActive", "startsWith", "char<PERSON>t", "length", "isPending", "aria<PERSON>urrent", "undefined", "filter", "Boolean", "join", "Form", "props", "FormImpl", "forwardedRef", "onSubmit", "fetcher<PERSON>ey", "routeId", "submit", "useSubmitImpl", "formMethod", "formAction", "useFormAction", "<PERSON><PERSON><PERSON><PERSON>", "preventDefault", "submitter", "nativeEvent", "currentTarget", "ScrollRestoration", "_ref7", "<PERSON><PERSON><PERSON>", "storageKey", "useScrollRestoration", "DataRouterHook", "DataRouterStateHook", "getDataRouterConsoleError", "<PERSON><PERSON><PERSON>", "useDataRouterContext", "ctx", "DataRouterContext", "useDataRouterState", "_temp", "replaceProp", "navigate", "useNavigate", "useCallback", "shouldProcessLinkClick", "createPath", "useSearchParams", "defaultInit", "defaultSearchParamsRef", "useMemo", "getSearchParamsForLocation", "search", "setSearchParams", "nextInit", "navigateOptions", "newSearchParams", "useSubmit", "router", "UseSubmitImpl", "document", "formEncType", "fetch", "_temp2", "NavigationContext", "routeContext", "RouteContext", "match", "matches", "slice", "resolvedAction", "hash", "route", "index", "params", "delete", "toString", "joinPaths", "createFetcherForm", "FetcherForm", "fetcherId", "useFetcher", "UseFetcher", "_route$matches", "id", "String", "load", "fetcher", "getFetcher", "fetcherWithComponents", "useEffect", "console", "warn", "deleteFetcher", "useFetchers", "UseFetchers", "fetchers", "values", "SCROLL_RESTORATION_STORAGE_KEY", "savedScrollPositions", "_temp3", "UseScrollRestoration", "restoreScrollPosition", "useMatches", "useNavigation", "scrollRestoration", "useBeforeUnload", "scrollY", "sessionStorage", "setItem", "JSON", "stringify", "sessionPositions", "getItem", "parse", "e", "disableScrollRestoration", "enableScrollRestoration", "scrollTo", "el", "getElementById", "scrollIntoView", "callback", "addEventListener", "removeEventListener", "warning", "cond", "message"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\react-router-dom\\dom.ts", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\react-router-dom\\index.tsx"], "sourcesContent": ["import type { FormEncType, FormMethod } from \"@remix-run/router\";\nimport type { RelativeRoutingType } from \"react-router\";\n\nexport const defaultMethod = \"get\";\nconst defaultEncType = \"application/x-www-form-urlencoded\";\n\nexport function isHtmlElement(object: any): object is HTMLElement {\n  return object != null && typeof object.tagName === \"string\";\n}\n\nexport function isButtonElement(object: any): object is HTMLButtonElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"button\";\n}\n\nexport function isFormElement(object: any): object is HTMLFormElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"form\";\n}\n\nexport function isInputElement(object: any): object is HTMLInputElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"input\";\n}\n\ntype LimitedMouseEvent = Pick<\n  MouseEvent,\n  \"button\" | \"metaKey\" | \"altKey\" | \"ctrlKey\" | \"shiftKey\"\n>;\n\nfunction isModifiedEvent(event: LimitedMouseEvent) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\n\nexport function shouldProcessLinkClick(\n  event: LimitedMouseEvent,\n  target?: string\n) {\n  return (\n    event.button === 0 && // Ignore everything but left clicks\n    (!target || target === \"_self\") && // Let browser handle \"target=_blank\" etc.\n    !isModifiedEvent(event) // Ignore clicks with modifier keys\n  );\n}\n\nexport type ParamKeyValuePair = [string, string];\n\nexport type URLSearchParamsInit =\n  | string\n  | ParamKeyValuePair[]\n  | Record<string, string | string[]>\n  | URLSearchParams;\n\n/**\n * Creates a URLSearchParams object using the given initializer.\n *\n * This is identical to `new URLSearchParams(init)` except it also\n * supports arrays as values in the object form of the initializer\n * instead of just strings. This is convenient when you need multiple\n * values for a given key, but don't want to use an array initializer.\n *\n * For example, instead of:\n *\n *   let searchParams = new URLSearchParams([\n *     ['sort', 'name'],\n *     ['sort', 'price']\n *   ]);\n *\n * you can do:\n *\n *   let searchParams = createSearchParams({\n *     sort: ['name', 'price']\n *   });\n */\nexport function createSearchParams(\n  init: URLSearchParamsInit = \"\"\n): URLSearchParams {\n  return new URLSearchParams(\n    typeof init === \"string\" ||\n    Array.isArray(init) ||\n    init instanceof URLSearchParams\n      ? init\n      : Object.keys(init).reduce((memo, key) => {\n          let value = init[key];\n          return memo.concat(\n            Array.isArray(value) ? value.map((v) => [key, v]) : [[key, value]]\n          );\n        }, [] as ParamKeyValuePair[])\n  );\n}\n\nexport function getSearchParamsForLocation(\n  locationSearch: string,\n  defaultSearchParams: URLSearchParams\n) {\n  let searchParams = createSearchParams(locationSearch);\n\n  for (let key of defaultSearchParams.keys()) {\n    if (!searchParams.has(key)) {\n      defaultSearchParams.getAll(key).forEach((value) => {\n        searchParams.append(key, value);\n      });\n    }\n  }\n\n  return searchParams;\n}\n\nexport interface SubmitOptions {\n  /**\n   * The HTTP method used to submit the form. Overrides `<form method>`.\n   * Defaults to \"GET\".\n   */\n  method?: FormMethod;\n\n  /**\n   * The action URL path used to submit the form. Overrides `<form action>`.\n   * Defaults to the path of the current route.\n   *\n   * Note: It is assumed the path is already resolved. If you need to resolve a\n   * relative path, use `useFormAction`.\n   */\n  action?: string;\n\n  /**\n   * The action URL used to submit the form. Overrides `<form encType>`.\n   * Defaults to \"application/x-www-form-urlencoded\".\n   */\n  encType?: FormEncType;\n\n  /**\n   * Set `true` to replace the current entry in the browser's history stack\n   * instead of creating a new one (i.e. stay on \"the same page\"). Defaults\n   * to `false`.\n   */\n  replace?: boolean;\n\n  /**\n   * Determines whether the form action is relative to the route hierarchy or\n   * the pathname.  Use this if you want to opt out of navigating the route\n   * hierarchy and want to instead route based on /-delimited URL segments\n   */\n  relative?: RelativeRoutingType;\n}\n\nexport function getFormSubmissionInfo(\n  target:\n    | HTMLFormElement\n    | HTMLButtonElement\n    | HTMLInputElement\n    | FormData\n    | URLSearchParams\n    | { [name: string]: string }\n    | null,\n  defaultAction: string,\n  options: SubmitOptions\n): {\n  url: URL;\n  method: string;\n  encType: string;\n  formData: FormData;\n} {\n  let method: string;\n  let action: string;\n  let encType: string;\n  let formData: FormData;\n\n  if (isFormElement(target)) {\n    let submissionTrigger: HTMLButtonElement | HTMLInputElement = (\n      options as any\n    ).submissionTrigger;\n\n    method = options.method || target.getAttribute(\"method\") || defaultMethod;\n    action = options.action || target.getAttribute(\"action\") || defaultAction;\n    encType =\n      options.encType || target.getAttribute(\"enctype\") || defaultEncType;\n\n    formData = new FormData(target);\n\n    if (submissionTrigger && submissionTrigger.name) {\n      formData.append(submissionTrigger.name, submissionTrigger.value);\n    }\n  } else if (\n    isButtonElement(target) ||\n    (isInputElement(target) &&\n      (target.type === \"submit\" || target.type === \"image\"))\n  ) {\n    let form = target.form;\n\n    if (form == null) {\n      throw new Error(\n        `Cannot submit a <button> or <input type=\"submit\"> without a <form>`\n      );\n    }\n\n    // <button>/<input type=\"submit\"> may override attributes of <form>\n\n    method =\n      options.method ||\n      target.getAttribute(\"formmethod\") ||\n      form.getAttribute(\"method\") ||\n      defaultMethod;\n    action =\n      options.action ||\n      target.getAttribute(\"formaction\") ||\n      form.getAttribute(\"action\") ||\n      defaultAction;\n    encType =\n      options.encType ||\n      target.getAttribute(\"formenctype\") ||\n      form.getAttribute(\"enctype\") ||\n      defaultEncType;\n\n    formData = new FormData(form);\n\n    // Include name + value from a <button>, appending in case the button name\n    // matches an existing input name\n    if (target.name) {\n      formData.append(target.name, target.value);\n    }\n  } else if (isHtmlElement(target)) {\n    throw new Error(\n      `Cannot submit element that is not <form>, <button>, or ` +\n        `<input type=\"submit|image\">`\n    );\n  } else {\n    method = options.method || defaultMethod;\n    action = options.action || defaultAction;\n    encType = options.encType || defaultEncType;\n\n    if (target instanceof FormData) {\n      formData = target;\n    } else {\n      formData = new FormData();\n\n      if (target instanceof URLSearchParams) {\n        for (let [name, value] of target) {\n          formData.append(name, value);\n        }\n      } else if (target != null) {\n        for (let name of Object.keys(target)) {\n          formData.append(name, target[name]);\n        }\n      }\n    }\n  }\n\n  let { protocol, host } = window.location;\n  let url = new URL(action, `${protocol}//${host}`);\n\n  return { url, method, encType, formData };\n}\n", "/**\n * NOTE: If you refactor this to split up the modules into separate files,\n * you'll need to update the rollup config for react-router-dom-v5-compat.\n */\nimport * as React from \"react\";\nimport type {\n  NavigateOptions,\n  RelativeRoutingType,\n  RouteObject,\n  To,\n} from \"react-router\";\nimport {\n  Router,\n  createPath,\n  useHref,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useResolvedPath,\n  UNSAFE_DataRouterContext as DataRouterContext,\n  UNSAFE_DataRouterStateContext as DataRouterStateContext,\n  UNSAFE_NavigationContext as NavigationContext,\n  UNSAFE_RouteContext as RouteContext,\n  UNSAFE_enhanceManualRouteObjects as enhanceManualRouteObjects,\n} from \"react-router\";\nimport type {\n  BrowserHistory,\n  Fetcher,\n  FormEncType,\n  FormMethod,\n  GetScrollRestorationKeyFunction,\n  HashHistory,\n  History,\n  HydrationState,\n  Router as RemixRouter,\n} from \"@remix-run/router\";\nimport {\n  createRouter,\n  createBrowserHistory,\n  createHashHistory,\n  invariant,\n  joinPaths,\n  matchPath,\n} from \"@remix-run/router\";\n\nimport type {\n  SubmitOptions,\n  ParamKeyValuePair,\n  URLSearchParamsInit,\n} from \"./dom\";\nimport {\n  createSearchParams,\n  defaultMethod,\n  getFormSubmissionInfo,\n  getSearchParamsForLocation,\n  shouldProcessLinkClick,\n} from \"./dom\";\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Re-exports\n////////////////////////////////////////////////////////////////////////////////\n\nexport type {\n  FormEncType,\n  FormMethod,\n  ParamKeyValuePair,\n  SubmitOptions,\n  URLSearchParamsInit,\n};\nexport { createSearchParams };\n\n// Note: Keep in sync with react-router exports!\nexport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AwaitProps,\n  DataRouteMatch,\n  DataRouteObject,\n  Fetcher,\n  Hash,\n  IndexRouteObject,\n  IndexRouteProps,\n  JsonFunction,\n  LayoutRouteProps,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  MemoryRouterProps,\n  NavigateFunction,\n  NavigateOptions,\n  NavigateProps,\n  Navigation,\n  Navigator,\n  NonIndexRouteObject,\n  OutletProps,\n  Params,\n  ParamParseKey,\n  Path,\n  PathMatch,\n  Pathname,\n  PathPattern,\n  PathRouteProps,\n  RedirectFunction,\n  RelativeRoutingType,\n  RouteMatch,\n  RouteObject,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n  Search,\n  ShouldRevalidateFunction,\n  To,\n} from \"react-router\";\nexport {\n  AbortedDeferredError,\n  Await,\n  MemoryRouter,\n  Navigate,\n  NavigationType,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n  createMemoryRouter,\n  createPath,\n  createRoutesFromChildren,\n  createRoutesFromElements,\n  defer,\n  isRouteErrorResponse,\n  generatePath,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  renderMatches,\n  resolvePath,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n  useRoutes,\n} from \"react-router\";\n\n///////////////////////////////////////////////////////////////////////////////\n// DANGER! PLEASE READ ME!\n// We provide these exports as an escape hatch in the event that you need any\n// routing data that we don't provide an explicit API for. With that said, we\n// want to cover your use case if we can, so if you feel the need to use these\n// we want to hear from you. Let us know what you're building and we'll do our\n// best to make sure we can support you!\n//\n// We consider these exports an implementation detail and do not guarantee\n// against any breaking changes, regardless of the semver release. Use with\n// extreme caution and only if you understand the consequences. Godspeed.\n///////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport {\n  UNSAFE_DataRouterContext,\n  UNSAFE_DataRouterStateContext,\n  UNSAFE_DataStaticRouterContext,\n  UNSAFE_NavigationContext,\n  UNSAFE_LocationContext,\n  UNSAFE_RouteContext,\n  UNSAFE_enhanceManualRouteObjects,\n} from \"react-router\";\n//#endregion\n\ndeclare global {\n  var __staticRouterHydrationData: HydrationState | undefined;\n}\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Routers\n////////////////////////////////////////////////////////////////////////////////\n\nexport function createBrowserRouter(\n  routes: RouteObject[],\n  opts?: {\n    basename?: string;\n    hydrationData?: HydrationState;\n    window?: Window;\n  }\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    history: createBrowserHistory({ window: opts?.window }),\n    hydrationData: opts?.hydrationData || window?.__staticRouterHydrationData,\n    routes: enhanceManualRouteObjects(routes),\n  }).initialize();\n}\n\nexport function createHashRouter(\n  routes: RouteObject[],\n  opts?: {\n    basename?: string;\n    hydrationData?: HydrationState;\n    window?: Window;\n  }\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    history: createHashHistory({ window: opts?.window }),\n    hydrationData: opts?.hydrationData || window?.__staticRouterHydrationData,\n    routes: enhanceManualRouteObjects(routes),\n  }).initialize();\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Components\n////////////////////////////////////////////////////////////////////////////////\n\nexport interface BrowserRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Provides the cleanest URLs.\n */\nexport function BrowserRouter({\n  basename,\n  children,\n  window,\n}: BrowserRouterProps) {\n  let historyRef = React.useRef<BrowserHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createBrowserHistory({ window, v5Compat: true });\n  }\n\n  let history = historyRef.current;\n  let [state, setState] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n    />\n  );\n}\n\nexport interface HashRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Stores the location in the hash\n * portion of the URL so it is not sent to the server.\n */\nexport function HashRouter({ basename, children, window }: HashRouterProps) {\n  let historyRef = React.useRef<HashHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createHashHistory({ window, v5Compat: true });\n  }\n\n  let history = historyRef.current;\n  let [state, setState] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n    />\n  );\n}\n\nexport interface HistoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  history: History;\n}\n\n/**\n * A `<Router>` that accepts a pre-instantiated history object. It's important\n * to note that using your own history object is highly discouraged and may add\n * two versions of the history library to your bundles unless you use the same\n * version of the history library that React Router uses internally.\n */\nfunction HistoryRouter({ basename, children, history }: HistoryRouterProps) {\n  const [state, setState] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n    />\n  );\n}\n\nif (__DEV__) {\n  HistoryRouter.displayName = \"unstable_HistoryRouter\";\n}\n\nexport { HistoryRouter as unstable_HistoryRouter };\n\nexport interface LinkProps\n  extends Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, \"href\"> {\n  reloadDocument?: boolean;\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n  to: To;\n}\n\n/**\n * The public API for rendering a history-aware <a>.\n */\nexport const Link = React.forwardRef<HTMLAnchorElement, LinkProps>(\n  function LinkWithRef(\n    {\n      onClick,\n      relative,\n      reloadDocument,\n      replace,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      ...rest\n    },\n    ref\n  ) {\n    let href = useHref(to, { relative });\n    let internalOnClick = useLinkClickHandler(to, {\n      replace,\n      state,\n      target,\n      preventScrollReset,\n      relative,\n    });\n    function handleClick(\n      event: React.MouseEvent<HTMLAnchorElement, MouseEvent>\n    ) {\n      if (onClick) onClick(event);\n      if (!event.defaultPrevented) {\n        internalOnClick(event);\n      }\n    }\n\n    return (\n      // eslint-disable-next-line jsx-a11y/anchor-has-content\n      <a\n        {...rest}\n        href={href}\n        onClick={reloadDocument ? onClick : handleClick}\n        ref={ref}\n        target={target}\n      />\n    );\n  }\n);\n\nif (__DEV__) {\n  Link.displayName = \"Link\";\n}\n\nexport interface NavLinkProps\n  extends Omit<LinkProps, \"className\" | \"style\" | \"children\"> {\n  children?:\n    | React.ReactNode\n    | ((props: { isActive: boolean; isPending: boolean }) => React.ReactNode);\n  caseSensitive?: boolean;\n  className?:\n    | string\n    | ((props: {\n        isActive: boolean;\n        isPending: boolean;\n      }) => string | undefined);\n  end?: boolean;\n  style?:\n    | React.CSSProperties\n    | ((props: {\n        isActive: boolean;\n        isPending: boolean;\n      }) => React.CSSProperties | undefined);\n}\n\n/**\n * A <Link> wrapper that knows if it's \"active\" or not.\n */\nexport const NavLink = React.forwardRef<HTMLAnchorElement, NavLinkProps>(\n  function NavLinkWithRef(\n    {\n      \"aria-current\": ariaCurrentProp = \"page\",\n      caseSensitive = false,\n      className: classNameProp = \"\",\n      end = false,\n      style: styleProp,\n      to,\n      children,\n      ...rest\n    },\n    ref\n  ) {\n    let path = useResolvedPath(to, { relative: rest.relative });\n    let location = useLocation();\n    let routerState = React.useContext(DataRouterStateContext);\n\n    let toPathname = path.pathname;\n    let locationPathname = location.pathname;\n    let nextLocationPathname =\n      routerState && routerState.navigation && routerState.navigation.location\n        ? routerState.navigation.location.pathname\n        : null;\n\n    if (!caseSensitive) {\n      locationPathname = locationPathname.toLowerCase();\n      nextLocationPathname = nextLocationPathname\n        ? nextLocationPathname.toLowerCase()\n        : null;\n      toPathname = toPathname.toLowerCase();\n    }\n\n    let isActive =\n      locationPathname === toPathname ||\n      (!end &&\n        locationPathname.startsWith(toPathname) &&\n        locationPathname.charAt(toPathname.length) === \"/\");\n\n    let isPending =\n      nextLocationPathname != null &&\n      (nextLocationPathname === toPathname ||\n        (!end &&\n          nextLocationPathname.startsWith(toPathname) &&\n          nextLocationPathname.charAt(toPathname.length) === \"/\"));\n\n    let ariaCurrent = isActive ? ariaCurrentProp : undefined;\n\n    let className: string | undefined;\n    if (typeof classNameProp === \"function\") {\n      className = classNameProp({ isActive, isPending });\n    } else {\n      // If the className prop is not a function, we use a default `active`\n      // class for <NavLink />s that are active. In v5 `active` was the default\n      // value for `activeClassName`, but we are removing that API and can still\n      // use the old default behavior for a cleaner upgrade path and keep the\n      // simple styling rules working as they currently do.\n      className = [\n        classNameProp,\n        isActive ? \"active\" : null,\n        isPending ? \"pending\" : null,\n      ]\n        .filter(Boolean)\n        .join(\" \");\n    }\n\n    let style =\n      typeof styleProp === \"function\"\n        ? styleProp({ isActive, isPending })\n        : styleProp;\n\n    return (\n      <Link\n        {...rest}\n        aria-current={ariaCurrent}\n        className={className}\n        ref={ref}\n        style={style}\n        to={to}\n      >\n        {typeof children === \"function\"\n          ? children({ isActive, isPending })\n          : children}\n      </Link>\n    );\n  }\n);\n\nif (__DEV__) {\n  NavLink.displayName = \"NavLink\";\n}\n\nexport interface FormProps extends React.FormHTMLAttributes<HTMLFormElement> {\n  /**\n   * The HTTP verb to use when the form is submit. Supports \"get\", \"post\",\n   * \"put\", \"delete\", \"patch\".\n   */\n  method?: FormMethod;\n\n  /**\n   * Normal `<form action>` but supports React Router's relative paths.\n   */\n  action?: string;\n\n  /**\n   * Forces a full document navigation instead of a fetch.\n   */\n  reloadDocument?: boolean;\n\n  /**\n   * Replaces the current entry in the browser history stack when the form\n   * navigates. Use this if you don't want the user to be able to click \"back\"\n   * to the page with the form on it.\n   */\n  replace?: boolean;\n\n  /**\n   * Determines whether the form action is relative to the route hierarchy or\n   * the pathname.  Use this if you want to opt out of navigating the route\n   * hierarchy and want to instead route based on /-delimited URL segments\n   */\n  relative?: RelativeRoutingType;\n\n  /**\n   * A function to call when the form is submitted. If you call\n   * `event.preventDefault()` then this form will not do anything.\n   */\n  onSubmit?: React.FormEventHandler<HTMLFormElement>;\n}\n\n/**\n * A `@remix-run/router`-aware `<form>`. It behaves like a normal form except\n * that the interaction with the server is with `fetch` instead of new document\n * requests, allowing components to add nicer UX to the page as the form is\n * submitted and returns with data.\n */\nexport const Form = React.forwardRef<HTMLFormElement, FormProps>(\n  (props, ref) => {\n    return <FormImpl {...props} ref={ref} />;\n  }\n);\n\nif (__DEV__) {\n  Form.displayName = \"Form\";\n}\n\ntype HTMLSubmitEvent = React.BaseSyntheticEvent<\n  SubmitEvent,\n  Event,\n  HTMLFormElement\n>;\n\ntype HTMLFormSubmitter = HTMLButtonElement | HTMLInputElement;\n\ninterface FormImplProps extends FormProps {\n  fetcherKey?: string;\n  routeId?: string;\n}\n\nconst FormImpl = React.forwardRef<HTMLFormElement, FormImplProps>(\n  (\n    {\n      reloadDocument,\n      replace,\n      method = defaultMethod,\n      action,\n      onSubmit,\n      fetcherKey,\n      routeId,\n      relative,\n      ...props\n    },\n    forwardedRef\n  ) => {\n    let submit = useSubmitImpl(fetcherKey, routeId);\n    let formMethod: FormMethod =\n      method.toLowerCase() === \"get\" ? \"get\" : \"post\";\n    let formAction = useFormAction(action, { relative });\n    let submitHandler: React.FormEventHandler<HTMLFormElement> = (event) => {\n      onSubmit && onSubmit(event);\n      if (event.defaultPrevented) return;\n      event.preventDefault();\n\n      let submitter = (event as unknown as HTMLSubmitEvent).nativeEvent\n        .submitter as HTMLFormSubmitter | null;\n\n      submit(submitter || event.currentTarget, { method, replace, relative });\n    };\n\n    return (\n      <form\n        ref={forwardedRef}\n        method={formMethod}\n        action={formAction}\n        onSubmit={reloadDocument ? onSubmit : submitHandler}\n        {...props}\n      />\n    );\n  }\n);\n\nif (__DEV__) {\n  Form.displayName = \"Form\";\n}\n\ninterface ScrollRestorationProps {\n  getKey?: GetScrollRestorationKeyFunction;\n  storageKey?: string;\n}\n\n/**\n * This component will emulate the browser's scroll restoration on location\n * changes.\n */\nexport function ScrollRestoration({\n  getKey,\n  storageKey,\n}: ScrollRestorationProps) {\n  useScrollRestoration({ getKey, storageKey });\n  return null;\n}\n\nif (__DEV__) {\n  ScrollRestoration.displayName = \"ScrollRestoration\";\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Hooks\n////////////////////////////////////////////////////////////////////////////////\n\nenum DataRouterHook {\n  UseScrollRestoration = \"useScrollRestoration\",\n  UseSubmitImpl = \"useSubmitImpl\",\n  UseFetcher = \"useFetcher\",\n}\n\nenum DataRouterStateHook {\n  UseFetchers = \"useFetchers\",\n  UseScrollRestoration = \"useScrollRestoration\",\n}\n\nfunction getDataRouterConsoleError(\n  hookName: DataRouterHook | DataRouterStateHook\n) {\n  return `${hookName} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`;\n}\n\nfunction useDataRouterContext(hookName: DataRouterHook) {\n  let ctx = React.useContext(DataRouterContext);\n  invariant(ctx, getDataRouterConsoleError(hookName));\n  return ctx;\n}\n\nfunction useDataRouterState(hookName: DataRouterStateHook) {\n  let state = React.useContext(DataRouterStateContext);\n  invariant(state, getDataRouterConsoleError(hookName));\n  return state;\n}\n\n/**\n * Handles the click behavior for router `<Link>` components. This is useful if\n * you need to create custom `<Link>` components with the same click behavior we\n * use in our exported `<Link>`.\n */\nexport function useLinkClickHandler<E extends Element = HTMLAnchorElement>(\n  to: To,\n  {\n    target,\n    replace: replaceProp,\n    state,\n    preventScrollReset,\n    relative,\n  }: {\n    target?: React.HTMLAttributeAnchorTarget;\n    replace?: boolean;\n    state?: any;\n    preventScrollReset?: boolean;\n    relative?: RelativeRoutingType;\n  } = {}\n): (event: React.MouseEvent<E, MouseEvent>) => void {\n  let navigate = useNavigate();\n  let location = useLocation();\n  let path = useResolvedPath(to, { relative });\n\n  return React.useCallback(\n    (event: React.MouseEvent<E, MouseEvent>) => {\n      if (shouldProcessLinkClick(event, target)) {\n        event.preventDefault();\n\n        // If the URL hasn't changed, a regular <a> will do a replace instead of\n        // a push, so do the same here unless the replace prop is explicitly set\n        let replace =\n          replaceProp !== undefined\n            ? replaceProp\n            : createPath(location) === createPath(path);\n\n        navigate(to, { replace, state, preventScrollReset, relative });\n      }\n    },\n    [\n      location,\n      navigate,\n      path,\n      replaceProp,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      relative,\n    ]\n  );\n}\n\n/**\n * A convenient wrapper for reading and writing search parameters via the\n * URLSearchParams interface.\n */\nexport function useSearchParams(\n  defaultInit?: URLSearchParamsInit\n): [URLSearchParams, SetURLSearchParams] {\n  warning(\n    typeof URLSearchParams !== \"undefined\",\n    `You cannot use the \\`useSearchParams\\` hook in a browser that does not ` +\n      `support the URLSearchParams API. If you need to support Internet ` +\n      `Explorer 11, we recommend you load a polyfill such as ` +\n      `https://github.com/ungap/url-search-params\\n\\n` +\n      `If you're unsure how to load polyfills, we recommend you check out ` +\n      `https://polyfill.io/v3/ which provides some recommendations about how ` +\n      `to load polyfills only for users that need them, instead of for every ` +\n      `user.`\n  );\n\n  let defaultSearchParamsRef = React.useRef(createSearchParams(defaultInit));\n\n  let location = useLocation();\n  let searchParams = React.useMemo(\n    () =>\n      getSearchParamsForLocation(\n        location.search,\n        defaultSearchParamsRef.current\n      ),\n    [location.search]\n  );\n\n  let navigate = useNavigate();\n  let setSearchParams = React.useCallback<SetURLSearchParams>(\n    (nextInit, navigateOptions) => {\n      const newSearchParams = createSearchParams(\n        typeof nextInit === \"function\" ? nextInit(searchParams) : nextInit\n      );\n      navigate(\"?\" + newSearchParams, navigateOptions);\n    },\n    [navigate, searchParams]\n  );\n\n  return [searchParams, setSearchParams];\n}\n\ntype SetURLSearchParams = (\n  nextInit?:\n    | URLSearchParamsInit\n    | ((prev: URLSearchParams) => URLSearchParamsInit),\n  navigateOpts?: NavigateOptions\n) => void;\n\ntype SubmitTarget =\n  | HTMLFormElement\n  | HTMLButtonElement\n  | HTMLInputElement\n  | FormData\n  | URLSearchParams\n  | { [name: string]: string }\n  | null;\n\n/**\n * Submits a HTML `<form>` to the server without reloading the page.\n */\nexport interface SubmitFunction {\n  (\n    /**\n     * Specifies the `<form>` to be submitted to the server, a specific\n     * `<button>` or `<input type=\"submit\">` to use to submit the form, or some\n     * arbitrary data to submit.\n     *\n     * Note: When using a `<button>` its `name` and `value` will also be\n     * included in the form data that is submitted.\n     */\n    target: SubmitTarget,\n\n    /**\n     * Options that override the `<form>`'s own attributes. Required when\n     * submitting arbitrary data without a backing `<form>`.\n     */\n    options?: SubmitOptions\n  ): void;\n}\n\n/**\n * Returns a function that may be used to programmatically submit a form (or\n * some arbitrary data) to the server.\n */\nexport function useSubmit(): SubmitFunction {\n  return useSubmitImpl();\n}\n\nfunction useSubmitImpl(fetcherKey?: string, routeId?: string): SubmitFunction {\n  let { router } = useDataRouterContext(DataRouterHook.UseSubmitImpl);\n  let defaultAction = useFormAction();\n\n  return React.useCallback(\n    (target, options = {}) => {\n      if (typeof document === \"undefined\") {\n        throw new Error(\n          \"You are calling submit during the server render. \" +\n            \"Try calling submit within a `useEffect` or callback instead.\"\n        );\n      }\n\n      let { method, encType, formData, url } = getFormSubmissionInfo(\n        target,\n        defaultAction,\n        options\n      );\n\n      let href = url.pathname + url.search;\n      let opts = {\n        replace: options.replace,\n        formData,\n        formMethod: method as FormMethod,\n        formEncType: encType as FormEncType,\n      };\n      if (fetcherKey) {\n        invariant(routeId != null, \"No routeId available for useFetcher()\");\n        router.fetch(fetcherKey, routeId, href, opts);\n      } else {\n        router.navigate(href, opts);\n      }\n    },\n    [defaultAction, router, fetcherKey, routeId]\n  );\n}\n\nexport function useFormAction(\n  action?: string,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): string {\n  let { basename } = React.useContext(NavigationContext);\n  let routeContext = React.useContext(RouteContext);\n  invariant(routeContext, \"useFormAction must be used inside a RouteContext\");\n\n  let [match] = routeContext.matches.slice(-1);\n  let resolvedAction = action ?? \".\";\n  // Shallow clone path so we can modify it below, otherwise we modify the\n  // object referenced by useMemo inside useResolvedPath\n  let path = { ...useResolvedPath(resolvedAction, { relative }) };\n\n  // Previously we set the default action to \".\". The problem with this is that\n  // `useResolvedPath(\".\")` excludes search params and the hash of the resolved\n  // URL. This is the intended behavior of when \".\" is specifically provided as\n  // the form action, but inconsistent w/ browsers when the action is omitted.\n  // https://github.com/remix-run/remix/issues/927\n  let location = useLocation();\n  if (action == null) {\n    // Safe to write to these directly here since if action was undefined, we\n    // would have called useResolvedPath(\".\") which will never include a search\n    // or hash\n    path.search = location.search;\n    path.hash = location.hash;\n\n    // When grabbing search params from the URL, remove the automatically\n    // inserted ?index param so we match the useResolvedPath search behavior\n    // which would not include ?index\n    if (match.route.index) {\n      let params = new URLSearchParams(path.search);\n      params.delete(\"index\");\n      path.search = params.toString() ? `?${params.toString()}` : \"\";\n    }\n  }\n\n  if ((!action || action === \".\") && match.route.index) {\n    path.search = path.search\n      ? path.search.replace(/^\\?/, \"?index&\")\n      : \"?index\";\n  }\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the form action.  If this is a root navigation, then just use\n  // the raw basename which allows the basename to have full control over the\n  // presence of a trailing slash on root actions\n  if (basename !== \"/\") {\n    path.pathname =\n      path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n\n  return createPath(path);\n}\n\nfunction createFetcherForm(fetcherKey: string, routeId: string) {\n  let FetcherForm = React.forwardRef<HTMLFormElement, FormProps>(\n    (props, ref) => {\n      return (\n        <FormImpl\n          {...props}\n          ref={ref}\n          fetcherKey={fetcherKey}\n          routeId={routeId}\n        />\n      );\n    }\n  );\n  if (__DEV__) {\n    FetcherForm.displayName = \"fetcher.Form\";\n  }\n  return FetcherForm;\n}\n\nlet fetcherId = 0;\n\nexport type FetcherWithComponents<TData> = Fetcher<TData> & {\n  Form: ReturnType<typeof createFetcherForm>;\n  submit: (\n    target: SubmitTarget,\n    // Fetchers cannot replace because they are not navigation events\n    options?: Omit<SubmitOptions, \"replace\">\n  ) => void;\n  load: (href: string) => void;\n};\n\n/**\n * Interacts with route loaders and actions without causing a navigation. Great\n * for any interaction that stays on the same page.\n */\nexport function useFetcher<TData = any>(): FetcherWithComponents<TData> {\n  let { router } = useDataRouterContext(DataRouterHook.UseFetcher);\n\n  let route = React.useContext(RouteContext);\n  invariant(route, `useFetcher must be used inside a RouteContext`);\n\n  let routeId = route.matches[route.matches.length - 1]?.route.id;\n  invariant(\n    routeId != null,\n    `useFetcher can only be used on routes that contain a unique \"id\"`\n  );\n\n  let [fetcherKey] = React.useState(() => String(++fetcherId));\n  let [Form] = React.useState(() => {\n    invariant(routeId, `No routeId available for fetcher.Form()`);\n    return createFetcherForm(fetcherKey, routeId);\n  });\n  let [load] = React.useState(() => (href: string) => {\n    invariant(router, \"No router available for fetcher.load()\");\n    invariant(routeId, \"No routeId available for fetcher.load()\");\n    router.fetch(fetcherKey, routeId, href);\n  });\n  let submit = useSubmitImpl(fetcherKey, routeId);\n\n  let fetcher = router.getFetcher<TData>(fetcherKey);\n\n  let fetcherWithComponents = React.useMemo(\n    () => ({\n      Form,\n      submit,\n      load,\n      ...fetcher,\n    }),\n    [fetcher, Form, submit, load]\n  );\n\n  React.useEffect(() => {\n    // Is this busted when the React team gets real weird and calls effects\n    // twice on mount?  We really just need to garbage collect here when this\n    // fetcher is no longer around.\n    return () => {\n      if (!router) {\n        console.warn(`No fetcher available to clean up from useFetcher()`);\n        return;\n      }\n      router.deleteFetcher(fetcherKey);\n    };\n  }, [router, fetcherKey]);\n\n  return fetcherWithComponents;\n}\n\n/**\n * Provides all fetchers currently on the page. Useful for layouts and parent\n * routes that need to provide pending/optimistic UI regarding the fetch.\n */\nexport function useFetchers(): Fetcher[] {\n  let state = useDataRouterState(DataRouterStateHook.UseFetchers);\n  return [...state.fetchers.values()];\n}\n\nconst SCROLL_RESTORATION_STORAGE_KEY = \"react-router-scroll-positions\";\nlet savedScrollPositions: Record<string, number> = {};\n\n/**\n * When rendered inside a RouterProvider, will restore scroll positions on navigations\n */\nfunction useScrollRestoration({\n  getKey,\n  storageKey,\n}: {\n  getKey?: GetScrollRestorationKeyFunction;\n  storageKey?: string;\n} = {}) {\n  let { router } = useDataRouterContext(DataRouterHook.UseScrollRestoration);\n  let { restoreScrollPosition, preventScrollReset } = useDataRouterState(\n    DataRouterStateHook.UseScrollRestoration\n  );\n  let location = useLocation();\n  let matches = useMatches();\n  let navigation = useNavigation();\n\n  // Trigger manual scroll restoration while we're active\n  React.useEffect(() => {\n    window.history.scrollRestoration = \"manual\";\n    return () => {\n      window.history.scrollRestoration = \"auto\";\n    };\n  }, []);\n\n  // Save positions on unload\n  useBeforeUnload(\n    React.useCallback(() => {\n      if (navigation.state === \"idle\") {\n        let key = (getKey ? getKey(location, matches) : null) || location.key;\n        savedScrollPositions[key] = window.scrollY;\n      }\n      sessionStorage.setItem(\n        storageKey || SCROLL_RESTORATION_STORAGE_KEY,\n        JSON.stringify(savedScrollPositions)\n      );\n      window.history.scrollRestoration = \"auto\";\n    }, [storageKey, getKey, navigation.state, location, matches])\n  );\n\n  // Read in any saved scroll locations\n  React.useLayoutEffect(() => {\n    try {\n      let sessionPositions = sessionStorage.getItem(\n        storageKey || SCROLL_RESTORATION_STORAGE_KEY\n      );\n      if (sessionPositions) {\n        savedScrollPositions = JSON.parse(sessionPositions);\n      }\n    } catch (e) {\n      // no-op, use default empty object\n    }\n  }, [storageKey]);\n\n  // Enable scroll restoration in the router\n  React.useLayoutEffect(() => {\n    let disableScrollRestoration = router?.enableScrollRestoration(\n      savedScrollPositions,\n      () => window.scrollY,\n      getKey\n    );\n    return () => disableScrollRestoration && disableScrollRestoration();\n  }, [router, getKey]);\n\n  // Restore scrolling when state.restoreScrollPosition changes\n  React.useLayoutEffect(() => {\n    // Explicit false means don't do anything (used for submissions)\n    if (restoreScrollPosition === false) {\n      return;\n    }\n\n    // been here before, scroll to it\n    if (typeof restoreScrollPosition === \"number\") {\n      window.scrollTo(0, restoreScrollPosition);\n      return;\n    }\n\n    // try to scroll to the hash\n    if (location.hash) {\n      let el = document.getElementById(location.hash.slice(1));\n      if (el) {\n        el.scrollIntoView();\n        return;\n      }\n    }\n\n    // Opt out of scroll reset if this link requested it\n    if (preventScrollReset === true) {\n      return;\n    }\n\n    // otherwise go to the top on new locations\n    window.scrollTo(0, 0);\n  }, [location, restoreScrollPosition, preventScrollReset]);\n}\n\nfunction useBeforeUnload(callback: () => any): void {\n  React.useEffect(() => {\n    window.addEventListener(\"beforeunload\", callback);\n    return () => {\n      window.removeEventListener(\"beforeunload\", callback);\n    };\n  }, [callback]);\n}\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Utils\n////////////////////////////////////////////////////////////////////////////////\n\nfunction warning(cond: boolean, message: string): void {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n\n    try {\n      // Welcome to debugging React Router!\n      //\n      // This error is thrown as a convenience so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message);\n      // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\n//#endregion\n"]}, "metadata": {}, "sourceType": "module"}