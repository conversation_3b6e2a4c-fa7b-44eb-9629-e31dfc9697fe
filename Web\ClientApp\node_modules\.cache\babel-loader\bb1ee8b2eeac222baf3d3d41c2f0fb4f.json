{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\GreenRP\\\\Web\\\\ClientApp\\\\src\\\\features\\\\orders\\\\Detail.tsx\",\n  _s = $RefreshSig$();\nimport React, { useCallback, useEffect, useState } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { useParams, useNavigate } from 'react-router';\nimport moment from 'moment';\nimport { Button, FormGroup, Input, Modal, ModalHeader, ModalBody, ModalFooter } from 'reactstrap';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { notificationsApi } from 'api/notifications-service';\nimport { createOrder, createOrderNumber } from 'api/models/orders';\nimport { routes } from 'app/routes';\nimport { useAuth } from 'features/auth/use-auth';\nimport { selectPlants } from 'features/plants/plants-slice';\nimport { selectCustomers } from 'features/customers/customers-slice';\nimport { selectZones } from 'features/zones/zones-slice';\nimport { handleFocus } from 'utils/focus';\nimport { toWeekAndDay, formatNumber, parseWeekAndDay, toWeekAndYear } from 'utils/format';\nimport { deleteOrder, saveOrder, selectOrder, setOrder } from './detail-slice';\nimport { selectAllOrders } from './orders-slice';\nimport { Variety } from './Variety';\nimport { SalesWeekRow } from './SalesWeekRow';\nimport { guid } from 'utils/guid';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport function Detail() {\n  _s();\n  var _order$salesWeeks, _order$fullSpaceZone, _order$lightsOutZone, _order$partialSpaceZo, _order$salesWeeks3;\n  const dispatch = useDispatch(),\n    navigate = useNavigate(),\n    {\n      isInRole,\n      user\n    } = useAuth(),\n    {\n      id\n    } = useParams(),\n    orders = useSelector(selectAllOrders),\n    order = useSelector(selectOrder),\n    customers = useSelector(selectCustomers),\n    zones = useSelector(selectZones),\n    plants = useSelector(selectPlants),\n    isNew = !(order !== null && order !== void 0 && order._rev),\n    [stickWeek, setStickWeek] = useState(''),\n    [lightsOutWeek, setLightsOutWeek] = useState(''),\n    [partialSpaceWeek, setPartialSpaceWeek] = useState(''),\n    [fullSpaceWeek, setFullSpaceWeek] = useState(''),\n    [pinchWeek, setPinchWeek] = useState(''),\n    [flowerWeek, setFlowerWeek] = useState(''),\n    [showNotificationModal, setShowNotificationModal] = useState(false),\n    [notificationMessage, setNotificationMessage] = useState(''),\n    hasSalesWeeks = Array.isArray(order === null || order === void 0 ? void 0 : order.salesWeeks),\n    canUpdate = isNew && isInRole('create:orders') || isInRole('update:orders'),\n    canDelete = isInRole('delete:orders'),\n    salesWeekTotal = order === null || order === void 0 ? void 0 : (_order$salesWeeks = order.salesWeeks) === null || _order$salesWeeks === void 0 ? void 0 : _order$salesWeeks.reduce((total, sw) => total + sw.cases, 0);\n  const onPlantChange = useCallback((plant, order) => {\n    const orderNumber = createOrderNumber(plant.abbreviation, order.customer.abbreviation, order.stickDate),\n      update = {\n        ...order,\n        orderNumber,\n        plant\n      };\n    update.hasLightsOut = plant.hasLightsOut;\n    if (plant.hasLightsOut) {\n      update.lightsOutDate = order.fullSpaceDate || order.stickDate;\n      update.lightsOutZone = order.fullSpaceZone || order.stickZone;\n    }\n    update.hasPinching = plant.hasPinching;\n    if (plant.hasPinching) {\n      update.pinchDate = order.pinchDate || moment(order.stickDate).add(plant.daysToPinch || 0, 'days').format('YYYY-MM-DD');\n      setPinchWeek(toWeekAndDay(update.pinchDate));\n    }\n    if (plant.varieties) {\n      update.varieties = [];\n    } else {\n      delete update.varieties;\n    }\n    dispatch(setOrder(update));\n  }, [dispatch]);\n  useEffect(() => {\n    const found = orders.find(o => o._id === id);\n    if (found && found._id !== (order === null || order === void 0 ? void 0 : order._id)) {\n      dispatch(setOrder(found));\n      setStickWeek(toWeekAndDay(found.stickDate));\n      if (found.lightsOutDate) {\n        setLightsOutWeek(toWeekAndDay(found.lightsOutDate));\n      }\n      if (found.partialSpaceDate) {\n        setPartialSpaceWeek(toWeekAndDay(found.partialSpaceDate));\n      }\n      if (found.fullSpaceDate) {\n        setFullSpaceWeek(toWeekAndDay(found.fullSpaceDate));\n      }\n      if (found.hasPinching) {\n        setPinchWeek(toWeekAndDay(found.pinchDate));\n      }\n      setFlowerWeek(toWeekAndDay(found.flowerDate));\n    } else if (id === 'new') {\n      if (!order) {\n        const newOrder = createOrder();\n        if (zones.length) {\n          newOrder.stickZone = zones[0];\n          if (newOrder.partialSpaceDate) {\n            newOrder.partialSpaceZone = zones[0];\n          }\n          if (newOrder.lightsOutDate) {\n            newOrder.lightsOutZone = zones[0];\n          }\n          if (newOrder.fullSpaceDate) {\n            newOrder.fullSpaceZone = zones[0];\n          }\n        }\n        dispatch(setOrder(newOrder));\n      } else if (plants.length && !plants.some(p => p._id === order.plant._id)) {\n        const firstPlant = plants[0];\n        onPlantChange(firstPlant, order);\n        setStickWeek(toWeekAndDay(order.stickDate));\n        if (order.lightsOutDate) {\n          setLightsOutWeek(toWeekAndDay(order.lightsOutDate));\n        }\n        if (order.partialSpaceDate) {\n          setPartialSpaceWeek(toWeekAndDay(order.partialSpaceDate));\n        }\n        if (order.fullSpaceDate) {\n          setFullSpaceWeek(toWeekAndDay(order.fullSpaceDate));\n        }\n        if (order.pinchDate) {\n          setPinchWeek(toWeekAndDay(order.pinchDate));\n        } else if (order.hasPinching) {\n          setPinchWeek(toWeekAndDay(moment(order.stickDate).add(firstPlant.daysToPinch || 0, 'days').format('YYYY-MM-DD')));\n        }\n        setFlowerWeek(toWeekAndDay(order.flowerDate));\n      }\n    }\n    return function cleanup() {\n      //dispatch(setOrder(null));\n    };\n  }, [dispatch, id, onPlantChange, order, orders, plants, zones]);\n  const handleCustomerChange = e => {\n    const abbreviation = e.target.value,\n      customer = customers.find(c => c.abbreviation === abbreviation);\n    if (order && customer) {\n      const orderNumber = createOrderNumber(order.plant.abbreviation, customer.abbreviation, order.stickDate),\n        update = {\n          ...order,\n          orderNumber,\n          customer\n        };\n      dispatch(setOrder(update));\n    }\n  };\n  const handlePlantChange = e => {\n    if (order) {\n      const id = e.target.value,\n        plant = plants.find(p => p._id === id);\n      if (plant) {\n        onPlantChange(plant, order);\n      }\n    }\n  };\n  const handleAddVarietyClick = () => {\n    if (order !== null && order !== void 0 && order.varieties) {\n      const varieties = order.varieties.map(v => ({\n          ...v\n        })).concat([{\n          name: '',\n          cuttings: 0,\n          pots: 0,\n          cases: 0,\n          comment: null\n        }]),\n        update = {\n          ...order,\n          varieties\n        };\n      dispatch(setOrder(update));\n    }\n  };\n  const handleCuttingsChange = e => {\n    if (order) {\n      const cuttings = e.target.valueAsNumber || 0;\n      onCuttingsChange(cuttings, order);\n    }\n  };\n  const handlePotsChange = e => {\n    if (order) {\n      const pots = e.target.valueAsNumber || 0;\n      onPotsChange(pots, order);\n    }\n  };\n  const handleCasesChange = e => {\n    if (order) {\n      const cases = e.target.valueAsNumber || 0;\n      onCasesChange(cases, order);\n    }\n  };\n  const onCuttingsChange = (cuttings, order) => {\n    const {\n        plant\n      } = order,\n      cuttingsPerPot = plant.cuttingsPerPot || 1,\n      potsPerCase = plant.potsPerCase || 1,\n      pots = Math.round(cuttings / cuttingsPerPot),\n      cases = Math.round(pots / potsPerCase),\n      cuttingsPerTableTight = plant.cuttingsPerTableTight || 1,\n      tableCountTight = Math.round(cuttings / cuttingsPerTableTight),\n      update = {\n        ...order,\n        cuttings,\n        pots,\n        cases,\n        tableCountTight\n      };\n    if (order.hasPartialSpace) {\n      const cuttingsPerTablePartiallySpaced = plant.cuttingsPerTablePartiallySpaced || 1,\n        tableCountPartiallySpaced = Math.round(cuttings / cuttingsPerTablePartiallySpaced);\n      update.tableCountPartiallySpaced = tableCountPartiallySpaced;\n    }\n    if (order.hasSpacing) {\n      const cuttingsPerTableSpaced = plant.cuttingsPerTableSpaced || 1,\n        tableCountSpaced = Math.round(cuttings / cuttingsPerTableSpaced);\n      update.tableCountSpaced = tableCountSpaced;\n    }\n    dispatch(setOrder(update));\n  };\n  const onPotsChange = (pots, order) => {\n    const {\n        plant\n      } = order,\n      potsPerCase = plant.potsPerCase || 1,\n      cases = Math.round(pots / potsPerCase),\n      update = {\n        ...order,\n        pots,\n        cases\n      };\n    dispatch(setOrder(update));\n  };\n  const onCasesChange = (cases, order) => {\n    const update = {\n      ...order,\n      cases\n    };\n    dispatch(setOrder(update));\n  };\n  const handleSupplierPoNumberChange = e => {\n    if (order) {\n      const supplierPoNumber = e.target.value,\n        update = {\n          ...order,\n          supplierPoNumber\n        };\n      dispatch(setOrder(update));\n    }\n  };\n  const handleStickDateChange = e => {\n    if (order) {\n      const stickDateValue = e.target.value,\n        m = moment(stickDateValue);\n      if (m.isValid()) {\n        const stickDate = m.format('YYYY-MM-DD'),\n          orderNumber = createOrderNumber(order.plant.abbreviation, order.customer.abbreviation, stickDate),\n          update = {\n            ...order,\n            stickDate,\n            orderNumber\n          };\n        if (order.plant.daysToPinch) {\n          const pinchDate = m.add(order.plant.daysToPinch, 'days').format('YYYY-MM-DD');\n          update.pinchDate = pinchDate;\n          setPinchWeek(toWeekAndDay(pinchDate));\n        }\n        dispatch(setOrder(update));\n        setStickWeek(toWeekAndDay(stickDate));\n      }\n    }\n  };\n  const handleStickWeekChange = e => {\n    if (order) {\n      const target = e.target.value,\n        weekAndDay = parseWeekAndDay(target, true);\n      setStickWeek(target);\n      if (weekAndDay) {\n        const date = moment(weekAndDay);\n        if (date.isValid()) {\n          if (date.isBefore()) {\n            date.add(1, 'year');\n          }\n          const stickDate = date.format('YYYY-MM-DD'),\n            orderNumber = createOrderNumber(order.plant.abbreviation, order.customer.abbreviation, stickDate),\n            update = {\n              ...order,\n              stickDate,\n              orderNumber\n            };\n          dispatch(setOrder(update));\n        }\n      }\n    }\n  };\n  const handleStickZoneChange = e => {\n    if (order) {\n      const name = e.target.value,\n        stickZone = zones.find(p => p.name === name);\n      if (stickZone) {\n        const update = {\n          ...order,\n          stickZone\n        };\n        dispatch(setOrder(update));\n      }\n    }\n  };\n  const handleHasPartialSpaceChange = e => {\n    if (order) {\n      const hasPartialSpace = e.target.checked,\n        update = {\n          ...order,\n          hasPartialSpace\n        };\n      if (hasPartialSpace) {\n        const partialSpaceDate = moment(order.stickDate).add(1, 'week').format('YYYY-MM-DD');\n        update.partialSpaceDate = partialSpaceDate;\n        update.partialSpaceZone = order.stickZone;\n        update.tableCountPartiallySpaced = order.tableCountTight;\n        setPartialSpaceWeek(toWeekAndDay(partialSpaceDate));\n      } else {\n        delete update.partialSpaceDate;\n        delete update.partialSpaceZone;\n        delete update.tableCountPartiallySpaced;\n        setPartialSpaceWeek('');\n      }\n      dispatch(setOrder(update));\n    }\n  };\n  const handlePartialSpaceWeekChange = e => {\n    if (order) {\n      const weekAndDay = e.target.value,\n        date = parseWeekAndDay(weekAndDay, true);\n      setPartialSpaceWeek(weekAndDay);\n      if (date) {\n        const partialSpaceDate = moment(date).format('YYYY-MM-DD'),\n          update = {\n            ...order,\n            partialSpaceDate\n          };\n        dispatch(setOrder(update));\n      }\n    }\n  };\n  const handlePartialSpaceDateChange = e => {\n    if (order) {\n      const partialSpaceDateValue = e.target.value,\n        m = moment(partialSpaceDateValue);\n      if (m.isValid()) {\n        const partialSpaceDate = m.format('YYYY-MM-DD'),\n          update = {\n            ...order,\n            partialSpaceDate\n          };\n        setPartialSpaceWeek(toWeekAndDay(partialSpaceDate));\n        dispatch(setOrder(update));\n      }\n    }\n  };\n  const handlePartialSpaceZoneChange = e => {\n    if (order) {\n      const name = e.target.value,\n        partialSpaceZone = zones.find(p => p.name === name);\n      if (partialSpaceZone) {\n        const update = {\n          ...order,\n          partialSpaceZone\n        };\n        dispatch(setOrder(update));\n      }\n    }\n  };\n  const handleTableCountPartiallySpacedChange = e => {\n    if (order) {\n      const tableCountPartiallySpaced = e.target.valueAsNumber || 0,\n        update = {\n          ...order,\n          tableCountPartiallySpaced\n        };\n      dispatch(setOrder(update));\n    }\n  };\n  const handleHasSpacingChange = e => {\n    if (order) {\n      const hasSpacing = e.target.checked,\n        update = {\n          ...order,\n          hasSpacing\n        };\n      if (hasSpacing) {\n        update.fullSpaceDate = update.stickDate;\n        update.fullSpaceZone = update.stickZone;\n        update.tableCountSpaced = update.tableCountTight;\n        setFullSpaceWeek(update.stickDate);\n      } else {\n        delete update.fullSpaceDate;\n        delete update.fullSpaceZone;\n        delete update.tableCountSpaced;\n        setFullSpaceWeek('');\n      }\n      dispatch(setOrder(update));\n    }\n  };\n  const handleFullSpaceDateChange = e => {\n    if (order) {\n      const fullSpaceDateValue = e.target.value,\n        m = moment(fullSpaceDateValue);\n      if (m.isValid()) {\n        const fullSpaceDate = m.format('YYYY-MM-DD'),\n          update = {\n            ...order,\n            fullSpaceDate\n          };\n        if (order.hasLightsOut) {\n          update.lightsOutDate = fullSpaceDate;\n        }\n        dispatch(setOrder(update));\n        dispatch(toWeekAndDay(fullSpaceDate));\n      }\n    }\n  };\n  const handleFullSpaceWeekChange = e => {\n    if (order) {\n      const weekAndDay = e.target.value,\n        date = parseWeekAndDay(weekAndDay, true);\n      setFullSpaceWeek(weekAndDay);\n      if (date) {\n        const fullSpaceDate = moment(date).format('YYYY-MM-DD'),\n          update = {\n            ...order,\n            fullSpaceDate\n          };\n        dispatch(setOrder(update));\n      }\n    }\n  };\n  const handleFullSpaceZoneChange = e => {\n    if (order) {\n      const name = e.target.value,\n        fullSpaceZone = zones.find(p => p.name === name);\n      if (fullSpaceZone) {\n        const update = {\n          ...order,\n          fullSpaceZone\n        };\n        if (order.hasLightsOut) {\n          update.lightsOutZone = fullSpaceZone;\n        }\n        dispatch(setOrder(update));\n      }\n    }\n  };\n  const handleHasLightsOutChange = e => {\n    if (order) {\n      const hasLightsOut = e.target.checked,\n        update = {\n          ...order,\n          hasLightsOut\n        };\n      if (hasLightsOut) {\n        update.lightsOutDate = order.fullSpaceDate || order.stickDate;\n        update.lightsOutZone = order.fullSpaceZone || order.stickZone;\n        setLightsOutWeek(update.lightsOutDate);\n      } else {\n        delete update.lightsOutDate;\n        setLightsOutWeek('');\n      }\n      dispatch(setOrder(update));\n    }\n  };\n  const handleLightsOutDateChange = e => {\n    if (order) {\n      const lightsOutDateValue = e.target.value,\n        m = moment(lightsOutDateValue);\n      if (m.isValid()) {\n        const lightsOutDate = m.format('YYYY-MM-DD'),\n          update = {\n            ...order,\n            lightsOutDate\n          };\n        dispatch(setOrder(update));\n        setLightsOutWeek(toWeekAndDay(lightsOutDate));\n      }\n    }\n  };\n  const handleLightsOutWeekChange = e => {\n    if (order) {\n      const weekAndDay = e.target.value,\n        date = parseWeekAndDay(weekAndDay, true);\n      setLightsOutWeek(weekAndDay);\n      if (date) {\n        const lightsOutDate = moment(date).format('YYYY-MM-DD'),\n          update = {\n            ...order,\n            lightsOutDate\n          };\n        dispatch(setOrder(update));\n      }\n    }\n  };\n  const handleLightsOutZoneChange = e => {\n    if (order) {\n      const name = e.target.value,\n        lightsOutZone = zones.find(p => p.name === name);\n      if (lightsOutZone) {\n        const update = {\n          ...order,\n          lightsOutZone\n        };\n        dispatch(setOrder(update));\n      }\n    }\n  };\n  const handleHasPinchingChange = e => {\n    if (order) {\n      const hasPinching = e.target.checked,\n        update = {\n          ...order,\n          hasPinching\n        };\n      if (hasPinching) {\n        var _order$plant;\n        const daysToPinch = ((_order$plant = order.plant) === null || _order$plant === void 0 ? void 0 : _order$plant.daysToPinch) || 0,\n          pinchDate = moment(order.pinchDate || moment(order.stickDate).add(daysToPinch, 'days').format('YYYY-MM-DD')).format('YYYY-MM-DD');\n        update.pinchDate = pinchDate;\n        setPinchWeek(toWeekAndDay(pinchDate));\n      } else {\n        delete update.pinchDate;\n        setPinchWeek('');\n      }\n      dispatch(setOrder(update));\n    }\n  };\n  const handlePinchWeekChange = e => {\n    if (order) {\n      const weekAndDay = e.target.value,\n        date = parseWeekAndDay(weekAndDay, true);\n      setPinchWeek(weekAndDay);\n      if (date) {\n        const pinchDate = moment(date).format('YYYY-MM-DD'),\n          update = {\n            ...order,\n            pinchDate\n          };\n        dispatch(setOrder(update));\n      }\n    }\n  };\n  const handlePinchDateChange = e => {\n    if (order) {\n      const pinchDateValue = e.target.value,\n        m = moment(pinchDateValue);\n      if (m.isValid()) {\n        const pinchDate = m.format('YYYY-MM-DD'),\n          update = {\n            ...order,\n            pinchDate\n          };\n        setPinchWeek(toWeekAndDay(pinchDate));\n        dispatch(setOrder(update));\n      }\n    }\n  };\n  const handleFlowerDateChange = e => {\n    if (order) {\n      const flowerDateValue = e.target.value,\n        m = moment(flowerDateValue);\n      if (m.isValid()) {\n        const flowerDate = m.format('YYYY-MM-DD'),\n          update = {\n            ...order,\n            flowerDate\n          };\n        dispatch(setOrder(update));\n        setFlowerWeek(toWeekAndDay(flowerDate));\n      }\n    }\n  };\n  const handleFlowerWeekChange = e => {\n    if (order) {\n      const weekAndDay = e.target.value,\n        date = parseWeekAndDay(weekAndDay, true);\n      setFlowerWeek(weekAndDay);\n      if (date) {\n        const flowerDate = moment(date).format('YYYY-MM-DD'),\n          update = {\n            ...order,\n            flowerDate\n          };\n        dispatch(setOrder(update));\n      }\n    }\n  };\n  const handleTableCountTightChange = e => {\n    if (order) {\n      const tableCountTight = e.target.valueAsNumber || 0,\n        update = {\n          ...order,\n          tableCountTight\n        };\n      dispatch(setOrder(update));\n    }\n  };\n  const handleTableCountSpacedChange = e => {\n    if (order) {\n      const tableCountSpaced = e.target.valueAsNumber || 0,\n        update = {\n          ...order,\n          tableCountSpaced\n        };\n      dispatch(setOrder(update));\n    }\n  };\n  const handleNotesChange = e => {\n    if (order) {\n      const notes = e.target.value,\n        update = {\n          ...order,\n          notes\n        };\n      dispatch(setOrder(update));\n    }\n  };\n  const handleVarietyChange = (name, variety) => {\n    if (order !== null && order !== void 0 && order.varieties) {\n      const index = order.varieties.findIndex(v => v.name === name);\n      if (index !== -1) {\n        const varieties = order.varieties.map(v => ({\n          ...v\n        }));\n        if (variety) {\n          varieties.splice(index, 1, variety);\n        } else {\n          varieties.splice(index, 1);\n        }\n        const cuttings = varieties.reduce((total, v) => total + v.cuttings, 0),\n          update = {\n            ...order,\n            varieties,\n            cuttings\n          };\n        onCuttingsChange(cuttings, update);\n      }\n    }\n  };\n  const handleSeasonChange = e => {\n    if (order) {\n      const season = e.target.value,\n        update = {\n          ...order,\n          season\n        };\n      dispatch(setOrder(update));\n    }\n  };\n  const handleGoBackClick = () => goBack();\n  const handleSaveClick = async () => {\n    if (user) {\n      var _order$salesWeeks2;\n      if (order !== null && order !== void 0 && (_order$salesWeeks2 = order.salesWeeks) !== null && _order$salesWeeks2 !== void 0 && _order$salesWeeks2.length && salesWeekTotal !== order.cases) {\n        alert('The total number of cases in the sales weeks must match the total number of cases in the order.');\n        return;\n      }\n      const result = await dispatch(saveOrder());\n      if (!result.error) {\n        if (!isNew) {\n          setShowNotificationModal(true);\n        } else {\n          goBack();\n        }\n      }\n    }\n  };\n  const handleDeleteClick = async () => {\n    const result = await dispatch(deleteOrder());\n    if (!result.error) {\n      goBack();\n    }\n  };\n  const goBack = () => {\n    if (window.history.length > 1) {\n      navigate(-1);\n    } else {\n      navigate(routes.orders.path);\n    }\n  };\n  const handleNotificationMessageChanged = e => {\n    setNotificationMessage(e.target.value);\n  };\n  const handleNotificationModalOpened = () => {\n    setNotificationMessage('');\n  };\n  const handleSendNotificationCancel = () => {\n    setShowNotificationModal(false);\n    goBack();\n  };\n  const handleSendNotificationConfirm = async () => {\n    if (order && user) {\n      try {\n        const link = window.location.href,\n          model = {\n            user: user.name,\n            plant: order.plant.name,\n            orderNumber: order.orderNumber,\n            link,\n            message: notificationMessage\n          },\n          args = {\n            user,\n            model\n          };\n        await notificationsApi.orderChanged(args);\n        goBack();\n      } catch (e) {\n        console.error(e);\n      }\n    }\n  };\n  const handleHasSalesWeeksChange = e => {\n    if (order) {\n      const hasSalesWeeks = e.target.checked;\n      if (hasSalesWeeks) {\n        if (!Array.isArray(order.salesWeeks)) {\n          const id = guid(),\n            week = toWeekAndYear(order.flowerDate),\n            salesWeek = {\n              id,\n              week,\n              cases: order.cases\n            },\n            update = {\n              ...order,\n              salesWeeks: [salesWeek]\n            };\n          dispatch(setOrder(update));\n        }\n      } else {\n        const update = {\n          ...order\n        };\n        delete update.salesWeeks;\n        dispatch(setOrder(update));\n      }\n    }\n  };\n  const handleAddSalesWeekClick = () => {\n    if (order !== null && order !== void 0 && order.salesWeeks) {\n      const id = guid(),\n        salesWeeks = order.salesWeeks.map(w => ({\n          ...w\n        })).concat([{\n          id,\n          week: '',\n          cases: 0\n        }]),\n        update = {\n          ...order,\n          salesWeeks\n        };\n      dispatch(setOrder(update));\n    }\n  };\n  const handleSalesWeekChange = (id, salesWeek) => {\n    if (order !== null && order !== void 0 && order.salesWeeks) {\n      const salesWeeks = order.salesWeeks.map(sw => ({\n          ...sw\n        })),\n        index = salesWeeks.findIndex(sw => sw.id === id);\n      if (index !== -1) {\n        if (salesWeek) {\n          salesWeeks.splice(index, 1, salesWeek);\n        } else {\n          salesWeeks.splice(index, 1);\n        }\n      }\n      const update = {\n        ...order,\n        salesWeeks\n      };\n      dispatch(setOrder(update));\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container d-grid gap-2\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row mt-2 sticky-top-navbar bg-white border-bottom pt-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-auto\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          color: \"link\",\n          onClick: handleGoBackClick,\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: ['fat', 'chevron-left']\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 872,\n            columnNumber: 13\n          }, this), \"\\xA0 Back to Orders List\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 871,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 870,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"col\",\n        children: isNew ? 'New Order' : order.orderNumber\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 876,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 869,\n      columnNumber: 7\n    }, this), !!order && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row p-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"order-customer\",\n            children: \"Customer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 882,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"order-customer\",\n            type: \"select\",\n            value: order.customer.abbreviation,\n            onChange: handleCustomerChange,\n            disabled: !canUpdate,\n            children: customers.map(customer => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: customer.abbreviation,\n              children: customer.name\n            }, customer.abbreviation, false, {\n              fileName: _jsxFileName,\n              lineNumber: 890,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 883,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 881,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"order-plant\",\n            children: \"Plant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 899,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"order-plant\",\n            type: \"select\",\n            value: order.plant._id,\n            onChange: handlePlantChange,\n            disabled: !canUpdate,\n            children: plants.map(plant => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: plant._id,\n              children: plant.name\n            }, plant._id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 907,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 900,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 898,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 880,\n        columnNumber: 11\n      }, this), !order.varieties && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row p-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"order-cuttings\",\n            children: \"Cuttings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 917,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"order-cuttings\",\n            type: \"number\",\n            value: order.cuttings,\n            onChange: handleCuttingsChange,\n            onFocus: handleFocus,\n            className: \"text-end\",\n            disabled: !canUpdate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 918,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 916,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"order-pots\",\n            children: \"Pots\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 929,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"order-pots\",\n            type: \"number\",\n            value: order.pots,\n            onChange: handlePotsChange,\n            onFocus: handleFocus,\n            className: \"text-end\",\n            disabled: !canUpdate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 930,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 928,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"order-cases\",\n            children: \"Cases\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 941,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"order-cases\",\n            type: \"number\",\n            value: order.cases,\n            onChange: handleCasesChange,\n            onFocus: handleFocus,\n            className: \"text-end\",\n            disabled: !canUpdate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 942,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 940,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 915,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row p-2 bg-light\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"order-stick-week\",\n            children: \"Stick Date\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 956,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"order-stick-week\",\n            value: stickWeek,\n            onChange: handleStickWeekChange,\n            onFocus: handleFocus,\n            disabled: !canUpdate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 957,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"order-stick-date\",\n            type: \"date\",\n            value: order.stickDate,\n            onChange: handleStickDateChange,\n            tabIndex: -1,\n            disabled: !canUpdate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 964,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 955,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"order-stick-zone\",\n            children: \"Stick Zone\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 974,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"order-stick-zone\",\n            type: \"select\",\n            value: order.stickZone.name,\n            onChange: handleStickZoneChange,\n            disabled: !canUpdate,\n            children: zones.map(zone => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: zone.name,\n              children: zone.name\n            }, zone.name, false, {\n              fileName: _jsxFileName,\n              lineNumber: 982,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 975,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 973,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"order-flower-week\",\n            children: \"Flower Date\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 989,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"order-flower-week\",\n            value: flowerWeek,\n            onChange: handleFlowerWeekChange,\n            onFocus: handleFocus,\n            disabled: !canUpdate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 990,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"order-flower-date\",\n            type: \"date\",\n            value: order.flowerDate,\n            onChange: handleFlowerDateChange,\n            tabIndex: -1,\n            disabled: !canUpdate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 997,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 988,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"order-table-count-tight\",\n            children: \"Tables (Tight)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1007,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"order-table-count-tight\",\n            type: \"number\",\n            value: order.tableCountTight,\n            onChange: handleTableCountTightChange,\n            onFocus: handleFocus,\n            disabled: !canUpdate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1008,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1006,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 954,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row p-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md\",\n          children: /*#__PURE__*/_jsxDEV(FormGroup, {\n            check: true,\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              id: \"order-has-spacing-space\",\n              type: \"checkbox\",\n              checked: order.hasSpacing,\n              onChange: handleHasSpacingChange,\n              disabled: !canUpdate\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1021,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"order-has-spacing-space\",\n              children: \"Product is Spaced?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1028,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1020,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1019,\n          columnNumber: 13\n        }, this), order.hasSpacing && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-md\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"order-space-week\",\n              children: \"Space Date\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1036,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              id: \"order-space-week\",\n              value: fullSpaceWeek,\n              onChange: handleFullSpaceWeekChange,\n              onFocus: handleFocus,\n              disabled: !canUpdate\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1037,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              id: \"order-space-date\",\n              type: \"date\",\n              value: order.fullSpaceDate,\n              onChange: handleFullSpaceDateChange,\n              tabIndex: -1,\n              disabled: !canUpdate\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1044,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1035,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-md\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"order-full-space-zone\",\n              children: \"Full Space Zone\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1054,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              id: \"order-full-space-zone\",\n              type: \"select\",\n              value: ((_order$fullSpaceZone = order.fullSpaceZone) === null || _order$fullSpaceZone === void 0 ? void 0 : _order$fullSpaceZone.name) || '',\n              onChange: handleFullSpaceZoneChange,\n              disabled: !canUpdate,\n              children: zones.map(zone => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: zone.name,\n                children: zone.name\n              }, zone.name, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1062,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1055,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1053,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-md\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"order-table-count-spaced\",\n              children: \"Tables (Spaced)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1069,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              id: \"order-table-count-spaced\",\n              type: \"number\",\n              value: order.tableCountSpaced,\n              onChange: handleTableCountSpacedChange,\n              onFocus: handleFocus,\n              disabled: !canUpdate\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1072,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1068,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1018,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row p-2 bg-light\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md\",\n          children: /*#__PURE__*/_jsxDEV(FormGroup, {\n            check: true,\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              id: \"order-has-lights-out\",\n              type: \"checkbox\",\n              checked: order.hasLightsOut,\n              onChange: handleHasLightsOutChange,\n              disabled: !canUpdate\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1087,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"order-has-lights-out\",\n              children: \"Product has Lights-Out?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1094,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1086,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1085,\n          columnNumber: 13\n        }, this), order.hasLightsOut && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-md\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"order-lights-out-week\",\n              children: \"Lights-Out Date\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1102,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              id: \"order-lights-out-week\",\n              value: lightsOutWeek,\n              onChange: handleLightsOutWeekChange,\n              onFocus: handleFocus,\n              disabled: !canUpdate\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1103,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              id: \"order-lights-out-date\",\n              type: \"date\",\n              value: order.lightsOutDate,\n              onChange: handleLightsOutDateChange,\n              tabIndex: -1,\n              disabled: !canUpdate\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1110,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1101,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-md\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"order-lights-out-zone\",\n              children: \"Lights-Out Zone\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1120,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              id: \"order-lights-out-zone\",\n              type: \"select\",\n              value: ((_order$lightsOutZone = order.lightsOutZone) === null || _order$lightsOutZone === void 0 ? void 0 : _order$lightsOutZone.name) || '',\n              onChange: handleLightsOutZoneChange,\n              disabled: !canUpdate,\n              children: zones.map(zone => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: zone.name,\n                children: zone.name\n              }, zone.name, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1128,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1121,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1119,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md\",\n            children: \"\\xA0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1134,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1084,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row p-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md\",\n          children: /*#__PURE__*/_jsxDEV(FormGroup, {\n            check: true,\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              id: \"order-has-partial-space\",\n              type: \"checkbox\",\n              checked: order.hasPartialSpace,\n              onChange: handleHasPartialSpaceChange,\n              disabled: !canUpdate\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1141,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"order-has-partial-space\",\n              children: \"Product is Partial Spaced?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1148,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1140,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1139,\n          columnNumber: 13\n        }, this), order.hasPartialSpace && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-md\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"order-partial-space-week\",\n              children: \"Partial Space Date\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1156,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              id: \"order-partial-space-week\",\n              value: partialSpaceWeek,\n              onChange: handlePartialSpaceWeekChange,\n              onFocus: handleFocus,\n              disabled: !canUpdate\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1159,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              id: \"order-partial-space-date\",\n              type: \"date\",\n              value: order.partialSpaceDate,\n              onChange: handlePartialSpaceDateChange,\n              tabIndex: -1,\n              disabled: !canUpdate\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1166,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1155,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-md\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"order-partial-space-zone\",\n              children: \"Partial Space Zone\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1176,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              id: \"order-partial-space-zone\",\n              type: \"select\",\n              value: ((_order$partialSpaceZo = order.partialSpaceZone) === null || _order$partialSpaceZo === void 0 ? void 0 : _order$partialSpaceZo.name) || '',\n              onChange: handlePartialSpaceZoneChange,\n              disabled: !canUpdate,\n              children: zones.map(zone => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: zone.name,\n                children: zone.name\n              }, zone.name, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1186,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1179,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1175,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-md\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"order-table-count-partially-spaced\",\n              children: \"Tables (Partially Spaced)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1193,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              id: \"order-table-count-partially-spaced\",\n              type: \"number\",\n              value: order.tableCountPartiallySpaced,\n              onChange: handleTableCountPartiallySpacedChange,\n              onFocus: handleFocus,\n              disabled: !canUpdate\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1196,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1192,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1138,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row p-2 bg-light\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md\",\n          children: /*#__PURE__*/_jsxDEV(FormGroup, {\n            check: true,\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              id: \"order-has-pinching\",\n              type: \"checkbox\",\n              checked: order.hasPinching,\n              onChange: handleHasPinchingChange,\n              disabled: !canUpdate\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1211,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"order-has-pinching\",\n              children: \"Product Needs Pinching?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1218,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1210,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1209,\n          columnNumber: 13\n        }, this), order.hasPinching && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-md\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"order-pinch-week\",\n              children: \"Pinch Date\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1226,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              id: \"order-pinch-week\",\n              value: pinchWeek,\n              onChange: handlePinchWeekChange,\n              onFocus: handleFocus,\n              disabled: !canUpdate\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1227,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              id: \"order-pinch-date\",\n              type: \"date\",\n              value: order.pinchDate,\n              onChange: handlePinchDateChange,\n              tabIndex: -1,\n              disabled: !canUpdate\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1234,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1225,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-0 col-md\",\n            children: \"\\xA0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1243,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-0 col-md\",\n            children: \"\\xA0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1244,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1208,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row p-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"order-supplier-po-number\",\n            children: \"Supplier PO #\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1250,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"order-supplier-po-number\",\n            value: order.supplierPoNumber,\n            onChange: handleSupplierPoNumberChange,\n            disabled: !canUpdate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1251,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1249,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"order-notes\",\n            children: \"Notes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1259,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"order=notes\",\n            type: \"textarea\",\n            value: order.notes || '',\n            onChange: handleNotesChange,\n            disabled: !canUpdate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1260,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1258,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"order-season\",\n            children: \"Season\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1269,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"order-season\",\n            value: order.season || '',\n            onChange: handleSeasonChange,\n            disabled: !canUpdate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1270,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1268,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1248,\n        columnNumber: 11\n      }, this), !!order.varieties && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"col\",\n            children: \"Varieties\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1281,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1280,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row fw-bold\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-md-2 offset-md-1 text-center\",\n            children: \"Variety\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1284,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-md-2 text-center\",\n            children: \"Cuttings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1287,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-md-2 text-center\",\n            children: \"Pots\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1288,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-md-2 text-center\",\n            children: \"Cases\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1289,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-md-3 text-center\",\n            children: \"Comment\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1290,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1283,\n          columnNumber: 15\n        }, this), order.varieties.map(variety => /*#__PURE__*/_jsxDEV(Variety, {\n          variety: variety,\n          onChange: v => handleVarietyChange(variety.name, v)\n        }, variety.name, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1293,\n          columnNumber: 17\n        }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row border-top-double\",\n          children: [canUpdate && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-1 text-center\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              color: \"success\",\n              size: \"sm\",\n              outline: true,\n              onClick: handleAddVarietyClick,\n              className: \"mt-1\",\n              children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: ['fat', 'plus']\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1308,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1302,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1301,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-md-2 offset-md-2\",\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              id: \"order-cuttings\",\n              value: formatNumber(order.cuttings),\n              readOnly: true,\n              plaintext: true,\n              className: \"bg-white text-end\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1313,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1312,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-md-2\",\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              id: \"order-pots\",\n              value: formatNumber(order.pots),\n              readOnly: true,\n              plaintext: true,\n              className: \"bg-white text-end\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1322,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1321,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-md-2\",\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              id: \"order-cases\",\n              value: formatNumber(order.cases),\n              readOnly: true,\n              plaintext: true,\n              className: \"bg-white text-end\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1331,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1330,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1299,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row mt-5\",\n        children: /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"col\",\n          children: \"Sales\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1343,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1342,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row\",\n        children: /*#__PURE__*/_jsxDEV(FormGroup, {\n          check: true,\n          className: \"col\",\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            id: \"order-has-sales-weeks\",\n            type: \"checkbox\",\n            checked: hasSalesWeeks,\n            onChange: handleHasSalesWeeksChange,\n            disabled: !canUpdate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1347,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"order-has-sales-weeks\",\n            children: \"Sells over multiple weeks\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1354,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1346,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1345,\n        columnNumber: 11\n      }, this), hasSalesWeeks && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row fw-bold\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-md-2 offset-md-1 text-center\",\n            children: \"Week\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1362,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-md-2 text-center\",\n            children: \"Cases\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1365,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1361,\n          columnNumber: 15\n        }, this), (_order$salesWeeks3 = order.salesWeeks) === null || _order$salesWeeks3 === void 0 ? void 0 : _order$salesWeeks3.map(salesWeek => /*#__PURE__*/_jsxDEV(SalesWeekRow, {\n          salesWeek: salesWeek,\n          onChange: handleSalesWeekChange\n        }, salesWeek.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1368,\n          columnNumber: 17\n        }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row border-top-double\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-1 text-center\",\n            children: canUpdate && /*#__PURE__*/_jsxDEV(Button, {\n              color: \"success\",\n              size: \"sm\",\n              outline: true,\n              onClick: handleAddSalesWeekClick,\n              className: \"mt-1\",\n              children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: ['fat', 'plus']\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1383,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1377,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1375,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-md-2 offset-md-2\",\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              id: \"sales-week-totals\",\n              value: formatNumber(salesWeekTotal),\n              readOnly: true,\n              plaintext: true,\n              className: \"bg-white text-end\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1388,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1387,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1374,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row mt-5 bg-white sticky-bottom border-top py-2\",\n        children: [!isNew && canDelete && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-auto\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleDeleteClick,\n            outline: true,\n            color: \"danger\",\n            size: \"lg\",\n            className: \"me-auto\",\n            children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: ['fat', 'trash']\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1409,\n              columnNumber: 19\n            }, this), \"\\xA0 Delete\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1403,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1402,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col text-end\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            outline: true,\n            size: \"lg\",\n            onClick: handleGoBackClick,\n            children: canUpdate ? 'Cancel' : 'Close'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1415,\n            columnNumber: 15\n          }, this), canUpdate && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [\"\\xA0\", /*#__PURE__*/_jsxDEV(Button, {\n              onClick: handleSaveClick,\n              color: \"success\",\n              size: \"lg\",\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: ['fat', 'save']\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1422,\n                columnNumber: 21\n              }, this), \"\\xA0 Save\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1421,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1414,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1400,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), /*#__PURE__*/_jsxDEV(Modal, {\n      isOpen: showNotificationModal,\n      toggle: handleSendNotificationCancel,\n      onOpened: handleNotificationModalOpened,\n      autoFocus: false,\n      children: [/*#__PURE__*/_jsxDEV(ModalHeader, {\n        toggle: handleSendNotificationCancel,\n        children: \"Send Notification\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1436,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ModalBody, {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-center\",\n          children: \"Would you like to send a notification about this change?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1440,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"notification-messge\",\n          children: \"Message:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1444,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"notification-messge\",\n          bsSize: \"lg\",\n          value: notificationMessage,\n          onChange: handleNotificationMessageChanged,\n          autoFocus: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1445,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1439,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ModalFooter, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          outline: true,\n          onClick: handleSendNotificationCancel,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1454,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          color: \"primary\",\n          outline: true,\n          onClick: handleSendNotificationConfirm,\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: ['fat', 'paper-plane']\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1461,\n            columnNumber: 13\n          }, this), \"\\xA0 Send\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1457,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1453,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1431,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 868,\n    columnNumber: 5\n  }, this);\n}\n_s(Detail, \"/dongj+PTWOmhia5LTo6KNpiKos=\", false, function () {\n  return [useDispatch, useNavigate, useAuth, useParams, useSelector, useSelector, useSelector, useSelector, useSelector];\n});\n_c = Detail;\nexport default Detail;\nvar _c;\n$RefreshReg$(_c, \"Detail\");", "map": {"version": 3, "names": ["React", "useCallback", "useEffect", "useState", "useSelector", "useDispatch", "useParams", "useNavigate", "moment", "<PERSON><PERSON>", "FormGroup", "Input", "Modal", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ModalBody", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FontAwesomeIcon", "notificationsApi", "createOrder", "createOrderNumber", "routes", "useAuth", "selectPlants", "selectCustomers", "selectZones", "handleFocus", "toWeekAndDay", "formatNumber", "parseWeekAndDay", "toWeekAndYear", "deleteOrder", "saveOrder", "selectOrder", "setOrder", "selectAllOrders", "Variety", "SalesWeekRow", "guid", "Detail", "dispatch", "navigate", "isInRole", "user", "id", "orders", "order", "customers", "zones", "plants", "isNew", "_rev", "stickWeek", "setStickWeek", "lightsOutWeek", "setLightsOutWeek", "partialSpaceWeek", "setPartialSpaceWeek", "fullSpaceWeek", "setFullSpaceWeek", "pinchWeek", "setPinchWeek", "flowerWeek", "setFlowerWeek", "showNotificationModal", "setShowNotificationModal", "notificationMessage", "setNotificationMessage", "hasSalesWeeks", "Array", "isArray", "salesWeeks", "canUpdate", "canDelete", "salesWeekTotal", "reduce", "total", "sw", "cases", "onPlantChange", "plant", "orderNumber", "abbreviation", "customer", "stickDate", "update", "hasLightsOut", "lightsOutDate", "fullSpaceDate", "lightsOutZone", "fullSpaceZone", "stickZone", "hasPinching", "pinchDate", "add", "daysToPinch", "format", "varieties", "found", "find", "o", "_id", "partialSpaceDate", "flowerDate", "newOrder", "length", "partialSpaceZone", "some", "p", "firstPlant", "cleanup", "handleCustomerChange", "e", "target", "value", "c", "handlePlantChange", "handleAddVarietyClick", "map", "v", "concat", "name", "cuttings", "pots", "comment", "handleCuttingsChange", "valueAsNumber", "onCuttingsChange", "handlePotsChange", "onPotsChange", "handleCasesChange", "onCasesChange", "cuttingsPerPot", "potsPerCase", "Math", "round", "cuttingsPerTableTight", "tableCountTight", "hasPartialSpace", "cuttingsPerTablePartiallySpaced", "tableCountPartiallySpaced", "hasSpacing", "cuttingsPerTableSpaced", "tableCountSpaced", "handleSupplierPoNumberChange", "supplierPoNumber", "handleStickDateChange", "stickDateValue", "m", "<PERSON><PERSON><PERSON><PERSON>", "handleStickWeekChange", "weekAndDay", "date", "isBefore", "handleStickZoneChange", "handleHasPartialSpaceChange", "checked", "handlePartialSpaceWeekChange", "handlePartialSpaceDateChange", "partialSpaceDateValue", "handlePartialSpaceZoneChange", "handleTableCountPartiallySpacedChange", "handleHasSpacingChange", "handleFullSpaceDateChange", "fullSpaceDateValue", "handleFullSpaceWeekChange", "handleFullSpaceZoneChange", "handleHasLightsOutChange", "handleLightsOutDateChange", "lightsOutDateValue", "handleLightsOutWeekChange", "handleLightsOutZoneChange", "handleHasPinchingChange", "handlePinchWeekChange", "handlePinchDateChange", "pinchDateValue", "handleFlowerDateChange", "flowerDateValue", "handleFlowerWeekChange", "handleTableCountTightChange", "handleTableCountSpacedChange", "handleNotesChange", "notes", "handleVarietyChange", "variety", "index", "findIndex", "splice", "handleSeasonChange", "season", "handleGoBackClick", "goBack", "handleSaveClick", "alert", "result", "error", "handleDeleteClick", "window", "history", "path", "handleNotificationMessageChanged", "handleNotificationModalOpened", "handleSendNotificationCancel", "handleSendNotificationConfirm", "link", "location", "href", "model", "message", "args", "orderChanged", "console", "handleHasSalesWeeksChange", "week", "salesWeek", "handleAddSalesWeekClick", "w", "handleSalesWeekChange", "zone"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/orders/Detail.tsx"], "sourcesContent": ["import React, { useCallback, useEffect, useState } from 'react';\r\nimport { useSelector, useDispatch } from 'react-redux';\r\nimport { useParams, useNavigate } from 'react-router';\r\nimport moment from 'moment';\r\nimport {\r\n  Button,\r\n  FormGroup,\r\n  Input,\r\n  Modal,\r\n  ModalHeader,\r\n  ModalBody,\r\n  ModalFooter,\r\n} from 'reactstrap';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { notificationsApi } from 'api/notifications-service';\r\nimport {\r\n  createOrder,\r\n  createOrderNumber,\r\n  Order,\r\n  OrderVariety,\r\n  SalesWeek,\r\n} from 'api/models/orders';\r\nimport { Plant } from 'api/models/plants';\r\nimport { routes } from 'app/routes';\r\nimport { useAuth } from 'features/auth/use-auth';\r\nimport { selectPlants } from 'features/plants/plants-slice';\r\nimport { selectCustomers } from 'features/customers/customers-slice';\r\nimport { selectZones } from 'features/zones/zones-slice';\r\nimport { handleFocus } from 'utils/focus';\r\nimport {\r\n  toWeekAndDay,\r\n  formatNumber,\r\n  parseWeekAndDay,\r\n  toWeekAndYear,\r\n} from 'utils/format';\r\nimport { deleteOrder, saveOrder, selectOrder, setOrder } from './detail-slice';\r\nimport { selectAllOrders } from './orders-slice';\r\nimport { Variety } from './Variety';\r\nimport { SalesWeekRow } from './SalesWeekRow';\r\nimport { guid } from 'utils/guid';\r\n\r\nexport function Detail() {\r\n  const dispatch = useDispatch(),\r\n    navigate = useNavigate(),\r\n    { isInRole, user } = useAuth(),\r\n    { id } = useParams<{ id: string }>(),\r\n    orders = useSelector(selectAllOrders),\r\n    order = useSelector(selectOrder),\r\n    customers = useSelector(selectCustomers),\r\n    zones = useSelector(selectZones),\r\n    plants = useSelector(selectPlants),\r\n    isNew = !order?._rev,\r\n    [stickWeek, setStickWeek] = useState(''),\r\n    [lightsOutWeek, setLightsOutWeek] = useState(''),\r\n    [partialSpaceWeek, setPartialSpaceWeek] = useState(''),\r\n    [fullSpaceWeek, setFullSpaceWeek] = useState(''),\r\n    [pinchWeek, setPinchWeek] = useState(''),\r\n    [flowerWeek, setFlowerWeek] = useState(''),\r\n    [showNotificationModal, setShowNotificationModal] = useState(false),\r\n    [notificationMessage, setNotificationMessage] = useState(''),\r\n    hasSalesWeeks = Array.isArray(order?.salesWeeks),\r\n    canUpdate =\r\n      (isNew && isInRole('create:orders')) || isInRole('update:orders'),\r\n    canDelete = isInRole('delete:orders'),\r\n    salesWeekTotal = order?.salesWeeks?.reduce(\r\n      (total, sw) => total + sw.cases,\r\n      0\r\n    );\r\n\r\n  const onPlantChange = useCallback(\r\n    (plant: Plant, order: Order) => {\r\n      const orderNumber = createOrderNumber(\r\n          plant.abbreviation,\r\n          order.customer.abbreviation,\r\n          order.stickDate\r\n        ),\r\n        update = { ...order, orderNumber, plant };\r\n\r\n      update.hasLightsOut = plant.hasLightsOut;\r\n      if (plant.hasLightsOut) {\r\n        update.lightsOutDate = order.fullSpaceDate || order.stickDate;\r\n        update.lightsOutZone = order.fullSpaceZone || order.stickZone;\r\n      }\r\n\r\n      update.hasPinching = plant.hasPinching;\r\n      if (plant.hasPinching) {\r\n        update.pinchDate =\r\n          order.pinchDate ||\r\n          moment(order.stickDate)\r\n            .add(plant.daysToPinch || 0, 'days')\r\n            .format('YYYY-MM-DD');\r\n        setPinchWeek(toWeekAndDay(update.pinchDate));\r\n      }\r\n\r\n      if (plant.varieties) {\r\n        update.varieties = [];\r\n      } else {\r\n        delete update.varieties;\r\n      }\r\n\r\n      dispatch(setOrder(update));\r\n    },\r\n    [dispatch]\r\n  );\r\n\r\n  useEffect(() => {\r\n    const found = orders.find((o) => o._id === id);\r\n    if (found && found._id !== order?._id) {\r\n      dispatch(setOrder(found));\r\n      setStickWeek(toWeekAndDay(found.stickDate));\r\n      if (found.lightsOutDate) {\r\n        setLightsOutWeek(toWeekAndDay(found.lightsOutDate));\r\n      }\r\n      if (found.partialSpaceDate) {\r\n        setPartialSpaceWeek(toWeekAndDay(found.partialSpaceDate));\r\n      }\r\n      if (found.fullSpaceDate) {\r\n        setFullSpaceWeek(toWeekAndDay(found.fullSpaceDate));\r\n      }\r\n      if (found.hasPinching) {\r\n        setPinchWeek(toWeekAndDay(found.pinchDate));\r\n      }\r\n      setFlowerWeek(toWeekAndDay(found.flowerDate));\r\n    } else if (id === 'new') {\r\n      if (!order) {\r\n        const newOrder = createOrder();\r\n        if (zones.length) {\r\n          newOrder.stickZone = zones[0];\r\n          if (newOrder.partialSpaceDate) {\r\n            newOrder.partialSpaceZone = zones[0];\r\n          }\r\n          if (newOrder.lightsOutDate) {\r\n            newOrder.lightsOutZone = zones[0];\r\n          }\r\n          if (newOrder.fullSpaceDate) {\r\n            newOrder.fullSpaceZone = zones[0];\r\n          }\r\n        }\r\n        dispatch(setOrder(newOrder));\r\n      } else if (\r\n        plants.length &&\r\n        !plants.some((p) => p._id === order.plant._id)\r\n      ) {\r\n        const firstPlant = plants[0];\r\n        onPlantChange(firstPlant, order);\r\n        setStickWeek(toWeekAndDay(order.stickDate));\r\n        if (order.lightsOutDate) {\r\n          setLightsOutWeek(toWeekAndDay(order.lightsOutDate));\r\n        }\r\n        if (order.partialSpaceDate) {\r\n          setPartialSpaceWeek(toWeekAndDay(order.partialSpaceDate));\r\n        }\r\n        if (order.fullSpaceDate) {\r\n          setFullSpaceWeek(toWeekAndDay(order.fullSpaceDate));\r\n        }\r\n        if (order.pinchDate) {\r\n          setPinchWeek(toWeekAndDay(order.pinchDate));\r\n        } else if (order.hasPinching) {\r\n          setPinchWeek(\r\n            toWeekAndDay(\r\n              moment(order.stickDate)\r\n                .add(firstPlant.daysToPinch || 0, 'days')\r\n                .format('YYYY-MM-DD')\r\n            )\r\n          );\r\n        }\r\n        setFlowerWeek(toWeekAndDay(order.flowerDate));\r\n      }\r\n    }\r\n\r\n    return function cleanup() {\r\n      //dispatch(setOrder(null));\r\n    };\r\n  }, [dispatch, id, onPlantChange, order, orders, plants, zones]);\r\n\r\n  const handleCustomerChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const abbreviation = e.target.value,\r\n      customer = customers.find((c) => c.abbreviation === abbreviation);\r\n\r\n    if (order && customer) {\r\n      const orderNumber = createOrderNumber(\r\n          order.plant.abbreviation,\r\n          customer.abbreviation,\r\n          order.stickDate\r\n        ),\r\n        update = { ...order, orderNumber, customer };\r\n      dispatch(setOrder(update));\r\n    }\r\n  };\r\n\r\n  const handlePlantChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (order) {\r\n      const id = e.target.value,\r\n        plant = plants.find((p) => p._id === id);\r\n\r\n      if (plant) {\r\n        onPlantChange(plant, order);\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleAddVarietyClick = () => {\r\n    if (order?.varieties) {\r\n      const varieties = order.varieties\r\n          .map((v) => ({ ...v }))\r\n          .concat([\r\n            { name: '', cuttings: 0, pots: 0, cases: 0, comment: null },\r\n          ]),\r\n        update = { ...order, varieties };\r\n\r\n      dispatch(setOrder(update));\r\n    }\r\n  };\r\n\r\n  const handleCuttingsChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (order) {\r\n      const cuttings = e.target.valueAsNumber || 0;\r\n      onCuttingsChange(cuttings, order);\r\n    }\r\n  };\r\n\r\n  const handlePotsChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (order) {\r\n      const pots = e.target.valueAsNumber || 0;\r\n      onPotsChange(pots, order);\r\n    }\r\n  };\r\n\r\n  const handleCasesChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (order) {\r\n      const cases = e.target.valueAsNumber || 0;\r\n      onCasesChange(cases, order);\r\n    }\r\n  };\r\n\r\n  const onCuttingsChange = (cuttings: number, order: Order) => {\r\n    const { plant } = order,\r\n      cuttingsPerPot = plant.cuttingsPerPot || 1,\r\n      potsPerCase = plant.potsPerCase || 1,\r\n      pots = Math.round(cuttings / cuttingsPerPot),\r\n      cases = Math.round(pots / potsPerCase),\r\n      cuttingsPerTableTight = plant.cuttingsPerTableTight || 1,\r\n      tableCountTight = Math.round(cuttings / cuttingsPerTableTight),\r\n      update = { ...order, cuttings, pots, cases, tableCountTight };\r\n\r\n    if (order.hasPartialSpace) {\r\n      const cuttingsPerTablePartiallySpaced =\r\n          plant.cuttingsPerTablePartiallySpaced || 1,\r\n        tableCountPartiallySpaced = Math.round(\r\n          cuttings / cuttingsPerTablePartiallySpaced\r\n        );\r\n\r\n      update.tableCountPartiallySpaced = tableCountPartiallySpaced;\r\n    }\r\n\r\n    if (order.hasSpacing) {\r\n      const cuttingsPerTableSpaced = plant.cuttingsPerTableSpaced || 1,\r\n        tableCountSpaced = Math.round(cuttings / cuttingsPerTableSpaced);\r\n\r\n      update.tableCountSpaced = tableCountSpaced;\r\n    }\r\n\r\n    dispatch(setOrder(update));\r\n  };\r\n\r\n  const onPotsChange = (pots: number, order: Order) => {\r\n    const { plant } = order,\r\n      potsPerCase = plant.potsPerCase || 1,\r\n      cases = Math.round(pots / potsPerCase),\r\n      update = { ...order, pots, cases };\r\n\r\n    dispatch(setOrder(update));\r\n  };\r\n\r\n  const onCasesChange = (cases: number, order: Order) => {\r\n    const update = { ...order, cases };\r\n\r\n    dispatch(setOrder(update));\r\n  };\r\n\r\n  const handleSupplierPoNumberChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    if (order) {\r\n      const supplierPoNumber = e.target.value,\r\n        update = { ...order, supplierPoNumber };\r\n\r\n      dispatch(setOrder(update));\r\n    }\r\n  };\r\n\r\n  const handleStickDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (order) {\r\n      const stickDateValue = e.target.value,\r\n        m = moment(stickDateValue);\r\n\r\n      if (m.isValid()) {\r\n        const stickDate = m.format('YYYY-MM-DD'),\r\n          orderNumber = createOrderNumber(\r\n            order.plant.abbreviation,\r\n            order.customer.abbreviation,\r\n            stickDate\r\n          ),\r\n          update = { ...order, stickDate, orderNumber };\r\n\r\n        if (order.plant.daysToPinch) {\r\n          const pinchDate = m\r\n            .add(order.plant.daysToPinch, 'days')\r\n            .format('YYYY-MM-DD');\r\n          update.pinchDate = pinchDate;\r\n          setPinchWeek(toWeekAndDay(pinchDate));\r\n        }\r\n\r\n        dispatch(setOrder(update));\r\n        setStickWeek(toWeekAndDay(stickDate));\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleStickWeekChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (order) {\r\n      const target = e.target.value,\r\n        weekAndDay = parseWeekAndDay(target, true);\r\n\r\n      setStickWeek(target);\r\n\r\n      if (weekAndDay) {\r\n        const date = moment(weekAndDay);\r\n\r\n        if (date.isValid()) {\r\n          if (date.isBefore()) {\r\n            date.add(1, 'year');\r\n          }\r\n          const stickDate = date.format('YYYY-MM-DD'),\r\n            orderNumber = createOrderNumber(\r\n              order.plant.abbreviation,\r\n              order.customer.abbreviation,\r\n              stickDate\r\n            ),\r\n            update = { ...order, stickDate, orderNumber };\r\n\r\n          dispatch(setOrder(update));\r\n        }\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleStickZoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (order) {\r\n      const name = e.target.value,\r\n        stickZone = zones.find((p) => p.name === name);\r\n\r\n      if (stickZone) {\r\n        const update = { ...order, stickZone };\r\n        dispatch(setOrder(update));\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleHasPartialSpaceChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    if (order) {\r\n      const hasPartialSpace = e.target.checked,\r\n        update = { ...order, hasPartialSpace };\r\n\r\n      if (hasPartialSpace) {\r\n        const partialSpaceDate = moment(order.stickDate)\r\n          .add(1, 'week')\r\n          .format('YYYY-MM-DD');\r\n        update.partialSpaceDate = partialSpaceDate;\r\n        update.partialSpaceZone = order.stickZone;\r\n        update.tableCountPartiallySpaced = order.tableCountTight;\r\n        setPartialSpaceWeek(toWeekAndDay(partialSpaceDate));\r\n      } else {\r\n        delete update.partialSpaceDate;\r\n        delete update.partialSpaceZone;\r\n        delete update.tableCountPartiallySpaced;\r\n        setPartialSpaceWeek('');\r\n      }\r\n\r\n      dispatch(setOrder(update));\r\n    }\r\n  };\r\n\r\n  const handlePartialSpaceWeekChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    if (order) {\r\n      const weekAndDay = e.target.value,\r\n        date = parseWeekAndDay(weekAndDay, true);\r\n\r\n      setPartialSpaceWeek(weekAndDay);\r\n\r\n      if (date) {\r\n        const partialSpaceDate = moment(date).format('YYYY-MM-DD'),\r\n          update = { ...order, partialSpaceDate };\r\n\r\n        dispatch(setOrder(update));\r\n      }\r\n    }\r\n  };\r\n\r\n  const handlePartialSpaceDateChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    if (order) {\r\n      const partialSpaceDateValue = e.target.value,\r\n        m = moment(partialSpaceDateValue);\r\n\r\n      if (m.isValid()) {\r\n        const partialSpaceDate = m.format('YYYY-MM-DD'),\r\n          update = { ...order, partialSpaceDate };\r\n\r\n        setPartialSpaceWeek(toWeekAndDay(partialSpaceDate));\r\n\r\n        dispatch(setOrder(update));\r\n      }\r\n    }\r\n  };\r\n\r\n  const handlePartialSpaceZoneChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    if (order) {\r\n      const name = e.target.value,\r\n        partialSpaceZone = zones.find((p) => p.name === name);\r\n\r\n      if (partialSpaceZone) {\r\n        const update = { ...order, partialSpaceZone };\r\n        dispatch(setOrder(update));\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleTableCountPartiallySpacedChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    if (order) {\r\n      const tableCountPartiallySpaced = e.target.valueAsNumber || 0,\r\n        update = { ...order, tableCountPartiallySpaced };\r\n      dispatch(setOrder(update));\r\n    }\r\n  };\r\n\r\n  const handleHasSpacingChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (order) {\r\n      const hasSpacing = e.target.checked,\r\n        update = { ...order, hasSpacing };\r\n\r\n      if (hasSpacing) {\r\n        update.fullSpaceDate = update.stickDate;\r\n        update.fullSpaceZone = update.stickZone;\r\n        update.tableCountSpaced = update.tableCountTight;\r\n        setFullSpaceWeek(update.stickDate);\r\n      } else {\r\n        delete update.fullSpaceDate;\r\n        delete update.fullSpaceZone;\r\n        delete update.tableCountSpaced;\r\n        setFullSpaceWeek('');\r\n      }\r\n\r\n      dispatch(setOrder(update));\r\n    }\r\n  };\r\n\r\n  const handleFullSpaceDateChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    if (order) {\r\n      const fullSpaceDateValue = e.target.value,\r\n        m = moment(fullSpaceDateValue);\r\n\r\n      if (m.isValid()) {\r\n        const fullSpaceDate = m.format('YYYY-MM-DD'),\r\n          update = { ...order, fullSpaceDate };\r\n\r\n        if (order.hasLightsOut) {\r\n          update.lightsOutDate = fullSpaceDate;\r\n        }\r\n\r\n        dispatch(setOrder(update));\r\n        dispatch(toWeekAndDay(fullSpaceDate));\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleFullSpaceWeekChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    if (order) {\r\n      const weekAndDay = e.target.value,\r\n        date = parseWeekAndDay(weekAndDay, true);\r\n\r\n      setFullSpaceWeek(weekAndDay);\r\n\r\n      if (date) {\r\n        const fullSpaceDate = moment(date).format('YYYY-MM-DD'),\r\n          update = { ...order, fullSpaceDate };\r\n\r\n        dispatch(setOrder(update));\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleFullSpaceZoneChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    if (order) {\r\n      const name = e.target.value,\r\n        fullSpaceZone = zones.find((p) => p.name === name);\r\n\r\n      if (fullSpaceZone) {\r\n        const update = { ...order, fullSpaceZone };\r\n\r\n        if (order.hasLightsOut) {\r\n          update.lightsOutZone = fullSpaceZone;\r\n        }\r\n\r\n        dispatch(setOrder(update));\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleHasLightsOutChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (order) {\r\n      const hasLightsOut = e.target.checked,\r\n        update = { ...order, hasLightsOut };\r\n\r\n      if (hasLightsOut) {\r\n        update.lightsOutDate = order.fullSpaceDate || order.stickDate;\r\n        update.lightsOutZone = order.fullSpaceZone || order.stickZone;\r\n        setLightsOutWeek(update.lightsOutDate);\r\n      } else {\r\n        delete update.lightsOutDate;\r\n        setLightsOutWeek('');\r\n      }\r\n\r\n      dispatch(setOrder(update));\r\n    }\r\n  };\r\n\r\n  const handleLightsOutDateChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    if (order) {\r\n      const lightsOutDateValue = e.target.value,\r\n        m = moment(lightsOutDateValue);\r\n\r\n      if (m.isValid()) {\r\n        const lightsOutDate = m.format('YYYY-MM-DD'),\r\n          update = { ...order, lightsOutDate };\r\n\r\n        dispatch(setOrder(update));\r\n        setLightsOutWeek(toWeekAndDay(lightsOutDate));\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleLightsOutWeekChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    if (order) {\r\n      const weekAndDay = e.target.value,\r\n        date = parseWeekAndDay(weekAndDay, true);\r\n\r\n      setLightsOutWeek(weekAndDay);\r\n\r\n      if (date) {\r\n        const lightsOutDate = moment(date).format('YYYY-MM-DD'),\r\n          update = { ...order, lightsOutDate };\r\n\r\n        dispatch(setOrder(update));\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleLightsOutZoneChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    if (order) {\r\n      const name = e.target.value,\r\n        lightsOutZone = zones.find((p) => p.name === name);\r\n\r\n      if (lightsOutZone) {\r\n        const update = { ...order, lightsOutZone };\r\n        dispatch(setOrder(update));\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleHasPinchingChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (order) {\r\n      const hasPinching = e.target.checked,\r\n        update = { ...order, hasPinching };\r\n\r\n      if (hasPinching) {\r\n        const daysToPinch = order.plant?.daysToPinch || 0,\r\n          pinchDate = moment(\r\n            order.pinchDate ||\r\n              moment(order.stickDate)\r\n                .add(daysToPinch, 'days')\r\n                .format('YYYY-MM-DD')\r\n          ).format('YYYY-MM-DD');\r\n        update.pinchDate = pinchDate;\r\n        setPinchWeek(toWeekAndDay(pinchDate));\r\n      } else {\r\n        delete update.pinchDate;\r\n        setPinchWeek('');\r\n      }\r\n\r\n      dispatch(setOrder(update));\r\n    }\r\n  };\r\n\r\n  const handlePinchWeekChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (order) {\r\n      const weekAndDay = e.target.value,\r\n        date = parseWeekAndDay(weekAndDay, true);\r\n\r\n      setPinchWeek(weekAndDay);\r\n\r\n      if (date) {\r\n        const pinchDate = moment(date).format('YYYY-MM-DD'),\r\n          update = { ...order, pinchDate };\r\n\r\n        dispatch(setOrder(update));\r\n      }\r\n    }\r\n  };\r\n\r\n  const handlePinchDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (order) {\r\n      const pinchDateValue = e.target.value,\r\n        m = moment(pinchDateValue);\r\n\r\n      if (m.isValid()) {\r\n        const pinchDate = m.format('YYYY-MM-DD'),\r\n          update = { ...order, pinchDate };\r\n\r\n        setPinchWeek(toWeekAndDay(pinchDate));\r\n\r\n        dispatch(setOrder(update));\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleFlowerDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (order) {\r\n      const flowerDateValue = e.target.value,\r\n        m = moment(flowerDateValue);\r\n\r\n      if (m.isValid()) {\r\n        const flowerDate = m.format('YYYY-MM-DD'),\r\n          update = { ...order, flowerDate };\r\n\r\n        dispatch(setOrder(update));\r\n        setFlowerWeek(toWeekAndDay(flowerDate));\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleFlowerWeekChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (order) {\r\n      const weekAndDay = e.target.value,\r\n        date = parseWeekAndDay(weekAndDay, true);\r\n\r\n      setFlowerWeek(weekAndDay);\r\n\r\n      if (date) {\r\n        const flowerDate = moment(date).format('YYYY-MM-DD'),\r\n          update = { ...order, flowerDate };\r\n\r\n        dispatch(setOrder(update));\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleTableCountTightChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    if (order) {\r\n      const tableCountTight = e.target.valueAsNumber || 0,\r\n        update = { ...order, tableCountTight };\r\n      dispatch(setOrder(update));\r\n    }\r\n  };\r\n\r\n  const handleTableCountSpacedChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    if (order) {\r\n      const tableCountSpaced = e.target.valueAsNumber || 0,\r\n        update = { ...order, tableCountSpaced };\r\n\r\n      dispatch(setOrder(update));\r\n    }\r\n  };\r\n\r\n  const handleNotesChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (order) {\r\n      const notes = e.target.value,\r\n        update = { ...order, notes };\r\n\r\n      dispatch(setOrder(update));\r\n    }\r\n  };\r\n\r\n  const handleVarietyChange = (name: string, variety: OrderVariety | null) => {\r\n    if (order?.varieties) {\r\n      const index = order.varieties.findIndex((v) => v.name === name);\r\n\r\n      if (index !== -1) {\r\n        const varieties = order.varieties.map((v) => ({ ...v }));\r\n        if (variety) {\r\n          varieties.splice(index, 1, variety);\r\n        } else {\r\n          varieties.splice(index, 1);\r\n        }\r\n\r\n        const cuttings = varieties.reduce((total, v) => total + v.cuttings, 0),\r\n          update = { ...order, varieties, cuttings };\r\n\r\n        onCuttingsChange(cuttings, update);\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleSeasonChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (order) {\r\n      const season = e.target.value,\r\n        update = { ...order, season };\r\n\r\n      dispatch(setOrder(update));\r\n    }\r\n  };\r\n\r\n  const handleGoBackClick = () => goBack();\r\n\r\n  const handleSaveClick = async () => {\r\n    if (user) {\r\n      if (order?.salesWeeks?.length && salesWeekTotal !== order.cases) {\r\n        alert(\r\n          'The total number of cases in the sales weeks must match the total number of cases in the order.'\r\n        );\r\n        return;\r\n      }\r\n\r\n      const result: any = await dispatch(saveOrder());\r\n\r\n      if (!result.error) {\r\n        if (!isNew) {\r\n          setShowNotificationModal(true);\r\n        } else {\r\n          goBack();\r\n        }\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleDeleteClick = async () => {\r\n    const result: any = await dispatch(deleteOrder());\r\n\r\n    if (!result.error) {\r\n      goBack();\r\n    }\r\n  };\r\n\r\n  const goBack = () => {\r\n    if (window.history.length > 1) {\r\n      navigate(-1);\r\n    } else {\r\n      navigate(routes.orders.path);\r\n    }\r\n  };\r\n\r\n  const handleNotificationMessageChanged = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    setNotificationMessage(e.target.value);\r\n  };\r\n\r\n  const handleNotificationModalOpened = () => {\r\n    setNotificationMessage('');\r\n  };\r\n\r\n  const handleSendNotificationCancel = () => {\r\n    setShowNotificationModal(false);\r\n    goBack();\r\n  };\r\n\r\n  const handleSendNotificationConfirm = async () => {\r\n    if (order && user) {\r\n      try {\r\n        const link = window.location.href,\r\n          model = {\r\n            user: user.name,\r\n            plant: order.plant.name,\r\n            orderNumber: order.orderNumber,\r\n            link,\r\n            message: notificationMessage,\r\n          },\r\n          args = { user, model };\r\n\r\n        await notificationsApi.orderChanged(args);\r\n\r\n        goBack();\r\n      } catch (e) {\r\n        console.error(e);\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleHasSalesWeeksChange = (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    if (order) {\r\n      const hasSalesWeeks = e.target.checked;\r\n\r\n      if (hasSalesWeeks) {\r\n        if (!Array.isArray(order.salesWeeks)) {\r\n          const id = guid(),\r\n            week = toWeekAndYear(order.flowerDate),\r\n            salesWeek = { id, week, cases: order.cases },\r\n            update = { ...order, salesWeeks: [salesWeek] };\r\n          dispatch(setOrder(update));\r\n        }\r\n      } else {\r\n        const update = { ...order };\r\n        delete update.salesWeeks;\r\n        dispatch(setOrder(update));\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleAddSalesWeekClick = () => {\r\n    if (order?.salesWeeks) {\r\n      const id = guid(),\r\n        salesWeeks = order.salesWeeks\r\n          .map((w) => ({ ...w }))\r\n          .concat([{ id, week: '', cases: 0 }]),\r\n        update = { ...order, salesWeeks };\r\n\r\n      dispatch(setOrder(update));\r\n    }\r\n  };\r\n\r\n  const handleSalesWeekChange = (id: string, salesWeek: SalesWeek | null) => {\r\n    if (order?.salesWeeks) {\r\n      const salesWeeks = order.salesWeeks.map((sw) => ({ ...sw })),\r\n        index = salesWeeks.findIndex((sw) => sw.id === id);\r\n\r\n      if (index !== -1) {\r\n        if (salesWeek) {\r\n          salesWeeks.splice(index, 1, salesWeek);\r\n        } else {\r\n          salesWeeks.splice(index, 1);\r\n        }\r\n      }\r\n\r\n      const update = { ...order, salesWeeks };\r\n\r\n      dispatch(setOrder(update));\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"container d-grid gap-2\">\r\n      <div className=\"row mt-2 sticky-top-navbar bg-white border-bottom pt-2\">\r\n        <div className=\"col-auto\">\r\n          <Button color=\"link\" onClick={handleGoBackClick}>\r\n            <FontAwesomeIcon icon={['fat', 'chevron-left']} />\r\n            &nbsp; Back to Orders List\r\n          </Button>\r\n        </div>\r\n        <h1 className=\"col\">{isNew ? 'New Order' : order.orderNumber}</h1>\r\n      </div>\r\n      {!!order && (\r\n        <>\r\n          <div className=\"row p-2\">\r\n            <div className=\"col-12 col-md-3\">\r\n              <label htmlFor=\"order-customer\">Customer</label>\r\n              <Input\r\n                id=\"order-customer\"\r\n                type=\"select\"\r\n                value={order.customer.abbreviation}\r\n                onChange={handleCustomerChange}\r\n                disabled={!canUpdate}>\r\n                {customers.map((customer) => (\r\n                  <option\r\n                    key={customer.abbreviation}\r\n                    value={customer.abbreviation}>\r\n                    {customer.name}\r\n                  </option>\r\n                ))}\r\n              </Input>\r\n            </div>\r\n            <div className=\"col-12 col-md-3\">\r\n              <label htmlFor=\"order-plant\">Plant</label>\r\n              <Input\r\n                id=\"order-plant\"\r\n                type=\"select\"\r\n                value={order.plant._id}\r\n                onChange={handlePlantChange}\r\n                disabled={!canUpdate}>\r\n                {plants.map((plant) => (\r\n                  <option key={plant._id} value={plant._id}>\r\n                    {plant.name}\r\n                  </option>\r\n                ))}\r\n              </Input>\r\n            </div>\r\n          </div>\r\n          {!order.varieties && (\r\n            <div className=\"row p-2\">\r\n              <div className=\"col-12 col-md-3\">\r\n                <label htmlFor=\"order-cuttings\">Cuttings</label>\r\n                <Input\r\n                  id=\"order-cuttings\"\r\n                  type=\"number\"\r\n                  value={order.cuttings}\r\n                  onChange={handleCuttingsChange}\r\n                  onFocus={handleFocus}\r\n                  className=\"text-end\"\r\n                  disabled={!canUpdate}\r\n                />\r\n              </div>\r\n              <div className=\"col-12 col-md-3\">\r\n                <label htmlFor=\"order-pots\">Pots</label>\r\n                <Input\r\n                  id=\"order-pots\"\r\n                  type=\"number\"\r\n                  value={order.pots}\r\n                  onChange={handlePotsChange}\r\n                  onFocus={handleFocus}\r\n                  className=\"text-end\"\r\n                  disabled={!canUpdate}\r\n                />\r\n              </div>\r\n              <div className=\"col-12 col-md-3\">\r\n                <label htmlFor=\"order-cases\">Cases</label>\r\n                <Input\r\n                  id=\"order-cases\"\r\n                  type=\"number\"\r\n                  value={order.cases}\r\n                  onChange={handleCasesChange}\r\n                  onFocus={handleFocus}\r\n                  className=\"text-end\"\r\n                  disabled={!canUpdate}\r\n                />\r\n              </div>\r\n            </div>\r\n          )}\r\n          <div className=\"row p-2 bg-light\">\r\n            <div className=\"col-12 col-md\">\r\n              <label htmlFor=\"order-stick-week\">Stick Date</label>\r\n              <Input\r\n                id=\"order-stick-week\"\r\n                value={stickWeek}\r\n                onChange={handleStickWeekChange}\r\n                onFocus={handleFocus}\r\n                disabled={!canUpdate}\r\n              />\r\n              <Input\r\n                id=\"order-stick-date\"\r\n                type=\"date\"\r\n                value={order.stickDate}\r\n                onChange={handleStickDateChange}\r\n                tabIndex={-1}\r\n                disabled={!canUpdate}\r\n              />\r\n            </div>\r\n            <div className=\"col-12 col-md\">\r\n              <label htmlFor=\"order-stick-zone\">Stick Zone</label>\r\n              <Input\r\n                id=\"order-stick-zone\"\r\n                type=\"select\"\r\n                value={order.stickZone.name}\r\n                onChange={handleStickZoneChange}\r\n                disabled={!canUpdate}>\r\n                {zones.map((zone) => (\r\n                  <option key={zone.name} value={zone.name}>\r\n                    {zone.name}\r\n                  </option>\r\n                ))}\r\n              </Input>\r\n            </div>\r\n            <div className=\"col-12 col-md\">\r\n              <label htmlFor=\"order-flower-week\">Flower Date</label>\r\n              <Input\r\n                id=\"order-flower-week\"\r\n                value={flowerWeek}\r\n                onChange={handleFlowerWeekChange}\r\n                onFocus={handleFocus}\r\n                disabled={!canUpdate}\r\n              />\r\n              <Input\r\n                id=\"order-flower-date\"\r\n                type=\"date\"\r\n                value={order.flowerDate}\r\n                onChange={handleFlowerDateChange}\r\n                tabIndex={-1}\r\n                disabled={!canUpdate}\r\n              />\r\n            </div>\r\n            <div className=\"col-12 col-md\">\r\n              <label htmlFor=\"order-table-count-tight\">Tables (Tight)</label>\r\n              <Input\r\n                id=\"order-table-count-tight\"\r\n                type=\"number\"\r\n                value={order.tableCountTight}\r\n                onChange={handleTableCountTightChange}\r\n                onFocus={handleFocus}\r\n                disabled={!canUpdate}\r\n              />\r\n            </div>\r\n          </div>\r\n          <div className=\"row p-2\">\r\n            <div className=\"col-12 col-md\">\r\n              <FormGroup check>\r\n                <Input\r\n                  id=\"order-has-spacing-space\"\r\n                  type=\"checkbox\"\r\n                  checked={order.hasSpacing}\r\n                  onChange={handleHasSpacingChange}\r\n                  disabled={!canUpdate}\r\n                />\r\n                <label htmlFor=\"order-has-spacing-space\">\r\n                  Product is Spaced?\r\n                </label>\r\n              </FormGroup>\r\n            </div>\r\n            {order.hasSpacing && (\r\n              <>\r\n                <div className=\"col-12 col-md\">\r\n                  <label htmlFor=\"order-space-week\">Space Date</label>\r\n                  <Input\r\n                    id=\"order-space-week\"\r\n                    value={fullSpaceWeek}\r\n                    onChange={handleFullSpaceWeekChange}\r\n                    onFocus={handleFocus}\r\n                    disabled={!canUpdate}\r\n                  />\r\n                  <Input\r\n                    id=\"order-space-date\"\r\n                    type=\"date\"\r\n                    value={order.fullSpaceDate}\r\n                    onChange={handleFullSpaceDateChange}\r\n                    tabIndex={-1}\r\n                    disabled={!canUpdate}\r\n                  />\r\n                </div>\r\n                <div className=\"col-12 col-md\">\r\n                  <label htmlFor=\"order-full-space-zone\">Full Space Zone</label>\r\n                  <Input\r\n                    id=\"order-full-space-zone\"\r\n                    type=\"select\"\r\n                    value={order.fullSpaceZone?.name || ''}\r\n                    onChange={handleFullSpaceZoneChange}\r\n                    disabled={!canUpdate}>\r\n                    {zones.map((zone) => (\r\n                      <option key={zone.name} value={zone.name}>\r\n                        {zone.name}\r\n                      </option>\r\n                    ))}\r\n                  </Input>\r\n                </div>\r\n                <div className=\"col-12 col-md\">\r\n                  <label htmlFor=\"order-table-count-spaced\">\r\n                    Tables (Spaced)\r\n                  </label>\r\n                  <Input\r\n                    id=\"order-table-count-spaced\"\r\n                    type=\"number\"\r\n                    value={order.tableCountSpaced}\r\n                    onChange={handleTableCountSpacedChange}\r\n                    onFocus={handleFocus}\r\n                    disabled={!canUpdate}\r\n                  />\r\n                </div>\r\n              </>\r\n            )}\r\n          </div>\r\n          <div className=\"row p-2 bg-light\">\r\n            <div className=\"col-12 col-md\">\r\n              <FormGroup check>\r\n                <Input\r\n                  id=\"order-has-lights-out\"\r\n                  type=\"checkbox\"\r\n                  checked={order.hasLightsOut}\r\n                  onChange={handleHasLightsOutChange}\r\n                  disabled={!canUpdate}\r\n                />\r\n                <label htmlFor=\"order-has-lights-out\">\r\n                  Product has Lights-Out?\r\n                </label>\r\n              </FormGroup>\r\n            </div>\r\n            {order.hasLightsOut && (\r\n              <>\r\n                <div className=\"col-12 col-md\">\r\n                  <label htmlFor=\"order-lights-out-week\">Lights-Out Date</label>\r\n                  <Input\r\n                    id=\"order-lights-out-week\"\r\n                    value={lightsOutWeek}\r\n                    onChange={handleLightsOutWeekChange}\r\n                    onFocus={handleFocus}\r\n                    disabled={!canUpdate}\r\n                  />\r\n                  <Input\r\n                    id=\"order-lights-out-date\"\r\n                    type=\"date\"\r\n                    value={order.lightsOutDate}\r\n                    onChange={handleLightsOutDateChange}\r\n                    tabIndex={-1}\r\n                    disabled={!canUpdate}\r\n                  />\r\n                </div>\r\n                <div className=\"col-12 col-md\">\r\n                  <label htmlFor=\"order-lights-out-zone\">Lights-Out Zone</label>\r\n                  <Input\r\n                    id=\"order-lights-out-zone\"\r\n                    type=\"select\"\r\n                    value={order.lightsOutZone?.name || ''}\r\n                    onChange={handleLightsOutZoneChange}\r\n                    disabled={!canUpdate}>\r\n                    {zones.map((zone) => (\r\n                      <option key={zone.name} value={zone.name}>\r\n                        {zone.name}\r\n                      </option>\r\n                    ))}\r\n                  </Input>\r\n                </div>\r\n                <div className=\"col-md\">&nbsp;</div>\r\n              </>\r\n            )}\r\n          </div>\r\n          <div className=\"row p-2\">\r\n            <div className=\"col-12 col-md\">\r\n              <FormGroup check>\r\n                <Input\r\n                  id=\"order-has-partial-space\"\r\n                  type=\"checkbox\"\r\n                  checked={order.hasPartialSpace}\r\n                  onChange={handleHasPartialSpaceChange}\r\n                  disabled={!canUpdate}\r\n                />\r\n                <label htmlFor=\"order-has-partial-space\">\r\n                  Product is Partial Spaced?\r\n                </label>\r\n              </FormGroup>\r\n            </div>\r\n            {order.hasPartialSpace && (\r\n              <>\r\n                <div className=\"col-12 col-md\">\r\n                  <label htmlFor=\"order-partial-space-week\">\r\n                    Partial Space Date\r\n                  </label>\r\n                  <Input\r\n                    id=\"order-partial-space-week\"\r\n                    value={partialSpaceWeek}\r\n                    onChange={handlePartialSpaceWeekChange}\r\n                    onFocus={handleFocus}\r\n                    disabled={!canUpdate}\r\n                  />\r\n                  <Input\r\n                    id=\"order-partial-space-date\"\r\n                    type=\"date\"\r\n                    value={order.partialSpaceDate}\r\n                    onChange={handlePartialSpaceDateChange}\r\n                    tabIndex={-1}\r\n                    disabled={!canUpdate}\r\n                  />\r\n                </div>\r\n                <div className=\"col-12 col-md\">\r\n                  <label htmlFor=\"order-partial-space-zone\">\r\n                    Partial Space Zone\r\n                  </label>\r\n                  <Input\r\n                    id=\"order-partial-space-zone\"\r\n                    type=\"select\"\r\n                    value={order.partialSpaceZone?.name || ''}\r\n                    onChange={handlePartialSpaceZoneChange}\r\n                    disabled={!canUpdate}>\r\n                    {zones.map((zone) => (\r\n                      <option key={zone.name} value={zone.name}>\r\n                        {zone.name}\r\n                      </option>\r\n                    ))}\r\n                  </Input>\r\n                </div>\r\n                <div className=\"col-12 col-md\">\r\n                  <label htmlFor=\"order-table-count-partially-spaced\">\r\n                    Tables (Partially Spaced)\r\n                  </label>\r\n                  <Input\r\n                    id=\"order-table-count-partially-spaced\"\r\n                    type=\"number\"\r\n                    value={order.tableCountPartiallySpaced}\r\n                    onChange={handleTableCountPartiallySpacedChange}\r\n                    onFocus={handleFocus}\r\n                    disabled={!canUpdate}\r\n                  />\r\n                </div>\r\n              </>\r\n            )}\r\n          </div>\r\n          <div className=\"row p-2 bg-light\">\r\n            <div className=\"col-12 col-md\">\r\n              <FormGroup check>\r\n                <Input\r\n                  id=\"order-has-pinching\"\r\n                  type=\"checkbox\"\r\n                  checked={order.hasPinching}\r\n                  onChange={handleHasPinchingChange}\r\n                  disabled={!canUpdate}\r\n                />\r\n                <label htmlFor=\"order-has-pinching\">\r\n                  Product Needs Pinching?\r\n                </label>\r\n              </FormGroup>\r\n            </div>\r\n            {order.hasPinching && (\r\n              <>\r\n                <div className=\"col-12 col-md\">\r\n                  <label htmlFor=\"order-pinch-week\">Pinch Date</label>\r\n                  <Input\r\n                    id=\"order-pinch-week\"\r\n                    value={pinchWeek}\r\n                    onChange={handlePinchWeekChange}\r\n                    onFocus={handleFocus}\r\n                    disabled={!canUpdate}\r\n                  />\r\n                  <Input\r\n                    id=\"order-pinch-date\"\r\n                    type=\"date\"\r\n                    value={order.pinchDate}\r\n                    onChange={handlePinchDateChange}\r\n                    tabIndex={-1}\r\n                    disabled={!canUpdate}\r\n                  />\r\n                </div>\r\n                <div className=\"col-0 col-md\">&nbsp;</div>\r\n                <div className=\"col-0 col-md\">&nbsp;</div>\r\n              </>\r\n            )}\r\n          </div>\r\n          <div className=\"row p-2\">\r\n            <div className=\"col-12 col-md\">\r\n              <label htmlFor=\"order-supplier-po-number\">Supplier PO #</label>\r\n              <Input\r\n                id=\"order-supplier-po-number\"\r\n                value={order.supplierPoNumber}\r\n                onChange={handleSupplierPoNumberChange}\r\n                disabled={!canUpdate}\r\n              />\r\n            </div>\r\n            <div className=\"col-12 col-md\">\r\n              <label htmlFor=\"order-notes\">Notes</label>\r\n              <Input\r\n                id=\"order=notes\"\r\n                type=\"textarea\"\r\n                value={order.notes || ''}\r\n                onChange={handleNotesChange}\r\n                disabled={!canUpdate}\r\n              />\r\n            </div>\r\n            <div className=\"col-12 col-md\">\r\n              <label htmlFor=\"order-season\">Season</label>\r\n              <Input\r\n                id=\"order-season\"\r\n                value={order.season || ''}\r\n                onChange={handleSeasonChange}\r\n                disabled={!canUpdate}\r\n              />\r\n            </div>\r\n          </div>\r\n          {!!order.varieties && (\r\n            <>\r\n              <div className=\"row\">\r\n                <h4 className=\"col\">Varieties</h4>\r\n              </div>\r\n              <div className=\"row fw-bold\">\r\n                <div className=\"col-12 col-md-2 offset-md-1 text-center\">\r\n                  Variety\r\n                </div>\r\n                <div className=\"col-12 col-md-2 text-center\">Cuttings</div>\r\n                <div className=\"col-12 col-md-2 text-center\">Pots</div>\r\n                <div className=\"col-12 col-md-2 text-center\">Cases</div>\r\n                <div className=\"col-12 col-md-3 text-center\">Comment</div>\r\n              </div>\r\n              {order.varieties.map((variety) => (\r\n                <Variety\r\n                  key={variety.name}\r\n                  variety={variety}\r\n                  onChange={(v) => handleVarietyChange(variety.name, v)}\r\n                />\r\n              ))}\r\n              <div className=\"row border-top-double\">\r\n                {canUpdate && (\r\n                  <div className=\"col-1 text-center\">\r\n                    <Button\r\n                      color=\"success\"\r\n                      size=\"sm\"\r\n                      outline\r\n                      onClick={handleAddVarietyClick}\r\n                      className=\"mt-1\">\r\n                      <FontAwesomeIcon icon={['fat', 'plus']} />\r\n                    </Button>\r\n                  </div>\r\n                )}\r\n                <div className=\"col-12 col-md-2 offset-md-2\">\r\n                  <Input\r\n                    id=\"order-cuttings\"\r\n                    value={formatNumber(order.cuttings)}\r\n                    readOnly\r\n                    plaintext\r\n                    className=\"bg-white text-end\"\r\n                  />\r\n                </div>\r\n                <div className=\"col-12 col-md-2\">\r\n                  <Input\r\n                    id=\"order-pots\"\r\n                    value={formatNumber(order.pots)}\r\n                    readOnly\r\n                    plaintext\r\n                    className=\"bg-white text-end\"\r\n                  />\r\n                </div>\r\n                <div className=\"col-12 col-md-2\">\r\n                  <Input\r\n                    id=\"order-cases\"\r\n                    value={formatNumber(order.cases)}\r\n                    readOnly\r\n                    plaintext\r\n                    className=\"bg-white text-end\"\r\n                  />\r\n                </div>\r\n              </div>\r\n            </>\r\n          )}\r\n          <div className=\"row mt-5\">\r\n            <h4 className=\"col\">Sales</h4>\r\n          </div>\r\n          <div className=\"row\">\r\n            <FormGroup check className=\"col\">\r\n              <Input\r\n                id=\"order-has-sales-weeks\"\r\n                type=\"checkbox\"\r\n                checked={hasSalesWeeks}\r\n                onChange={handleHasSalesWeeksChange}\r\n                disabled={!canUpdate}\r\n              />\r\n              <label htmlFor=\"order-has-sales-weeks\">\r\n                Sells over multiple weeks\r\n              </label>\r\n            </FormGroup>\r\n          </div>\r\n          {hasSalesWeeks && (\r\n            <>\r\n              <div className=\"row fw-bold\">\r\n                <div className=\"col-12 col-md-2 offset-md-1 text-center\">\r\n                  Week\r\n                </div>\r\n                <div className=\"col-12 col-md-2 text-center\">Cases</div>\r\n              </div>\r\n              {order.salesWeeks?.map((salesWeek) => (\r\n                <SalesWeekRow\r\n                  key={salesWeek.id}\r\n                  salesWeek={salesWeek}\r\n                  onChange={handleSalesWeekChange}\r\n                />\r\n              ))}\r\n              <div className=\"row border-top-double\">\r\n                <div className=\"col-1 text-center\">\r\n                  {canUpdate && (\r\n                    <Button\r\n                      color=\"success\"\r\n                      size=\"sm\"\r\n                      outline\r\n                      onClick={handleAddSalesWeekClick}\r\n                      className=\"mt-1\">\r\n                      <FontAwesomeIcon icon={['fat', 'plus']} />\r\n                    </Button>\r\n                  )}\r\n                </div>\r\n                <div className=\"col-12 col-md-2 offset-md-2\">\r\n                  <Input\r\n                    id=\"sales-week-totals\"\r\n                    value={formatNumber(salesWeekTotal)}\r\n                    readOnly\r\n                    plaintext\r\n                    className=\"bg-white text-end\"\r\n                  />\r\n                </div>\r\n              </div>\r\n            </>\r\n          )}\r\n\r\n          <div className=\"row mt-5 bg-white sticky-bottom border-top py-2\">\r\n            {!isNew && canDelete && (\r\n              <div className=\"col-auto\">\r\n                <Button\r\n                  onClick={handleDeleteClick}\r\n                  outline\r\n                  color=\"danger\"\r\n                  size=\"lg\"\r\n                  className=\"me-auto\">\r\n                  <FontAwesomeIcon icon={['fat', 'trash']} />\r\n                  &nbsp; Delete\r\n                </Button>\r\n              </div>\r\n            )}\r\n            <div className=\"col text-end\">\r\n              <Button outline size=\"lg\" onClick={handleGoBackClick}>\r\n                {canUpdate ? 'Cancel' : 'Close'}\r\n              </Button>\r\n              {canUpdate && (\r\n                <>\r\n                  &nbsp;\r\n                  <Button onClick={handleSaveClick} color=\"success\" size=\"lg\">\r\n                    <FontAwesomeIcon icon={['fat', 'save']} />\r\n                    &nbsp; Save\r\n                  </Button>\r\n                </>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </>\r\n      )}\r\n      <Modal\r\n        isOpen={showNotificationModal}\r\n        toggle={handleSendNotificationCancel}\r\n        onOpened={handleNotificationModalOpened}\r\n        autoFocus={false}>\r\n        <ModalHeader toggle={handleSendNotificationCancel}>\r\n          Send Notification\r\n        </ModalHeader>\r\n        <ModalBody>\r\n          <p className=\"text-center\">\r\n            Would you like to send a notification about this change?\r\n          </p>\r\n\r\n          <label htmlFor=\"notification-messge\">Message:</label>\r\n          <Input\r\n            id=\"notification-messge\"\r\n            bsSize=\"lg\"\r\n            value={notificationMessage}\r\n            onChange={handleNotificationMessageChanged}\r\n            autoFocus\r\n          />\r\n        </ModalBody>\r\n        <ModalFooter>\r\n          <Button outline onClick={handleSendNotificationCancel}>\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            color=\"primary\"\r\n            outline\r\n            onClick={handleSendNotificationConfirm}>\r\n            <FontAwesomeIcon icon={['fat', 'paper-plane']} />\r\n            &nbsp; Send\r\n          </Button>\r\n        </ModalFooter>\r\n      </Modal>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Detail;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,WAAW,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC/D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,SAAS,EAAEC,WAAW,QAAQ,cAAc;AACrD,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SACEC,MAAM,EACNC,SAAS,EACTC,KAAK,EACLC,KAAK,EACLC,WAAW,EACXC,SAAS,EACTC,WAAW,QACN,YAAY;AACnB,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SACEC,WAAW,EACXC,iBAAiB,QAIZ,mBAAmB;AAE1B,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,eAAe,QAAQ,oCAAoC;AACpE,SAASC,WAAW,QAAQ,4BAA4B;AACxD,SAASC,WAAW,QAAQ,aAAa;AACzC,SACEC,YAAY,EACZC,YAAY,EACZC,eAAe,EACfC,aAAa,QACR,cAAc;AACrB,SAASC,WAAW,EAAEC,SAAS,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,gBAAgB;AAC9E,SAASC,eAAe,QAAQ,gBAAgB;AAChD,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,IAAI,QAAQ,YAAY;AAAC;AAAA;AAElC,OAAO,SAASC,MAAM,GAAG;EAAA;EAAA;EACvB,MAAMC,QAAQ,GAAGlC,WAAW,EAAE;IAC5BmC,QAAQ,GAAGjC,WAAW,EAAE;IACxB;MAAEkC,QAAQ;MAAEC;IAAK,CAAC,GAAGrB,OAAO,EAAE;IAC9B;MAAEsB;IAAG,CAAC,GAAGrC,SAAS,EAAkB;IACpCsC,MAAM,GAAGxC,WAAW,CAAC8B,eAAe,CAAC;IACrCW,KAAK,GAAGzC,WAAW,CAAC4B,WAAW,CAAC;IAChCc,SAAS,GAAG1C,WAAW,CAACmB,eAAe,CAAC;IACxCwB,KAAK,GAAG3C,WAAW,CAACoB,WAAW,CAAC;IAChCwB,MAAM,GAAG5C,WAAW,CAACkB,YAAY,CAAC;IAClC2B,KAAK,GAAG,EAACJ,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEK,IAAI;IACpB,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;IACxC,CAACkD,aAAa,EAAEC,gBAAgB,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;IAChD,CAACoD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;IACtD,CAACsD,aAAa,EAAEC,gBAAgB,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;IAChD,CAACwD,SAAS,EAAEC,YAAY,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;IACxC,CAAC0D,UAAU,EAAEC,aAAa,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;IAC1C,CAAC4D,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;IACnE,CAAC8D,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;IAC5DgE,aAAa,GAAGC,KAAK,CAACC,OAAO,CAACxB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEyB,UAAU,CAAC;IAChDC,SAAS,GACNtB,KAAK,IAAIR,QAAQ,CAAC,eAAe,CAAC,IAAKA,QAAQ,CAAC,eAAe,CAAC;IACnE+B,SAAS,GAAG/B,QAAQ,CAAC,eAAe,CAAC;IACrCgC,cAAc,GAAG5B,KAAK,aAALA,KAAK,4CAALA,KAAK,CAAEyB,UAAU,sDAAjB,kBAAmBI,MAAM,CACxC,CAACC,KAAK,EAAEC,EAAE,KAAKD,KAAK,GAAGC,EAAE,CAACC,KAAK,EAC/B,CAAC,CACF;EAEH,MAAMC,aAAa,GAAG7E,WAAW,CAC/B,CAAC8E,KAAY,EAAElC,KAAY,KAAK;IAC9B,MAAMmC,WAAW,GAAG7D,iBAAiB,CACjC4D,KAAK,CAACE,YAAY,EAClBpC,KAAK,CAACqC,QAAQ,CAACD,YAAY,EAC3BpC,KAAK,CAACsC,SAAS,CAChB;MACDC,MAAM,GAAG;QAAE,GAAGvC,KAAK;QAAEmC,WAAW;QAAED;MAAM,CAAC;IAE3CK,MAAM,CAACC,YAAY,GAAGN,KAAK,CAACM,YAAY;IACxC,IAAIN,KAAK,CAACM,YAAY,EAAE;MACtBD,MAAM,CAACE,aAAa,GAAGzC,KAAK,CAAC0C,aAAa,IAAI1C,KAAK,CAACsC,SAAS;MAC7DC,MAAM,CAACI,aAAa,GAAG3C,KAAK,CAAC4C,aAAa,IAAI5C,KAAK,CAAC6C,SAAS;IAC/D;IAEAN,MAAM,CAACO,WAAW,GAAGZ,KAAK,CAACY,WAAW;IACtC,IAAIZ,KAAK,CAACY,WAAW,EAAE;MACrBP,MAAM,CAACQ,SAAS,GACd/C,KAAK,CAAC+C,SAAS,IACfpF,MAAM,CAACqC,KAAK,CAACsC,SAAS,CAAC,CACpBU,GAAG,CAACd,KAAK,CAACe,WAAW,IAAI,CAAC,EAAE,MAAM,CAAC,CACnCC,MAAM,CAAC,YAAY,CAAC;MACzBnC,YAAY,CAAClC,YAAY,CAAC0D,MAAM,CAACQ,SAAS,CAAC,CAAC;IAC9C;IAEA,IAAIb,KAAK,CAACiB,SAAS,EAAE;MACnBZ,MAAM,CAACY,SAAS,GAAG,EAAE;IACvB,CAAC,MAAM;MACL,OAAOZ,MAAM,CAACY,SAAS;IACzB;IAEAzD,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;EAC5B,CAAC,EACD,CAAC7C,QAAQ,CAAC,CACX;EAEDrC,SAAS,CAAC,MAAM;IACd,MAAM+F,KAAK,GAAGrD,MAAM,CAACsD,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,GAAG,KAAKzD,EAAE,CAAC;IAC9C,IAAIsD,KAAK,IAAIA,KAAK,CAACG,GAAG,MAAKvD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEuD,GAAG,GAAE;MACrC7D,QAAQ,CAACN,QAAQ,CAACgE,KAAK,CAAC,CAAC;MACzB7C,YAAY,CAAC1B,YAAY,CAACuE,KAAK,CAACd,SAAS,CAAC,CAAC;MAC3C,IAAIc,KAAK,CAACX,aAAa,EAAE;QACvBhC,gBAAgB,CAAC5B,YAAY,CAACuE,KAAK,CAACX,aAAa,CAAC,CAAC;MACrD;MACA,IAAIW,KAAK,CAACI,gBAAgB,EAAE;QAC1B7C,mBAAmB,CAAC9B,YAAY,CAACuE,KAAK,CAACI,gBAAgB,CAAC,CAAC;MAC3D;MACA,IAAIJ,KAAK,CAACV,aAAa,EAAE;QACvB7B,gBAAgB,CAAChC,YAAY,CAACuE,KAAK,CAACV,aAAa,CAAC,CAAC;MACrD;MACA,IAAIU,KAAK,CAACN,WAAW,EAAE;QACrB/B,YAAY,CAAClC,YAAY,CAACuE,KAAK,CAACL,SAAS,CAAC,CAAC;MAC7C;MACA9B,aAAa,CAACpC,YAAY,CAACuE,KAAK,CAACK,UAAU,CAAC,CAAC;IAC/C,CAAC,MAAM,IAAI3D,EAAE,KAAK,KAAK,EAAE;MACvB,IAAI,CAACE,KAAK,EAAE;QACV,MAAM0D,QAAQ,GAAGrF,WAAW,EAAE;QAC9B,IAAI6B,KAAK,CAACyD,MAAM,EAAE;UAChBD,QAAQ,CAACb,SAAS,GAAG3C,KAAK,CAAC,CAAC,CAAC;UAC7B,IAAIwD,QAAQ,CAACF,gBAAgB,EAAE;YAC7BE,QAAQ,CAACE,gBAAgB,GAAG1D,KAAK,CAAC,CAAC,CAAC;UACtC;UACA,IAAIwD,QAAQ,CAACjB,aAAa,EAAE;YAC1BiB,QAAQ,CAACf,aAAa,GAAGzC,KAAK,CAAC,CAAC,CAAC;UACnC;UACA,IAAIwD,QAAQ,CAAChB,aAAa,EAAE;YAC1BgB,QAAQ,CAACd,aAAa,GAAG1C,KAAK,CAAC,CAAC,CAAC;UACnC;QACF;QACAR,QAAQ,CAACN,QAAQ,CAACsE,QAAQ,CAAC,CAAC;MAC9B,CAAC,MAAM,IACLvD,MAAM,CAACwD,MAAM,IACb,CAACxD,MAAM,CAAC0D,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACP,GAAG,KAAKvD,KAAK,CAACkC,KAAK,CAACqB,GAAG,CAAC,EAC9C;QACA,MAAMQ,UAAU,GAAG5D,MAAM,CAAC,CAAC,CAAC;QAC5B8B,aAAa,CAAC8B,UAAU,EAAE/D,KAAK,CAAC;QAChCO,YAAY,CAAC1B,YAAY,CAACmB,KAAK,CAACsC,SAAS,CAAC,CAAC;QAC3C,IAAItC,KAAK,CAACyC,aAAa,EAAE;UACvBhC,gBAAgB,CAAC5B,YAAY,CAACmB,KAAK,CAACyC,aAAa,CAAC,CAAC;QACrD;QACA,IAAIzC,KAAK,CAACwD,gBAAgB,EAAE;UAC1B7C,mBAAmB,CAAC9B,YAAY,CAACmB,KAAK,CAACwD,gBAAgB,CAAC,CAAC;QAC3D;QACA,IAAIxD,KAAK,CAAC0C,aAAa,EAAE;UACvB7B,gBAAgB,CAAChC,YAAY,CAACmB,KAAK,CAAC0C,aAAa,CAAC,CAAC;QACrD;QACA,IAAI1C,KAAK,CAAC+C,SAAS,EAAE;UACnBhC,YAAY,CAAClC,YAAY,CAACmB,KAAK,CAAC+C,SAAS,CAAC,CAAC;QAC7C,CAAC,MAAM,IAAI/C,KAAK,CAAC8C,WAAW,EAAE;UAC5B/B,YAAY,CACVlC,YAAY,CACVlB,MAAM,CAACqC,KAAK,CAACsC,SAAS,CAAC,CACpBU,GAAG,CAACe,UAAU,CAACd,WAAW,IAAI,CAAC,EAAE,MAAM,CAAC,CACxCC,MAAM,CAAC,YAAY,CAAC,CACxB,CACF;QACH;QACAjC,aAAa,CAACpC,YAAY,CAACmB,KAAK,CAACyD,UAAU,CAAC,CAAC;MAC/C;IACF;IAEA,OAAO,SAASO,OAAO,GAAG;MACxB;IAAA,CACD;EACH,CAAC,EAAE,CAACtE,QAAQ,EAAEI,EAAE,EAAEmC,aAAa,EAAEjC,KAAK,EAAED,MAAM,EAAEI,MAAM,EAAED,KAAK,CAAC,CAAC;EAE/D,MAAM+D,oBAAoB,GAAIC,CAAsC,IAAK;IACvE,MAAM9B,YAAY,GAAG8B,CAAC,CAACC,MAAM,CAACC,KAAK;MACjC/B,QAAQ,GAAGpC,SAAS,CAACoD,IAAI,CAAEgB,CAAC,IAAKA,CAAC,CAACjC,YAAY,KAAKA,YAAY,CAAC;IAEnE,IAAIpC,KAAK,IAAIqC,QAAQ,EAAE;MACrB,MAAMF,WAAW,GAAG7D,iBAAiB,CACjC0B,KAAK,CAACkC,KAAK,CAACE,YAAY,EACxBC,QAAQ,CAACD,YAAY,EACrBpC,KAAK,CAACsC,SAAS,CAChB;QACDC,MAAM,GAAG;UAAE,GAAGvC,KAAK;UAAEmC,WAAW;UAAEE;QAAS,CAAC;MAC9C3C,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;IAC5B;EACF,CAAC;EAED,MAAM+B,iBAAiB,GAAIJ,CAAsC,IAAK;IACpE,IAAIlE,KAAK,EAAE;MACT,MAAMF,EAAE,GAAGoE,CAAC,CAACC,MAAM,CAACC,KAAK;QACvBlC,KAAK,GAAG/B,MAAM,CAACkD,IAAI,CAAES,CAAC,IAAKA,CAAC,CAACP,GAAG,KAAKzD,EAAE,CAAC;MAE1C,IAAIoC,KAAK,EAAE;QACTD,aAAa,CAACC,KAAK,EAAElC,KAAK,CAAC;MAC7B;IACF;EACF,CAAC;EAED,MAAMuE,qBAAqB,GAAG,MAAM;IAClC,IAAIvE,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEmD,SAAS,EAAE;MACpB,MAAMA,SAAS,GAAGnD,KAAK,CAACmD,SAAS,CAC5BqB,GAAG,CAAEC,CAAC,KAAM;UAAE,GAAGA;QAAE,CAAC,CAAC,CAAC,CACtBC,MAAM,CAAC,CACN;UAAEC,IAAI,EAAE,EAAE;UAAEC,QAAQ,EAAE,CAAC;UAAEC,IAAI,EAAE,CAAC;UAAE7C,KAAK,EAAE,CAAC;UAAE8C,OAAO,EAAE;QAAK,CAAC,CAC5D,CAAC;QACJvC,MAAM,GAAG;UAAE,GAAGvC,KAAK;UAAEmD;QAAU,CAAC;MAElCzD,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;IAC5B;EACF,CAAC;EAED,MAAMwC,oBAAoB,GAAIb,CAAsC,IAAK;IACvE,IAAIlE,KAAK,EAAE;MACT,MAAM4E,QAAQ,GAAGV,CAAC,CAACC,MAAM,CAACa,aAAa,IAAI,CAAC;MAC5CC,gBAAgB,CAACL,QAAQ,EAAE5E,KAAK,CAAC;IACnC;EACF,CAAC;EAED,MAAMkF,gBAAgB,GAAIhB,CAAsC,IAAK;IACnE,IAAIlE,KAAK,EAAE;MACT,MAAM6E,IAAI,GAAGX,CAAC,CAACC,MAAM,CAACa,aAAa,IAAI,CAAC;MACxCG,YAAY,CAACN,IAAI,EAAE7E,KAAK,CAAC;IAC3B;EACF,CAAC;EAED,MAAMoF,iBAAiB,GAAIlB,CAAsC,IAAK;IACpE,IAAIlE,KAAK,EAAE;MACT,MAAMgC,KAAK,GAAGkC,CAAC,CAACC,MAAM,CAACa,aAAa,IAAI,CAAC;MACzCK,aAAa,CAACrD,KAAK,EAAEhC,KAAK,CAAC;IAC7B;EACF,CAAC;EAED,MAAMiF,gBAAgB,GAAG,CAACL,QAAgB,EAAE5E,KAAY,KAAK;IAC3D,MAAM;QAAEkC;MAAM,CAAC,GAAGlC,KAAK;MACrBsF,cAAc,GAAGpD,KAAK,CAACoD,cAAc,IAAI,CAAC;MAC1CC,WAAW,GAAGrD,KAAK,CAACqD,WAAW,IAAI,CAAC;MACpCV,IAAI,GAAGW,IAAI,CAACC,KAAK,CAACb,QAAQ,GAAGU,cAAc,CAAC;MAC5CtD,KAAK,GAAGwD,IAAI,CAACC,KAAK,CAACZ,IAAI,GAAGU,WAAW,CAAC;MACtCG,qBAAqB,GAAGxD,KAAK,CAACwD,qBAAqB,IAAI,CAAC;MACxDC,eAAe,GAAGH,IAAI,CAACC,KAAK,CAACb,QAAQ,GAAGc,qBAAqB,CAAC;MAC9DnD,MAAM,GAAG;QAAE,GAAGvC,KAAK;QAAE4E,QAAQ;QAAEC,IAAI;QAAE7C,KAAK;QAAE2D;MAAgB,CAAC;IAE/D,IAAI3F,KAAK,CAAC4F,eAAe,EAAE;MACzB,MAAMC,+BAA+B,GACjC3D,KAAK,CAAC2D,+BAA+B,IAAI,CAAC;QAC5CC,yBAAyB,GAAGN,IAAI,CAACC,KAAK,CACpCb,QAAQ,GAAGiB,+BAA+B,CAC3C;MAEHtD,MAAM,CAACuD,yBAAyB,GAAGA,yBAAyB;IAC9D;IAEA,IAAI9F,KAAK,CAAC+F,UAAU,EAAE;MACpB,MAAMC,sBAAsB,GAAG9D,KAAK,CAAC8D,sBAAsB,IAAI,CAAC;QAC9DC,gBAAgB,GAAGT,IAAI,CAACC,KAAK,CAACb,QAAQ,GAAGoB,sBAAsB,CAAC;MAElEzD,MAAM,CAAC0D,gBAAgB,GAAGA,gBAAgB;IAC5C;IAEAvG,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAM4C,YAAY,GAAG,CAACN,IAAY,EAAE7E,KAAY,KAAK;IACnD,MAAM;QAAEkC;MAAM,CAAC,GAAGlC,KAAK;MACrBuF,WAAW,GAAGrD,KAAK,CAACqD,WAAW,IAAI,CAAC;MACpCvD,KAAK,GAAGwD,IAAI,CAACC,KAAK,CAACZ,IAAI,GAAGU,WAAW,CAAC;MACtChD,MAAM,GAAG;QAAE,GAAGvC,KAAK;QAAE6E,IAAI;QAAE7C;MAAM,CAAC;IAEpCtC,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAM8C,aAAa,GAAG,CAACrD,KAAa,EAAEhC,KAAY,KAAK;IACrD,MAAMuC,MAAM,GAAG;MAAE,GAAGvC,KAAK;MAAEgC;IAAM,CAAC;IAElCtC,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;EAC5B,CAAC;EAED,MAAM2D,4BAA4B,GAChChC,CAAsC,IACnC;IACH,IAAIlE,KAAK,EAAE;MACT,MAAMmG,gBAAgB,GAAGjC,CAAC,CAACC,MAAM,CAACC,KAAK;QACrC7B,MAAM,GAAG;UAAE,GAAGvC,KAAK;UAAEmG;QAAiB,CAAC;MAEzCzG,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;IAC5B;EACF,CAAC;EAED,MAAM6D,qBAAqB,GAAIlC,CAAsC,IAAK;IACxE,IAAIlE,KAAK,EAAE;MACT,MAAMqG,cAAc,GAAGnC,CAAC,CAACC,MAAM,CAACC,KAAK;QACnCkC,CAAC,GAAG3I,MAAM,CAAC0I,cAAc,CAAC;MAE5B,IAAIC,CAAC,CAACC,OAAO,EAAE,EAAE;QACf,MAAMjE,SAAS,GAAGgE,CAAC,CAACpD,MAAM,CAAC,YAAY,CAAC;UACtCf,WAAW,GAAG7D,iBAAiB,CAC7B0B,KAAK,CAACkC,KAAK,CAACE,YAAY,EACxBpC,KAAK,CAACqC,QAAQ,CAACD,YAAY,EAC3BE,SAAS,CACV;UACDC,MAAM,GAAG;YAAE,GAAGvC,KAAK;YAAEsC,SAAS;YAAEH;UAAY,CAAC;QAE/C,IAAInC,KAAK,CAACkC,KAAK,CAACe,WAAW,EAAE;UAC3B,MAAMF,SAAS,GAAGuD,CAAC,CAChBtD,GAAG,CAAChD,KAAK,CAACkC,KAAK,CAACe,WAAW,EAAE,MAAM,CAAC,CACpCC,MAAM,CAAC,YAAY,CAAC;UACvBX,MAAM,CAACQ,SAAS,GAAGA,SAAS;UAC5BhC,YAAY,CAAClC,YAAY,CAACkE,SAAS,CAAC,CAAC;QACvC;QAEArD,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;QAC1BhC,YAAY,CAAC1B,YAAY,CAACyD,SAAS,CAAC,CAAC;MACvC;IACF;EACF,CAAC;EAED,MAAMkE,qBAAqB,GAAItC,CAAsC,IAAK;IACxE,IAAIlE,KAAK,EAAE;MACT,MAAMmE,MAAM,GAAGD,CAAC,CAACC,MAAM,CAACC,KAAK;QAC3BqC,UAAU,GAAG1H,eAAe,CAACoF,MAAM,EAAE,IAAI,CAAC;MAE5C5D,YAAY,CAAC4D,MAAM,CAAC;MAEpB,IAAIsC,UAAU,EAAE;QACd,MAAMC,IAAI,GAAG/I,MAAM,CAAC8I,UAAU,CAAC;QAE/B,IAAIC,IAAI,CAACH,OAAO,EAAE,EAAE;UAClB,IAAIG,IAAI,CAACC,QAAQ,EAAE,EAAE;YACnBD,IAAI,CAAC1D,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC;UACrB;UACA,MAAMV,SAAS,GAAGoE,IAAI,CAACxD,MAAM,CAAC,YAAY,CAAC;YACzCf,WAAW,GAAG7D,iBAAiB,CAC7B0B,KAAK,CAACkC,KAAK,CAACE,YAAY,EACxBpC,KAAK,CAACqC,QAAQ,CAACD,YAAY,EAC3BE,SAAS,CACV;YACDC,MAAM,GAAG;cAAE,GAAGvC,KAAK;cAAEsC,SAAS;cAAEH;YAAY,CAAC;UAE/CzC,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;QAC5B;MACF;IACF;EACF,CAAC;EAED,MAAMqE,qBAAqB,GAAI1C,CAAsC,IAAK;IACxE,IAAIlE,KAAK,EAAE;MACT,MAAM2E,IAAI,GAAGT,CAAC,CAACC,MAAM,CAACC,KAAK;QACzBvB,SAAS,GAAG3C,KAAK,CAACmD,IAAI,CAAES,CAAC,IAAKA,CAAC,CAACa,IAAI,KAAKA,IAAI,CAAC;MAEhD,IAAI9B,SAAS,EAAE;QACb,MAAMN,MAAM,GAAG;UAAE,GAAGvC,KAAK;UAAE6C;QAAU,CAAC;QACtCnD,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;MAC5B;IACF;EACF,CAAC;EAED,MAAMsE,2BAA2B,GAC/B3C,CAAsC,IACnC;IACH,IAAIlE,KAAK,EAAE;MACT,MAAM4F,eAAe,GAAG1B,CAAC,CAACC,MAAM,CAAC2C,OAAO;QACtCvE,MAAM,GAAG;UAAE,GAAGvC,KAAK;UAAE4F;QAAgB,CAAC;MAExC,IAAIA,eAAe,EAAE;QACnB,MAAMpC,gBAAgB,GAAG7F,MAAM,CAACqC,KAAK,CAACsC,SAAS,CAAC,CAC7CU,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CACdE,MAAM,CAAC,YAAY,CAAC;QACvBX,MAAM,CAACiB,gBAAgB,GAAGA,gBAAgB;QAC1CjB,MAAM,CAACqB,gBAAgB,GAAG5D,KAAK,CAAC6C,SAAS;QACzCN,MAAM,CAACuD,yBAAyB,GAAG9F,KAAK,CAAC2F,eAAe;QACxDhF,mBAAmB,CAAC9B,YAAY,CAAC2E,gBAAgB,CAAC,CAAC;MACrD,CAAC,MAAM;QACL,OAAOjB,MAAM,CAACiB,gBAAgB;QAC9B,OAAOjB,MAAM,CAACqB,gBAAgB;QAC9B,OAAOrB,MAAM,CAACuD,yBAAyB;QACvCnF,mBAAmB,CAAC,EAAE,CAAC;MACzB;MAEAjB,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;IAC5B;EACF,CAAC;EAED,MAAMwE,4BAA4B,GAChC7C,CAAsC,IACnC;IACH,IAAIlE,KAAK,EAAE;MACT,MAAMyG,UAAU,GAAGvC,CAAC,CAACC,MAAM,CAACC,KAAK;QAC/BsC,IAAI,GAAG3H,eAAe,CAAC0H,UAAU,EAAE,IAAI,CAAC;MAE1C9F,mBAAmB,CAAC8F,UAAU,CAAC;MAE/B,IAAIC,IAAI,EAAE;QACR,MAAMlD,gBAAgB,GAAG7F,MAAM,CAAC+I,IAAI,CAAC,CAACxD,MAAM,CAAC,YAAY,CAAC;UACxDX,MAAM,GAAG;YAAE,GAAGvC,KAAK;YAAEwD;UAAiB,CAAC;QAEzC9D,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;MAC5B;IACF;EACF,CAAC;EAED,MAAMyE,4BAA4B,GAChC9C,CAAsC,IACnC;IACH,IAAIlE,KAAK,EAAE;MACT,MAAMiH,qBAAqB,GAAG/C,CAAC,CAACC,MAAM,CAACC,KAAK;QAC1CkC,CAAC,GAAG3I,MAAM,CAACsJ,qBAAqB,CAAC;MAEnC,IAAIX,CAAC,CAACC,OAAO,EAAE,EAAE;QACf,MAAM/C,gBAAgB,GAAG8C,CAAC,CAACpD,MAAM,CAAC,YAAY,CAAC;UAC7CX,MAAM,GAAG;YAAE,GAAGvC,KAAK;YAAEwD;UAAiB,CAAC;QAEzC7C,mBAAmB,CAAC9B,YAAY,CAAC2E,gBAAgB,CAAC,CAAC;QAEnD9D,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;MAC5B;IACF;EACF,CAAC;EAED,MAAM2E,4BAA4B,GAChChD,CAAsC,IACnC;IACH,IAAIlE,KAAK,EAAE;MACT,MAAM2E,IAAI,GAAGT,CAAC,CAACC,MAAM,CAACC,KAAK;QACzBR,gBAAgB,GAAG1D,KAAK,CAACmD,IAAI,CAAES,CAAC,IAAKA,CAAC,CAACa,IAAI,KAAKA,IAAI,CAAC;MAEvD,IAAIf,gBAAgB,EAAE;QACpB,MAAMrB,MAAM,GAAG;UAAE,GAAGvC,KAAK;UAAE4D;QAAiB,CAAC;QAC7ClE,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;MAC5B;IACF;EACF,CAAC;EAED,MAAM4E,qCAAqC,GACzCjD,CAAsC,IACnC;IACH,IAAIlE,KAAK,EAAE;MACT,MAAM8F,yBAAyB,GAAG5B,CAAC,CAACC,MAAM,CAACa,aAAa,IAAI,CAAC;QAC3DzC,MAAM,GAAG;UAAE,GAAGvC,KAAK;UAAE8F;QAA0B,CAAC;MAClDpG,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;IAC5B;EACF,CAAC;EAED,MAAM6E,sBAAsB,GAAIlD,CAAsC,IAAK;IACzE,IAAIlE,KAAK,EAAE;MACT,MAAM+F,UAAU,GAAG7B,CAAC,CAACC,MAAM,CAAC2C,OAAO;QACjCvE,MAAM,GAAG;UAAE,GAAGvC,KAAK;UAAE+F;QAAW,CAAC;MAEnC,IAAIA,UAAU,EAAE;QACdxD,MAAM,CAACG,aAAa,GAAGH,MAAM,CAACD,SAAS;QACvCC,MAAM,CAACK,aAAa,GAAGL,MAAM,CAACM,SAAS;QACvCN,MAAM,CAAC0D,gBAAgB,GAAG1D,MAAM,CAACoD,eAAe;QAChD9E,gBAAgB,CAAC0B,MAAM,CAACD,SAAS,CAAC;MACpC,CAAC,MAAM;QACL,OAAOC,MAAM,CAACG,aAAa;QAC3B,OAAOH,MAAM,CAACK,aAAa;QAC3B,OAAOL,MAAM,CAAC0D,gBAAgB;QAC9BpF,gBAAgB,CAAC,EAAE,CAAC;MACtB;MAEAnB,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;IAC5B;EACF,CAAC;EAED,MAAM8E,yBAAyB,GAC7BnD,CAAsC,IACnC;IACH,IAAIlE,KAAK,EAAE;MACT,MAAMsH,kBAAkB,GAAGpD,CAAC,CAACC,MAAM,CAACC,KAAK;QACvCkC,CAAC,GAAG3I,MAAM,CAAC2J,kBAAkB,CAAC;MAEhC,IAAIhB,CAAC,CAACC,OAAO,EAAE,EAAE;QACf,MAAM7D,aAAa,GAAG4D,CAAC,CAACpD,MAAM,CAAC,YAAY,CAAC;UAC1CX,MAAM,GAAG;YAAE,GAAGvC,KAAK;YAAE0C;UAAc,CAAC;QAEtC,IAAI1C,KAAK,CAACwC,YAAY,EAAE;UACtBD,MAAM,CAACE,aAAa,GAAGC,aAAa;QACtC;QAEAhD,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;QAC1B7C,QAAQ,CAACb,YAAY,CAAC6D,aAAa,CAAC,CAAC;MACvC;IACF;EACF,CAAC;EAED,MAAM6E,yBAAyB,GAC7BrD,CAAsC,IACnC;IACH,IAAIlE,KAAK,EAAE;MACT,MAAMyG,UAAU,GAAGvC,CAAC,CAACC,MAAM,CAACC,KAAK;QAC/BsC,IAAI,GAAG3H,eAAe,CAAC0H,UAAU,EAAE,IAAI,CAAC;MAE1C5F,gBAAgB,CAAC4F,UAAU,CAAC;MAE5B,IAAIC,IAAI,EAAE;QACR,MAAMhE,aAAa,GAAG/E,MAAM,CAAC+I,IAAI,CAAC,CAACxD,MAAM,CAAC,YAAY,CAAC;UACrDX,MAAM,GAAG;YAAE,GAAGvC,KAAK;YAAE0C;UAAc,CAAC;QAEtChD,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;MAC5B;IACF;EACF,CAAC;EAED,MAAMiF,yBAAyB,GAC7BtD,CAAsC,IACnC;IACH,IAAIlE,KAAK,EAAE;MACT,MAAM2E,IAAI,GAAGT,CAAC,CAACC,MAAM,CAACC,KAAK;QACzBxB,aAAa,GAAG1C,KAAK,CAACmD,IAAI,CAAES,CAAC,IAAKA,CAAC,CAACa,IAAI,KAAKA,IAAI,CAAC;MAEpD,IAAI/B,aAAa,EAAE;QACjB,MAAML,MAAM,GAAG;UAAE,GAAGvC,KAAK;UAAE4C;QAAc,CAAC;QAE1C,IAAI5C,KAAK,CAACwC,YAAY,EAAE;UACtBD,MAAM,CAACI,aAAa,GAAGC,aAAa;QACtC;QAEAlD,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;MAC5B;IACF;EACF,CAAC;EAED,MAAMkF,wBAAwB,GAAIvD,CAAsC,IAAK;IAC3E,IAAIlE,KAAK,EAAE;MACT,MAAMwC,YAAY,GAAG0B,CAAC,CAACC,MAAM,CAAC2C,OAAO;QACnCvE,MAAM,GAAG;UAAE,GAAGvC,KAAK;UAAEwC;QAAa,CAAC;MAErC,IAAIA,YAAY,EAAE;QAChBD,MAAM,CAACE,aAAa,GAAGzC,KAAK,CAAC0C,aAAa,IAAI1C,KAAK,CAACsC,SAAS;QAC7DC,MAAM,CAACI,aAAa,GAAG3C,KAAK,CAAC4C,aAAa,IAAI5C,KAAK,CAAC6C,SAAS;QAC7DpC,gBAAgB,CAAC8B,MAAM,CAACE,aAAa,CAAC;MACxC,CAAC,MAAM;QACL,OAAOF,MAAM,CAACE,aAAa;QAC3BhC,gBAAgB,CAAC,EAAE,CAAC;MACtB;MAEAf,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;IAC5B;EACF,CAAC;EAED,MAAMmF,yBAAyB,GAC7BxD,CAAsC,IACnC;IACH,IAAIlE,KAAK,EAAE;MACT,MAAM2H,kBAAkB,GAAGzD,CAAC,CAACC,MAAM,CAACC,KAAK;QACvCkC,CAAC,GAAG3I,MAAM,CAACgK,kBAAkB,CAAC;MAEhC,IAAIrB,CAAC,CAACC,OAAO,EAAE,EAAE;QACf,MAAM9D,aAAa,GAAG6D,CAAC,CAACpD,MAAM,CAAC,YAAY,CAAC;UAC1CX,MAAM,GAAG;YAAE,GAAGvC,KAAK;YAAEyC;UAAc,CAAC;QAEtC/C,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;QAC1B9B,gBAAgB,CAAC5B,YAAY,CAAC4D,aAAa,CAAC,CAAC;MAC/C;IACF;EACF,CAAC;EAED,MAAMmF,yBAAyB,GAC7B1D,CAAsC,IACnC;IACH,IAAIlE,KAAK,EAAE;MACT,MAAMyG,UAAU,GAAGvC,CAAC,CAACC,MAAM,CAACC,KAAK;QAC/BsC,IAAI,GAAG3H,eAAe,CAAC0H,UAAU,EAAE,IAAI,CAAC;MAE1ChG,gBAAgB,CAACgG,UAAU,CAAC;MAE5B,IAAIC,IAAI,EAAE;QACR,MAAMjE,aAAa,GAAG9E,MAAM,CAAC+I,IAAI,CAAC,CAACxD,MAAM,CAAC,YAAY,CAAC;UACrDX,MAAM,GAAG;YAAE,GAAGvC,KAAK;YAAEyC;UAAc,CAAC;QAEtC/C,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;MAC5B;IACF;EACF,CAAC;EAED,MAAMsF,yBAAyB,GAC7B3D,CAAsC,IACnC;IACH,IAAIlE,KAAK,EAAE;MACT,MAAM2E,IAAI,GAAGT,CAAC,CAACC,MAAM,CAACC,KAAK;QACzBzB,aAAa,GAAGzC,KAAK,CAACmD,IAAI,CAAES,CAAC,IAAKA,CAAC,CAACa,IAAI,KAAKA,IAAI,CAAC;MAEpD,IAAIhC,aAAa,EAAE;QACjB,MAAMJ,MAAM,GAAG;UAAE,GAAGvC,KAAK;UAAE2C;QAAc,CAAC;QAC1CjD,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;MAC5B;IACF;EACF,CAAC;EAED,MAAMuF,uBAAuB,GAAI5D,CAAsC,IAAK;IAC1E,IAAIlE,KAAK,EAAE;MACT,MAAM8C,WAAW,GAAGoB,CAAC,CAACC,MAAM,CAAC2C,OAAO;QAClCvE,MAAM,GAAG;UAAE,GAAGvC,KAAK;UAAE8C;QAAY,CAAC;MAEpC,IAAIA,WAAW,EAAE;QAAA;QACf,MAAMG,WAAW,GAAG,iBAAAjD,KAAK,CAACkC,KAAK,iDAAX,aAAae,WAAW,KAAI,CAAC;UAC/CF,SAAS,GAAGpF,MAAM,CAChBqC,KAAK,CAAC+C,SAAS,IACbpF,MAAM,CAACqC,KAAK,CAACsC,SAAS,CAAC,CACpBU,GAAG,CAACC,WAAW,EAAE,MAAM,CAAC,CACxBC,MAAM,CAAC,YAAY,CAAC,CAC1B,CAACA,MAAM,CAAC,YAAY,CAAC;QACxBX,MAAM,CAACQ,SAAS,GAAGA,SAAS;QAC5BhC,YAAY,CAAClC,YAAY,CAACkE,SAAS,CAAC,CAAC;MACvC,CAAC,MAAM;QACL,OAAOR,MAAM,CAACQ,SAAS;QACvBhC,YAAY,CAAC,EAAE,CAAC;MAClB;MAEArB,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;IAC5B;EACF,CAAC;EAED,MAAMwF,qBAAqB,GAAI7D,CAAsC,IAAK;IACxE,IAAIlE,KAAK,EAAE;MACT,MAAMyG,UAAU,GAAGvC,CAAC,CAACC,MAAM,CAACC,KAAK;QAC/BsC,IAAI,GAAG3H,eAAe,CAAC0H,UAAU,EAAE,IAAI,CAAC;MAE1C1F,YAAY,CAAC0F,UAAU,CAAC;MAExB,IAAIC,IAAI,EAAE;QACR,MAAM3D,SAAS,GAAGpF,MAAM,CAAC+I,IAAI,CAAC,CAACxD,MAAM,CAAC,YAAY,CAAC;UACjDX,MAAM,GAAG;YAAE,GAAGvC,KAAK;YAAE+C;UAAU,CAAC;QAElCrD,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;MAC5B;IACF;EACF,CAAC;EAED,MAAMyF,qBAAqB,GAAI9D,CAAsC,IAAK;IACxE,IAAIlE,KAAK,EAAE;MACT,MAAMiI,cAAc,GAAG/D,CAAC,CAACC,MAAM,CAACC,KAAK;QACnCkC,CAAC,GAAG3I,MAAM,CAACsK,cAAc,CAAC;MAE5B,IAAI3B,CAAC,CAACC,OAAO,EAAE,EAAE;QACf,MAAMxD,SAAS,GAAGuD,CAAC,CAACpD,MAAM,CAAC,YAAY,CAAC;UACtCX,MAAM,GAAG;YAAE,GAAGvC,KAAK;YAAE+C;UAAU,CAAC;QAElChC,YAAY,CAAClC,YAAY,CAACkE,SAAS,CAAC,CAAC;QAErCrD,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;MAC5B;IACF;EACF,CAAC;EAED,MAAM2F,sBAAsB,GAAIhE,CAAsC,IAAK;IACzE,IAAIlE,KAAK,EAAE;MACT,MAAMmI,eAAe,GAAGjE,CAAC,CAACC,MAAM,CAACC,KAAK;QACpCkC,CAAC,GAAG3I,MAAM,CAACwK,eAAe,CAAC;MAE7B,IAAI7B,CAAC,CAACC,OAAO,EAAE,EAAE;QACf,MAAM9C,UAAU,GAAG6C,CAAC,CAACpD,MAAM,CAAC,YAAY,CAAC;UACvCX,MAAM,GAAG;YAAE,GAAGvC,KAAK;YAAEyD;UAAW,CAAC;QAEnC/D,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;QAC1BtB,aAAa,CAACpC,YAAY,CAAC4E,UAAU,CAAC,CAAC;MACzC;IACF;EACF,CAAC;EAED,MAAM2E,sBAAsB,GAAIlE,CAAsC,IAAK;IACzE,IAAIlE,KAAK,EAAE;MACT,MAAMyG,UAAU,GAAGvC,CAAC,CAACC,MAAM,CAACC,KAAK;QAC/BsC,IAAI,GAAG3H,eAAe,CAAC0H,UAAU,EAAE,IAAI,CAAC;MAE1CxF,aAAa,CAACwF,UAAU,CAAC;MAEzB,IAAIC,IAAI,EAAE;QACR,MAAMjD,UAAU,GAAG9F,MAAM,CAAC+I,IAAI,CAAC,CAACxD,MAAM,CAAC,YAAY,CAAC;UAClDX,MAAM,GAAG;YAAE,GAAGvC,KAAK;YAAEyD;UAAW,CAAC;QAEnC/D,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;MAC5B;IACF;EACF,CAAC;EAED,MAAM8F,2BAA2B,GAC/BnE,CAAsC,IACnC;IACH,IAAIlE,KAAK,EAAE;MACT,MAAM2F,eAAe,GAAGzB,CAAC,CAACC,MAAM,CAACa,aAAa,IAAI,CAAC;QACjDzC,MAAM,GAAG;UAAE,GAAGvC,KAAK;UAAE2F;QAAgB,CAAC;MACxCjG,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;IAC5B;EACF,CAAC;EAED,MAAM+F,4BAA4B,GAChCpE,CAAsC,IACnC;IACH,IAAIlE,KAAK,EAAE;MACT,MAAMiG,gBAAgB,GAAG/B,CAAC,CAACC,MAAM,CAACa,aAAa,IAAI,CAAC;QAClDzC,MAAM,GAAG;UAAE,GAAGvC,KAAK;UAAEiG;QAAiB,CAAC;MAEzCvG,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;IAC5B;EACF,CAAC;EAED,MAAMgG,iBAAiB,GAAIrE,CAAsC,IAAK;IACpE,IAAIlE,KAAK,EAAE;MACT,MAAMwI,KAAK,GAAGtE,CAAC,CAACC,MAAM,CAACC,KAAK;QAC1B7B,MAAM,GAAG;UAAE,GAAGvC,KAAK;UAAEwI;QAAM,CAAC;MAE9B9I,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;IAC5B;EACF,CAAC;EAED,MAAMkG,mBAAmB,GAAG,CAAC9D,IAAY,EAAE+D,OAA4B,KAAK;IAC1E,IAAI1I,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEmD,SAAS,EAAE;MACpB,MAAMwF,KAAK,GAAG3I,KAAK,CAACmD,SAAS,CAACyF,SAAS,CAAEnE,CAAC,IAAKA,CAAC,CAACE,IAAI,KAAKA,IAAI,CAAC;MAE/D,IAAIgE,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,MAAMxF,SAAS,GAAGnD,KAAK,CAACmD,SAAS,CAACqB,GAAG,CAAEC,CAAC,KAAM;UAAE,GAAGA;QAAE,CAAC,CAAC,CAAC;QACxD,IAAIiE,OAAO,EAAE;UACXvF,SAAS,CAAC0F,MAAM,CAACF,KAAK,EAAE,CAAC,EAAED,OAAO,CAAC;QACrC,CAAC,MAAM;UACLvF,SAAS,CAAC0F,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;QAC5B;QAEA,MAAM/D,QAAQ,GAAGzB,SAAS,CAACtB,MAAM,CAAC,CAACC,KAAK,EAAE2C,CAAC,KAAK3C,KAAK,GAAG2C,CAAC,CAACG,QAAQ,EAAE,CAAC,CAAC;UACpErC,MAAM,GAAG;YAAE,GAAGvC,KAAK;YAAEmD,SAAS;YAAEyB;UAAS,CAAC;QAE5CK,gBAAgB,CAACL,QAAQ,EAAErC,MAAM,CAAC;MACpC;IACF;EACF,CAAC;EAED,MAAMuG,kBAAkB,GAAI5E,CAAsC,IAAK;IACrE,IAAIlE,KAAK,EAAE;MACT,MAAM+I,MAAM,GAAG7E,CAAC,CAACC,MAAM,CAACC,KAAK;QAC3B7B,MAAM,GAAG;UAAE,GAAGvC,KAAK;UAAE+I;QAAO,CAAC;MAE/BrJ,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;IAC5B;EACF,CAAC;EAED,MAAMyG,iBAAiB,GAAG,MAAMC,MAAM,EAAE;EAExC,MAAMC,eAAe,GAAG,YAAY;IAClC,IAAIrJ,IAAI,EAAE;MAAA;MACR,IAAIG,KAAK,aAALA,KAAK,qCAALA,KAAK,CAAEyB,UAAU,+CAAjB,mBAAmBkC,MAAM,IAAI/B,cAAc,KAAK5B,KAAK,CAACgC,KAAK,EAAE;QAC/DmH,KAAK,CACH,iGAAiG,CAClG;QACD;MACF;MAEA,MAAMC,MAAW,GAAG,MAAM1J,QAAQ,CAACR,SAAS,EAAE,CAAC;MAE/C,IAAI,CAACkK,MAAM,CAACC,KAAK,EAAE;QACjB,IAAI,CAACjJ,KAAK,EAAE;UACVe,wBAAwB,CAAC,IAAI,CAAC;QAChC,CAAC,MAAM;UACL8H,MAAM,EAAE;QACV;MACF;IACF;EACF,CAAC;EAED,MAAMK,iBAAiB,GAAG,YAAY;IACpC,MAAMF,MAAW,GAAG,MAAM1J,QAAQ,CAACT,WAAW,EAAE,CAAC;IAEjD,IAAI,CAACmK,MAAM,CAACC,KAAK,EAAE;MACjBJ,MAAM,EAAE;IACV;EACF,CAAC;EAED,MAAMA,MAAM,GAAG,MAAM;IACnB,IAAIM,MAAM,CAACC,OAAO,CAAC7F,MAAM,GAAG,CAAC,EAAE;MAC7BhE,QAAQ,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,MAAM;MACLA,QAAQ,CAACpB,MAAM,CAACwB,MAAM,CAAC0J,IAAI,CAAC;IAC9B;EACF,CAAC;EAED,MAAMC,gCAAgC,GACpCxF,CAAsC,IACnC;IACH7C,sBAAsB,CAAC6C,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EACxC,CAAC;EAED,MAAMuF,6BAA6B,GAAG,MAAM;IAC1CtI,sBAAsB,CAAC,EAAE,CAAC;EAC5B,CAAC;EAED,MAAMuI,4BAA4B,GAAG,MAAM;IACzCzI,wBAAwB,CAAC,KAAK,CAAC;IAC/B8H,MAAM,EAAE;EACV,CAAC;EAED,MAAMY,6BAA6B,GAAG,YAAY;IAChD,IAAI7J,KAAK,IAAIH,IAAI,EAAE;MACjB,IAAI;QACF,MAAMiK,IAAI,GAAGP,MAAM,CAACQ,QAAQ,CAACC,IAAI;UAC/BC,KAAK,GAAG;YACNpK,IAAI,EAAEA,IAAI,CAAC8E,IAAI;YACfzC,KAAK,EAAElC,KAAK,CAACkC,KAAK,CAACyC,IAAI;YACvBxC,WAAW,EAAEnC,KAAK,CAACmC,WAAW;YAC9B2H,IAAI;YACJI,OAAO,EAAE9I;UACX,CAAC;UACD+I,IAAI,GAAG;YAAEtK,IAAI;YAAEoK;UAAM,CAAC;QAExB,MAAM7L,gBAAgB,CAACgM,YAAY,CAACD,IAAI,CAAC;QAEzClB,MAAM,EAAE;MACV,CAAC,CAAC,OAAO/E,CAAC,EAAE;QACVmG,OAAO,CAAChB,KAAK,CAACnF,CAAC,CAAC;MAClB;IACF;EACF,CAAC;EAED,MAAMoG,yBAAyB,GAC7BpG,CAAsC,IACnC;IACH,IAAIlE,KAAK,EAAE;MACT,MAAMsB,aAAa,GAAG4C,CAAC,CAACC,MAAM,CAAC2C,OAAO;MAEtC,IAAIxF,aAAa,EAAE;QACjB,IAAI,CAACC,KAAK,CAACC,OAAO,CAACxB,KAAK,CAACyB,UAAU,CAAC,EAAE;UACpC,MAAM3B,EAAE,GAAGN,IAAI,EAAE;YACf+K,IAAI,GAAGvL,aAAa,CAACgB,KAAK,CAACyD,UAAU,CAAC;YACtC+G,SAAS,GAAG;cAAE1K,EAAE;cAAEyK,IAAI;cAAEvI,KAAK,EAAEhC,KAAK,CAACgC;YAAM,CAAC;YAC5CO,MAAM,GAAG;cAAE,GAAGvC,KAAK;cAAEyB,UAAU,EAAE,CAAC+I,SAAS;YAAE,CAAC;UAChD9K,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;QAC5B;MACF,CAAC,MAAM;QACL,MAAMA,MAAM,GAAG;UAAE,GAAGvC;QAAM,CAAC;QAC3B,OAAOuC,MAAM,CAACd,UAAU;QACxB/B,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;MAC5B;IACF;EACF,CAAC;EAED,MAAMkI,uBAAuB,GAAG,MAAM;IACpC,IAAIzK,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEyB,UAAU,EAAE;MACrB,MAAM3B,EAAE,GAAGN,IAAI,EAAE;QACfiC,UAAU,GAAGzB,KAAK,CAACyB,UAAU,CAC1B+C,GAAG,CAAEkG,CAAC,KAAM;UAAE,GAAGA;QAAE,CAAC,CAAC,CAAC,CACtBhG,MAAM,CAAC,CAAC;UAAE5E,EAAE;UAAEyK,IAAI,EAAE,EAAE;UAAEvI,KAAK,EAAE;QAAE,CAAC,CAAC,CAAC;QACvCO,MAAM,GAAG;UAAE,GAAGvC,KAAK;UAAEyB;QAAW,CAAC;MAEnC/B,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;IAC5B;EACF,CAAC;EAED,MAAMoI,qBAAqB,GAAG,CAAC7K,EAAU,EAAE0K,SAA2B,KAAK;IACzE,IAAIxK,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEyB,UAAU,EAAE;MACrB,MAAMA,UAAU,GAAGzB,KAAK,CAACyB,UAAU,CAAC+C,GAAG,CAAEzC,EAAE,KAAM;UAAE,GAAGA;QAAG,CAAC,CAAC,CAAC;QAC1D4G,KAAK,GAAGlH,UAAU,CAACmH,SAAS,CAAE7G,EAAE,IAAKA,EAAE,CAACjC,EAAE,KAAKA,EAAE,CAAC;MAEpD,IAAI6I,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,IAAI6B,SAAS,EAAE;UACb/I,UAAU,CAACoH,MAAM,CAACF,KAAK,EAAE,CAAC,EAAE6B,SAAS,CAAC;QACxC,CAAC,MAAM;UACL/I,UAAU,CAACoH,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;QAC7B;MACF;MAEA,MAAMpG,MAAM,GAAG;QAAE,GAAGvC,KAAK;QAAEyB;MAAW,CAAC;MAEvC/B,QAAQ,CAACN,QAAQ,CAACmD,MAAM,CAAC,CAAC;IAC5B;EACF,CAAC;EAED,oBACE;IAAK,SAAS,EAAC,wBAAwB;IAAA,wBACrC;MAAK,SAAS,EAAC,wDAAwD;MAAA,wBACrE;QAAK,SAAS,EAAC,UAAU;QAAA,uBACvB,QAAC,MAAM;UAAC,KAAK,EAAC,MAAM;UAAC,OAAO,EAAEyG,iBAAkB;UAAA,wBAC9C,QAAC,eAAe;YAAC,IAAI,EAAE,CAAC,KAAK,EAAE,cAAc;UAAE;YAAA;YAAA;YAAA;UAAA,QAAG;QAAA;UAAA;UAAA;UAAA;QAAA;MAE3C;QAAA;QAAA;QAAA;MAAA,QACL,eACN;QAAI,SAAS,EAAC,KAAK;QAAA,UAAE5I,KAAK,GAAG,WAAW,GAAGJ,KAAK,CAACmC;MAAW;QAAA;QAAA;QAAA;MAAA,QAAM;IAAA;MAAA;MAAA;MAAA;IAAA,QAC9D,EACL,CAAC,CAACnC,KAAK,iBACN;MAAA,wBACE;QAAK,SAAS,EAAC,SAAS;QAAA,wBACtB;UAAK,SAAS,EAAC,iBAAiB;UAAA,wBAC9B;YAAO,OAAO,EAAC,gBAAgB;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAiB,eAChD,QAAC,KAAK;YACJ,EAAE,EAAC,gBAAgB;YACnB,IAAI,EAAC,QAAQ;YACb,KAAK,EAAEA,KAAK,CAACqC,QAAQ,CAACD,YAAa;YACnC,QAAQ,EAAE6B,oBAAqB;YAC/B,QAAQ,EAAE,CAACvC,SAAU;YAAA,UACpBzB,SAAS,CAACuE,GAAG,CAAEnC,QAAQ,iBACtB;cAEE,KAAK,EAAEA,QAAQ,CAACD,YAAa;cAAA,UAC5BC,QAAQ,CAACsC;YAAI,GAFTtC,QAAQ,CAACD,YAAY;cAAA;cAAA;cAAA;YAAA,QAI7B;UAAC;YAAA;YAAA;YAAA;UAAA,QACI;QAAA;UAAA;UAAA;UAAA;QAAA,QACJ,eACN;UAAK,SAAS,EAAC,iBAAiB;UAAA,wBAC9B;YAAO,OAAO,EAAC,aAAa;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAc,eAC1C,QAAC,KAAK;YACJ,EAAE,EAAC,aAAa;YAChB,IAAI,EAAC,QAAQ;YACb,KAAK,EAAEpC,KAAK,CAACkC,KAAK,CAACqB,GAAI;YACvB,QAAQ,EAAEe,iBAAkB;YAC5B,QAAQ,EAAE,CAAC5C,SAAU;YAAA,UACpBvB,MAAM,CAACqE,GAAG,CAAEtC,KAAK,iBAChB;cAAwB,KAAK,EAAEA,KAAK,CAACqB,GAAI;cAAA,UACtCrB,KAAK,CAACyC;YAAI,GADAzC,KAAK,CAACqB,GAAG;cAAA;cAAA;cAAA;YAAA,QAGvB;UAAC;YAAA;YAAA;YAAA;UAAA,QACI;QAAA;UAAA;UAAA;UAAA;QAAA,QACJ;MAAA;QAAA;QAAA;QAAA;MAAA,QACF,EACL,CAACvD,KAAK,CAACmD,SAAS,iBACf;QAAK,SAAS,EAAC,SAAS;QAAA,wBACtB;UAAK,SAAS,EAAC,iBAAiB;UAAA,wBAC9B;YAAO,OAAO,EAAC,gBAAgB;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAiB,eAChD,QAAC,KAAK;YACJ,EAAE,EAAC,gBAAgB;YACnB,IAAI,EAAC,QAAQ;YACb,KAAK,EAAEnD,KAAK,CAAC4E,QAAS;YACtB,QAAQ,EAAEG,oBAAqB;YAC/B,OAAO,EAAEnG,WAAY;YACrB,SAAS,EAAC,UAAU;YACpB,QAAQ,EAAE,CAAC8C;UAAU;YAAA;YAAA;YAAA;UAAA,QACrB;QAAA;UAAA;UAAA;UAAA;QAAA,QACE,eACN;UAAK,SAAS,EAAC,iBAAiB;UAAA,wBAC9B;YAAO,OAAO,EAAC,YAAY;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAa,eACxC,QAAC,KAAK;YACJ,EAAE,EAAC,YAAY;YACf,IAAI,EAAC,QAAQ;YACb,KAAK,EAAE1B,KAAK,CAAC6E,IAAK;YAClB,QAAQ,EAAEK,gBAAiB;YAC3B,OAAO,EAAEtG,WAAY;YACrB,SAAS,EAAC,UAAU;YACpB,QAAQ,EAAE,CAAC8C;UAAU;YAAA;YAAA;YAAA;UAAA,QACrB;QAAA;UAAA;UAAA;UAAA;QAAA,QACE,eACN;UAAK,SAAS,EAAC,iBAAiB;UAAA,wBAC9B;YAAO,OAAO,EAAC,aAAa;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAc,eAC1C,QAAC,KAAK;YACJ,EAAE,EAAC,aAAa;YAChB,IAAI,EAAC,QAAQ;YACb,KAAK,EAAE1B,KAAK,CAACgC,KAAM;YACnB,QAAQ,EAAEoD,iBAAkB;YAC5B,OAAO,EAAExG,WAAY;YACrB,SAAS,EAAC,UAAU;YACpB,QAAQ,EAAE,CAAC8C;UAAU;YAAA;YAAA;YAAA;UAAA,QACrB;QAAA;UAAA;UAAA;UAAA;QAAA,QACE;MAAA;QAAA;QAAA;QAAA;MAAA,QAET,eACD;QAAK,SAAS,EAAC,kBAAkB;QAAA,wBAC/B;UAAK,SAAS,EAAC,eAAe;UAAA,wBAC5B;YAAO,OAAO,EAAC,kBAAkB;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAmB,eACpD,QAAC,KAAK;YACJ,EAAE,EAAC,kBAAkB;YACrB,KAAK,EAAEpB,SAAU;YACjB,QAAQ,EAAEkG,qBAAsB;YAChC,OAAO,EAAE5H,WAAY;YACrB,QAAQ,EAAE,CAAC8C;UAAU;YAAA;YAAA;YAAA;UAAA,QACrB,eACF,QAAC,KAAK;YACJ,EAAE,EAAC,kBAAkB;YACrB,IAAI,EAAC,MAAM;YACX,KAAK,EAAE1B,KAAK,CAACsC,SAAU;YACvB,QAAQ,EAAE8D,qBAAsB;YAChC,QAAQ,EAAE,CAAC,CAAE;YACb,QAAQ,EAAE,CAAC1E;UAAU;YAAA;YAAA;YAAA;UAAA,QACrB;QAAA;UAAA;UAAA;UAAA;QAAA,QACE,eACN;UAAK,SAAS,EAAC,eAAe;UAAA,wBAC5B;YAAO,OAAO,EAAC,kBAAkB;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAmB,eACpD,QAAC,KAAK;YACJ,EAAE,EAAC,kBAAkB;YACrB,IAAI,EAAC,QAAQ;YACb,KAAK,EAAE1B,KAAK,CAAC6C,SAAS,CAAC8B,IAAK;YAC5B,QAAQ,EAAEiC,qBAAsB;YAChC,QAAQ,EAAE,CAAClF,SAAU;YAAA,UACpBxB,KAAK,CAACsE,GAAG,CAAEoG,IAAI,iBACd;cAAwB,KAAK,EAAEA,IAAI,CAACjG,IAAK;cAAA,UACtCiG,IAAI,CAACjG;YAAI,GADCiG,IAAI,CAACjG,IAAI;cAAA;cAAA;cAAA;YAAA,QAGvB;UAAC;YAAA;YAAA;YAAA;UAAA,QACI;QAAA;UAAA;UAAA;UAAA;QAAA,QACJ,eACN;UAAK,SAAS,EAAC,eAAe;UAAA,wBAC5B;YAAO,OAAO,EAAC,mBAAmB;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAoB,eACtD,QAAC,KAAK;YACJ,EAAE,EAAC,mBAAmB;YACtB,KAAK,EAAE3D,UAAW;YAClB,QAAQ,EAAEoH,sBAAuB;YACjC,OAAO,EAAExJ,WAAY;YACrB,QAAQ,EAAE,CAAC8C;UAAU;YAAA;YAAA;YAAA;UAAA,QACrB,eACF,QAAC,KAAK;YACJ,EAAE,EAAC,mBAAmB;YACtB,IAAI,EAAC,MAAM;YACX,KAAK,EAAE1B,KAAK,CAACyD,UAAW;YACxB,QAAQ,EAAEyE,sBAAuB;YACjC,QAAQ,EAAE,CAAC,CAAE;YACb,QAAQ,EAAE,CAACxG;UAAU;YAAA;YAAA;YAAA;UAAA,QACrB;QAAA;UAAA;UAAA;UAAA;QAAA,QACE,eACN;UAAK,SAAS,EAAC,eAAe;UAAA,wBAC5B;YAAO,OAAO,EAAC,yBAAyB;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAuB,eAC/D,QAAC,KAAK;YACJ,EAAE,EAAC,yBAAyB;YAC5B,IAAI,EAAC,QAAQ;YACb,KAAK,EAAE1B,KAAK,CAAC2F,eAAgB;YAC7B,QAAQ,EAAE0C,2BAA4B;YACtC,OAAO,EAAEzJ,WAAY;YACrB,QAAQ,EAAE,CAAC8C;UAAU;YAAA;YAAA;YAAA;UAAA,QACrB;QAAA;UAAA;UAAA;UAAA;QAAA,QACE;MAAA;QAAA;QAAA;QAAA;MAAA,QACF,eACN;QAAK,SAAS,EAAC,SAAS;QAAA,wBACtB;UAAK,SAAS,EAAC,eAAe;UAAA,uBAC5B,QAAC,SAAS;YAAC,KAAK;YAAA,wBACd,QAAC,KAAK;cACJ,EAAE,EAAC,yBAAyB;cAC5B,IAAI,EAAC,UAAU;cACf,OAAO,EAAE1B,KAAK,CAAC+F,UAAW;cAC1B,QAAQ,EAAEqB,sBAAuB;cACjC,QAAQ,EAAE,CAAC1F;YAAU;cAAA;cAAA;cAAA;YAAA,QACrB,eACF;cAAO,OAAO,EAAC,yBAAyB;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAEhC;UAAA;YAAA;YAAA;YAAA;UAAA;QACE;UAAA;UAAA;UAAA;QAAA,QACR,EACL1B,KAAK,CAAC+F,UAAU,iBACf;UAAA,wBACE;YAAK,SAAS,EAAC,eAAe;YAAA,wBAC5B;cAAO,OAAO,EAAC,kBAAkB;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAAmB,eACpD,QAAC,KAAK;cACJ,EAAE,EAAC,kBAAkB;cACrB,KAAK,EAAEnF,aAAc;cACrB,QAAQ,EAAE2G,yBAA0B;cACpC,OAAO,EAAE3I,WAAY;cACrB,QAAQ,EAAE,CAAC8C;YAAU;cAAA;cAAA;cAAA;YAAA,QACrB,eACF,QAAC,KAAK;cACJ,EAAE,EAAC,kBAAkB;cACrB,IAAI,EAAC,MAAM;cACX,KAAK,EAAE1B,KAAK,CAAC0C,aAAc;cAC3B,QAAQ,EAAE2E,yBAA0B;cACpC,QAAQ,EAAE,CAAC,CAAE;cACb,QAAQ,EAAE,CAAC3F;YAAU;cAAA;cAAA;cAAA;YAAA,QACrB;UAAA;YAAA;YAAA;YAAA;UAAA,QACE,eACN;YAAK,SAAS,EAAC,eAAe;YAAA,wBAC5B;cAAO,OAAO,EAAC,uBAAuB;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAAwB,eAC9D,QAAC,KAAK;cACJ,EAAE,EAAC,uBAAuB;cAC1B,IAAI,EAAC,QAAQ;cACb,KAAK,EAAE,yBAAA1B,KAAK,CAAC4C,aAAa,yDAAnB,qBAAqB+B,IAAI,KAAI,EAAG;cACvC,QAAQ,EAAE6C,yBAA0B;cACpC,QAAQ,EAAE,CAAC9F,SAAU;cAAA,UACpBxB,KAAK,CAACsE,GAAG,CAAEoG,IAAI,iBACd;gBAAwB,KAAK,EAAEA,IAAI,CAACjG,IAAK;gBAAA,UACtCiG,IAAI,CAACjG;cAAI,GADCiG,IAAI,CAACjG,IAAI;gBAAA;gBAAA;gBAAA;cAAA,QAGvB;YAAC;cAAA;cAAA;cAAA;YAAA,QACI;UAAA;YAAA;YAAA;YAAA;UAAA,QACJ,eACN;YAAK,SAAS,EAAC,eAAe;YAAA,wBAC5B;cAAO,OAAO,EAAC,0BAA0B;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAEjC,eACR,QAAC,KAAK;cACJ,EAAE,EAAC,0BAA0B;cAC7B,IAAI,EAAC,QAAQ;cACb,KAAK,EAAE3E,KAAK,CAACiG,gBAAiB;cAC9B,QAAQ,EAAEqC,4BAA6B;cACvC,OAAO,EAAE1J,WAAY;cACrB,QAAQ,EAAE,CAAC8C;YAAU;cAAA;cAAA;cAAA;YAAA,QACrB;UAAA;YAAA;YAAA;YAAA;UAAA,QACE;QAAA,gBAET;MAAA;QAAA;QAAA;QAAA;MAAA,QACG,eACN;QAAK,SAAS,EAAC,kBAAkB;QAAA,wBAC/B;UAAK,SAAS,EAAC,eAAe;UAAA,uBAC5B,QAAC,SAAS;YAAC,KAAK;YAAA,wBACd,QAAC,KAAK;cACJ,EAAE,EAAC,sBAAsB;cACzB,IAAI,EAAC,UAAU;cACf,OAAO,EAAE1B,KAAK,CAACwC,YAAa;cAC5B,QAAQ,EAAEiF,wBAAyB;cACnC,QAAQ,EAAE,CAAC/F;YAAU;cAAA;cAAA;cAAA;YAAA,QACrB,eACF;cAAO,OAAO,EAAC,sBAAsB;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAE7B;UAAA;YAAA;YAAA;YAAA;UAAA;QACE;UAAA;UAAA;UAAA;QAAA,QACR,EACL1B,KAAK,CAACwC,YAAY,iBACjB;UAAA,wBACE;YAAK,SAAS,EAAC,eAAe;YAAA,wBAC5B;cAAO,OAAO,EAAC,uBAAuB;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAAwB,eAC9D,QAAC,KAAK;cACJ,EAAE,EAAC,uBAAuB;cAC1B,KAAK,EAAEhC,aAAc;cACrB,QAAQ,EAAEoH,yBAA0B;cACpC,OAAO,EAAEhJ,WAAY;cACrB,QAAQ,EAAE,CAAC8C;YAAU;cAAA;cAAA;cAAA;YAAA,QACrB,eACF,QAAC,KAAK;cACJ,EAAE,EAAC,uBAAuB;cAC1B,IAAI,EAAC,MAAM;cACX,KAAK,EAAE1B,KAAK,CAACyC,aAAc;cAC3B,QAAQ,EAAEiF,yBAA0B;cACpC,QAAQ,EAAE,CAAC,CAAE;cACb,QAAQ,EAAE,CAAChG;YAAU;cAAA;cAAA;cAAA;YAAA,QACrB;UAAA;YAAA;YAAA;YAAA;UAAA,QACE,eACN;YAAK,SAAS,EAAC,eAAe;YAAA,wBAC5B;cAAO,OAAO,EAAC,uBAAuB;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAAwB,eAC9D,QAAC,KAAK;cACJ,EAAE,EAAC,uBAAuB;cAC1B,IAAI,EAAC,QAAQ;cACb,KAAK,EAAE,yBAAA1B,KAAK,CAAC2C,aAAa,yDAAnB,qBAAqBgC,IAAI,KAAI,EAAG;cACvC,QAAQ,EAAEkD,yBAA0B;cACpC,QAAQ,EAAE,CAACnG,SAAU;cAAA,UACpBxB,KAAK,CAACsE,GAAG,CAAEoG,IAAI,iBACd;gBAAwB,KAAK,EAAEA,IAAI,CAACjG,IAAK;gBAAA,UACtCiG,IAAI,CAACjG;cAAI,GADCiG,IAAI,CAACjG,IAAI;gBAAA;gBAAA;gBAAA;cAAA,QAGvB;YAAC;cAAA;cAAA;cAAA;YAAA,QACI;UAAA;YAAA;YAAA;YAAA;UAAA,QACJ,eACN;YAAK,SAAS,EAAC,QAAQ;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAa;QAAA,gBAEvC;MAAA;QAAA;QAAA;QAAA;MAAA,QACG,eACN;QAAK,SAAS,EAAC,SAAS;QAAA,wBACtB;UAAK,SAAS,EAAC,eAAe;UAAA,uBAC5B,QAAC,SAAS;YAAC,KAAK;YAAA,wBACd,QAAC,KAAK;cACJ,EAAE,EAAC,yBAAyB;cAC5B,IAAI,EAAC,UAAU;cACf,OAAO,EAAE3E,KAAK,CAAC4F,eAAgB;cAC/B,QAAQ,EAAEiB,2BAA4B;cACtC,QAAQ,EAAE,CAACnF;YAAU;cAAA;cAAA;cAAA;YAAA,QACrB,eACF;cAAO,OAAO,EAAC,yBAAyB;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAEhC;UAAA;YAAA;YAAA;YAAA;UAAA;QACE;UAAA;UAAA;UAAA;QAAA,QACR,EACL1B,KAAK,CAAC4F,eAAe,iBACpB;UAAA,wBACE;YAAK,SAAS,EAAC,eAAe;YAAA,wBAC5B;cAAO,OAAO,EAAC,0BAA0B;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAEjC,eACR,QAAC,KAAK;cACJ,EAAE,EAAC,0BAA0B;cAC7B,KAAK,EAAElF,gBAAiB;cACxB,QAAQ,EAAEqG,4BAA6B;cACvC,OAAO,EAAEnI,WAAY;cACrB,QAAQ,EAAE,CAAC8C;YAAU;cAAA;cAAA;cAAA;YAAA,QACrB,eACF,QAAC,KAAK;cACJ,EAAE,EAAC,0BAA0B;cAC7B,IAAI,EAAC,MAAM;cACX,KAAK,EAAE1B,KAAK,CAACwD,gBAAiB;cAC9B,QAAQ,EAAEwD,4BAA6B;cACvC,QAAQ,EAAE,CAAC,CAAE;cACb,QAAQ,EAAE,CAACtF;YAAU;cAAA;cAAA;cAAA;YAAA,QACrB;UAAA;YAAA;YAAA;YAAA;UAAA,QACE,eACN;YAAK,SAAS,EAAC,eAAe;YAAA,wBAC5B;cAAO,OAAO,EAAC,0BAA0B;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAEjC,eACR,QAAC,KAAK;cACJ,EAAE,EAAC,0BAA0B;cAC7B,IAAI,EAAC,QAAQ;cACb,KAAK,EAAE,0BAAA1B,KAAK,CAAC4D,gBAAgB,0DAAtB,sBAAwBe,IAAI,KAAI,EAAG;cAC1C,QAAQ,EAAEuC,4BAA6B;cACvC,QAAQ,EAAE,CAACxF,SAAU;cAAA,UACpBxB,KAAK,CAACsE,GAAG,CAAEoG,IAAI,iBACd;gBAAwB,KAAK,EAAEA,IAAI,CAACjG,IAAK;gBAAA,UACtCiG,IAAI,CAACjG;cAAI,GADCiG,IAAI,CAACjG,IAAI;gBAAA;gBAAA;gBAAA;cAAA,QAGvB;YAAC;cAAA;cAAA;cAAA;YAAA,QACI;UAAA;YAAA;YAAA;YAAA;UAAA,QACJ,eACN;YAAK,SAAS,EAAC,eAAe;YAAA,wBAC5B;cAAO,OAAO,EAAC,oCAAoC;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAE3C,eACR,QAAC,KAAK;cACJ,EAAE,EAAC,oCAAoC;cACvC,IAAI,EAAC,QAAQ;cACb,KAAK,EAAE3E,KAAK,CAAC8F,yBAA0B;cACvC,QAAQ,EAAEqB,qCAAsC;cAChD,OAAO,EAAEvI,WAAY;cACrB,QAAQ,EAAE,CAAC8C;YAAU;cAAA;cAAA;cAAA;YAAA,QACrB;UAAA;YAAA;YAAA;YAAA;UAAA,QACE;QAAA,gBAET;MAAA;QAAA;QAAA;QAAA;MAAA,QACG,eACN;QAAK,SAAS,EAAC,kBAAkB;QAAA,wBAC/B;UAAK,SAAS,EAAC,eAAe;UAAA,uBAC5B,QAAC,SAAS;YAAC,KAAK;YAAA,wBACd,QAAC,KAAK;cACJ,EAAE,EAAC,oBAAoB;cACvB,IAAI,EAAC,UAAU;cACf,OAAO,EAAE1B,KAAK,CAAC8C,WAAY;cAC3B,QAAQ,EAAEgF,uBAAwB;cAClC,QAAQ,EAAE,CAACpG;YAAU;cAAA;cAAA;cAAA;YAAA,QACrB,eACF;cAAO,OAAO,EAAC,oBAAoB;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAE3B;UAAA;YAAA;YAAA;YAAA;UAAA;QACE;UAAA;UAAA;UAAA;QAAA,QACR,EACL1B,KAAK,CAAC8C,WAAW,iBAChB;UAAA,wBACE;YAAK,SAAS,EAAC,eAAe;YAAA,wBAC5B;cAAO,OAAO,EAAC,kBAAkB;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAAmB,eACpD,QAAC,KAAK;cACJ,EAAE,EAAC,kBAAkB;cACrB,KAAK,EAAEhC,SAAU;cACjB,QAAQ,EAAEiH,qBAAsB;cAChC,OAAO,EAAEnJ,WAAY;cACrB,QAAQ,EAAE,CAAC8C;YAAU;cAAA;cAAA;cAAA;YAAA,QACrB,eACF,QAAC,KAAK;cACJ,EAAE,EAAC,kBAAkB;cACrB,IAAI,EAAC,MAAM;cACX,KAAK,EAAE1B,KAAK,CAAC+C,SAAU;cACvB,QAAQ,EAAEiF,qBAAsB;cAChC,QAAQ,EAAE,CAAC,CAAE;cACb,QAAQ,EAAE,CAACtG;YAAU;cAAA;cAAA;cAAA;YAAA,QACrB;UAAA;YAAA;YAAA;YAAA;UAAA,QACE,eACN;YAAK,SAAS,EAAC,cAAc;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAa,eAC1C;YAAK,SAAS,EAAC,cAAc;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAa;QAAA,gBAE7C;MAAA;QAAA;QAAA;QAAA;MAAA,QACG,eACN;QAAK,SAAS,EAAC,SAAS;QAAA,wBACtB;UAAK,SAAS,EAAC,eAAe;UAAA,wBAC5B;YAAO,OAAO,EAAC,0BAA0B;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAsB,eAC/D,QAAC,KAAK;YACJ,EAAE,EAAC,0BAA0B;YAC7B,KAAK,EAAE1B,KAAK,CAACmG,gBAAiB;YAC9B,QAAQ,EAAED,4BAA6B;YACvC,QAAQ,EAAE,CAACxE;UAAU;YAAA;YAAA;YAAA;UAAA,QACrB;QAAA;UAAA;UAAA;UAAA;QAAA,QACE,eACN;UAAK,SAAS,EAAC,eAAe;UAAA,wBAC5B;YAAO,OAAO,EAAC,aAAa;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAc,eAC1C,QAAC,KAAK;YACJ,EAAE,EAAC,aAAa;YAChB,IAAI,EAAC,UAAU;YACf,KAAK,EAAE1B,KAAK,CAACwI,KAAK,IAAI,EAAG;YACzB,QAAQ,EAAED,iBAAkB;YAC5B,QAAQ,EAAE,CAAC7G;UAAU;YAAA;YAAA;YAAA;UAAA,QACrB;QAAA;UAAA;UAAA;UAAA;QAAA,QACE,eACN;UAAK,SAAS,EAAC,eAAe;UAAA,wBAC5B;YAAO,OAAO,EAAC,cAAc;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAe,eAC5C,QAAC,KAAK;YACJ,EAAE,EAAC,cAAc;YACjB,KAAK,EAAE1B,KAAK,CAAC+I,MAAM,IAAI,EAAG;YAC1B,QAAQ,EAAED,kBAAmB;YAC7B,QAAQ,EAAE,CAACpH;UAAU;YAAA;YAAA;YAAA;UAAA,QACrB;QAAA;UAAA;UAAA;UAAA;QAAA,QACE;MAAA;QAAA;QAAA;QAAA;MAAA,QACF,EACL,CAAC,CAAC1B,KAAK,CAACmD,SAAS,iBAChB;QAAA,wBACE;UAAK,SAAS,EAAC,KAAK;UAAA,uBAClB;YAAI,SAAS,EAAC,KAAK;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA;QAAe;UAAA;UAAA;UAAA;QAAA,QAC9B,eACN;UAAK,SAAS,EAAC,aAAa;UAAA,wBAC1B;YAAK,SAAS,EAAC,yCAAyC;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAElD,eACN;YAAK,SAAS,EAAC,6BAA6B;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAe,eAC3D;YAAK,SAAS,EAAC,6BAA6B;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAW,eACvD;YAAK,SAAS,EAAC,6BAA6B;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAY,eACxD;YAAK,SAAS,EAAC,6BAA6B;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAc;QAAA;UAAA;UAAA;UAAA;QAAA,QACtD,EACLnD,KAAK,CAACmD,SAAS,CAACqB,GAAG,CAAEkE,OAAO,iBAC3B,QAAC,OAAO;UAEN,OAAO,EAAEA,OAAQ;UACjB,QAAQ,EAAGjE,CAAC,IAAKgE,mBAAmB,CAACC,OAAO,CAAC/D,IAAI,EAAEF,CAAC;QAAE,GAFjDiE,OAAO,CAAC/D,IAAI;UAAA;UAAA;UAAA;QAAA,QAIpB,CAAC,eACF;UAAK,SAAS,EAAC,uBAAuB;UAAA,WACnCjD,SAAS,iBACR;YAAK,SAAS,EAAC,mBAAmB;YAAA,uBAChC,QAAC,MAAM;cACL,KAAK,EAAC,SAAS;cACf,IAAI,EAAC,IAAI;cACT,OAAO;cACP,OAAO,EAAE6C,qBAAsB;cAC/B,SAAS,EAAC,MAAM;cAAA,uBAChB,QAAC,eAAe;gBAAC,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM;cAAE;gBAAA;gBAAA;gBAAA;cAAA;YAAG;cAAA;cAAA;cAAA;YAAA;UACnC;YAAA;YAAA;YAAA;UAAA,QAEZ,eACD;YAAK,SAAS,EAAC,6BAA6B;YAAA,uBAC1C,QAAC,KAAK;cACJ,EAAE,EAAC,gBAAgB;cACnB,KAAK,EAAEzF,YAAY,CAACkB,KAAK,CAAC4E,QAAQ,CAAE;cACpC,QAAQ;cACR,SAAS;cACT,SAAS,EAAC;YAAmB;cAAA;cAAA;cAAA;YAAA;UAC7B;YAAA;YAAA;YAAA;UAAA,QACE,eACN;YAAK,SAAS,EAAC,iBAAiB;YAAA,uBAC9B,QAAC,KAAK;cACJ,EAAE,EAAC,YAAY;cACf,KAAK,EAAE9F,YAAY,CAACkB,KAAK,CAAC6E,IAAI,CAAE;cAChC,QAAQ;cACR,SAAS;cACT,SAAS,EAAC;YAAmB;cAAA;cAAA;cAAA;YAAA;UAC7B;YAAA;YAAA;YAAA;UAAA,QACE,eACN;YAAK,SAAS,EAAC,iBAAiB;YAAA,uBAC9B,QAAC,KAAK;cACJ,EAAE,EAAC,aAAa;cAChB,KAAK,EAAE/F,YAAY,CAACkB,KAAK,CAACgC,KAAK,CAAE;cACjC,QAAQ;cACR,SAAS;cACT,SAAS,EAAC;YAAmB;cAAA;cAAA;cAAA;YAAA;UAC7B;YAAA;YAAA;YAAA;UAAA,QACE;QAAA;UAAA;UAAA;UAAA;QAAA,QACF;MAAA,gBAET,eACD;QAAK,SAAS,EAAC,UAAU;QAAA,uBACvB;UAAI,SAAS,EAAC,KAAK;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA;MAAW;QAAA;QAAA;QAAA;MAAA,QAC1B,eACN;QAAK,SAAS,EAAC,KAAK;QAAA,uBAClB,QAAC,SAAS;UAAC,KAAK;UAAC,SAAS,EAAC,KAAK;UAAA,wBAC9B,QAAC,KAAK;YACJ,EAAE,EAAC,uBAAuB;YAC1B,IAAI,EAAC,UAAU;YACf,OAAO,EAAEV,aAAc;YACvB,QAAQ,EAAEgJ,yBAA0B;YACpC,QAAQ,EAAE,CAAC5I;UAAU;YAAA;YAAA;YAAA;UAAA,QACrB,eACF;YAAO,OAAO,EAAC,uBAAuB;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAE9B;QAAA;UAAA;UAAA;UAAA;QAAA;MACE;QAAA;QAAA;QAAA;MAAA,QACR,EACLJ,aAAa,iBACZ;QAAA,wBACE;UAAK,SAAS,EAAC,aAAa;UAAA,wBAC1B;YAAK,SAAS,EAAC,yCAAyC;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAElD,eACN;YAAK,SAAS,EAAC,6BAA6B;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAY;QAAA;UAAA;UAAA;UAAA;QAAA,QACpD,wBACLtB,KAAK,CAACyB,UAAU,uDAAhB,mBAAkB+C,GAAG,CAAEgG,SAAS,iBAC/B,QAAC,YAAY;UAEX,SAAS,EAAEA,SAAU;UACrB,QAAQ,EAAEG;QAAsB,GAF3BH,SAAS,CAAC1K,EAAE;UAAA;UAAA;UAAA;QAAA,QAIpB,CAAC,eACF;UAAK,SAAS,EAAC,uBAAuB;UAAA,wBACpC;YAAK,SAAS,EAAC,mBAAmB;YAAA,UAC/B4B,SAAS,iBACR,QAAC,MAAM;cACL,KAAK,EAAC,SAAS;cACf,IAAI,EAAC,IAAI;cACT,OAAO;cACP,OAAO,EAAE+I,uBAAwB;cACjC,SAAS,EAAC,MAAM;cAAA,uBAChB,QAAC,eAAe;gBAAC,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM;cAAE;gBAAA;gBAAA;gBAAA;cAAA;YAAG;cAAA;cAAA;cAAA;YAAA;UAE7C;YAAA;YAAA;YAAA;UAAA,QACG,eACN;YAAK,SAAS,EAAC,6BAA6B;YAAA,uBAC1C,QAAC,KAAK;cACJ,EAAE,EAAC,mBAAmB;cACtB,KAAK,EAAE3L,YAAY,CAAC8C,cAAc,CAAE;cACpC,QAAQ;cACR,SAAS;cACT,SAAS,EAAC;YAAmB;cAAA;cAAA;cAAA;YAAA;UAC7B;YAAA;YAAA;YAAA;UAAA,QACE;QAAA;UAAA;UAAA;UAAA;QAAA,QACF;MAAA,gBAET,eAED;QAAK,SAAS,EAAC,iDAAiD;QAAA,WAC7D,CAACxB,KAAK,IAAIuB,SAAS,iBAClB;UAAK,SAAS,EAAC,UAAU;UAAA,uBACvB,QAAC,MAAM;YACL,OAAO,EAAE2H,iBAAkB;YAC3B,OAAO;YACP,KAAK,EAAC,QAAQ;YACd,IAAI,EAAC,IAAI;YACT,SAAS,EAAC,SAAS;YAAA,wBACnB,QAAC,eAAe;cAAC,IAAI,EAAE,CAAC,KAAK,EAAE,OAAO;YAAE;cAAA;cAAA;cAAA;YAAA,QAAG;UAAA;YAAA;YAAA;YAAA;UAAA;QAEpC;UAAA;UAAA;UAAA;QAAA,QAEZ,eACD;UAAK,SAAS,EAAC,cAAc;UAAA,wBAC3B,QAAC,MAAM;YAAC,OAAO;YAAC,IAAI,EAAC,IAAI;YAAC,OAAO,EAAEN,iBAAkB;YAAA,UAClDtH,SAAS,GAAG,QAAQ,GAAG;UAAO;YAAA;YAAA;YAAA;UAAA,QACxB,EACRA,SAAS,iBACR;YAAA,gCAEE,QAAC,MAAM;cAAC,OAAO,EAAEwH,eAAgB;cAAC,KAAK,EAAC,SAAS;cAAC,IAAI,EAAC,IAAI;cAAA,wBACzD,QAAC,eAAe;gBAAC,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM;cAAE;gBAAA;gBAAA;gBAAA;cAAA,QAAG;YAAA;cAAA;cAAA;cAAA;YAAA,QAEnC;UAAA,gBAEZ;QAAA;UAAA;UAAA;UAAA;QAAA,QACG;MAAA;QAAA;QAAA;QAAA;MAAA,QACF;IAAA,gBAET,eACD,QAAC,KAAK;MACJ,MAAM,EAAEhI,qBAAsB;MAC9B,MAAM,EAAE0I,4BAA6B;MACrC,QAAQ,EAAED,6BAA8B;MACxC,SAAS,EAAE,KAAM;MAAA,wBACjB,QAAC,WAAW;QAAC,MAAM,EAAEC,4BAA6B;QAAA;MAAA;QAAA;QAAA;QAAA;MAAA,QAEpC,eACd,QAAC,SAAS;QAAA,wBACR;UAAG,SAAS,EAAC,aAAa;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAEtB,eAEJ;UAAO,OAAO,EAAC,qBAAqB;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAAiB,eACrD,QAAC,KAAK;UACJ,EAAE,EAAC,qBAAqB;UACxB,MAAM,EAAC,IAAI;UACX,KAAK,EAAExI,mBAAoB;UAC3B,QAAQ,EAAEsI,gCAAiC;UAC3C,SAAS;QAAA;UAAA;UAAA;UAAA;QAAA,QACT;MAAA;QAAA;QAAA;QAAA;MAAA,QACQ,eACZ,QAAC,WAAW;QAAA,wBACV,QAAC,MAAM;UAAC,OAAO;UAAC,OAAO,EAAEE,4BAA6B;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAE7C,eACT,QAAC,MAAM;UACL,KAAK,EAAC,SAAS;UACf,OAAO;UACP,OAAO,EAAEC,6BAA8B;UAAA,wBACvC,QAAC,eAAe;YAAC,IAAI,EAAE,CAAC,KAAK,EAAE,aAAa;UAAE;YAAA;YAAA;YAAA;UAAA,QAAG;QAAA;UAAA;UAAA;UAAA;QAAA,QAE1C;MAAA;QAAA;QAAA;QAAA;MAAA,QACG;IAAA;MAAA;MAAA;MAAA;IAAA,QACR;EAAA;IAAA;IAAA;IAAA;EAAA,QACJ;AAEV;AAAC,GAl5CepK,MAAM;EAAA,QACHjC,WAAW,EACfE,WAAW,EACDc,OAAO,EACnBf,SAAS,EACTF,WAAW,EACZA,WAAW,EACPA,WAAW,EACfA,WAAW,EACVA,WAAW;AAAA;AAAA,KATRkC,MAAM;AAo5CtB,eAAeA,MAAM;AAAC;AAAA"}, "metadata": {}, "sourceType": "module"}