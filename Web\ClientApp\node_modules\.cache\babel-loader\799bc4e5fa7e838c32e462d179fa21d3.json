{"ast": null, "code": "import moment from 'moment';\nexport function weekDisplay(date) {\n  if (!date) {\n    return '';\n  }\n  const m = moment(date);\n  if (!m.isValid()) {\n    return '';\n  }\n  return `Week ${m.isoWeek()}, ${m.isoWeekYear()}`;\n}", "map": {"version": 3, "names": ["moment", "weekDisplay", "date", "m", "<PERSON><PERSON><PERSON><PERSON>", "isoWeek", "isoWeekYear"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/utils/weeks.ts"], "sourcesContent": ["import moment from 'moment';\r\n\r\nexport function weekDisplay(date: string | null | undefined) {\r\n  if(!date) {\r\n    return '';\r\n  }\r\n\r\n  const m = moment(date);\r\n  if(!m.isValid()) {\r\n    return '';\r\n  }\r\n\r\n  return `Week ${m.isoWeek()}, ${m.isoWeekYear()}`;\r\n}\r\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,QAAQ;AAE3B,OAAO,SAASC,WAAW,CAACC,IAA+B,EAAE;EAC3D,IAAG,CAACA,IAAI,EAAE;IACR,OAAO,EAAE;EACX;EAEA,MAAMC,CAAC,GAAGH,MAAM,CAACE,IAAI,CAAC;EACtB,IAAG,CAACC,CAAC,CAACC,OAAO,EAAE,EAAE;IACf,OAAO,EAAE;EACX;EAEA,OAAQ,QAAOD,CAAC,CAACE,OAAO,EAAG,KAAIF,CAAC,CAACG,WAAW,EAAG,EAAC;AAClD"}, "metadata": {}, "sourceType": "module"}