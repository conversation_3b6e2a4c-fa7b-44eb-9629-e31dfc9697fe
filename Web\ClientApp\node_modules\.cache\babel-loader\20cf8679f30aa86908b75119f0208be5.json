{"ast": null, "code": "import { api } from './api-base';\nimport { getBasicAuth } from './auth-service';\nclass NotificationsServer {\n  orderChanged(_ref) {\n    let {\n      model,\n      user\n    } = _ref;\n    const authorization = getBasicAuth(user.name, user.password),\n      headers = {\n        Authorization: authorization\n      };\n    api.post('/notifications/order', model, {\n      headers\n    });\n  }\n}\nexport const notificationsApi = new NotificationsServer();", "map": {"version": 3, "names": ["api", "getBasicAuth", "NotificationsServer", "orderChanged", "model", "user", "authorization", "name", "password", "headers", "Authorization", "post", "notificationsApi"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/api/notifications-service.ts"], "sourcesContent": ["import { api } from './api-base';\r\nimport { getBasicAuth } from './auth-service';\r\nimport { UserInfo } from './models/auth';\r\nimport * as models from './models/notifications';\r\n\r\nexport interface OrderChangedArgs {\r\n  model: models.OrderChangedModel;\r\n  user: UserInfo;\r\n}\r\n\r\nclass NotificationsServer {\r\n  orderChanged({model, user}: OrderChangedArgs) {\r\n    const authorization = getBasicAuth(user.name, user.password),\r\n      headers = { Authorization: authorization};\r\n      \r\n    api.post('/notifications/order', model, { headers });\r\n  }\r\n}\r\n\r\nexport const notificationsApi = new NotificationsServer();\r\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,YAAY;AAChC,SAASC,YAAY,QAAQ,gBAAgB;AAS7C,MAAMC,mBAAmB,CAAC;EACxBC,YAAY,OAAkC;IAAA,IAAjC;MAACC,KAAK;MAAEC;IAAsB,CAAC;IAC1C,MAAMC,aAAa,GAAGL,YAAY,CAACI,IAAI,CAACE,IAAI,EAAEF,IAAI,CAACG,QAAQ,CAAC;MAC1DC,OAAO,GAAG;QAAEC,aAAa,EAAEJ;MAAa,CAAC;IAE3CN,GAAG,CAACW,IAAI,CAAC,sBAAsB,EAAEP,KAAK,EAAE;MAAEK;IAAQ,CAAC,CAAC;EACtD;AACF;AAEA,OAAO,MAAMG,gBAAgB,GAAG,IAAIV,mBAAmB,EAAE"}, "metadata": {}, "sourceType": "module"}