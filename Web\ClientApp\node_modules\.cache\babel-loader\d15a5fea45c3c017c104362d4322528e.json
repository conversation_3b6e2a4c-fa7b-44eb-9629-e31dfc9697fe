{"ast": null, "code": "import warning from '../utils/warning';\nfunction verify(selector, methodName, displayName) {\n  if (!selector) {\n    throw new Error(\"Unexpected value for \" + methodName + \" in \" + displayName + \".\");\n  } else if (methodName === 'mapStateToProps' || methodName === 'mapDispatchToProps') {\n    if (!Object.prototype.hasOwnProperty.call(selector, 'dependsOnOwnProps')) {\n      warning(\"The selector for \" + methodName + \" of \" + displayName + \" did not specify a value for dependsOnOwnProps.\");\n    }\n  }\n}\nexport default function verifySubselectors(mapStateToProps, mapDispatchToProps, mergeProps, displayName) {\n  verify(mapStateToProps, 'mapStateToProps', displayName);\n  verify(mapDispatchToProps, 'mapDispatchToProps', displayName);\n  verify(mergeProps, 'mergeProps', displayName);\n}", "map": {"version": 3, "names": ["warning", "verify", "selector", "methodName", "displayName", "Error", "Object", "prototype", "hasOwnProperty", "call", "verifySubselectors", "mapStateToProps", "mapDispatchToProps", "mergeProps"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/node_modules/react-redux/es/connect/verifySubselectors.js"], "sourcesContent": ["import warning from '../utils/warning';\n\nfunction verify(selector, methodName, displayName) {\n  if (!selector) {\n    throw new Error(\"Unexpected value for \" + methodName + \" in \" + displayName + \".\");\n  } else if (methodName === 'mapStateToProps' || methodName === 'mapDispatchToProps') {\n    if (!Object.prototype.hasOwnProperty.call(selector, 'dependsOnOwnProps')) {\n      warning(\"The selector for \" + methodName + \" of \" + displayName + \" did not specify a value for dependsOnOwnProps.\");\n    }\n  }\n}\n\nexport default function verifySubselectors(mapStateToProps, mapDispatchToProps, mergeProps, displayName) {\n  verify(mapStateToProps, 'mapStateToProps', displayName);\n  verify(mapDispatchToProps, 'mapDispatchToProps', displayName);\n  verify(mergeProps, 'mergeProps', displayName);\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,kBAAkB;AAEtC,SAASC,MAAM,CAACC,QAAQ,EAAEC,UAAU,EAAEC,WAAW,EAAE;EACjD,IAAI,CAACF,QAAQ,EAAE;IACb,MAAM,IAAIG,KAAK,CAAC,uBAAuB,GAAGF,UAAU,GAAG,MAAM,GAAGC,WAAW,GAAG,GAAG,CAAC;EACpF,CAAC,MAAM,IAAID,UAAU,KAAK,iBAAiB,IAAIA,UAAU,KAAK,oBAAoB,EAAE;IAClF,IAAI,CAACG,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,QAAQ,EAAE,mBAAmB,CAAC,EAAE;MACxEF,OAAO,CAAC,mBAAmB,GAAGG,UAAU,GAAG,MAAM,GAAGC,WAAW,GAAG,iDAAiD,CAAC;IACtH;EACF;AACF;AAEA,eAAe,SAASM,kBAAkB,CAACC,eAAe,EAAEC,kBAAkB,EAAEC,UAAU,EAAET,WAAW,EAAE;EACvGH,MAAM,CAACU,eAAe,EAAE,iBAAiB,EAAEP,WAAW,CAAC;EACvDH,MAAM,CAACW,kBAAkB,EAAE,oBAAoB,EAAER,WAAW,CAAC;EAC7DH,MAAM,CAACY,UAAU,EAAE,YAAY,EAAET,WAAW,CAAC;AAC/C"}, "metadata": {}, "sourceType": "module"}