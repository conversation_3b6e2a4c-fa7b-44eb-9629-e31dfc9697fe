{"ast": null, "code": "import { library } from '@fortawesome/fontawesome-svg-core';\nimport { fat } from '@fortawesome/pro-thin-svg-icons';\nlibrary.add(fat);", "map": {"version": 3, "names": ["library", "fat", "add"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/boot/font-awesome.ts"], "sourcesContent": ["import {library} from '@fortawesome/fontawesome-svg-core';\r\nimport {fat} from '@fortawesome/pro-thin-svg-icons';\r\n\r\nlibrary.add(fat);\r\n"], "mappings": "AAAA,SAAQA,OAAO,QAAO,mCAAmC;AACzD,SAAQC,GAAG,QAAO,iCAAiC;AAEnDD,OAAO,CAACE,GAAG,CAACD,GAAG,CAAC"}, "metadata": {}, "sourceType": "module"}