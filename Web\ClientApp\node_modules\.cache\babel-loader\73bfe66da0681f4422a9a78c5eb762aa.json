{"ast": null, "code": "import moment from 'moment';\nimport { guid } from 'utils/guid';\nexport const DriverTaskType = 'driver-task';\nexport const HighPriority = 'High';\nexport const NormalPriority = 'Normal';\nexport const LowPriority = 'Low';\nexport const NotStartedStatus = 'Not Started';\nexport const InProgressStatus = 'In Progress';\nexport const CompleteStatus = 'Complete';\nexport function createDriverTask(who) {\n  return {\n    _id: guid(),\n    type: DriverTaskType,\n    createdBy: who.name,\n    createdOn: moment().toISOString(),\n    dueDate: moment().format('YYYY-MM-DD'),\n    assignedTo: null,\n    priority: 'Normal',\n    notes: '',\n    status: 'Not Started',\n    fromLocation: '',\n    toLocation: ''\n  };\n}", "map": {"version": 3, "names": ["moment", "guid", "DriverTaskType", "HighPriority", "NormalPriority", "LowPriority", "NotStartedStatus", "InProgressStatus", "CompleteStatus", "createDriverTask", "who", "_id", "type", "created<PERSON>y", "name", "createdOn", "toISOString", "dueDate", "format", "assignedTo", "priority", "notes", "status", "fromLocation", "toLocation"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/api/models/driver-tasks.ts"], "sourcesContent": ["import moment from 'moment';\r\nimport { guid } from 'utils/guid';\r\nimport { UserInfo } from './auth';\r\nimport { ModelBase } from './model-base';\r\n\r\nexport const DriverTaskType = 'driver-task';\r\n\r\nexport const HighPriority = 'High';\r\nexport const NormalPriority = 'Normal';\r\nexport const LowPriority = 'Low';\r\n\r\nexport type Priority = 'High' | 'Normal' | 'Low';\r\n\r\nexport const NotStartedStatus = 'Not Started';\r\nexport const InProgressStatus = 'In Progress';\r\nexport const CompleteStatus = 'Complete';\r\n\r\nexport type Status = 'Not Started' | 'In Progress' | 'Complete';\r\n\r\nexport interface Driver {\r\n  name: string;\r\n  email: string | null;\r\n  phone: string | null;\r\n}\r\n\r\nexport interface DriverTask extends ModelBase {\r\n  type: string;\r\n  createdBy: string;\r\n  createdOn: string;\r\n  dueDate: string;\r\n  assignedTo: Driver | null;\r\n  priority: Priority;\r\n  notes: string;\r\n  status: Status;\r\n  fromLocation: string;\r\n  toLocation: string;\r\n}\r\n\r\nexport function createDriverTask(who: UserInfo): DriverTask {\r\n  return {\r\n    _id: guid(),\r\n    type: DriverTaskType,\r\n    createdBy: who.name,\r\n    createdOn: moment().toISOString(),\r\n    dueDate: moment().format('YYYY-MM-DD'),\r\n    assignedTo: null,\r\n    priority: 'Normal',\r\n    notes: '',\r\n    status: 'Not Started',\r\n    fromLocation: '',\r\n    toLocation: '',\r\n  };\r\n}\r\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,QAAQ;AAC3B,SAASC,IAAI,QAAQ,YAAY;AAIjC,OAAO,MAAMC,cAAc,GAAG,aAAa;AAE3C,OAAO,MAAMC,YAAY,GAAG,MAAM;AAClC,OAAO,MAAMC,cAAc,GAAG,QAAQ;AACtC,OAAO,MAAMC,WAAW,GAAG,KAAK;AAIhC,OAAO,MAAMC,gBAAgB,GAAG,aAAa;AAC7C,OAAO,MAAMC,gBAAgB,GAAG,aAAa;AAC7C,OAAO,MAAMC,cAAc,GAAG,UAAU;AAuBxC,OAAO,SAASC,gBAAgB,CAACC,GAAa,EAAc;EAC1D,OAAO;IACLC,GAAG,EAAEV,IAAI,EAAE;IACXW,IAAI,EAAEV,cAAc;IACpBW,SAAS,EAAEH,GAAG,CAACI,IAAI;IACnBC,SAAS,EAAEf,MAAM,EAAE,CAACgB,WAAW,EAAE;IACjCC,OAAO,EAAEjB,MAAM,EAAE,CAACkB,MAAM,CAAC,YAAY,CAAC;IACtCC,UAAU,EAAE,IAAI;IAChBC,QAAQ,EAAE,QAAQ;IAClBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,aAAa;IACrBC,YAAY,EAAE,EAAE;IAChBC,UAAU,EAAE;EACd,CAAC;AACH"}, "metadata": {}, "sourceType": "module"}