{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\GreenRP\\\\Web\\\\ClientApp\\\\src\\\\features\\\\orders\\\\SalesWeekRow.tsx\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport { Button, Input } from 'reactstrap';\nimport moment from 'moment';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { useAuth } from 'features/auth/use-auth';\nimport { handleFocus } from 'utils/focus';\nimport { parseWeekAndYear } from 'utils/format';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport function SalesWeekRow(_ref) {\n  _s();\n  var _parseWeekAndYear;\n  let {\n    salesWeek,\n    onChange\n  } = _ref;\n  const {\n      isInRole\n    } = useAuth(),\n    salesWeekDate = (_parseWeekAndYear = parseWeekAndYear(salesWeek.week)) !== null && _parseWeekAndYear !== void 0 ? _parseWeekAndYear : new Date(),\n    m = moment(salesWeekDate),\n    [cases, setCases] = useState(salesWeek.cases),\n    [year, setYear] = useState(m.isoWeekYear()),\n    [week, setWeek] = useState(m.isoWeek()),\n    canUpdate = isInRole('update:orders');\n  const handleCasesChange = e => {\n    const cases = e.target.valueAsNumber || 0;\n    setCases(cases);\n  };\n  const handleYearChange = e => {\n    const year = e.target.valueAsNumber || 0;\n    setYear(year);\n  };\n  const handleWeekChange = e => {\n    const week = e.target.valueAsNumber || 0;\n    setWeek(week);\n  };\n  const handleBlur = () => {\n    const update = {\n      id: salesWeek.id,\n      week: `${week}/${year}`,\n      cases\n    };\n    onChange(salesWeek.id, update);\n  };\n  const handleDeleteClick = () => {\n    onChange(salesWeek.id, null);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"row pt-2 mt-2 border-top\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-12 col-md-1 text-center\",\n      children: canUpdate && /*#__PURE__*/_jsxDEV(Button, {\n        color: \"danger\",\n        outline: true,\n        size: \"sm\",\n        onClick: handleDeleteClick,\n        className: \"mt-1\",\n        children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n          icon: ['fat', 'trash']\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-12 col-md-2\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row gx-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md-6\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            type: \"number\",\n            value: year,\n            onChange: handleYearChange,\n            onBlur: handleBlur,\n            onFocus: handleFocus,\n            disabled: !canUpdate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md-6\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            type: \"number\",\n            value: week,\n            onChange: handleWeekChange,\n            onBlur: handleBlur,\n            onFocus: handleFocus,\n            disabled: !canUpdate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-12 col-md-2\",\n      children: /*#__PURE__*/_jsxDEV(Input, {\n        type: \"number\",\n        className: \"text-end\",\n        value: cases,\n        onChange: handleCasesChange,\n        onBlur: handleBlur,\n        onFocus: handleFocus,\n        disabled: !canUpdate\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 5\n  }, this);\n}\n_s(SalesWeekRow, \"amCzhgyDyjtvs/EOrvKj6ecwfDQ=\", false, function () {\n  return [useAuth];\n});\n_c = SalesWeekRow;\nvar _c;\n$RefreshReg$(_c, \"SalesWeekRow\");", "map": {"version": 3, "names": ["useState", "<PERSON><PERSON>", "Input", "moment", "FontAwesomeIcon", "useAuth", "handleFocus", "parseWeekAndYear", "SalesWeekRow", "salesWeek", "onChange", "isInRole", "salesWeekDate", "week", "Date", "m", "cases", "setCases", "year", "setYear", "isoWeekYear", "setWeek", "isoWeek", "canUpdate", "handleCasesChange", "e", "target", "valueAsNumber", "handleYearChange", "handleWeekChange", "handleBlur", "update", "id", "handleDeleteClick"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/orders/SalesWeekRow.tsx"], "sourcesContent": ["import { useState } from 'react';\r\nimport { Button, Input } from 'reactstrap';\r\nimport moment from 'moment';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { SalesWeek } from 'api/models/orders';\r\nimport { useAuth } from 'features/auth/use-auth';\r\nimport { handleFocus } from 'utils/focus';\r\nimport { parseWeekAndYear } from 'utils/format';\r\n\r\ninterface SalesWeekRowProps {\r\n  salesWeek: SalesWeek;\r\n  onChange: (id: string, salesWeek: SalesWeek | null) => void;\r\n}\r\n\r\nexport function SalesWeekRow({ salesWeek, onChange }: SalesWeekRowProps) {\r\n  const { isInRole } = useAuth(),\r\n    salesWeekDate = parseWeekAndYear(salesWeek.week) ?? new Date(),\r\n    m = moment(salesWeekDate),\r\n    [cases, setCases] = useState(salesWeek.cases),\r\n    [year, setYear] = useState(m.isoWeekYear()),\r\n    [week, setWeek] = useState(m.isoWeek()),\r\n    canUpdate = isInRole('update:orders');\r\n\r\n  const handleCasesChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const cases = e.target.valueAsNumber || 0;\r\n\r\n    setCases(cases);\r\n  };\r\n\r\n  const handleYearChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const year = e.target.valueAsNumber || 0;\r\n\r\n    setYear(year);\r\n  };\r\n\r\n  const handleWeekChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const week = e.target.valueAsNumber || 0;\r\n\r\n    setWeek(week);\r\n  };\r\n\r\n  const handleBlur = () => {\r\n    const update = { id: salesWeek.id, week: `${week}/${year}`, cases };\r\n    onChange(salesWeek.id, update);\r\n  };\r\n\r\n  const handleDeleteClick = () => {\r\n    onChange(salesWeek.id, null);\r\n  };\r\n\r\n  return (\r\n    <div className=\"row pt-2 mt-2 border-top\">\r\n      <div className=\"col-12 col-md-1 text-center\">\r\n        {canUpdate && (\r\n          <Button\r\n            color=\"danger\"\r\n            outline\r\n            size=\"sm\"\r\n            onClick={handleDeleteClick}\r\n            className=\"mt-1\">\r\n            <FontAwesomeIcon icon={['fat', 'trash']} />\r\n          </Button>\r\n        )}\r\n      </div>\r\n      <div className=\"col-12 col-md-2\">\r\n        <div className=\"row gx-0\">\r\n          <div className=\"col-12 col-md-6\">\r\n            <Input\r\n              type=\"number\"\r\n              value={year}\r\n              onChange={handleYearChange}\r\n              onBlur={handleBlur}\r\n              onFocus={handleFocus}\r\n              disabled={!canUpdate}\r\n            />\r\n          </div>\r\n          <div className=\"col-12 col-md-6\">\r\n            <Input\r\n              type=\"number\"\r\n              value={week}\r\n              onChange={handleWeekChange}\r\n              onBlur={handleBlur}\r\n              onFocus={handleFocus}\r\n              disabled={!canUpdate}\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div className=\"col-12 col-md-2\">\r\n        <Input\r\n          type=\"number\"\r\n          className=\"text-end\"\r\n          value={cases}\r\n          onChange={handleCasesChange}\r\n          onBlur={handleBlur}\r\n          onFocus={handleFocus}\r\n          disabled={!canUpdate}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,MAAM,EAAEC,KAAK,QAAQ,YAAY;AAC1C,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,eAAe,QAAQ,gCAAgC;AAEhE,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,gBAAgB,QAAQ,cAAc;AAAC;AAOhD,OAAO,SAASC,YAAY,OAA6C;EAAA;EAAA;EAAA,IAA5C;IAAEC,SAAS;IAAEC;EAA4B,CAAC;EACrE,MAAM;MAAEC;IAAS,CAAC,GAAGN,OAAO,EAAE;IAC5BO,aAAa,wBAAGL,gBAAgB,CAACE,SAAS,CAACI,IAAI,CAAC,iEAAI,IAAIC,IAAI,EAAE;IAC9DC,CAAC,GAAGZ,MAAM,CAACS,aAAa,CAAC;IACzB,CAACI,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAACS,SAAS,CAACO,KAAK,CAAC;IAC7C,CAACE,IAAI,EAAEC,OAAO,CAAC,GAAGnB,QAAQ,CAACe,CAAC,CAACK,WAAW,EAAE,CAAC;IAC3C,CAACP,IAAI,EAAEQ,OAAO,CAAC,GAAGrB,QAAQ,CAACe,CAAC,CAACO,OAAO,EAAE,CAAC;IACvCC,SAAS,GAAGZ,QAAQ,CAAC,eAAe,CAAC;EAEvC,MAAMa,iBAAiB,GAAIC,CAAsC,IAAK;IACpE,MAAMT,KAAK,GAAGS,CAAC,CAACC,MAAM,CAACC,aAAa,IAAI,CAAC;IAEzCV,QAAQ,CAACD,KAAK,CAAC;EACjB,CAAC;EAED,MAAMY,gBAAgB,GAAIH,CAAsC,IAAK;IACnE,MAAMP,IAAI,GAAGO,CAAC,CAACC,MAAM,CAACC,aAAa,IAAI,CAAC;IAExCR,OAAO,CAACD,IAAI,CAAC;EACf,CAAC;EAED,MAAMW,gBAAgB,GAAIJ,CAAsC,IAAK;IACnE,MAAMZ,IAAI,GAAGY,CAAC,CAACC,MAAM,CAACC,aAAa,IAAI,CAAC;IAExCN,OAAO,CAACR,IAAI,CAAC;EACf,CAAC;EAED,MAAMiB,UAAU,GAAG,MAAM;IACvB,MAAMC,MAAM,GAAG;MAAEC,EAAE,EAAEvB,SAAS,CAACuB,EAAE;MAAEnB,IAAI,EAAG,GAAEA,IAAK,IAAGK,IAAK,EAAC;MAAEF;IAAM,CAAC;IACnEN,QAAQ,CAACD,SAAS,CAACuB,EAAE,EAAED,MAAM,CAAC;EAChC,CAAC;EAED,MAAME,iBAAiB,GAAG,MAAM;IAC9BvB,QAAQ,CAACD,SAAS,CAACuB,EAAE,EAAE,IAAI,CAAC;EAC9B,CAAC;EAED,oBACE;IAAK,SAAS,EAAC,0BAA0B;IAAA,wBACvC;MAAK,SAAS,EAAC,6BAA6B;MAAA,UACzCT,SAAS,iBACR,QAAC,MAAM;QACL,KAAK,EAAC,QAAQ;QACd,OAAO;QACP,IAAI,EAAC,IAAI;QACT,OAAO,EAAEU,iBAAkB;QAC3B,SAAS,EAAC,MAAM;QAAA,uBAChB,QAAC,eAAe;UAAC,IAAI,EAAE,CAAC,KAAK,EAAE,OAAO;QAAE;UAAA;UAAA;UAAA;QAAA;MAAG;QAAA;QAAA;QAAA;MAAA;IAE9C;MAAA;MAAA;MAAA;IAAA,QACG,eACN;MAAK,SAAS,EAAC,iBAAiB;MAAA,uBAC9B;QAAK,SAAS,EAAC,UAAU;QAAA,wBACvB;UAAK,SAAS,EAAC,iBAAiB;UAAA,uBAC9B,QAAC,KAAK;YACJ,IAAI,EAAC,QAAQ;YACb,KAAK,EAAEf,IAAK;YACZ,QAAQ,EAAEU,gBAAiB;YAC3B,MAAM,EAAEE,UAAW;YACnB,OAAO,EAAExB,WAAY;YACrB,QAAQ,EAAE,CAACiB;UAAU;YAAA;YAAA;YAAA;UAAA;QACrB;UAAA;UAAA;UAAA;QAAA,QACE,eACN;UAAK,SAAS,EAAC,iBAAiB;UAAA,uBAC9B,QAAC,KAAK;YACJ,IAAI,EAAC,QAAQ;YACb,KAAK,EAAEV,IAAK;YACZ,QAAQ,EAAEgB,gBAAiB;YAC3B,MAAM,EAAEC,UAAW;YACnB,OAAO,EAAExB,WAAY;YACrB,QAAQ,EAAE,CAACiB;UAAU;YAAA;YAAA;YAAA;UAAA;QACrB;UAAA;UAAA;UAAA;QAAA,QACE;MAAA;QAAA;QAAA;QAAA;MAAA;IACF;MAAA;MAAA;MAAA;IAAA,QACF,eACN;MAAK,SAAS,EAAC,iBAAiB;MAAA,uBAC9B,QAAC,KAAK;QACJ,IAAI,EAAC,QAAQ;QACb,SAAS,EAAC,UAAU;QACpB,KAAK,EAAEP,KAAM;QACb,QAAQ,EAAEQ,iBAAkB;QAC5B,MAAM,EAAEM,UAAW;QACnB,OAAO,EAAExB,WAAY;QACrB,QAAQ,EAAE,CAACiB;MAAU;QAAA;QAAA;QAAA;MAAA;IACrB;MAAA;MAAA;MAAA;IAAA,QACE;EAAA;IAAA;IAAA;IAAA;EAAA,QACF;AAEV;AAAC,GAvFef,YAAY;EAAA,QACLH,OAAO;AAAA;AAAA,KADdG,YAAY;AAAA;AAAA"}, "metadata": {}, "sourceType": "module"}