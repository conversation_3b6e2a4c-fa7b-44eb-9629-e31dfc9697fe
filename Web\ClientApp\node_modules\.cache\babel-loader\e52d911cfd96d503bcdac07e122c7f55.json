{"ast": null, "code": "import React from 'react';\nexport const AuthContext = /*#__PURE__*/React.createContext(null);", "map": {"version": 3, "names": ["React", "AuthContext", "createContext"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/auth/auth-context.ts"], "sourcesContent": ["import React from 'react';\r\nimport { Roles, UserInfo } from 'api/models/auth';\r\n\r\ninterface AuthContextType {\r\n  user: UserInfo | null;\r\n  signin: (user: string, password: string) => Promise<void>;\r\n  signout: (callback: VoidFunction) => void;\r\n  isInRole: (role: Roles) => boolean;\r\n}\r\n\r\nexport const AuthContext = React.createContext<AuthContextType>(null!);\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAUzB,OAAO,MAAMC,WAAW,gBAAGD,KAAK,CAACE,aAAa,CAAkB,IAAI,CAAE"}, "metadata": {}, "sourceType": "module"}