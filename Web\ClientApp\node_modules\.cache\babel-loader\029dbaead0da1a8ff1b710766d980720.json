{"ast": null, "code": "'use strict';\n\n/**\n * Stringify/parse functions that don't operate\n * recursively, so they avoid call stack exceeded\n * errors.\n */\nexports.stringify = function stringify(input) {\n  var queue = [];\n  queue.push({\n    obj: input\n  });\n  var res = '';\n  var next, obj, prefix, val, i, arrayPrefix, keys, k, key, value, objPrefix;\n  while (next = queue.pop()) {\n    obj = next.obj;\n    prefix = next.prefix || '';\n    val = next.val || '';\n    res += prefix;\n    if (val) {\n      res += val;\n    } else if (typeof obj !== 'object') {\n      res += typeof obj === 'undefined' ? null : JSON.stringify(obj);\n    } else if (obj === null) {\n      res += 'null';\n    } else if (Array.isArray(obj)) {\n      queue.push({\n        val: ']'\n      });\n      for (i = obj.length - 1; i >= 0; i--) {\n        arrayPrefix = i === 0 ? '' : ',';\n        queue.push({\n          obj: obj[i],\n          prefix: arrayPrefix\n        });\n      }\n      queue.push({\n        val: '['\n      });\n    } else {\n      // object\n      keys = [];\n      for (k in obj) {\n        if (obj.hasOwnProperty(k)) {\n          keys.push(k);\n        }\n      }\n      queue.push({\n        val: '}'\n      });\n      for (i = keys.length - 1; i >= 0; i--) {\n        key = keys[i];\n        value = obj[key];\n        objPrefix = i > 0 ? ',' : '';\n        objPrefix += JSON.stringify(key) + ':';\n        queue.push({\n          obj: value,\n          prefix: objPrefix\n        });\n      }\n      queue.push({\n        val: '{'\n      });\n    }\n  }\n  return res;\n};\n\n// Convenience function for the parse function.\n// This pop function is basically copied from\n// pouchCollate.parseIndexableString\nfunction pop(obj, stack, metaStack) {\n  var lastMetaElement = metaStack[metaStack.length - 1];\n  if (obj === lastMetaElement.element) {\n    // popping a meta-element, e.g. an object whose value is another object\n    metaStack.pop();\n    lastMetaElement = metaStack[metaStack.length - 1];\n  }\n  var element = lastMetaElement.element;\n  var lastElementIndex = lastMetaElement.index;\n  if (Array.isArray(element)) {\n    element.push(obj);\n  } else if (lastElementIndex === stack.length - 2) {\n    // obj with key+value\n    var key = stack.pop();\n    element[key] = obj;\n  } else {\n    stack.push(obj); // obj with key only\n  }\n}\n\nexports.parse = function (str) {\n  var stack = [];\n  var metaStack = []; // stack for arrays and objects\n  var i = 0;\n  var collationIndex, parsedNum, numChar;\n  var parsedString, lastCh, numConsecutiveSlashes, ch;\n  var arrayElement, objElement;\n  while (true) {\n    collationIndex = str[i++];\n    if (collationIndex === '}' || collationIndex === ']' || typeof collationIndex === 'undefined') {\n      if (stack.length === 1) {\n        return stack.pop();\n      } else {\n        pop(stack.pop(), stack, metaStack);\n        continue;\n      }\n    }\n    switch (collationIndex) {\n      case ' ':\n      case '\\t':\n      case '\\n':\n      case ':':\n      case ',':\n        break;\n      case 'n':\n        i += 3; // 'ull'\n        pop(null, stack, metaStack);\n        break;\n      case 't':\n        i += 3; // 'rue'\n        pop(true, stack, metaStack);\n        break;\n      case 'f':\n        i += 4; // 'alse'\n        pop(false, stack, metaStack);\n        break;\n      case '0':\n      case '1':\n      case '2':\n      case '3':\n      case '4':\n      case '5':\n      case '6':\n      case '7':\n      case '8':\n      case '9':\n      case '-':\n        parsedNum = '';\n        i--;\n        while (true) {\n          numChar = str[i++];\n          if (/[\\d\\.\\-e\\+]/.test(numChar)) {\n            parsedNum += numChar;\n          } else {\n            i--;\n            break;\n          }\n        }\n        pop(parseFloat(parsedNum), stack, metaStack);\n        break;\n      case '\"':\n        parsedString = '';\n        lastCh = void 0;\n        numConsecutiveSlashes = 0;\n        while (true) {\n          ch = str[i++];\n          if (ch !== '\"' || lastCh === '\\\\' && numConsecutiveSlashes % 2 === 1) {\n            parsedString += ch;\n            lastCh = ch;\n            if (lastCh === '\\\\') {\n              numConsecutiveSlashes++;\n            } else {\n              numConsecutiveSlashes = 0;\n            }\n          } else {\n            break;\n          }\n        }\n        pop(JSON.parse('\"' + parsedString + '\"'), stack, metaStack);\n        break;\n      case '[':\n        arrayElement = {\n          element: [],\n          index: stack.length\n        };\n        stack.push(arrayElement.element);\n        metaStack.push(arrayElement);\n        break;\n      case '{':\n        objElement = {\n          element: {},\n          index: stack.length\n        };\n        stack.push(objElement.element);\n        metaStack.push(objElement);\n        break;\n      default:\n        throw new Error('unexpectedly reached end of input: ' + collationIndex);\n    }\n  }\n};", "map": {"version": 3, "names": ["exports", "stringify", "input", "queue", "push", "obj", "res", "next", "prefix", "val", "i", "arrayPrefix", "keys", "k", "key", "value", "objPrefix", "pop", "JSON", "Array", "isArray", "length", "hasOwnProperty", "stack", "metaStack", "lastMetaElement", "element", "lastElementIndex", "index", "parse", "str", "collationIndex", "parsedNum", "numChar", "parsedString", "lastCh", "numConsecutiveSlashes", "ch", "arrayElement", "objElement", "test", "parseFloat", "Error"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/node_modules/vuvuzela/index.js"], "sourcesContent": ["'use strict';\n\n/**\n * Stringify/parse functions that don't operate\n * recursively, so they avoid call stack exceeded\n * errors.\n */\nexports.stringify = function stringify(input) {\n  var queue = [];\n  queue.push({obj: input});\n\n  var res = '';\n  var next, obj, prefix, val, i, arrayPrefix, keys, k, key, value, objPrefix;\n  while ((next = queue.pop())) {\n    obj = next.obj;\n    prefix = next.prefix || '';\n    val = next.val || '';\n    res += prefix;\n    if (val) {\n      res += val;\n    } else if (typeof obj !== 'object') {\n      res += typeof obj === 'undefined' ? null : JSON.stringify(obj);\n    } else if (obj === null) {\n      res += 'null';\n    } else if (Array.isArray(obj)) {\n      queue.push({val: ']'});\n      for (i = obj.length - 1; i >= 0; i--) {\n        arrayPrefix = i === 0 ? '' : ',';\n        queue.push({obj: obj[i], prefix: arrayPrefix});\n      }\n      queue.push({val: '['});\n    } else { // object\n      keys = [];\n      for (k in obj) {\n        if (obj.hasOwnProperty(k)) {\n          keys.push(k);\n        }\n      }\n      queue.push({val: '}'});\n      for (i = keys.length - 1; i >= 0; i--) {\n        key = keys[i];\n        value = obj[key];\n        objPrefix = (i > 0 ? ',' : '');\n        objPrefix += JSON.stringify(key) + ':';\n        queue.push({obj: value, prefix: objPrefix});\n      }\n      queue.push({val: '{'});\n    }\n  }\n  return res;\n};\n\n// Convenience function for the parse function.\n// This pop function is basically copied from\n// pouchCollate.parseIndexableString\nfunction pop(obj, stack, metaStack) {\n  var lastMetaElement = metaStack[metaStack.length - 1];\n  if (obj === lastMetaElement.element) {\n    // popping a meta-element, e.g. an object whose value is another object\n    metaStack.pop();\n    lastMetaElement = metaStack[metaStack.length - 1];\n  }\n  var element = lastMetaElement.element;\n  var lastElementIndex = lastMetaElement.index;\n  if (Array.isArray(element)) {\n    element.push(obj);\n  } else if (lastElementIndex === stack.length - 2) { // obj with key+value\n    var key = stack.pop();\n    element[key] = obj;\n  } else {\n    stack.push(obj); // obj with key only\n  }\n}\n\nexports.parse = function (str) {\n  var stack = [];\n  var metaStack = []; // stack for arrays and objects\n  var i = 0;\n  var collationIndex,parsedNum,numChar;\n  var parsedString,lastCh,numConsecutiveSlashes,ch;\n  var arrayElement, objElement;\n  while (true) {\n    collationIndex = str[i++];\n    if (collationIndex === '}' ||\n        collationIndex === ']' ||\n        typeof collationIndex === 'undefined') {\n      if (stack.length === 1) {\n        return stack.pop();\n      } else {\n        pop(stack.pop(), stack, metaStack);\n        continue;\n      }\n    }\n    switch (collationIndex) {\n      case ' ':\n      case '\\t':\n      case '\\n':\n      case ':':\n      case ',':\n        break;\n      case 'n':\n        i += 3; // 'ull'\n        pop(null, stack, metaStack);\n        break;\n      case 't':\n        i += 3; // 'rue'\n        pop(true, stack, metaStack);\n        break;\n      case 'f':\n        i += 4; // 'alse'\n        pop(false, stack, metaStack);\n        break;\n      case '0':\n      case '1':\n      case '2':\n      case '3':\n      case '4':\n      case '5':\n      case '6':\n      case '7':\n      case '8':\n      case '9':\n      case '-':\n        parsedNum = '';\n        i--;\n        while (true) {\n          numChar = str[i++];\n          if (/[\\d\\.\\-e\\+]/.test(numChar)) {\n            parsedNum += numChar;\n          } else {\n            i--;\n            break;\n          }\n        }\n        pop(parseFloat(parsedNum), stack, metaStack);\n        break;\n      case '\"':\n        parsedString = '';\n        lastCh = void 0;\n        numConsecutiveSlashes = 0;\n        while (true) {\n          ch = str[i++];\n          if (ch !== '\"' || (lastCh === '\\\\' &&\n              numConsecutiveSlashes % 2 === 1)) {\n            parsedString += ch;\n            lastCh = ch;\n            if (lastCh === '\\\\') {\n              numConsecutiveSlashes++;\n            } else {\n              numConsecutiveSlashes = 0;\n            }\n          } else {\n            break;\n          }\n        }\n        pop(JSON.parse('\"' + parsedString + '\"'), stack, metaStack);\n        break;\n      case '[':\n        arrayElement = { element: [], index: stack.length };\n        stack.push(arrayElement.element);\n        metaStack.push(arrayElement);\n        break;\n      case '{':\n        objElement = { element: {}, index: stack.length };\n        stack.push(objElement.element);\n        metaStack.push(objElement);\n        break;\n      default:\n        throw new Error(\n          'unexpectedly reached end of input: ' + collationIndex);\n    }\n  }\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACAA,OAAO,CAACC,SAAS,GAAG,SAASA,SAAS,CAACC,KAAK,EAAE;EAC5C,IAAIC,KAAK,GAAG,EAAE;EACdA,KAAK,CAACC,IAAI,CAAC;IAACC,GAAG,EAAEH;EAAK,CAAC,CAAC;EAExB,IAAII,GAAG,GAAG,EAAE;EACZ,IAAIC,IAAI,EAAEF,GAAG,EAAEG,MAAM,EAAEC,GAAG,EAAEC,CAAC,EAAEC,WAAW,EAAEC,IAAI,EAAEC,CAAC,EAAEC,GAAG,EAAEC,KAAK,EAAEC,SAAS;EAC1E,OAAQT,IAAI,GAAGJ,KAAK,CAACc,GAAG,EAAE,EAAG;IAC3BZ,GAAG,GAAGE,IAAI,CAACF,GAAG;IACdG,MAAM,GAAGD,IAAI,CAACC,MAAM,IAAI,EAAE;IAC1BC,GAAG,GAAGF,IAAI,CAACE,GAAG,IAAI,EAAE;IACpBH,GAAG,IAAIE,MAAM;IACb,IAAIC,GAAG,EAAE;MACPH,GAAG,IAAIG,GAAG;IACZ,CAAC,MAAM,IAAI,OAAOJ,GAAG,KAAK,QAAQ,EAAE;MAClCC,GAAG,IAAI,OAAOD,GAAG,KAAK,WAAW,GAAG,IAAI,GAAGa,IAAI,CAACjB,SAAS,CAACI,GAAG,CAAC;IAChE,CAAC,MAAM,IAAIA,GAAG,KAAK,IAAI,EAAE;MACvBC,GAAG,IAAI,MAAM;IACf,CAAC,MAAM,IAAIa,KAAK,CAACC,OAAO,CAACf,GAAG,CAAC,EAAE;MAC7BF,KAAK,CAACC,IAAI,CAAC;QAACK,GAAG,EAAE;MAAG,CAAC,CAAC;MACtB,KAAKC,CAAC,GAAGL,GAAG,CAACgB,MAAM,GAAG,CAAC,EAAEX,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACpCC,WAAW,GAAGD,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG;QAChCP,KAAK,CAACC,IAAI,CAAC;UAACC,GAAG,EAAEA,GAAG,CAACK,CAAC,CAAC;UAAEF,MAAM,EAAEG;QAAW,CAAC,CAAC;MAChD;MACAR,KAAK,CAACC,IAAI,CAAC;QAACK,GAAG,EAAE;MAAG,CAAC,CAAC;IACxB,CAAC,MAAM;MAAE;MACPG,IAAI,GAAG,EAAE;MACT,KAAKC,CAAC,IAAIR,GAAG,EAAE;QACb,IAAIA,GAAG,CAACiB,cAAc,CAACT,CAAC,CAAC,EAAE;UACzBD,IAAI,CAACR,IAAI,CAACS,CAAC,CAAC;QACd;MACF;MACAV,KAAK,CAACC,IAAI,CAAC;QAACK,GAAG,EAAE;MAAG,CAAC,CAAC;MACtB,KAAKC,CAAC,GAAGE,IAAI,CAACS,MAAM,GAAG,CAAC,EAAEX,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACrCI,GAAG,GAAGF,IAAI,CAACF,CAAC,CAAC;QACbK,KAAK,GAAGV,GAAG,CAACS,GAAG,CAAC;QAChBE,SAAS,GAAIN,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,EAAG;QAC9BM,SAAS,IAAIE,IAAI,CAACjB,SAAS,CAACa,GAAG,CAAC,GAAG,GAAG;QACtCX,KAAK,CAACC,IAAI,CAAC;UAACC,GAAG,EAAEU,KAAK;UAAEP,MAAM,EAAEQ;QAAS,CAAC,CAAC;MAC7C;MACAb,KAAK,CAACC,IAAI,CAAC;QAACK,GAAG,EAAE;MAAG,CAAC,CAAC;IACxB;EACF;EACA,OAAOH,GAAG;AACZ,CAAC;;AAED;AACA;AACA;AACA,SAASW,GAAG,CAACZ,GAAG,EAAEkB,KAAK,EAAEC,SAAS,EAAE;EAClC,IAAIC,eAAe,GAAGD,SAAS,CAACA,SAAS,CAACH,MAAM,GAAG,CAAC,CAAC;EACrD,IAAIhB,GAAG,KAAKoB,eAAe,CAACC,OAAO,EAAE;IACnC;IACAF,SAAS,CAACP,GAAG,EAAE;IACfQ,eAAe,GAAGD,SAAS,CAACA,SAAS,CAACH,MAAM,GAAG,CAAC,CAAC;EACnD;EACA,IAAIK,OAAO,GAAGD,eAAe,CAACC,OAAO;EACrC,IAAIC,gBAAgB,GAAGF,eAAe,CAACG,KAAK;EAC5C,IAAIT,KAAK,CAACC,OAAO,CAACM,OAAO,CAAC,EAAE;IAC1BA,OAAO,CAACtB,IAAI,CAACC,GAAG,CAAC;EACnB,CAAC,MAAM,IAAIsB,gBAAgB,KAAKJ,KAAK,CAACF,MAAM,GAAG,CAAC,EAAE;IAAE;IAClD,IAAIP,GAAG,GAAGS,KAAK,CAACN,GAAG,EAAE;IACrBS,OAAO,CAACZ,GAAG,CAAC,GAAGT,GAAG;EACpB,CAAC,MAAM;IACLkB,KAAK,CAACnB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EACnB;AACF;;AAEAL,OAAO,CAAC6B,KAAK,GAAG,UAAUC,GAAG,EAAE;EAC7B,IAAIP,KAAK,GAAG,EAAE;EACd,IAAIC,SAAS,GAAG,EAAE,CAAC,CAAC;EACpB,IAAId,CAAC,GAAG,CAAC;EACT,IAAIqB,cAAc,EAACC,SAAS,EAACC,OAAO;EACpC,IAAIC,YAAY,EAACC,MAAM,EAACC,qBAAqB,EAACC,EAAE;EAChD,IAAIC,YAAY,EAAEC,UAAU;EAC5B,OAAO,IAAI,EAAE;IACXR,cAAc,GAAGD,GAAG,CAACpB,CAAC,EAAE,CAAC;IACzB,IAAIqB,cAAc,KAAK,GAAG,IACtBA,cAAc,KAAK,GAAG,IACtB,OAAOA,cAAc,KAAK,WAAW,EAAE;MACzC,IAAIR,KAAK,CAACF,MAAM,KAAK,CAAC,EAAE;QACtB,OAAOE,KAAK,CAACN,GAAG,EAAE;MACpB,CAAC,MAAM;QACLA,GAAG,CAACM,KAAK,CAACN,GAAG,EAAE,EAAEM,KAAK,EAAEC,SAAS,CAAC;QAClC;MACF;IACF;IACA,QAAQO,cAAc;MACpB,KAAK,GAAG;MACR,KAAK,IAAI;MACT,KAAK,IAAI;MACT,KAAK,GAAG;MACR,KAAK,GAAG;QACN;MACF,KAAK,GAAG;QACNrB,CAAC,IAAI,CAAC,CAAC,CAAC;QACRO,GAAG,CAAC,IAAI,EAAEM,KAAK,EAAEC,SAAS,CAAC;QAC3B;MACF,KAAK,GAAG;QACNd,CAAC,IAAI,CAAC,CAAC,CAAC;QACRO,GAAG,CAAC,IAAI,EAAEM,KAAK,EAAEC,SAAS,CAAC;QAC3B;MACF,KAAK,GAAG;QACNd,CAAC,IAAI,CAAC,CAAC,CAAC;QACRO,GAAG,CAAC,KAAK,EAAEM,KAAK,EAAEC,SAAS,CAAC;QAC5B;MACF,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;QACNQ,SAAS,GAAG,EAAE;QACdtB,CAAC,EAAE;QACH,OAAO,IAAI,EAAE;UACXuB,OAAO,GAAGH,GAAG,CAACpB,CAAC,EAAE,CAAC;UAClB,IAAI,aAAa,CAAC8B,IAAI,CAACP,OAAO,CAAC,EAAE;YAC/BD,SAAS,IAAIC,OAAO;UACtB,CAAC,MAAM;YACLvB,CAAC,EAAE;YACH;UACF;QACF;QACAO,GAAG,CAACwB,UAAU,CAACT,SAAS,CAAC,EAAET,KAAK,EAAEC,SAAS,CAAC;QAC5C;MACF,KAAK,GAAG;QACNU,YAAY,GAAG,EAAE;QACjBC,MAAM,GAAG,KAAK,CAAC;QACfC,qBAAqB,GAAG,CAAC;QACzB,OAAO,IAAI,EAAE;UACXC,EAAE,GAAGP,GAAG,CAACpB,CAAC,EAAE,CAAC;UACb,IAAI2B,EAAE,KAAK,GAAG,IAAKF,MAAM,KAAK,IAAI,IAC9BC,qBAAqB,GAAG,CAAC,KAAK,CAAE,EAAE;YACpCF,YAAY,IAAIG,EAAE;YAClBF,MAAM,GAAGE,EAAE;YACX,IAAIF,MAAM,KAAK,IAAI,EAAE;cACnBC,qBAAqB,EAAE;YACzB,CAAC,MAAM;cACLA,qBAAqB,GAAG,CAAC;YAC3B;UACF,CAAC,MAAM;YACL;UACF;QACF;QACAnB,GAAG,CAACC,IAAI,CAACW,KAAK,CAAC,GAAG,GAAGK,YAAY,GAAG,GAAG,CAAC,EAAEX,KAAK,EAAEC,SAAS,CAAC;QAC3D;MACF,KAAK,GAAG;QACNc,YAAY,GAAG;UAAEZ,OAAO,EAAE,EAAE;UAAEE,KAAK,EAAEL,KAAK,CAACF;QAAO,CAAC;QACnDE,KAAK,CAACnB,IAAI,CAACkC,YAAY,CAACZ,OAAO,CAAC;QAChCF,SAAS,CAACpB,IAAI,CAACkC,YAAY,CAAC;QAC5B;MACF,KAAK,GAAG;QACNC,UAAU,GAAG;UAAEb,OAAO,EAAE,CAAC,CAAC;UAAEE,KAAK,EAAEL,KAAK,CAACF;QAAO,CAAC;QACjDE,KAAK,CAACnB,IAAI,CAACmC,UAAU,CAACb,OAAO,CAAC;QAC9BF,SAAS,CAACpB,IAAI,CAACmC,UAAU,CAAC;QAC1B;MACF;QACE,MAAM,IAAIG,KAAK,CACb,qCAAqC,GAAGX,cAAc,CAAC;IAAC;EAEhE;AACF,CAAC"}, "metadata": {}, "sourceType": "script"}