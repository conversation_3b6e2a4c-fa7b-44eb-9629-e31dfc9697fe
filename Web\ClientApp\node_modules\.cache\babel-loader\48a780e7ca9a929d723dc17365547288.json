{"ast": null, "code": "import { api } from './api-base';\nimport { configuration } from 'features/auth/configuration';\nclass AuthService {\n  async login(username, password) {\n    const url = `${configuration.remote_server}/_session`,\n      body = `name=${encodeURI(username)}&password=${encodeURI(password)}`,\n      authorization = getBasicAuth(username, password),\n      headers = {\n        'Content-Type': 'application/x-www-form-urlencoded',\n        'Authorization': authorization\n      },\n      session = await api.post(url, body, {\n        headers\n      }),\n      user = await api.get(`${configuration.remote_server}/_users/org.couchdb.user:${session.name}`, {\n        headers\n      });\n    return user;\n  }\n  async getAllUsers(user) {\n    const url = `${configuration.remote_server}/_users/_design/user-filters/_view/boekestyn-users?include_docs=true`,\n      authorization = getBasicAuth(user.name, user.password),\n      headers = {\n        Authorization: authorization\n      },\n      response = await api.get(url, {\n        headers\n      }),\n      users = response.rows.flatMap(row => row.doc ? [row.doc] : []);\n    return users;\n  }\n  async updateUser(credentials, user) {\n    const model = {\n        ...user\n      },\n      url = `${configuration.remote_server}/_users/${user._id}`,\n      authorization = getBasicAuth(credentials.name, credentials.password),\n      headers = {\n        Authorization: authorization\n      },\n      response = await api.put(url, model, {\n        headers\n      });\n    return response.ok;\n  }\n  async updatePassword(credentials, user, password) {\n    const model = {\n      ...user\n    };\n    delete model.password_sha;\n    delete model.salt;\n    model.password = password;\n    const url = `${configuration.remote_server}/_users/${user._id}`,\n      authorization = getBasicAuth(credentials.name, credentials.password),\n      headers = {\n        Authorization: authorization\n      },\n      response = await api.put(url, model, {\n        headers\n      });\n    return response.ok;\n  }\n}\nexport function getBasicAuth(username, password) {\n  const data = `${username}:${password}`,\n    header = `Basic ${window.btoa(data)}`;\n  return header;\n}\nexport const authApi = new AuthService();", "map": {"version": 3, "names": ["api", "configuration", "AuthService", "login", "username", "password", "url", "remote_server", "body", "encodeURI", "authorization", "getBasicAuth", "headers", "session", "post", "user", "get", "name", "getAllUsers", "Authorization", "response", "users", "rows", "flatMap", "row", "doc", "updateUser", "credentials", "model", "_id", "put", "ok", "updatePassword", "password_sha", "salt", "data", "header", "window", "btoa", "authApi"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/api/auth-service.ts"], "sourcesContent": ["import { api } from './api-base';\r\nimport * as models from './models/auth';\r\nimport { configuration } from 'features/auth/configuration';\r\n\r\nclass AuthService {\r\n  async login(username: string, password: string): Promise<models.CouchUserDoc | null> {\r\n    const url = `${configuration.remote_server}/_session`,\r\n      body = `name=${encodeURI(username)}&password=${encodeURI(password)}`,\r\n      authorization = getBasicAuth(username, password),\r\n      headers = { 'Content-Type': 'application/x-www-form-urlencoded', 'Authorization': authorization },\r\n      session = await api.post<models.CouchSessionDoc>(url, body, { headers }),\r\n      user = await api.get<models.CouchUserDoc>(`${configuration.remote_server}/_users/org.couchdb.user:${session.name}`, { headers });\r\n   \r\n    return user;\r\n  }\r\n\r\n  async getAllUsers(user: models.UserInfo): Promise<models.UserDoc[]> {\r\n    const url = `${configuration.remote_server}/_users/_design/user-filters/_view/boekestyn-users?include_docs=true`,\r\n      authorization = getBasicAuth(user.name, user.password),\r\n      headers = { Authorization: authorization},\r\n      response = await api.get<PouchDB.Query.Response<models.CouchUserDoc>>(url, { headers }),\r\n      users = response.rows.flatMap(row => row.doc ? [row.doc] : []);\r\n\r\n    return users;\r\n  }\r\n\r\n  async updateUser(credentials: models.UserInfo, user: models.CouchUserDoc): Promise<boolean> {\r\n    const model: any = {...user},\r\n      url = `${configuration.remote_server}/_users/${user._id}`,\r\n      authorization = getBasicAuth(credentials.name, credentials.password),\r\n      headers = { Authorization: authorization},\r\n      response = await api.put<PouchDB.Core.Response>(url, model, { headers });\r\n\r\n    return response.ok;\r\n  }\r\n\r\n  async updatePassword(credentials: models.UserInfo, user: models.CouchUserDoc, password: string): Promise<boolean> {\r\n    const model: any = {...user};\r\n\r\n    delete model.password_sha;\r\n    delete model.salt;\r\n    model.password = password;\r\n\r\n    const url = `${configuration.remote_server}/_users/${user._id}`,\r\n      authorization = getBasicAuth(credentials.name, credentials.password),\r\n      headers = { Authorization: authorization},\r\n      response = await api.put<PouchDB.Core.Response>(url, model, { headers });\r\n\r\n    return response.ok;\r\n  }\r\n}\r\n\r\nexport function getBasicAuth(username: string, password: string) {\r\n  const data = `${username}:${password}`,\r\n      header = `Basic ${window.btoa(data)}`;\r\n\r\n  return header;\r\n}\r\n\r\nexport const authApi = new AuthService();\r\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,YAAY;AAEhC,SAASC,aAAa,QAAQ,6BAA6B;AAE3D,MAAMC,WAAW,CAAC;EAChB,MAAMC,KAAK,CAACC,QAAgB,EAAEC,QAAgB,EAAuC;IACnF,MAAMC,GAAG,GAAI,GAAEL,aAAa,CAACM,aAAc,WAAU;MACnDC,IAAI,GAAI,QAAOC,SAAS,CAACL,QAAQ,CAAE,aAAYK,SAAS,CAACJ,QAAQ,CAAE,EAAC;MACpEK,aAAa,GAAGC,YAAY,CAACP,QAAQ,EAAEC,QAAQ,CAAC;MAChDO,OAAO,GAAG;QAAE,cAAc,EAAE,mCAAmC;QAAE,eAAe,EAAEF;MAAc,CAAC;MACjGG,OAAO,GAAG,MAAMb,GAAG,CAACc,IAAI,CAAyBR,GAAG,EAAEE,IAAI,EAAE;QAAEI;MAAQ,CAAC,CAAC;MACxEG,IAAI,GAAG,MAAMf,GAAG,CAACgB,GAAG,CAAuB,GAAEf,aAAa,CAACM,aAAc,4BAA2BM,OAAO,CAACI,IAAK,EAAC,EAAE;QAAEL;MAAQ,CAAC,CAAC;IAElI,OAAOG,IAAI;EACb;EAEA,MAAMG,WAAW,CAACH,IAAqB,EAA6B;IAClE,MAAMT,GAAG,GAAI,GAAEL,aAAa,CAACM,aAAc,sEAAqE;MAC9GG,aAAa,GAAGC,YAAY,CAACI,IAAI,CAACE,IAAI,EAAEF,IAAI,CAACV,QAAQ,CAAC;MACtDO,OAAO,GAAG;QAAEO,aAAa,EAAET;MAAa,CAAC;MACzCU,QAAQ,GAAG,MAAMpB,GAAG,CAACgB,GAAG,CAA8CV,GAAG,EAAE;QAAEM;MAAQ,CAAC,CAAC;MACvFS,KAAK,GAAGD,QAAQ,CAACE,IAAI,CAACC,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,GAAG,GAAG,CAACD,GAAG,CAACC,GAAG,CAAC,GAAG,EAAE,CAAC;IAEhE,OAAOJ,KAAK;EACd;EAEA,MAAMK,UAAU,CAACC,WAA4B,EAAEZ,IAAyB,EAAoB;IAC1F,MAAMa,KAAU,GAAG;QAAC,GAAGb;MAAI,CAAC;MAC1BT,GAAG,GAAI,GAAEL,aAAa,CAACM,aAAc,WAAUQ,IAAI,CAACc,GAAI,EAAC;MACzDnB,aAAa,GAAGC,YAAY,CAACgB,WAAW,CAACV,IAAI,EAAEU,WAAW,CAACtB,QAAQ,CAAC;MACpEO,OAAO,GAAG;QAAEO,aAAa,EAAET;MAAa,CAAC;MACzCU,QAAQ,GAAG,MAAMpB,GAAG,CAAC8B,GAAG,CAAwBxB,GAAG,EAAEsB,KAAK,EAAE;QAAEhB;MAAQ,CAAC,CAAC;IAE1E,OAAOQ,QAAQ,CAACW,EAAE;EACpB;EAEA,MAAMC,cAAc,CAACL,WAA4B,EAAEZ,IAAyB,EAAEV,QAAgB,EAAoB;IAChH,MAAMuB,KAAU,GAAG;MAAC,GAAGb;IAAI,CAAC;IAE5B,OAAOa,KAAK,CAACK,YAAY;IACzB,OAAOL,KAAK,CAACM,IAAI;IACjBN,KAAK,CAACvB,QAAQ,GAAGA,QAAQ;IAEzB,MAAMC,GAAG,GAAI,GAAEL,aAAa,CAACM,aAAc,WAAUQ,IAAI,CAACc,GAAI,EAAC;MAC7DnB,aAAa,GAAGC,YAAY,CAACgB,WAAW,CAACV,IAAI,EAAEU,WAAW,CAACtB,QAAQ,CAAC;MACpEO,OAAO,GAAG;QAAEO,aAAa,EAAET;MAAa,CAAC;MACzCU,QAAQ,GAAG,MAAMpB,GAAG,CAAC8B,GAAG,CAAwBxB,GAAG,EAAEsB,KAAK,EAAE;QAAEhB;MAAQ,CAAC,CAAC;IAE1E,OAAOQ,QAAQ,CAACW,EAAE;EACpB;AACF;AAEA,OAAO,SAASpB,YAAY,CAACP,QAAgB,EAAEC,QAAgB,EAAE;EAC/D,MAAM8B,IAAI,GAAI,GAAE/B,QAAS,IAAGC,QAAS,EAAC;IAClC+B,MAAM,GAAI,SAAQC,MAAM,CAACC,IAAI,CAACH,IAAI,CAAE,EAAC;EAEzC,OAAOC,MAAM;AACf;AAEA,OAAO,MAAMG,OAAO,GAAG,IAAIrC,WAAW,EAAE"}, "metadata": {}, "sourceType": "module"}