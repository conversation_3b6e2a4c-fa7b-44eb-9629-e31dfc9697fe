{"ast": null, "code": "export function sortBy(propertyName, direction) {\n  const factor = direction === 'descending' ? -1 : 1;\n  return function (a, b) {\n    if (a == null && b == null) return 0;\n    if (a != null && b == null) return 1 * factor;\n    if (a == null && b != null) return -1 * factor;\n    var first = a[propertyName],\n      second = b[propertyName];\n    if (typeof first === 'number' && typeof (second === 'number')) {\n      return (first < second ? -1 : 1) * factor;\n    }\n    if (typeof first === 'boolean' && typeof (second === 'boolean')) {\n      return (first === second ? 0 : first ? 1 : -1) * factor;\n    }\n    if (toString.call(first) === '[object Date]' && toString.call(second) === '[object Date]') {\n      return (first < second ? -1 : first > second ? 1 : 0) * factor;\n    }\n    first = (first || '').toString().toLowerCase();\n    second = (second || '').toString().toLowerCase();\n    return (first < second ? -1 : first > second ? 1 : 0) * factor;\n  };\n}\nexport function sortSizeName(a, b) {\n  const intA = parseFloat(a),\n    intB = parseFloat(b);\n  if (intA && intB) {\n    return intA < intB ? -1 : intA > intB ? 1 : 0;\n  }\n  return a < b ? -1 : a > b ? 1 : 0;\n}", "map": {"version": 3, "names": ["sortBy", "propertyName", "direction", "factor", "a", "b", "first", "second", "toString", "call", "toLowerCase", "sortSizeName", "intA", "parseFloat", "intB"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/utils/sort.ts"], "sourcesContent": ["export function sortBy(propertyName: string, direction?: string) {\r\n  const factor = direction === 'descending' ? -1 : 1;\r\n\r\n  return function(a: any, b: any) {\r\n    if(a == null && b == null) return 0;\r\n    if(a != null && b == null) return 1 * factor;\r\n    if(a == null && b != null) return -1 * factor;\r\n\r\n    var first = a[propertyName],\r\n        second = b[propertyName];\r\n\r\n      if(typeof first === 'number' && typeof(second === 'number')) {\r\n        return (first < second ? -1 : 1) * factor;\r\n      }\r\n\r\n      if(typeof first === 'boolean' && typeof(second === 'boolean')) {\r\n        return ((first === second) ? 0 : first ? 1 : -1) * factor;\r\n      }\r\n\r\n      if(toString.call(first) === '[object Date]' && toString.call(second) === '[object Date]') {\r\n        return (first < second ? -1 : first > second ? 1 : 0) * factor;\r\n      }\r\n\r\n      first = (first || '').toString().toLowerCase();\r\n      second = (second || '').toString().toLowerCase();\r\n      return (first < second ? -1 : first > second ? 1 : 0) * factor;\r\n  }\r\n}\r\n\r\nexport function sortSizeName(a: string, b: string) {\r\n  const intA = parseFloat(a),\r\n    intB = parseFloat(b);\r\n\r\n  if(intA && intB) {\r\n    return intA < intB ? -1 : intA > intB ? 1 : 0;\r\n  }\r\n\r\n  return a < b ? -1 : a > b ? 1 : 0;\r\n}\r\n"], "mappings": "AAAA,OAAO,SAASA,MAAM,CAACC,YAAoB,EAAEC,SAAkB,EAAE;EAC/D,MAAMC,MAAM,GAAGD,SAAS,KAAK,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC;EAElD,OAAO,UAASE,CAAM,EAAEC,CAAM,EAAE;IAC9B,IAAGD,CAAC,IAAI,IAAI,IAAIC,CAAC,IAAI,IAAI,EAAE,OAAO,CAAC;IACnC,IAAGD,CAAC,IAAI,IAAI,IAAIC,CAAC,IAAI,IAAI,EAAE,OAAO,CAAC,GAAGF,MAAM;IAC5C,IAAGC,CAAC,IAAI,IAAI,IAAIC,CAAC,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC,GAAGF,MAAM;IAE7C,IAAIG,KAAK,GAAGF,CAAC,CAACH,YAAY,CAAC;MACvBM,MAAM,GAAGF,CAAC,CAACJ,YAAY,CAAC;IAE1B,IAAG,OAAOK,KAAK,KAAK,QAAQ,IAAI,QAAOC,MAAM,KAAK,QAAQ,CAAC,EAAE;MAC3D,OAAO,CAACD,KAAK,GAAGC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIJ,MAAM;IAC3C;IAEA,IAAG,OAAOG,KAAK,KAAK,SAAS,IAAI,QAAOC,MAAM,KAAK,SAAS,CAAC,EAAE;MAC7D,OAAO,CAAED,KAAK,KAAKC,MAAM,GAAI,CAAC,GAAGD,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,IAAIH,MAAM;IAC3D;IAEA,IAAGK,QAAQ,CAACC,IAAI,CAACH,KAAK,CAAC,KAAK,eAAe,IAAIE,QAAQ,CAACC,IAAI,CAACF,MAAM,CAAC,KAAK,eAAe,EAAE;MACxF,OAAO,CAACD,KAAK,GAAGC,MAAM,GAAG,CAAC,CAAC,GAAGD,KAAK,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,IAAIJ,MAAM;IAChE;IAEAG,KAAK,GAAG,CAACA,KAAK,IAAI,EAAE,EAAEE,QAAQ,EAAE,CAACE,WAAW,EAAE;IAC9CH,MAAM,GAAG,CAACA,MAAM,IAAI,EAAE,EAAEC,QAAQ,EAAE,CAACE,WAAW,EAAE;IAChD,OAAO,CAACJ,KAAK,GAAGC,MAAM,GAAG,CAAC,CAAC,GAAGD,KAAK,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,IAAIJ,MAAM;EAClE,CAAC;AACH;AAEA,OAAO,SAASQ,YAAY,CAACP,CAAS,EAAEC,CAAS,EAAE;EACjD,MAAMO,IAAI,GAAGC,UAAU,CAACT,CAAC,CAAC;IACxBU,IAAI,GAAGD,UAAU,CAACR,CAAC,CAAC;EAEtB,IAAGO,IAAI,IAAIE,IAAI,EAAE;IACf,OAAOF,IAAI,GAAGE,IAAI,GAAG,CAAC,CAAC,GAAGF,IAAI,GAAGE,IAAI,GAAG,CAAC,GAAG,CAAC;EAC/C;EAEA,OAAOV,CAAC,GAAGC,CAAC,GAAG,CAAC,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAG,CAAC;AACnC"}, "metadata": {}, "sourceType": "module"}