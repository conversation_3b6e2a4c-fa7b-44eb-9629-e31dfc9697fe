{"ast": null, "code": "import React, { useMemo } from 'react';\nimport PropTypes from 'prop-types';\nimport { ReactReduxContext } from './Context';\nimport { createSubscription } from '../utils/Subscription';\nimport { useIsomorphicLayoutEffect } from '../utils/useIsomorphicLayoutEffect';\nfunction Provider(_ref) {\n  var store = _ref.store,\n    context = _ref.context,\n    children = _ref.children;\n  var contextValue = useMemo(function () {\n    var subscription = createSubscription(store);\n    return {\n      store: store,\n      subscription: subscription\n    };\n  }, [store]);\n  var previousState = useMemo(function () {\n    return store.getState();\n  }, [store]);\n  useIsomorphicLayoutEffect(function () {\n    var subscription = contextValue.subscription;\n    subscription.onStateChange = subscription.notifyNestedSubs;\n    subscription.trySubscribe();\n    if (previousState !== store.getState()) {\n      subscription.notifyNestedSubs();\n    }\n    return function () {\n      subscription.tryUnsubscribe();\n      subscription.onStateChange = null;\n    };\n  }, [contextValue, previousState]);\n  var Context = context || ReactReduxContext;\n  return /*#__PURE__*/React.createElement(Context.Provider, {\n    value: contextValue\n  }, children);\n}\nif (process.env.NODE_ENV !== 'production') {\n  Provider.propTypes = {\n    store: PropTypes.shape({\n      subscribe: PropTypes.func.isRequired,\n      dispatch: PropTypes.func.isRequired,\n      getState: PropTypes.func.isRequired\n    }),\n    context: PropTypes.object,\n    children: PropTypes.any\n  };\n}\nexport default Provider;", "map": {"version": 3, "names": ["React", "useMemo", "PropTypes", "ReactReduxContext", "createSubscription", "useIsomorphicLayoutEffect", "Provider", "_ref", "store", "context", "children", "contextValue", "subscription", "previousState", "getState", "onStateChange", "notifyNestedSubs", "trySubscribe", "tryUnsubscribe", "Context", "createElement", "value", "process", "env", "NODE_ENV", "propTypes", "shape", "subscribe", "func", "isRequired", "dispatch", "object", "any"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/node_modules/react-redux/es/components/Provider.js"], "sourcesContent": ["import React, { useMemo } from 'react';\nimport PropTypes from 'prop-types';\nimport { ReactReduxContext } from './Context';\nimport { createSubscription } from '../utils/Subscription';\nimport { useIsomorphicLayoutEffect } from '../utils/useIsomorphicLayoutEffect';\n\nfunction Provider(_ref) {\n  var store = _ref.store,\n      context = _ref.context,\n      children = _ref.children;\n  var contextValue = useMemo(function () {\n    var subscription = createSubscription(store);\n    return {\n      store: store,\n      subscription: subscription\n    };\n  }, [store]);\n  var previousState = useMemo(function () {\n    return store.getState();\n  }, [store]);\n  useIsomorphicLayoutEffect(function () {\n    var subscription = contextValue.subscription;\n    subscription.onStateChange = subscription.notifyNestedSubs;\n    subscription.trySubscribe();\n\n    if (previousState !== store.getState()) {\n      subscription.notifyNestedSubs();\n    }\n\n    return function () {\n      subscription.tryUnsubscribe();\n      subscription.onStateChange = null;\n    };\n  }, [contextValue, previousState]);\n  var Context = context || ReactReduxContext;\n  return /*#__PURE__*/React.createElement(Context.Provider, {\n    value: contextValue\n  }, children);\n}\n\nif (process.env.NODE_ENV !== 'production') {\n  Provider.propTypes = {\n    store: PropTypes.shape({\n      subscribe: PropTypes.func.isRequired,\n      dispatch: PropTypes.func.isRequired,\n      getState: PropTypes.func.isRequired\n    }),\n    context: PropTypes.object,\n    children: PropTypes.any\n  };\n}\n\nexport default Provider;"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,OAAO,QAAQ,OAAO;AACtC,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,iBAAiB,QAAQ,WAAW;AAC7C,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,yBAAyB,QAAQ,oCAAoC;AAE9E,SAASC,QAAQ,CAACC,IAAI,EAAE;EACtB,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBC,OAAO,GAAGF,IAAI,CAACE,OAAO;IACtBC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;EAC5B,IAAIC,YAAY,GAAGV,OAAO,CAAC,YAAY;IACrC,IAAIW,YAAY,GAAGR,kBAAkB,CAACI,KAAK,CAAC;IAC5C,OAAO;MACLA,KAAK,EAAEA,KAAK;MACZI,YAAY,EAAEA;IAChB,CAAC;EACH,CAAC,EAAE,CAACJ,KAAK,CAAC,CAAC;EACX,IAAIK,aAAa,GAAGZ,OAAO,CAAC,YAAY;IACtC,OAAOO,KAAK,CAACM,QAAQ,EAAE;EACzB,CAAC,EAAE,CAACN,KAAK,CAAC,CAAC;EACXH,yBAAyB,CAAC,YAAY;IACpC,IAAIO,YAAY,GAAGD,YAAY,CAACC,YAAY;IAC5CA,YAAY,CAACG,aAAa,GAAGH,YAAY,CAACI,gBAAgB;IAC1DJ,YAAY,CAACK,YAAY,EAAE;IAE3B,IAAIJ,aAAa,KAAKL,KAAK,CAACM,QAAQ,EAAE,EAAE;MACtCF,YAAY,CAACI,gBAAgB,EAAE;IACjC;IAEA,OAAO,YAAY;MACjBJ,YAAY,CAACM,cAAc,EAAE;MAC7BN,YAAY,CAACG,aAAa,GAAG,IAAI;IACnC,CAAC;EACH,CAAC,EAAE,CAACJ,YAAY,EAAEE,aAAa,CAAC,CAAC;EACjC,IAAIM,OAAO,GAAGV,OAAO,IAAIN,iBAAiB;EAC1C,OAAO,aAAaH,KAAK,CAACoB,aAAa,CAACD,OAAO,CAACb,QAAQ,EAAE;IACxDe,KAAK,EAAEV;EACT,CAAC,EAAED,QAAQ,CAAC;AACd;AAEA,IAAIY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzClB,QAAQ,CAACmB,SAAS,GAAG;IACnBjB,KAAK,EAAEN,SAAS,CAACwB,KAAK,CAAC;MACrBC,SAAS,EAAEzB,SAAS,CAAC0B,IAAI,CAACC,UAAU;MACpCC,QAAQ,EAAE5B,SAAS,CAAC0B,IAAI,CAACC,UAAU;MACnCf,QAAQ,EAAEZ,SAAS,CAAC0B,IAAI,CAACC;IAC3B,CAAC,CAAC;IACFpB,OAAO,EAAEP,SAAS,CAAC6B,MAAM;IACzBrB,QAAQ,EAAER,SAAS,CAAC8B;EACtB,CAAC;AACH;AAEA,eAAe1B,QAAQ"}, "metadata": {}, "sourceType": "module"}