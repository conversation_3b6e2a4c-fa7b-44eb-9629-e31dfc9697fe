{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\GreenRP\\\\Web\\\\ClientApp\\\\src\\\\features\\\\customers\\\\Detail.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { useParams, useNavigate } from 'react-router';\nimport { Link } from 'react-router-dom';\nimport { Button, Input } from 'reactstrap';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { routes } from 'app/routes';\nimport { useAuth } from 'features/auth/use-auth';\nimport { selectCustomers } from './customers-slice';\nimport { deleteCustomer, saveCustomer, selectCustomer, setCustomer } from './detail-slice';\nimport { createCustomer } from 'api/models/customers';\nimport { handleFocus } from 'utils/focus';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport function Detail() {\n  _s();\n  const dispatch = useDispatch(),\n    navigate = useNavigate(),\n    {\n      isInRole\n    } = useAuth(),\n    {\n      id\n    } = useParams(),\n    customers = useSelector(selectCustomers),\n    customer = useSelector(selectCustomer),\n    isNew = !customer._rev,\n    canUpdate = isNew && isInRole('create:customers') || isInRole('update:customers'),\n    canDelete = isInRole('delete:customers');\n  useEffect(() => {\n    const found = customers.find(p => p._id === id);\n    if (found && found._id !== customer._id) {\n      dispatch(setCustomer(found));\n    } else if (id === 'new' && customer._rev) {\n      dispatch(setCustomer(createCustomer()));\n    }\n  }, [dispatch, id, customer, customers]);\n  const handleNameChange = e => {\n    const name = e.target.value,\n      update = {\n        ...customer,\n        name\n      };\n    dispatch(setCustomer(update));\n  };\n  const handleAbbreviationChange = e => {\n    const abbreviation = e.target.value,\n      update = {\n        ...customer,\n        abbreviation\n      };\n    dispatch(setCustomer(update));\n  };\n  const handleSaveClick = async () => {\n    const result = await dispatch(saveCustomer());\n    if (!result.error) {\n      navigate(routes.customers.path);\n    }\n  };\n  const handleDeleteClick = async () => {\n    const result = await dispatch(deleteCustomer());\n    if (!result.error) {\n      navigate(routes.customers.path);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container d-grid gap-2\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: routes.customers.path,\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: ['fat', 'chevron-left']\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this), \"\\xA0 Back to Customers List\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n      children: isNew ? 'New Customer' : customer.name\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"customer-name\",\n          children: \"Name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"customer-name\",\n          value: customer.name,\n          onChange: handleNameChange,\n          disabled: !canUpdate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"customer-abbreviation\",\n          children: \"Abbreviation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"customer-abbreviation\",\n          value: customer.abbreviation,\n          onChange: handleAbbreviationChange,\n          onFocus: handleFocus,\n          disabled: !canUpdate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row sticky-bottom bg-white border-top py-2\",\n      children: [!isNew && canDelete && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-auto\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleDeleteClick,\n          outline: true,\n          color: \"danger\",\n          size: \"lg\",\n          className: \"me-auto\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: ['fat', 'trash-alt']\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 15\n          }, this), \"\\xA0 Delete\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col text-end\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          tag: Link,\n          to: routes.customers.path,\n          outline: true,\n          size: \"lg\",\n          children: canUpdate ? 'Cancel' : 'Close'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), canUpdate && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [\"\\xA0\", /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSaveClick,\n            color: \"success\",\n            size: \"lg\",\n            children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: ['fat', 'save']\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this), \"\\xA0 Save\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 65,\n    columnNumber: 5\n  }, this);\n}\n_s(Detail, \"oj79EXay0ycpoxbS3VU5wOgYXRU=\", false, function () {\n  return [useDispatch, useNavigate, useAuth, useParams, useSelector, useSelector];\n});\n_c = Detail;\nexport default Detail;\nvar _c;\n$RefreshReg$(_c, \"Detail\");", "map": {"version": 3, "names": ["React", "useEffect", "useSelector", "useDispatch", "useParams", "useNavigate", "Link", "<PERSON><PERSON>", "Input", "FontAwesomeIcon", "routes", "useAuth", "selectCustomers", "deleteCustomer", "saveCustomer", "selectCustomer", "setCustomer", "createCustomer", "handleFocus", "Detail", "dispatch", "navigate", "isInRole", "id", "customers", "customer", "isNew", "_rev", "canUpdate", "canDelete", "found", "find", "p", "_id", "handleNameChange", "e", "name", "target", "value", "update", "handleAbbreviationChange", "abbreviation", "handleSaveClick", "result", "error", "path", "handleDeleteClick"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/customers/Detail.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\r\nimport { useSelector, useDispatch } from 'react-redux';\r\nimport { useParams, useNavigate } from 'react-router';\r\nimport { Link } from 'react-router-dom';\r\nimport { Button, Input } from 'reactstrap';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { routes } from 'app/routes';\r\nimport { useAuth } from 'features/auth/use-auth';\r\nimport { selectCustomers } from './customers-slice';\r\nimport { deleteCustomer, saveCustomer, selectCustomer, setCustomer } from './detail-slice';\r\nimport { createCustomer } from 'api/models/customers';\r\nimport { handleFocus } from 'utils/focus';\r\n\r\nexport function Detail() {\r\n  const dispatch = useDispatch(),\r\n    navigate = useNavigate(),\r\n    {isInRole} = useAuth(),\r\n    {id} = useParams<{id: string}>(),\r\n    customers = useSelector(selectCustomers),\r\n    customer = useSelector(selectCustomer),\r\n    isNew = !customer._rev,\r\n    canUpdate = (isNew && isInRole('create:customers')) || isInRole('update:customers'),\r\n    canDelete = isInRole('delete:customers');\r\n\r\n  useEffect(() => {\r\n    const found = customers.find(p => p._id === id);\r\n    if(found && found._id !== customer._id) {\r\n      dispatch(setCustomer(found));\r\n    } else if(id === 'new' && customer._rev) {\r\n      dispatch(setCustomer(createCustomer()));\r\n    }\r\n  }, [dispatch, id, customer, customers]);\r\n\r\n  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const name = e.target.value,\r\n      update = {...customer, name};\r\n\r\n    dispatch(setCustomer(update));\r\n  };\r\n\r\n  const handleAbbreviationChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const abbreviation = e.target.value,\r\n      update = {...customer, abbreviation};\r\n\r\n    dispatch(setCustomer(update));\r\n  };\r\n\r\n  const handleSaveClick = async () => {\r\n    const result: any = await dispatch(saveCustomer());\r\n\r\n    if(!result.error) {\r\n      navigate(routes.customers.path);\r\n    }\r\n  };\r\n\r\n  const handleDeleteClick = async () => {\r\n    const result: any = await dispatch(deleteCustomer());\r\n\r\n    if(!result.error) {\r\n      navigate(routes.customers.path);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"container d-grid gap-2\">\r\n      <div className=\"row\">\r\n        <div className=\"col\">\r\n          <Link to={routes.customers.path}>\r\n            <FontAwesomeIcon icon={['fat', 'chevron-left']} />\r\n            &nbsp;\r\n            Back to Customers List</Link>\r\n        </div>\r\n      </div>\r\n      <h1>{isNew ? 'New Customer' : customer.name}</h1>      \r\n      <div className=\"row\">\r\n        <div className=\"col-12 col-md-4\">\r\n          <label htmlFor=\"customer-name\">Name</label>\r\n          <Input id=\"customer-name\" value={customer.name} onChange={handleNameChange} disabled={!canUpdate} />\r\n        </div>\r\n        <div className=\"col-12 col-md-4\">\r\n          <label htmlFor=\"customer-abbreviation\">Abbreviation</label>\r\n          <Input id=\"customer-abbreviation\" value={customer.abbreviation} onChange={handleAbbreviationChange} onFocus={handleFocus} disabled={!canUpdate} />\r\n        </div>\r\n      </div>\r\n      <div className=\"row sticky-bottom bg-white border-top py-2\">        \r\n        {!isNew && canDelete &&\r\n          <div className=\"col-auto\">\r\n            <Button onClick={handleDeleteClick} outline color=\"danger\" size=\"lg\" className=\"me-auto\">\r\n              <FontAwesomeIcon icon={['fat', 'trash-alt']} />\r\n              &nbsp;\r\n              Delete\r\n            </Button>\r\n          </div>\r\n        }\r\n        <div className=\"col text-end\">\r\n          <Button tag={Link} to={routes.customers.path} outline size=\"lg\">{canUpdate ? 'Cancel' : 'Close'}</Button>\r\n          {canUpdate &&\r\n          <>\r\n            &nbsp;\r\n            <Button onClick={handleSaveClick} color=\"success\" size=\"lg\">\r\n              <FontAwesomeIcon icon={['fat', 'save']} />\r\n              &nbsp;\r\n              Save\r\n            </Button>\r\n          </>\r\n          }\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default Detail;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,SAAS,EAAEC,WAAW,QAAQ,cAAc;AACrD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,MAAM,EAAEC,KAAK,QAAQ,YAAY;AAC1C,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,cAAc,EAAEC,YAAY,EAAEC,cAAc,EAAEC,WAAW,QAAQ,gBAAgB;AAC1F,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,WAAW,QAAQ,aAAa;AAAC;AAAA;AAE1C,OAAO,SAASC,MAAM,GAAG;EAAA;EACvB,MAAMC,QAAQ,GAAGjB,WAAW,EAAE;IAC5BkB,QAAQ,GAAGhB,WAAW,EAAE;IACxB;MAACiB;IAAQ,CAAC,GAAGX,OAAO,EAAE;IACtB;MAACY;IAAE,CAAC,GAAGnB,SAAS,EAAgB;IAChCoB,SAAS,GAAGtB,WAAW,CAACU,eAAe,CAAC;IACxCa,QAAQ,GAAGvB,WAAW,CAACa,cAAc,CAAC;IACtCW,KAAK,GAAG,CAACD,QAAQ,CAACE,IAAI;IACtBC,SAAS,GAAIF,KAAK,IAAIJ,QAAQ,CAAC,kBAAkB,CAAC,IAAKA,QAAQ,CAAC,kBAAkB,CAAC;IACnFO,SAAS,GAAGP,QAAQ,CAAC,kBAAkB,CAAC;EAE1CrB,SAAS,CAAC,MAAM;IACd,MAAM6B,KAAK,GAAGN,SAAS,CAACO,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKV,EAAE,CAAC;IAC/C,IAAGO,KAAK,IAAIA,KAAK,CAACG,GAAG,KAAKR,QAAQ,CAACQ,GAAG,EAAE;MACtCb,QAAQ,CAACJ,WAAW,CAACc,KAAK,CAAC,CAAC;IAC9B,CAAC,MAAM,IAAGP,EAAE,KAAK,KAAK,IAAIE,QAAQ,CAACE,IAAI,EAAE;MACvCP,QAAQ,CAACJ,WAAW,CAACC,cAAc,EAAE,CAAC,CAAC;IACzC;EACF,CAAC,EAAE,CAACG,QAAQ,EAAEG,EAAE,EAAEE,QAAQ,EAAED,SAAS,CAAC,CAAC;EAEvC,MAAMU,gBAAgB,GAAIC,CAAsC,IAAK;IACnE,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK;MACzBC,MAAM,GAAG;QAAC,GAAGd,QAAQ;QAAEW;MAAI,CAAC;IAE9BhB,QAAQ,CAACJ,WAAW,CAACuB,MAAM,CAAC,CAAC;EAC/B,CAAC;EAED,MAAMC,wBAAwB,GAAIL,CAAsC,IAAK;IAC3E,MAAMM,YAAY,GAAGN,CAAC,CAACE,MAAM,CAACC,KAAK;MACjCC,MAAM,GAAG;QAAC,GAAGd,QAAQ;QAAEgB;MAAY,CAAC;IAEtCrB,QAAQ,CAACJ,WAAW,CAACuB,MAAM,CAAC,CAAC;EAC/B,CAAC;EAED,MAAMG,eAAe,GAAG,YAAY;IAClC,MAAMC,MAAW,GAAG,MAAMvB,QAAQ,CAACN,YAAY,EAAE,CAAC;IAElD,IAAG,CAAC6B,MAAM,CAACC,KAAK,EAAE;MAChBvB,QAAQ,CAACX,MAAM,CAACc,SAAS,CAACqB,IAAI,CAAC;IACjC;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAG,YAAY;IACpC,MAAMH,MAAW,GAAG,MAAMvB,QAAQ,CAACP,cAAc,EAAE,CAAC;IAEpD,IAAG,CAAC8B,MAAM,CAACC,KAAK,EAAE;MAChBvB,QAAQ,CAACX,MAAM,CAACc,SAAS,CAACqB,IAAI,CAAC;IACjC;EACF,CAAC;EAED,oBACE;IAAK,SAAS,EAAC,wBAAwB;IAAA,wBACrC;MAAK,SAAS,EAAC,KAAK;MAAA,uBAClB;QAAK,SAAS,EAAC,KAAK;QAAA,uBAClB,QAAC,IAAI;UAAC,EAAE,EAAEnC,MAAM,CAACc,SAAS,CAACqB,IAAK;UAAA,wBAC9B,QAAC,eAAe;YAAC,IAAI,EAAE,CAAC,KAAK,EAAE,cAAc;UAAE;YAAA;YAAA;YAAA;UAAA,QAAG;QAAA;UAAA;UAAA;UAAA;QAAA;MAErB;QAAA;QAAA;QAAA;MAAA;IAC3B;MAAA;MAAA;MAAA;IAAA,QACF,eACN;MAAA,UAAKnB,KAAK,GAAG,cAAc,GAAGD,QAAQ,CAACW;IAAI;MAAA;MAAA;MAAA;IAAA,QAAM,eACjD;MAAK,SAAS,EAAC,KAAK;MAAA,wBAClB;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,eAAe;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAAa,eAC3C,QAAC,KAAK;UAAC,EAAE,EAAC,eAAe;UAAC,KAAK,EAAEX,QAAQ,CAACW,IAAK;UAAC,QAAQ,EAAEF,gBAAiB;UAAC,QAAQ,EAAE,CAACN;QAAU;UAAA;UAAA;UAAA;QAAA,QAAG;MAAA;QAAA;QAAA;QAAA;MAAA,QAChG,eACN;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,uBAAuB;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAAqB,eAC3D,QAAC,KAAK;UAAC,EAAE,EAAC,uBAAuB;UAAC,KAAK,EAAEH,QAAQ,CAACgB,YAAa;UAAC,QAAQ,EAAED,wBAAyB;UAAC,OAAO,EAAEtB,WAAY;UAAC,QAAQ,EAAE,CAACU;QAAU;UAAA;UAAA;UAAA;QAAA,QAAG;MAAA;QAAA;QAAA;QAAA;MAAA,QAC9I;IAAA;MAAA;MAAA;MAAA;IAAA,QACF,eACN;MAAK,SAAS,EAAC,4CAA4C;MAAA,WACxD,CAACF,KAAK,IAAIG,SAAS,iBAClB;QAAK,SAAS,EAAC,UAAU;QAAA,uBACvB,QAAC,MAAM;UAAC,OAAO,EAAEiB,iBAAkB;UAAC,OAAO;UAAC,KAAK,EAAC,QAAQ;UAAC,IAAI,EAAC,IAAI;UAAC,SAAS,EAAC,SAAS;UAAA,wBACtF,QAAC,eAAe;YAAC,IAAI,EAAE,CAAC,KAAK,EAAE,WAAW;UAAE;YAAA;YAAA;YAAA;UAAA,QAAG;QAAA;UAAA;UAAA;UAAA;QAAA;MAGxC;QAAA;QAAA;QAAA;MAAA,QACL,eAER;QAAK,SAAS,EAAC,cAAc;QAAA,wBAC3B,QAAC,MAAM;UAAC,GAAG,EAAExC,IAAK;UAAC,EAAE,EAAEI,MAAM,CAACc,SAAS,CAACqB,IAAK;UAAC,OAAO;UAAC,IAAI,EAAC,IAAI;UAAA,UAAEjB,SAAS,GAAG,QAAQ,GAAG;QAAO;UAAA;UAAA;UAAA;QAAA,QAAU,EACxGA,SAAS,iBACV;UAAA,gCAEE,QAAC,MAAM;YAAC,OAAO,EAAEc,eAAgB;YAAC,KAAK,EAAC,SAAS;YAAC,IAAI,EAAC,IAAI;YAAA,wBACzD,QAAC,eAAe;cAAC,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM;YAAE;cAAA;cAAA;cAAA;YAAA,QAAG;UAAA;YAAA;YAAA;YAAA;UAAA,QAGnC;QAAA,gBACR;MAAA;QAAA;QAAA;QAAA;MAAA,QAEC;IAAA;MAAA;MAAA;MAAA;IAAA,QACF;EAAA;IAAA;IAAA;IAAA;EAAA,QACF;AAEV;AAAC,GAjGevB,MAAM;EAAA,QACHhB,WAAW,EACfE,WAAW,EACTM,OAAO,EACbP,SAAS,EACJF,WAAW,EACZA,WAAW;AAAA;AAAA,KANViB,MAAM;AAmGtB,eAAeA,MAAM;AAAC;AAAA"}, "metadata": {}, "sourceType": "module"}