{"ast": null, "code": "import { guid } from \"utils/guid\";\nexport const ColourType = 'colour';\nexport function createColour(name, hex) {\n  return {\n    _id: guid(),\n    type: ColourType,\n    name,\n    hex\n  };\n}", "map": {"version": 3, "names": ["guid", "ColourType", "createColour", "name", "hex", "_id", "type"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/api/models/colour.ts"], "sourcesContent": ["import { guid } from \"utils/guid\";\r\nimport { ModelBase } from \"./model-base\";\r\n\r\n\r\nexport const ColourType = 'colour';\r\n\r\nexport interface Colour extends ModelBase {\r\n  type: string;\r\n  name: string;\r\n  hex: string;\r\n}\r\n\r\nexport function createColour(name: string, hex: string): Colour {\r\n  return {\r\n    _id: guid(),\r\n    type: ColourType,\r\n    name,\r\n    hex,\r\n  };\r\n}\r\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,YAAY;AAIjC,OAAO,MAAMC,UAAU,GAAG,QAAQ;AAQlC,OAAO,SAASC,YAAY,CAACC,IAAY,EAAEC,GAAW,EAAU;EAC9D,OAAO;IACLC,GAAG,EAAEL,IAAI,EAAE;IACXM,IAAI,EAAEL,UAAU;IAChBE,IAAI;IACJC;EACF,CAAC;AACH"}, "metadata": {}, "sourceType": "module"}