{"ast": null, "code": "import { ServiceBase } from './service-base';\nclass ColourService extends ServiceBase {\n  getAll() {\n    return this.query('filters/colours');\n  }\n  save(colour) {\n    return this.saveDocument(colour);\n  }\n}\nexport const colourApi = new ColourService();", "map": {"version": 3, "names": ["ServiceBase", "ColourService", "getAll", "query", "save", "colour", "saveDocument", "colourApi"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/api/colour-service.ts"], "sourcesContent": ["import * as models from './models/colour';\r\nimport { ServiceBase } from './service-base';\r\n\r\nclass ColourService extends ServiceBase {\r\n  getAll() {\r\n    return this.query<models.Colour>('filters/colours');\r\n  }\r\n\r\n  save(colour: models.Colour) {\r\n    return this.saveDocument<models.Colour>(colour);\r\n  }\r\n}\r\n\r\nexport const colourApi = new ColourService();\r\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,gBAAgB;AAE5C,MAAMC,aAAa,SAASD,WAAW,CAAC;EACtCE,MAAM,GAAG;IACP,OAAO,IAAI,CAACC,KAAK,CAAgB,iBAAiB,CAAC;EACrD;EAEAC,IAAI,CAACC,MAAqB,EAAE;IAC1B,OAAO,IAAI,CAACC,YAAY,CAAgBD,MAAM,CAAC;EACjD;AACF;AAEA,OAAO,MAAME,SAAS,GAAG,IAAIN,aAAa,EAAE"}, "metadata": {}, "sourceType": "module"}