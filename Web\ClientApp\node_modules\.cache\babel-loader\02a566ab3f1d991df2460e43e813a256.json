{"ast": null, "code": "import { database } from 'app/database';\nimport { createProblemDetails } from 'utils/problem-details';\nexport class ServiceBase {\n  async getAllDocuments(filter) {\n    try {\n      const {\n          rows\n        } = await database.db.query(filter, {\n          include_docs: true\n        }),\n        // https://stackoverflow.com/a/59726888\n        docs = rows.flatMap(row => row.doc ? [row.doc] : []);\n      return docs;\n    } catch (e) {\n      throw createProblemDetails(e);\n    }\n  }\n  async query(view, startkey, endkey) {\n    try {\n      const options = {\n        include_docs: true\n      };\n      if (startkey) {\n        options.startkey = startkey;\n      }\n      if (endkey) {\n        options.endkey = endkey;\n      }\n      const {\n          rows\n        } = await database.db.query(view, options),\n        // https://stackoverflow.com/a/59726888\n        docs = rows.flatMap(row => row.doc ? [row.doc] : []);\n      return docs;\n    } catch (e) {\n      throw createProblemDetails(e);\n    }\n  }\n  async getOneDocument(id) {\n    try {\n      return await database.db.get(id);\n    } catch (e) {\n      throw createProblemDetails(e);\n    }\n  }\n  async saveDocument(model) {\n    try {\n      const response = model._rev ? await database.db.put(model) : await database.db.post(model);\n      if (response.ok) {\n        model._id = response.id;\n        model._rev = response.rev;\n        return model;\n      }\n      console.error(response);\n      throw createProblemDetails('Document was not saved');\n    } catch (e) {\n      throw createProblemDetails(e);\n    }\n  }\n  async delete(model) {\n    try {\n      if (model._rev) {\n        const response = await database.db.remove(model._id, model._rev);\n        if (!response.ok) {\n          console.error(response);\n          throw createProblemDetails('Document was not deleted');\n        }\n      } else {\n        throw createProblemDetails('Could not delete unsaved document.');\n      }\n    } catch (e) {\n      throw createProblemDetails(e);\n    }\n  }\n}", "map": {"version": 3, "names": ["database", "createProblemDetails", "ServiceBase", "getAllDocuments", "filter", "rows", "db", "query", "include_docs", "docs", "flatMap", "row", "doc", "e", "view", "startkey", "endkey", "options", "getOneDocument", "id", "get", "saveDocument", "model", "response", "_rev", "put", "post", "ok", "_id", "rev", "console", "error", "delete", "remove"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/api/service-base.ts"], "sourcesContent": ["import { database } from 'app/database';\r\nimport { createProblemDetails } from 'utils/problem-details';\r\nimport * as models from './models/model-base';\r\n\r\ntype KeyType = string | number | string[] | number[];\r\n\r\nexport class ServiceBase {\r\n  async getAllDocuments<T>(filter: string): Promise<T[]> {\r\n    try {\r\n      const { rows } = await database.db.query<T>(filter, { include_docs: true }),\r\n        // https://stackoverflow.com/a/59726888\r\n        docs = rows.flatMap(row => (row.doc ? [row.doc] : []));\r\n\r\n      return docs;\r\n    } catch (e) {\r\n      throw createProblemDetails(e);\r\n    }\r\n  }\r\n\r\n  async query<T extends {}>(view: string, startkey?: KeyType, endkey?: KeyType): Promise<T[]> {\r\n    try {\r\n      const options: PouchDB.Query.Options<T, T> = { include_docs: true };\r\n      if (startkey) {\r\n        options.startkey = startkey;\r\n      }\r\n      if (endkey) {\r\n        options.endkey = endkey;\r\n      }\r\n\r\n      const { rows } = await database.db.query<T, T>(view, options),\r\n        // https://stackoverflow.com/a/59726888\r\n        docs = rows.flatMap(row => (row.doc ? [row.doc] : []));\r\n\r\n      return docs;\r\n    } catch (e) {\r\n      throw createProblemDetails(e);\r\n    }\r\n  }\r\n\r\n  async getOneDocument<T>(id: string): Promise<T> {\r\n    try {\r\n      return await database.db.get<T>(id);\r\n    } catch (e) {\r\n      throw createProblemDetails(e);\r\n    }\r\n  }\r\n\r\n  async saveDocument<T extends models.ModelBase>(model: T) {\r\n    try {\r\n      const response = model._rev ? await database.db.put<T>(model) : await database.db.post<T>(model);\r\n      if (response.ok) {\r\n        model._id = response.id;\r\n        model._rev = response.rev;\r\n        return model;\r\n      }\r\n\r\n      console.error(response);\r\n      throw createProblemDetails('Document was not saved');\r\n    } catch (e) {\r\n      throw createProblemDetails(e);\r\n    }\r\n  }\r\n\r\n  async delete(model: models.ModelBase) {\r\n    try {\r\n      if (model._rev) {\r\n        const response = await database.db.remove(model._id, model._rev);\r\n\r\n        if (!response.ok) {\r\n          console.error(response);\r\n          throw createProblemDetails('Document was not deleted');\r\n        }\r\n      } else {\r\n        throw createProblemDetails('Could not delete unsaved document.');\r\n      }\r\n    } catch (e) {\r\n      throw createProblemDetails(e);\r\n    }\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,cAAc;AACvC,SAASC,oBAAoB,QAAQ,uBAAuB;AAK5D,OAAO,MAAMC,WAAW,CAAC;EACvB,MAAMC,eAAe,CAAIC,MAAc,EAAgB;IACrD,IAAI;MACF,MAAM;UAAEC;QAAK,CAAC,GAAG,MAAML,QAAQ,CAACM,EAAE,CAACC,KAAK,CAAIH,MAAM,EAAE;UAAEI,YAAY,EAAE;QAAK,CAAC,CAAC;QACzE;QACAC,IAAI,GAAGJ,IAAI,CAACK,OAAO,CAACC,GAAG,IAAKA,GAAG,CAACC,GAAG,GAAG,CAACD,GAAG,CAACC,GAAG,CAAC,GAAG,EAAG,CAAC;MAExD,OAAOH,IAAI;IACb,CAAC,CAAC,OAAOI,CAAC,EAAE;MACV,MAAMZ,oBAAoB,CAACY,CAAC,CAAC;IAC/B;EACF;EAEA,MAAMN,KAAK,CAAeO,IAAY,EAAEC,QAAkB,EAAEC,MAAgB,EAAgB;IAC1F,IAAI;MACF,MAAMC,OAAoC,GAAG;QAAET,YAAY,EAAE;MAAK,CAAC;MACnE,IAAIO,QAAQ,EAAE;QACZE,OAAO,CAACF,QAAQ,GAAGA,QAAQ;MAC7B;MACA,IAAIC,MAAM,EAAE;QACVC,OAAO,CAACD,MAAM,GAAGA,MAAM;MACzB;MAEA,MAAM;UAAEX;QAAK,CAAC,GAAG,MAAML,QAAQ,CAACM,EAAE,CAACC,KAAK,CAAOO,IAAI,EAAEG,OAAO,CAAC;QAC3D;QACAR,IAAI,GAAGJ,IAAI,CAACK,OAAO,CAACC,GAAG,IAAKA,GAAG,CAACC,GAAG,GAAG,CAACD,GAAG,CAACC,GAAG,CAAC,GAAG,EAAG,CAAC;MAExD,OAAOH,IAAI;IACb,CAAC,CAAC,OAAOI,CAAC,EAAE;MACV,MAAMZ,oBAAoB,CAACY,CAAC,CAAC;IAC/B;EACF;EAEA,MAAMK,cAAc,CAAIC,EAAU,EAAc;IAC9C,IAAI;MACF,OAAO,MAAMnB,QAAQ,CAACM,EAAE,CAACc,GAAG,CAAID,EAAE,CAAC;IACrC,CAAC,CAAC,OAAON,CAAC,EAAE;MACV,MAAMZ,oBAAoB,CAACY,CAAC,CAAC;IAC/B;EACF;EAEA,MAAMQ,YAAY,CAA6BC,KAAQ,EAAE;IACvD,IAAI;MACF,MAAMC,QAAQ,GAAGD,KAAK,CAACE,IAAI,GAAG,MAAMxB,QAAQ,CAACM,EAAE,CAACmB,GAAG,CAAIH,KAAK,CAAC,GAAG,MAAMtB,QAAQ,CAACM,EAAE,CAACoB,IAAI,CAAIJ,KAAK,CAAC;MAChG,IAAIC,QAAQ,CAACI,EAAE,EAAE;QACfL,KAAK,CAACM,GAAG,GAAGL,QAAQ,CAACJ,EAAE;QACvBG,KAAK,CAACE,IAAI,GAAGD,QAAQ,CAACM,GAAG;QACzB,OAAOP,KAAK;MACd;MAEAQ,OAAO,CAACC,KAAK,CAACR,QAAQ,CAAC;MACvB,MAAMtB,oBAAoB,CAAC,wBAAwB,CAAC;IACtD,CAAC,CAAC,OAAOY,CAAC,EAAE;MACV,MAAMZ,oBAAoB,CAACY,CAAC,CAAC;IAC/B;EACF;EAEA,MAAMmB,MAAM,CAACV,KAAuB,EAAE;IACpC,IAAI;MACF,IAAIA,KAAK,CAACE,IAAI,EAAE;QACd,MAAMD,QAAQ,GAAG,MAAMvB,QAAQ,CAACM,EAAE,CAAC2B,MAAM,CAACX,KAAK,CAACM,GAAG,EAAEN,KAAK,CAACE,IAAI,CAAC;QAEhE,IAAI,CAACD,QAAQ,CAACI,EAAE,EAAE;UAChBG,OAAO,CAACC,KAAK,CAACR,QAAQ,CAAC;UACvB,MAAMtB,oBAAoB,CAAC,0BAA0B,CAAC;QACxD;MACF,CAAC,MAAM;QACL,MAAMA,oBAAoB,CAAC,oCAAoC,CAAC;MAClE;IACF,CAAC,CAAC,OAAOY,CAAC,EAAE;MACV,MAAMZ,oBAAoB,CAACY,CAAC,CAAC;IAC/B;EACF;AACF"}, "metadata": {}, "sourceType": "module"}