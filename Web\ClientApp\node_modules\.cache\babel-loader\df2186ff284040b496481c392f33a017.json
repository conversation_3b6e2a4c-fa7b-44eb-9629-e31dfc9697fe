{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\GreenRP\\\\Web\\\\ClientApp\\\\src\\\\features\\\\orders\\\\LabourReport.tsx\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport { <PERSON><PERSON>, Modal, ModalHeader, ModalBody, ModalFooter, FormGroup, Input } from 'reactstrap';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { reportsApi } from 'api/reports-service';\nimport { Error } from 'features/error/Error';\nimport { createProblemDetails } from 'utils/problem-details';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport function LabourReport(_ref) {\n  _s();\n  let {\n    show,\n    onClose\n  } = _ref;\n  const [year, setYear] = useState(new Date().getFullYear().toString()),\n    [error, setError] = useState(null);\n  const handleYearChange = e => {\n    setYear(e.target.value);\n  };\n  const handleDownloadClick = async () => {\n    try {\n      setError(null);\n      const yearInt = parseInt(year);\n      if (!yearInt) {\n        return setError(createProblemDetails('Please choose a valid year.'));\n      }\n      await reportsApi.labourReport(parseInt(year));\n      onClose();\n    } catch (e) {\n      setError(e);\n    }\n  };\n  const handleCancelClick = () => {\n    onClose();\n  };\n  const handleClearErrorCLick = () => {\n    setError(null);\n  };\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    isOpen: show,\n    toggle: handleCancelClick,\n    autoFocus: false,\n    children: [/*#__PURE__*/_jsxDEV(ModalHeader, {\n      toggle: handleCancelClick,\n      children: \"Labour Hours Report\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ModalBody, {\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-center lead\",\n        children: \"This will download an Excel file comparing Labour Hours for the selected year with the prior year.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n        className: \"max-w-120px mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"year\",\n          children: \"Year\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"year\",\n          type: \"number\",\n          bsSize: \"lg\",\n          className: \"text-center\",\n          value: year,\n          onChange: handleYearChange,\n          autoFocus: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Error, {\n        error: error,\n        clearError: handleClearErrorCLick\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ModalFooter, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        outline: true,\n        onClick: handleCancelClick,\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        color: \"primary\",\n        outline: true,\n        onClick: handleDownloadClick,\n        children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n          icon: ['fat', 'chart-line']\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), \"\\xA0 Download\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n}\n_s(LabourReport, \"CFFmP/q98M2zMmdH+D8/lQtIvAE=\");\n_c = LabourReport;\nvar _c;\n$RefreshReg$(_c, \"LabourReport\");", "map": {"version": 3, "names": ["useState", "<PERSON><PERSON>", "Modal", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ModalBody", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FormGroup", "Input", "FontAwesomeIcon", "reportsApi", "Error", "createProblemDetails", "LabourReport", "show", "onClose", "year", "setYear", "Date", "getFullYear", "toString", "error", "setError", "handleYearChange", "e", "target", "value", "handleDownloadClick", "yearInt", "parseInt", "labourReport", "handleCancelClick", "handleClearErrorCLick"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/orders/LabourReport.tsx"], "sourcesContent": ["import { useState } from 'react';\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON>er,\r\n  ModalBody,\r\n  ModalFooter,\r\n  FormGroup,\r\n  Input,\r\n} from 'reactstrap';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { reportsApi } from 'api/reports-service';\r\nimport { Error } from 'features/error/Error';\r\nimport { createProblemDetails, ProblemDetails } from 'utils/problem-details';\r\n\r\ninterface LabourReportProps {\r\n  show: boolean;\r\n  onClose: () => void;\r\n}\r\n\r\nexport function LabourReport({ show, onClose }: LabourReportProps) {\r\n  const [year, setYear] = useState(new Date().getFullYear().toString()),\r\n    [error, setError] = useState<ProblemDetails | null>(null);\r\n\r\n  const handleYearChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    setYear(e.target.value);\r\n  };\r\n\r\n  const handleDownloadClick = async () => {\r\n    try {\r\n      setError(null);\r\n      const yearInt = parseInt(year);\r\n      if (!yearInt) {\r\n        return setError(createProblemDetails('Please choose a valid year.'));\r\n      }\r\n\r\n      await reportsApi.labourReport(parseInt(year));\r\n      onClose();\r\n    } catch (e) {\r\n      setError(e as ProblemDetails);\r\n    }\r\n  };\r\n\r\n  const handleCancelClick = () => {\r\n    onClose();\r\n  };\r\n\r\n  const handleClearErrorCLick = () => {\r\n    setError(null);\r\n  };\r\n\r\n  return (\r\n    <Modal isOpen={show} toggle={handleCancelClick} autoFocus={false}>\r\n      <ModalHeader toggle={handleCancelClick}>Labour Hours Report</ModalHeader>\r\n      <ModalBody>\r\n        <p className=\"text-center lead\">\r\n          This will download an Excel file comparing Labour Hours for the\r\n          selected year with the prior year.\r\n        </p>\r\n        <FormGroup className=\"max-w-120px mx-auto\">\r\n          <label htmlFor=\"year\">Year</label>\r\n          <Input\r\n            id=\"year\"\r\n            type=\"number\"\r\n            bsSize=\"lg\"\r\n            className=\"text-center\"\r\n            value={year}\r\n            onChange={handleYearChange}\r\n            autoFocus\r\n          />\r\n        </FormGroup>\r\n        <Error error={error} clearError={handleClearErrorCLick} />\r\n      </ModalBody>\r\n      <ModalFooter>\r\n        <Button outline onClick={handleCancelClick}>\r\n          Cancel\r\n        </Button>\r\n        <Button color=\"primary\" outline onClick={handleDownloadClick}>\r\n          <FontAwesomeIcon icon={['fat', 'chart-line']} />\r\n          &nbsp; Download\r\n        </Button>\r\n      </ModalFooter>\r\n    </Modal>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SACEC,MAAM,EACNC,KAAK,EACLC,WAAW,EACXC,SAAS,EACTC,WAAW,EACXC,SAAS,EACTC,KAAK,QACA,YAAY;AACnB,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,KAAK,QAAQ,sBAAsB;AAC5C,SAASC,oBAAoB,QAAwB,uBAAuB;AAAC;AAO7E,OAAO,SAASC,YAAY,OAAuC;EAAA;EAAA,IAAtC;IAAEC,IAAI;IAAEC;EAA2B,CAAC;EAC/D,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGhB,QAAQ,CAAC,IAAIiB,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,QAAQ,EAAE,CAAC;IACnE,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAwB,IAAI,CAAC;EAE3D,MAAMsB,gBAAgB,GAAIC,CAAsC,IAAK;IACnEP,OAAO,CAACO,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EACzB,CAAC;EAED,MAAMC,mBAAmB,GAAG,YAAY;IACtC,IAAI;MACFL,QAAQ,CAAC,IAAI,CAAC;MACd,MAAMM,OAAO,GAAGC,QAAQ,CAACb,IAAI,CAAC;MAC9B,IAAI,CAACY,OAAO,EAAE;QACZ,OAAON,QAAQ,CAACV,oBAAoB,CAAC,6BAA6B,CAAC,CAAC;MACtE;MAEA,MAAMF,UAAU,CAACoB,YAAY,CAACD,QAAQ,CAACb,IAAI,CAAC,CAAC;MAC7CD,OAAO,EAAE;IACX,CAAC,CAAC,OAAOS,CAAC,EAAE;MACVF,QAAQ,CAACE,CAAC,CAAmB;IAC/B;EACF,CAAC;EAED,MAAMO,iBAAiB,GAAG,MAAM;IAC9BhB,OAAO,EAAE;EACX,CAAC;EAED,MAAMiB,qBAAqB,GAAG,MAAM;IAClCV,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;EAED,oBACE,QAAC,KAAK;IAAC,MAAM,EAAER,IAAK;IAAC,MAAM,EAAEiB,iBAAkB;IAAC,SAAS,EAAE,KAAM;IAAA,wBAC/D,QAAC,WAAW;MAAC,MAAM,EAAEA,iBAAkB;MAAA;IAAA;MAAA;MAAA;MAAA;IAAA,QAAkC,eACzE,QAAC,SAAS;MAAA,wBACR;QAAG,SAAS,EAAC,kBAAkB;QAAA;MAAA;QAAA;QAAA;QAAA;MAAA,QAG3B,eACJ,QAAC,SAAS;QAAC,SAAS,EAAC,qBAAqB;QAAA,wBACxC;UAAO,OAAO,EAAC,MAAM;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAAa,eAClC,QAAC,KAAK;UACJ,EAAE,EAAC,MAAM;UACT,IAAI,EAAC,QAAQ;UACb,MAAM,EAAC,IAAI;UACX,SAAS,EAAC,aAAa;UACvB,KAAK,EAAEf,IAAK;UACZ,QAAQ,EAAEO,gBAAiB;UAC3B,SAAS;QAAA;UAAA;UAAA;UAAA;QAAA,QACT;MAAA;QAAA;QAAA;QAAA;MAAA,QACQ,eACZ,QAAC,KAAK;QAAC,KAAK,EAAEF,KAAM;QAAC,UAAU,EAAEW;MAAsB;QAAA;QAAA;QAAA;MAAA,QAAG;IAAA;MAAA;MAAA;MAAA;IAAA,QAChD,eACZ,QAAC,WAAW;MAAA,wBACV,QAAC,MAAM;QAAC,OAAO;QAAC,OAAO,EAAED,iBAAkB;QAAA;MAAA;QAAA;QAAA;QAAA;MAAA,QAElC,eACT,QAAC,MAAM;QAAC,KAAK,EAAC,SAAS;QAAC,OAAO;QAAC,OAAO,EAAEJ,mBAAoB;QAAA,wBAC3D,QAAC,eAAe;UAAC,IAAI,EAAE,CAAC,KAAK,EAAE,YAAY;QAAE;UAAA;UAAA;UAAA;QAAA,QAAG;MAAA;QAAA;QAAA;QAAA;MAAA,QAEzC;IAAA;MAAA;MAAA;MAAA;IAAA,QACG;EAAA;IAAA;IAAA;IAAA;EAAA,QACR;AAEZ;AAAC,GAhEed,YAAY;AAAA,KAAZA,YAAY;AAAA;AAAA"}, "metadata": {}, "sourceType": "module"}