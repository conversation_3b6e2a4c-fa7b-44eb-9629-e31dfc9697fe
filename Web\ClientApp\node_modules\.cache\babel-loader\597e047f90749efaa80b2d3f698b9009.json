{"ast": null, "code": "import { guid } from 'utils/guid';\nexport const CustomerType = 'customer';\nexport const WeeklyName = 'Weekly';\nexport const WeeklyAbbreviation = 'Wk';\nexport function createCustomer() {\n  return {\n    _id: guid(),\n    type: CustomerType,\n    abbreviation: '',\n    name: ''\n  };\n}\nexport function weeklyCustomer() {\n  return {\n    _id: guid(),\n    type: CustomerType,\n    abbreviation: WeeklyAbbreviation,\n    name: WeeklyName\n  };\n}", "map": {"version": 3, "names": ["guid", "CustomerType", "WeeklyName", "WeeklyAbbreviation", "createCustomer", "_id", "type", "abbreviation", "name", "weeklyCustomer"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/api/models/customers.ts"], "sourcesContent": ["import { guid } from 'utils/guid';\r\nimport { ModelBase } from './model-base';\r\n\r\nexport const CustomerType = 'customer';\r\n\r\nexport const WeeklyName = 'Weekly';\r\nexport const WeeklyAbbreviation = 'Wk';\r\n\r\nexport interface Customer extends ModelBase {\r\n  type: string;\r\n  abbreviation: string;\r\n  name: string;\r\n}\r\n\r\nexport function createCustomer(): Customer {\r\n  return {\r\n    _id: guid(),\r\n    type: CustomerType,\r\n    abbreviation: '',\r\n    name: ''\r\n  };\r\n}\r\n\r\nexport function weeklyCustomer(): Customer {\r\n  return {\r\n    _id: guid(),\r\n    type: CustomerType,\r\n    abbreviation: WeeklyAbbreviation,\r\n    name: WeeklyName\r\n  };\r\n}\r\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,YAAY;AAGjC,OAAO,MAAMC,YAAY,GAAG,UAAU;AAEtC,OAAO,MAAMC,UAAU,GAAG,QAAQ;AAClC,OAAO,MAAMC,kBAAkB,GAAG,IAAI;AAQtC,OAAO,SAASC,cAAc,GAAa;EACzC,OAAO;IACLC,GAAG,EAAEL,IAAI,EAAE;IACXM,IAAI,EAAEL,YAAY;IAClBM,YAAY,EAAE,EAAE;IAChBC,IAAI,EAAE;EACR,CAAC;AACH;AAEA,OAAO,SAASC,cAAc,GAAa;EACzC,OAAO;IACLJ,GAAG,EAAEL,IAAI,EAAE;IACXM,IAAI,EAAEL,YAAY;IAClBM,YAAY,EAAEJ,kBAAkB;IAChCK,IAAI,EAAEN;EACR,CAAC;AACH"}, "metadata": {}, "sourceType": "module"}