{"ast": null, "code": "export function equals(a, b) {\n  if (a == null || b == null) return false;\n  return a.toLowerCase() === b.toLowerCase();\n}\nexport function contains(str, value) {\n  return (str || '').toLowerCase().indexOf((value || '').toLowerCase()) !== -1;\n}", "map": {"version": 3, "names": ["equals", "a", "b", "toLowerCase", "contains", "str", "value", "indexOf"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/utils/equals.ts"], "sourcesContent": ["export function equals(\r\n  a: string | null | undefined,\r\n  b: string | null | undefined\r\n): boolean {\r\n  if (a == null || b == null) return false;\r\n  return a.toLowerCase() === b.toLowerCase();\r\n}\r\n\r\nexport function contains(str: string | null, value: string | null) {\r\n  return (str || '').toLowerCase().indexOf((value || '').toLowerCase()) !== -1;\r\n}\r\n"], "mappings": "AAAA,OAAO,SAASA,MAAM,CACpBC,CAA4B,EAC5BC,CAA4B,EACnB;EACT,IAAID,CAAC,IAAI,IAAI,IAAIC,CAAC,IAAI,IAAI,EAAE,OAAO,KAAK;EACxC,OAAOD,CAAC,CAACE,WAAW,EAAE,KAAKD,CAAC,CAACC,WAAW,EAAE;AAC5C;AAEA,OAAO,SAASC,QAAQ,CAACC,GAAkB,EAAEC,KAAoB,EAAE;EACjE,OAAO,CAACD,GAAG,IAAI,EAAE,EAAEF,WAAW,EAAE,CAACI,OAAO,CAAC,CAACD,KAAK,IAAI,EAAE,EAAEH,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC;AAC9E"}, "metadata": {}, "sourceType": "module"}