{"ast": null, "code": "export { default as v1 } from './v1.js';\nexport { default as v3 } from './v3.js';\nexport { default as v4 } from './v4.js';\nexport { default as v5 } from './v5.js';\nexport { default as NIL } from './nil.js';\nexport { default as version } from './version.js';\nexport { default as validate } from './validate.js';\nexport { default as stringify } from './stringify.js';\nexport { default as parse } from './parse.js';", "map": {"version": 3, "names": ["default", "v1", "v3", "v4", "v5", "NIL", "version", "validate", "stringify", "parse"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/node_modules/uuid/dist/esm-browser/index.js"], "sourcesContent": ["export { default as v1 } from './v1.js';\nexport { default as v3 } from './v3.js';\nexport { default as v4 } from './v4.js';\nexport { default as v5 } from './v5.js';\nexport { default as NIL } from './nil.js';\nexport { default as version } from './version.js';\nexport { default as validate } from './validate.js';\nexport { default as stringify } from './stringify.js';\nexport { default as parse } from './parse.js';"], "mappings": "AAAA,SAASA,OAAO,IAAIC,EAAE,QAAQ,SAAS;AACvC,SAASD,OAAO,IAAIE,EAAE,QAAQ,SAAS;AACvC,SAASF,OAAO,IAAIG,EAAE,QAAQ,SAAS;AACvC,SAASH,OAAO,IAAII,EAAE,QAAQ,SAAS;AACvC,SAASJ,OAAO,IAAIK,GAAG,QAAQ,UAAU;AACzC,SAASL,OAAO,IAAIM,OAAO,QAAQ,cAAc;AACjD,SAASN,OAAO,IAAIO,QAAQ,QAAQ,eAAe;AACnD,SAASP,OAAO,IAAIQ,SAAS,QAAQ,gBAAgB;AACrD,SAASR,OAAO,IAAIS,KAAK,QAAQ,YAAY"}, "metadata": {}, "sourceType": "module"}