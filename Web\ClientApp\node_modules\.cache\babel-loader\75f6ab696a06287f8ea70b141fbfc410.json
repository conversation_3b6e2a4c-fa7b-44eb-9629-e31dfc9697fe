{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\GreenRP\\\\Web\\\\ClientApp\\\\src\\\\features\\\\auth\\\\require-auth.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate, useLocation } from 'react-router';\nimport { useAuth } from './use-auth';\nimport { Loading } from 'features/loading/Loading';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport function RequireAuth(_ref) {\n  _s();\n  let {\n    element: ChildElement\n  } = _ref;\n  const auth = useAuth(),\n    location = useLocation();\n  if (!(auth !== null && auth !== void 0 && auth.user)) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      state: {\n        from: location\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(React.Suspense, {\n    fallback: /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 36\n    }, this),\n    children: /*#__PURE__*/_jsxDEV(ChildElement, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 10\n  }, this);\n}\n_s(RequireAuth, \"HRLOLLAXFjObMMH3UCaG4AIfmXk=\", false, function () {\n  return [useAuth, useLocation];\n});\n_c = RequireAuth;\nvar _c;\n$RefreshReg$(_c, \"RequireAuth\");", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "useAuth", "Loading", "RequireAuth", "element", "ChildElement", "auth", "location", "user", "from"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/auth/require-auth.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Navigate, useLocation } from 'react-router';\r\nimport { useAuth } from './use-auth';\r\nimport { Loading } from 'features/loading/Loading';\r\n\r\ninterface RequireAuthProps {\r\n  element: React.LazyExoticComponent<() => JSX.Element>;\r\n}\r\n\r\nexport function RequireAuth({element: ChildElement}: RequireAuthProps) {\r\n  const auth = useAuth(),\r\n    location = useLocation();\r\n\r\n  if(!auth?.user) {\r\n    return <Navigate to=\"/login\" state={{from: location}} replace />;\r\n  }\r\n\r\n  return <React.Suspense fallback={<Loading />}>\r\n      <ChildElement />\r\n    </React.Suspense>;\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,WAAW,QAAQ,cAAc;AACpD,SAASC,OAAO,QAAQ,YAAY;AACpC,SAASC,OAAO,QAAQ,0BAA0B;AAAC;AAMnD,OAAO,SAASC,WAAW,OAA4C;EAAA;EAAA,IAA3C;IAACC,OAAO,EAAEC;EAA8B,CAAC;EACnE,MAAMC,IAAI,GAAGL,OAAO,EAAE;IACpBM,QAAQ,GAAGP,WAAW,EAAE;EAE1B,IAAG,EAACM,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEE,IAAI,GAAE;IACd,oBAAO,QAAC,QAAQ;MAAC,EAAE,EAAC,QAAQ;MAAC,KAAK,EAAE;QAACC,IAAI,EAAEF;MAAQ,CAAE;MAAC,OAAO;IAAA;MAAA;MAAA;MAAA;IAAA,QAAG;EAClE;EAEA,oBAAO,QAAC,KAAK,CAAC,QAAQ;IAAC,QAAQ,eAAE,QAAC,OAAO;MAAA;MAAA;MAAA;IAAA,QAAI;IAAA,uBACzC,QAAC,YAAY;MAAA;MAAA;MAAA;IAAA;EAAG;IAAA;IAAA;IAAA;EAAA,QACD;AACrB;AAAC,GAXeJ,WAAW;EAAA,QACZF,OAAO,EACPD,WAAW;AAAA;AAAA,KAFVG,WAAW;AAAA;AAAA"}, "metadata": {}, "sourceType": "module"}