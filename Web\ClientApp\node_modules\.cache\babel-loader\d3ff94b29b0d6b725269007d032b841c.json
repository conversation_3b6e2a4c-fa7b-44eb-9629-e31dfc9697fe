{"ast": null, "code": "import { createAction, createAsyncThunk, createSlice } from '@reduxjs/toolkit';\nimport { createCustomer } from 'api/models/customers';\nimport { customerApi } from 'api/customer-service';\nconst initialState = {\n  isLoading: false,\n  customer: createCustomer(),\n  error: null\n};\nexport const saveCustomer = createAsyncThunk('customer-detail/save-customer', async (_, _ref) => {\n  let {\n    rejectWithValue,\n    getState\n  } = _ref;\n  try {\n    const customer = getState().customerDetail.customer,\n      doc = {\n        ...customer\n      };\n    const updated = await customerApi.save(doc);\n    return updated;\n  } catch (e) {\n    return rejectWithValue(e);\n  }\n});\nexport const deleteCustomer = createAsyncThunk('customer-detail/delete-customer', async (_, _ref2) => {\n  let {\n    rejectWithValue,\n    getState\n  } = _ref2;\n  try {\n    const customer = getState().customerDetail.customer,\n      doc = {\n        ...customer\n      };\n    const updated = await customerApi.delete(doc);\n    return updated;\n  } catch (e) {\n    return rejectWithValue(e);\n  }\n});\nconst savePending = createAction(saveCustomer.pending.type),\n  saveFulfilled = createAction(saveCustomer.fulfilled.type),\n  saveRejected = createAction(saveCustomer.rejected.type),\n  deletePending = createAction(deleteCustomer.pending.type),\n  deleteFulfilled = createAction(deleteCustomer.fulfilled.type),\n  deleteRejected = createAction(deleteCustomer.rejected.type);\nexport const customerDetailSlice = createSlice({\n  name: 'customer-detail',\n  initialState,\n  reducers: {\n    setCustomer(state, action) {\n      state.customer = action.payload;\n    }\n  },\n  extraReducers: builder => builder.addCase(savePending, state => {\n    state.isLoading = true;\n    state.error = null;\n  }).addCase(saveFulfilled, (state, action) => {\n    state.isLoading = false;\n    if (action.payload) {\n      state.customer = action.payload;\n    }\n  }).addCase(saveRejected, (state, action) => {\n    state.isLoading = false;\n    state.error = action.payload;\n  }).addCase(deletePending, state => {\n    state.isLoading = true;\n    state.error = null;\n  }).addCase(deleteFulfilled, (state, action) => {\n    state.isLoading = false;\n    state.customer = createCustomer();\n  }).addCase(deleteRejected, (state, action) => {\n    state.isLoading = false;\n    state.error = action.payload;\n  })\n});\nexport const {\n  setCustomer\n} = customerDetailSlice.actions;\nexport const selectCustomer = state => state.customerDetail.customer;\nexport const selectIsLoading = state => state.customerDetail.isLoading;\nexport const selectError = state => state.customerDetail.customer;\nexport default customerDetailSlice.reducer;", "map": {"version": 3, "names": ["createAction", "createAsyncThunk", "createSlice", "createCustomer", "customerApi", "initialState", "isLoading", "customer", "error", "saveCustomer", "_", "rejectWithValue", "getState", "customerDetail", "doc", "updated", "save", "e", "deleteCustomer", "delete", "savePending", "pending", "type", "saveFulfilled", "fulfilled", "saveRejected", "rejected", "deletePending", "deleteFulfilled", "deleteRejected", "customerDetailSlice", "name", "reducers", "setCustomer", "state", "action", "payload", "extraReducers", "builder", "addCase", "actions", "selectCustomer", "selectIsLoading", "selectError", "reducer"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/customers/detail-slice.ts"], "sourcesContent": ["import { AsyncThunk, createAction, createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';\r\nimport { createCustomer, Customer } from 'api/models/customers';\r\nimport { customerApi } from 'api/customer-service';\r\nimport { RootState } from 'app/store';\r\nimport { ProblemDetails } from 'utils/problem-details';\r\n\r\ninterface CustomerDetailState {\r\n  isLoading: boolean;\r\n  customer: Customer;\r\n  error: ProblemDetails | null;\r\n}\r\n\r\nconst initialState: CustomerDetailState = {\r\n  isLoading: false,\r\n  customer: createCustomer(),\r\n  error: null\r\n};\r\n\r\nexport const saveCustomer: AsyncThunk<Customer, void, {state: RootState}> = createAsyncThunk(\r\n  'customer-detail/save-customer',\r\n  async (_, {rejectWithValue, getState}) => {\r\n    try {\r\n\r\n      const customer = (getState() as RootState).customerDetail.customer,\r\n        doc = {...customer};\r\n      \r\n      const updated = await customerApi.save(doc);\r\n      return updated;\r\n\r\n    } catch(e) {\r\n      return rejectWithValue(e as ProblemDetails);\r\n    }\r\n  }\r\n);\r\n\r\nexport const deleteCustomer: AsyncThunk<void, void, {state: RootState}> = createAsyncThunk(\r\n  'customer-detail/delete-customer',\r\n  async (_, {rejectWithValue, getState}) => {\r\n    try {\r\n\r\n      const customer = (getState() as RootState).customerDetail.customer,\r\n        doc = {...customer};\r\n      \r\n      const updated = await customerApi.delete(doc);\r\n      return updated;\r\n\r\n    } catch(e) {\r\n      return rejectWithValue(e as ProblemDetails);\r\n    }\r\n  }\r\n);\r\n\r\nconst savePending = createAction(saveCustomer.pending.type),\r\n  saveFulfilled = createAction<Customer | undefined>(saveCustomer.fulfilled.type),\r\n  saveRejected = createAction<ProblemDetails>(saveCustomer.rejected.type),\r\n  deletePending = createAction(deleteCustomer.pending.type),\r\n  deleteFulfilled = createAction(deleteCustomer.fulfilled.type),\r\n  deleteRejected = createAction<ProblemDetails>(deleteCustomer.rejected.type);\r\n\r\nexport const customerDetailSlice = createSlice({\r\n  name: 'customer-detail',\r\n  initialState,\r\n  reducers: {\r\n    setCustomer(state, action: PayloadAction<Customer>) {\r\n      state.customer = action.payload;\r\n    }\r\n  },\r\n  extraReducers: builder =>\r\n    builder\r\n      .addCase(savePending, state => {\r\n        state.isLoading = true;\r\n        state.error = null;\r\n      })\r\n      .addCase(saveFulfilled, (state, action) => {\r\n        state.isLoading = false;\r\n        if(action.payload) {\r\n          state.customer = action.payload;\r\n        }\r\n      })\r\n      .addCase(saveRejected, (state, action) => {\r\n        state.isLoading = false;\r\n        state.error = action.payload;\r\n      })\r\n      .addCase(deletePending, state => {\r\n        state.isLoading = true;\r\n        state.error = null;\r\n      })\r\n      .addCase(deleteFulfilled, (state, action) => {\r\n        state.isLoading = false;\r\n        state.customer = createCustomer();\r\n      })\r\n      .addCase(deleteRejected, (state, action) => {\r\n        state.isLoading = false;\r\n        state.error = action.payload;\r\n      })\r\n});\r\n\r\nexport const { setCustomer } = customerDetailSlice.actions;\r\n\r\nexport const selectCustomer = (state: RootState) => state.customerDetail.customer;\r\nexport const selectIsLoading = (state: RootState) => state.customerDetail.isLoading;\r\nexport const selectError = (state: RootState) => state.customerDetail.customer;\r\n\r\nexport default customerDetailSlice.reducer;\r\n"], "mappings": "AAAA,SAAqBA,YAAY,EAAEC,gBAAgB,EAAEC,WAAW,QAAuB,kBAAkB;AACzG,SAASC,cAAc,QAAkB,sBAAsB;AAC/D,SAASC,WAAW,QAAQ,sBAAsB;AAUlD,MAAMC,YAAiC,GAAG;EACxCC,SAAS,EAAE,KAAK;EAChBC,QAAQ,EAAEJ,cAAc,EAAE;EAC1BK,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,YAA4D,GAAGR,gBAAgB,CAC1F,+BAA+B,EAC/B,OAAOS,CAAC,WAAkC;EAAA,IAAhC;IAACC,eAAe;IAAEC;EAAQ,CAAC;EACnC,IAAI;IAEF,MAAML,QAAQ,GAAIK,QAAQ,EAAE,CAAeC,cAAc,CAACN,QAAQ;MAChEO,GAAG,GAAG;QAAC,GAAGP;MAAQ,CAAC;IAErB,MAAMQ,OAAO,GAAG,MAAMX,WAAW,CAACY,IAAI,CAACF,GAAG,CAAC;IAC3C,OAAOC,OAAO;EAEhB,CAAC,CAAC,OAAME,CAAC,EAAE;IACT,OAAON,eAAe,CAACM,CAAC,CAAmB;EAC7C;AACF,CAAC,CACF;AAED,OAAO,MAAMC,cAA0D,GAAGjB,gBAAgB,CACxF,iCAAiC,EACjC,OAAOS,CAAC,YAAkC;EAAA,IAAhC;IAACC,eAAe;IAAEC;EAAQ,CAAC;EACnC,IAAI;IAEF,MAAML,QAAQ,GAAIK,QAAQ,EAAE,CAAeC,cAAc,CAACN,QAAQ;MAChEO,GAAG,GAAG;QAAC,GAAGP;MAAQ,CAAC;IAErB,MAAMQ,OAAO,GAAG,MAAMX,WAAW,CAACe,MAAM,CAACL,GAAG,CAAC;IAC7C,OAAOC,OAAO;EAEhB,CAAC,CAAC,OAAME,CAAC,EAAE;IACT,OAAON,eAAe,CAACM,CAAC,CAAmB;EAC7C;AACF,CAAC,CACF;AAED,MAAMG,WAAW,GAAGpB,YAAY,CAACS,YAAY,CAACY,OAAO,CAACC,IAAI,CAAC;EACzDC,aAAa,GAAGvB,YAAY,CAAuBS,YAAY,CAACe,SAAS,CAACF,IAAI,CAAC;EAC/EG,YAAY,GAAGzB,YAAY,CAAiBS,YAAY,CAACiB,QAAQ,CAACJ,IAAI,CAAC;EACvEK,aAAa,GAAG3B,YAAY,CAACkB,cAAc,CAACG,OAAO,CAACC,IAAI,CAAC;EACzDM,eAAe,GAAG5B,YAAY,CAACkB,cAAc,CAACM,SAAS,CAACF,IAAI,CAAC;EAC7DO,cAAc,GAAG7B,YAAY,CAAiBkB,cAAc,CAACQ,QAAQ,CAACJ,IAAI,CAAC;AAE7E,OAAO,MAAMQ,mBAAmB,GAAG5B,WAAW,CAAC;EAC7C6B,IAAI,EAAE,iBAAiB;EACvB1B,YAAY;EACZ2B,QAAQ,EAAE;IACRC,WAAW,CAACC,KAAK,EAAEC,MAA+B,EAAE;MAClDD,KAAK,CAAC3B,QAAQ,GAAG4B,MAAM,CAACC,OAAO;IACjC;EACF,CAAC;EACDC,aAAa,EAAEC,OAAO,IACpBA,OAAO,CACJC,OAAO,CAACnB,WAAW,EAAEc,KAAK,IAAI;IAC7BA,KAAK,CAAC5B,SAAS,GAAG,IAAI;IACtB4B,KAAK,CAAC1B,KAAK,GAAG,IAAI;EACpB,CAAC,CAAC,CACD+B,OAAO,CAAChB,aAAa,EAAE,CAACW,KAAK,EAAEC,MAAM,KAAK;IACzCD,KAAK,CAAC5B,SAAS,GAAG,KAAK;IACvB,IAAG6B,MAAM,CAACC,OAAO,EAAE;MACjBF,KAAK,CAAC3B,QAAQ,GAAG4B,MAAM,CAACC,OAAO;IACjC;EACF,CAAC,CAAC,CACDG,OAAO,CAACd,YAAY,EAAE,CAACS,KAAK,EAAEC,MAAM,KAAK;IACxCD,KAAK,CAAC5B,SAAS,GAAG,KAAK;IACvB4B,KAAK,CAAC1B,KAAK,GAAG2B,MAAM,CAACC,OAAO;EAC9B,CAAC,CAAC,CACDG,OAAO,CAACZ,aAAa,EAAEO,KAAK,IAAI;IAC/BA,KAAK,CAAC5B,SAAS,GAAG,IAAI;IACtB4B,KAAK,CAAC1B,KAAK,GAAG,IAAI;EACpB,CAAC,CAAC,CACD+B,OAAO,CAACX,eAAe,EAAE,CAACM,KAAK,EAAEC,MAAM,KAAK;IAC3CD,KAAK,CAAC5B,SAAS,GAAG,KAAK;IACvB4B,KAAK,CAAC3B,QAAQ,GAAGJ,cAAc,EAAE;EACnC,CAAC,CAAC,CACDoC,OAAO,CAACV,cAAc,EAAE,CAACK,KAAK,EAAEC,MAAM,KAAK;IAC1CD,KAAK,CAAC5B,SAAS,GAAG,KAAK;IACvB4B,KAAK,CAAC1B,KAAK,GAAG2B,MAAM,CAACC,OAAO;EAC9B,CAAC;AACP,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEH;AAAY,CAAC,GAAGH,mBAAmB,CAACU,OAAO;AAE1D,OAAO,MAAMC,cAAc,GAAIP,KAAgB,IAAKA,KAAK,CAACrB,cAAc,CAACN,QAAQ;AACjF,OAAO,MAAMmC,eAAe,GAAIR,KAAgB,IAAKA,KAAK,CAACrB,cAAc,CAACP,SAAS;AACnF,OAAO,MAAMqC,WAAW,GAAIT,KAAgB,IAAKA,KAAK,CAACrB,cAAc,CAACN,QAAQ;AAE9E,eAAeuB,mBAAmB,CAACc,OAAO"}, "metadata": {}, "sourceType": "module"}