{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\GreenRP\\\\Web\\\\ClientApp\\\\src\\\\features\\\\zones\\\\List.tsx\",\n  _s = $RefreshSig$();\nimport { useSelector } from 'react-redux';\nimport { Link } from 'react-router-dom';\nimport { Button } from 'reactstrap';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { routes } from 'app/routes';\nimport { useAuth } from 'features/auth/use-auth';\nimport { selectZones } from './zones-slice';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport function List() {\n  _s();\n  const zones = useSelector(selectZones),\n    {\n      isInRole\n    } = useAuth(),\n    canCreate = isInRole('create:zones');\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container d-grid gap-2\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row sticky-top-navbar bg-white mt-2 py-2 border-bottom shadow\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 row mt-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"col\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: ['fat', 'map-location-dot']\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this), \"\\xA0 Zones List\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), canCreate && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-auto\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            tag: Link,\n            to: routes.zones.routes.new(),\n            outline: true,\n            color: \"success\",\n            children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: ['fat', 'plus']\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 17\n            }, this), \"\\xA0 New Zone\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n      className: \"table w-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          className: \"sticky-top bg-white\",\n          style: {\n            top: '140px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"\\xA0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"text-center\",\n            children: \"Tables\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"text-center\",\n            children: \"Offsite\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        children: zones.map(zone => /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: routes.zones.routes.detail.to(zone._id),\n              children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: ['fat', 'edit']\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: zone.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"text-center\",\n            children: zone.tables\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"text-center\",\n            children: zone.isOffsite && /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: ['fat', 'check-square']\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 15\n          }, this)]\n        }, zone._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n}\n_s(List, \"Yk762gYAnYnyNx1IKGJRnpncG7o=\", false, function () {\n  return [useSelector, useAuth];\n});\n_c = List;\nexport default List;\nvar _c;\n$RefreshReg$(_c, \"List\");", "map": {"version": 3, "names": ["useSelector", "Link", "<PERSON><PERSON>", "FontAwesomeIcon", "routes", "useAuth", "selectZones", "List", "zones", "isInRole", "canCreate", "new", "top", "map", "zone", "detail", "to", "_id", "name", "tables", "isOffsite"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/zones/List.tsx"], "sourcesContent": ["import { useSelector } from 'react-redux';\r\nimport { Link } from 'react-router-dom';\r\nimport { Button } from 'reactstrap';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { routes } from 'app/routes';\r\nimport { useAuth } from 'features/auth/use-auth';\r\nimport { selectZones } from './zones-slice';\r\n\r\nexport function List() {\r\n  const zones = useSelector(selectZones),\r\n    { isInRole } = useAuth(),\r\n    canCreate = isInRole('create:zones');\r\n\r\n  return (\r\n    <div className=\"container d-grid gap-2\">\r\n      <div className=\"row sticky-top-navbar bg-white mt-2 py-2 border-bottom shadow\">\r\n        <div className=\"col-12 row mt-2\">\r\n          <h1 className=\"col\">\r\n            <FontAwesomeIcon icon={['fat', 'map-location-dot']} />\r\n            &nbsp; Zones List\r\n          </h1>\r\n          {canCreate && (\r\n            <div className=\"col-auto\">\r\n              <Button\r\n                tag={Link}\r\n                to={routes.zones.routes.new()}\r\n                outline\r\n                color=\"success\">\r\n                <FontAwesomeIcon icon={['fat', 'plus']} />\r\n                &nbsp; New Zone\r\n              </Button>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n      <table className=\"table w-auto\">\r\n        <thead>\r\n          <tr className=\"sticky-top bg-white\" style={{ top: '140px' }}>\r\n            <th>&nbsp;</th>\r\n            <th>Name</th>\r\n            <th className=\"text-center\">Tables</th>\r\n            <th className=\"text-center\">Offsite</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          {zones.map((zone) => (\r\n            <tr key={zone._id}>\r\n              <td>\r\n                <Link to={routes.zones.routes.detail.to(zone._id)}>\r\n                  <FontAwesomeIcon icon={['fat', 'edit']} />\r\n                </Link>\r\n              </td>\r\n              <td>{zone.name}</td>\r\n              <td className=\"text-center\">{zone.tables}</td>\r\n              <td className=\"text-center\">\r\n                {zone.isOffsite && (\r\n                  <FontAwesomeIcon icon={['fat', 'check-square']} />\r\n                )}\r\n              </td>\r\n            </tr>\r\n          ))}\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default List;\r\n"], "mappings": ";;AAAA,SAASA,WAAW,QAAQ,aAAa;AACzC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,WAAW,QAAQ,eAAe;AAAC;AAE5C,OAAO,SAASC,IAAI,GAAG;EAAA;EACrB,MAAMC,KAAK,GAAGR,WAAW,CAACM,WAAW,CAAC;IACpC;MAAEG;IAAS,CAAC,GAAGJ,OAAO,EAAE;IACxBK,SAAS,GAAGD,QAAQ,CAAC,cAAc,CAAC;EAEtC,oBACE;IAAK,SAAS,EAAC,wBAAwB;IAAA,wBACrC;MAAK,SAAS,EAAC,+DAA+D;MAAA,uBAC5E;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAI,SAAS,EAAC,KAAK;UAAA,wBACjB,QAAC,eAAe;YAAC,IAAI,EAAE,CAAC,KAAK,EAAE,kBAAkB;UAAE;YAAA;YAAA;YAAA;UAAA,QAAG;QAAA;UAAA;UAAA;UAAA;QAAA,QAEnD,EACJC,SAAS,iBACR;UAAK,SAAS,EAAC,UAAU;UAAA,uBACvB,QAAC,MAAM;YACL,GAAG,EAAET,IAAK;YACV,EAAE,EAAEG,MAAM,CAACI,KAAK,CAACJ,MAAM,CAACO,GAAG,EAAG;YAC9B,OAAO;YACP,KAAK,EAAC,SAAS;YAAA,wBACf,QAAC,eAAe;cAAC,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM;YAAE;cAAA;cAAA;cAAA;YAAA,QAAG;UAAA;YAAA;YAAA;YAAA;UAAA;QAEnC;UAAA;UAAA;UAAA;QAAA,QAEZ;MAAA;QAAA;QAAA;QAAA;MAAA;IACG;MAAA;MAAA;MAAA;IAAA,QACF,eACN;MAAO,SAAS,EAAC,cAAc;MAAA,wBAC7B;QAAA,uBACE;UAAI,SAAS,EAAC,qBAAqB;UAAC,KAAK,EAAE;YAAEC,GAAG,EAAE;UAAQ,CAAE;UAAA,wBAC1D;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAe,eACf;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAa,eACb;YAAI,SAAS,EAAC,aAAa;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAY,eACvC;YAAI,SAAS,EAAC,aAAa;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAa;QAAA;UAAA;UAAA;UAAA;QAAA;MACrC;QAAA;QAAA;QAAA;MAAA,QACC,eACR;QAAA,UACGJ,KAAK,CAACK,GAAG,CAAEC,IAAI,iBACd;UAAA,wBACE;YAAA,uBACE,QAAC,IAAI;cAAC,EAAE,EAAEV,MAAM,CAACI,KAAK,CAACJ,MAAM,CAACW,MAAM,CAACC,EAAE,CAACF,IAAI,CAACG,GAAG,CAAE;cAAA,uBAChD,QAAC,eAAe;gBAAC,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM;cAAE;gBAAA;gBAAA;gBAAA;cAAA;YAAG;cAAA;cAAA;cAAA;YAAA;UACrC;YAAA;YAAA;YAAA;UAAA,QACJ,eACL;YAAA,UAAKH,IAAI,CAACI;UAAI;YAAA;YAAA;YAAA;UAAA,QAAM,eACpB;YAAI,SAAS,EAAC,aAAa;YAAA,UAAEJ,IAAI,CAACK;UAAM;YAAA;YAAA;YAAA;UAAA,QAAM,eAC9C;YAAI,SAAS,EAAC,aAAa;YAAA,UACxBL,IAAI,CAACM,SAAS,iBACb,QAAC,eAAe;cAAC,IAAI,EAAE,CAAC,KAAK,EAAE,cAAc;YAAE;cAAA;cAAA;cAAA;YAAA;UAChD;YAAA;YAAA;YAAA;UAAA,QACE;QAAA,GAZEN,IAAI,CAACG,GAAG;UAAA;UAAA;UAAA;QAAA,QAclB;MAAC;QAAA;QAAA;QAAA;MAAA,QACI;IAAA;MAAA;MAAA;MAAA;IAAA,QACF;EAAA;IAAA;IAAA;IAAA;EAAA,QACJ;AAEV;AAAC,GAzDeV,IAAI;EAAA,QACJP,WAAW,EACRK,OAAO;AAAA;AAAA,KAFVE,IAAI;AA2DpB,eAAeA,IAAI;AAAC;AAAA"}, "metadata": {}, "sourceType": "module"}