{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\nimport { sortBy } from 'utils/sort';\nconst initialState = {\n  zones: []\n};\nexport const zonesSlice = createSlice({\n  name: 'zones',\n  initialState,\n  reducers: {\n    setZones(state, action) {\n      state.zones = action.payload;\n    }\n  }\n});\nexport const {\n  setZones\n} = zonesSlice.actions;\nexport const selectZones = state => state.zones.zones.map(z => ({\n  ...z\n})).sort(sortBy('name'));\nexport default zonesSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "sortBy", "initialState", "zones", "zonesSlice", "name", "reducers", "setZones", "state", "action", "payload", "actions", "selectZones", "map", "z", "sort", "reducer"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/zones/zones-slice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\r\nimport { Zone } from 'api/models/zones';\r\nimport { RootState } from 'app/store';\r\nimport { sortBy } from 'utils/sort';\r\n\r\nexport interface ZonesState {\r\n  zones: Zone[];\r\n}\r\n\r\nconst initialState: ZonesState = {\r\n  zones: []\r\n};\r\n\r\nexport const zonesSlice = createSlice({\r\n  name: 'zones',\r\n  initialState,\r\n  reducers: {\r\n    setZones(state, action: PayloadAction<Zone[]>) {\r\n      state.zones = action.payload;\r\n    }\r\n  }\r\n});\r\n\r\nexport const { setZones } = zonesSlice.actions;\r\n\r\nexport const selectZones = (state: RootState) => state.zones.zones.map(z => ({...z})).sort(sortBy('name'));\r\n\r\nexport default zonesSlice.reducer;\r\n"], "mappings": "AAAA,SAASA,WAAW,QAAuB,kBAAkB;AAG7D,SAASC,MAAM,QAAQ,YAAY;AAMnC,MAAMC,YAAwB,GAAG;EAC/BC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,UAAU,GAAGJ,WAAW,CAAC;EACpCK,IAAI,EAAE,OAAO;EACbH,YAAY;EACZI,QAAQ,EAAE;IACRC,QAAQ,CAACC,KAAK,EAAEC,MAA6B,EAAE;MAC7CD,KAAK,CAACL,KAAK,GAAGM,MAAM,CAACC,OAAO;IAC9B;EACF;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEH;AAAS,CAAC,GAAGH,UAAU,CAACO,OAAO;AAE9C,OAAO,MAAMC,WAAW,GAAIJ,KAAgB,IAAKA,KAAK,CAACL,KAAK,CAACA,KAAK,CAACU,GAAG,CAACC,CAAC,KAAK;EAAC,GAAGA;AAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAACd,MAAM,CAAC,MAAM,CAAC,CAAC;AAE1G,eAAeG,UAAU,CAACY,OAAO"}, "metadata": {}, "sourceType": "module"}