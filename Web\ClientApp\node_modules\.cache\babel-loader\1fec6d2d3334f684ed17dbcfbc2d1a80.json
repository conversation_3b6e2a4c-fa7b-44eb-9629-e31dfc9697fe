{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\GreenRP\\\\Web\\\\ClientApp\\\\src\\\\pages\\\\Index.tsx\",\n  _s = $RefreshSig$();\nimport { routes } from 'app/routes';\nimport { Navigate } from 'react-router-dom';\nimport { useAuth } from 'features/auth/use-auth';\nimport { Loading } from 'features/loading/Loading';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport function Index() {\n  _s();\n  const {\n      user,\n      isInRole\n    } = useAuth(),\n    isDriver = isInRole('driver');\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 12\n    }, this);\n  }\n  if (isDriver) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: routes.driverTasks.list.path\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Navigate, {\n    to: routes.orders.path\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 10\n  }, this);\n}\n_s(Index, \"SPuTxHx9kJ293/Rk6OX6JlTVyw4=\", false, function () {\n  return [useAuth];\n});\n_c = Index;\nexport default Index;\nvar _c;\n$RefreshReg$(_c, \"Index\");", "map": {"version": 3, "names": ["routes", "Navigate", "useAuth", "Loading", "Index", "user", "isInRole", "isDriver", "driverTasks", "list", "path", "orders"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/pages/Index.tsx"], "sourcesContent": ["import { routes } from 'app/routes';\r\nimport { Navigate } from 'react-router-dom';\r\nimport { useAuth } from 'features/auth/use-auth';\r\nimport { Loading } from 'features/loading/Loading';\r\n\r\nexport function Index() {\r\n  const { user, isInRole } = useAuth(),\r\n    isDriver = isInRole('driver');\r\n\r\n  if (!user) {\r\n    return <Loading />;\r\n  }\r\n\r\n  if (isDriver) {\r\n    return <Navigate to={routes.driverTasks.list.path} />;\r\n  }\r\n\r\n  return <Navigate to={routes.orders.path} />;\r\n}\r\n\r\nexport default Index;\r\n"], "mappings": ";;AAAA,SAASA,MAAM,QAAQ,YAAY;AACnC,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,OAAO,QAAQ,0BAA0B;AAAC;AAEnD,OAAO,SAASC,KAAK,GAAG;EAAA;EACtB,MAAM;MAAEC,IAAI;MAAEC;IAAS,CAAC,GAAGJ,OAAO,EAAE;IAClCK,QAAQ,GAAGD,QAAQ,CAAC,QAAQ,CAAC;EAE/B,IAAI,CAACD,IAAI,EAAE;IACT,oBAAO,QAAC,OAAO;MAAA;MAAA;MAAA;IAAA,QAAG;EACpB;EAEA,IAAIE,QAAQ,EAAE;IACZ,oBAAO,QAAC,QAAQ;MAAC,EAAE,EAAEP,MAAM,CAACQ,WAAW,CAACC,IAAI,CAACC;IAAK;MAAA;MAAA;MAAA;IAAA,QAAG;EACvD;EAEA,oBAAO,QAAC,QAAQ;IAAC,EAAE,EAAEV,MAAM,CAACW,MAAM,CAACD;EAAK;IAAA;IAAA;IAAA;EAAA,QAAG;AAC7C;AAAC,GAbeN,KAAK;EAAA,QACQF,OAAO;AAAA;AAAA,KADpBE,KAAK;AAerB,eAAeA,KAAK;AAAC;AAAA"}, "metadata": {}, "sourceType": "module"}