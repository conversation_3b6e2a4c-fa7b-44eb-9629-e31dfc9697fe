{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\GreenRP\\\\Web\\\\ClientApp\\\\src\\\\features\\\\users\\\\List.tsx\",\n  _s = $RefreshSig$();\nimport { useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { Link } from 'react-router-dom';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { getAllUsers, selectError, selectIsLoading, selectUsers } from './users-slice';\nimport { routes } from 'app/routes';\nimport { useAuth } from 'features/auth/use-auth';\nimport { Error } from 'features/error/Error';\nimport { Loading } from 'features/loading/Loading';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction List() {\n  _s();\n  const dispatch = useDispatch(),\n    users = useSelector(selectUsers),\n    error = useSelector(selectError),\n    isLoading = useSelector(selectIsLoading),\n    {\n      user\n    } = useAuth();\n  useEffect(() => {\n    if (user) {\n      dispatch(getAllUsers(user));\n    }\n  }, [user, dispatch]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row sticky-top-navbar bg-white mt-2 py-2 border-bottom shadow\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 row my-2\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"col\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: ['fat', 'user']\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this), \"\\xA0 Users\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this), isLoading && /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Error, {\n      error: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this), !isLoading && !error && /*#__PURE__*/_jsxDEV(\"table\", {\n      className: \"table\",\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          className: \"sticky-top bg-white\",\n          style: {\n            top: '145px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"\\xA0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"User\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Phone\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        children: users.map(user => /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: routes.users.routes.detail.to(user.name),\n              children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: ['fat', 'edit']\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: user.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: user.email\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: user.phone\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 19\n          }, this)]\n        }, user.name, true, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 17\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n}\n_s(List, \"hlZvQQVxWI8bvzBdQe1asWimW7U=\", false, function () {\n  return [useDispatch, useSelector, useSelector, useSelector, useAuth];\n});\n_c = List;\nexport default List;\nvar _c;\n$RefreshReg$(_c, \"List\");", "map": {"version": 3, "names": ["useEffect", "useDispatch", "useSelector", "Link", "FontAwesomeIcon", "getAllUsers", "selectError", "selectIsLoading", "selectUsers", "routes", "useAuth", "Error", "Loading", "List", "dispatch", "users", "error", "isLoading", "user", "top", "map", "detail", "to", "name", "email", "phone"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/users/List.tsx"], "sourcesContent": ["import { useEffect } from 'react';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { Link } from 'react-router-dom';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { getAllUsers, selectError, selectIsLoading, selectUsers } from './users-slice';\r\nimport { routes } from 'app/routes';\r\nimport { useAuth } from 'features/auth/use-auth';\r\nimport { Error } from 'features/error/Error';\r\nimport { Loading } from 'features/loading/Loading';\r\n\r\nfunction List() {\r\n  const dispatch = useDispatch(),\r\n    users = useSelector(selectUsers),\r\n    error = useSelector(selectError),\r\n    isLoading = useSelector(selectIsLoading),\r\n    {user} = useAuth();\r\n\r\n  useEffect(() => {\r\n    if(user) {\r\n      dispatch(getAllUsers(user));\r\n    }\r\n  }, [user, dispatch]);\r\n\r\n  return (\r\n    <div className=\"container\">\r\n      <div className=\"row sticky-top-navbar bg-white mt-2 py-2 border-bottom shadow\">\r\n        <div className=\"col-12 row my-2\">\r\n          <h1 className=\"col\">\r\n            <FontAwesomeIcon icon={['fat', 'user']} />\r\n            &nbsp;\r\n            Users\r\n          </h1>\r\n        </div>\r\n      </div>\r\n      {isLoading &&\r\n        <Loading />\r\n      }\r\n      <Error error={error} />\r\n      {!isLoading && !error &&\r\n        <table className=\"table\">\r\n            <thead>\r\n              <tr className=\"sticky-top bg-white\" style={{top: '145px'}}>\r\n                <th>&nbsp;</th>\r\n                <th>User</th>\r\n                <th>Email</th>\r\n                <th>Phone</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              {users.map(user =>\r\n                <tr key={user.name}>\r\n                  <td>\r\n                    <Link to={routes.users.routes.detail.to(user.name)}>\r\n                      <FontAwesomeIcon icon={['fat', 'edit']} />\r\n                    </Link>\r\n                  </td>\r\n                  <td>{user.name}</td>\r\n                  <td>{user.email}</td>\r\n                  <td>{user.phone}</td>\r\n                </tr>\r\n              )}\r\n          </tbody>\r\n        </table>\r\n      }\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default List;\r\n"], "mappings": ";;AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,WAAW,EAAEC,WAAW,EAAEC,eAAe,EAAEC,WAAW,QAAQ,eAAe;AACtF,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,KAAK,QAAQ,sBAAsB;AAC5C,SAASC,OAAO,QAAQ,0BAA0B;AAAC;AAEnD,SAASC,IAAI,GAAG;EAAA;EACd,MAAMC,QAAQ,GAAGb,WAAW,EAAE;IAC5Bc,KAAK,GAAGb,WAAW,CAACM,WAAW,CAAC;IAChCQ,KAAK,GAAGd,WAAW,CAACI,WAAW,CAAC;IAChCW,SAAS,GAAGf,WAAW,CAACK,eAAe,CAAC;IACxC;MAACW;IAAI,CAAC,GAAGR,OAAO,EAAE;EAEpBV,SAAS,CAAC,MAAM;IACd,IAAGkB,IAAI,EAAE;MACPJ,QAAQ,CAACT,WAAW,CAACa,IAAI,CAAC,CAAC;IAC7B;EACF,CAAC,EAAE,CAACA,IAAI,EAAEJ,QAAQ,CAAC,CAAC;EAEpB,oBACE;IAAK,SAAS,EAAC,WAAW;IAAA,wBACxB;MAAK,SAAS,EAAC,+DAA+D;MAAA,uBAC5E;QAAK,SAAS,EAAC,iBAAiB;QAAA,uBAC9B;UAAI,SAAS,EAAC,KAAK;UAAA,wBACjB,QAAC,eAAe;YAAC,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM;UAAE;YAAA;YAAA;YAAA;UAAA,QAAG;QAAA;UAAA;UAAA;UAAA;QAAA;MAGvC;QAAA;QAAA;QAAA;MAAA;IACD;MAAA;MAAA;MAAA;IAAA,QACF,EACLG,SAAS,iBACR,QAAC,OAAO;MAAA;MAAA;MAAA;IAAA,QAAG,eAEb,QAAC,KAAK;MAAC,KAAK,EAAED;IAAM;MAAA;MAAA;MAAA;IAAA,QAAG,EACtB,CAACC,SAAS,IAAI,CAACD,KAAK,iBACnB;MAAO,SAAS,EAAC,OAAO;MAAA,wBACpB;QAAA,uBACE;UAAI,SAAS,EAAC,qBAAqB;UAAC,KAAK,EAAE;YAACG,GAAG,EAAE;UAAO,CAAE;UAAA,wBACxD;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAe,eACf;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAa,eACb;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAc,eACd;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAc;QAAA;UAAA;UAAA;UAAA;QAAA;MACX;QAAA;QAAA;QAAA;MAAA,QACC,eACR;QAAA,UACGJ,KAAK,CAACK,GAAG,CAACF,IAAI,iBACb;UAAA,wBACE;YAAA,uBACE,QAAC,IAAI;cAAC,EAAE,EAAET,MAAM,CAACM,KAAK,CAACN,MAAM,CAACY,MAAM,CAACC,EAAE,CAACJ,IAAI,CAACK,IAAI,CAAE;cAAA,uBACjD,QAAC,eAAe;gBAAC,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM;cAAE;gBAAA;gBAAA;gBAAA;cAAA;YAAG;cAAA;cAAA;cAAA;YAAA;UACrC;YAAA;YAAA;YAAA;UAAA,QACJ,eACL;YAAA,UAAKL,IAAI,CAACK;UAAI;YAAA;YAAA;YAAA;UAAA,QAAM,eACpB;YAAA,UAAKL,IAAI,CAACM;UAAK;YAAA;YAAA;YAAA;UAAA,QAAM,eACrB;YAAA,UAAKN,IAAI,CAACO;UAAK;YAAA;YAAA;YAAA;UAAA,QAAM;QAAA,GARdP,IAAI,CAACK,IAAI;UAAA;UAAA;UAAA;QAAA,QASb;MACN;QAAA;QAAA;QAAA;MAAA,QACG;IAAA;MAAA;MAAA;MAAA;IAAA,QACF;EAAA;IAAA;IAAA;IAAA;EAAA,QAEN;AAEV;AAAC,GAxDQV,IAAI;EAAA,QACMZ,WAAW,EAClBC,WAAW,EACXA,WAAW,EACPA,WAAW,EACdQ,OAAO;AAAA;AAAA,KALXG,IAAI;AA0Db,eAAeA,IAAI;AAAC;AAAA"}, "metadata": {}, "sourceType": "module"}