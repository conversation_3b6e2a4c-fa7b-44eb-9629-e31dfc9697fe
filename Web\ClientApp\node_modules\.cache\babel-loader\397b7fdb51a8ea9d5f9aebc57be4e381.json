{"ast": null, "code": "// https://stackoverflow.com/a/2117523\nexport function guid() {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n    // eslint-disable-next-line\n    var r = Math.random() * 16 | 0,\n      v = c === 'x' ? r : r & 0x3 | 0x8;\n    return v.toString(16);\n  });\n}", "map": {"version": 3, "names": ["guid", "replace", "c", "r", "Math", "random", "v", "toString"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/utils/guid.ts"], "sourcesContent": ["// https://stackoverflow.com/a/2117523\r\nexport function guid(): string {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\r\n    // eslint-disable-next-line\r\n    var r = Math.random() * 16 | 0, v = c === 'x' ? r : (r & 0x3 | 0x8);\r\n    return v.toString(16);\r\n  });\r\n}\r\n"], "mappings": "AAAA;AACA,OAAO,SAASA,IAAI,GAAW;EAC7B,OAAO,sCAAsC,CAACC,OAAO,CAAC,OAAO,EAAE,UAASC,CAAC,EAAE;IACzE;IACA,IAAIC,CAAC,GAAGC,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC;MAAEC,CAAC,GAAGJ,CAAC,KAAK,GAAG,GAAGC,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAG,GAAI;IACnE,OAAOG,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;EACvB,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module"}