{"ast": null, "code": "/**\n * @remix-run/router v1.0.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Types and Constants\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Actions represent the type of change to a location value.\n */\nvar Action;\n(function (Action) {\n  /**\n   * A POP indicates a change to an arbitrary index in the history stack, such\n   * as a back or forward navigation. It does not describe the direction of the\n   * navigation, only that the current index changed.\n   *\n   * Note: This is the default action for newly created history objects.\n   */\n  Action[\"Pop\"] = \"POP\";\n  /**\n   * A PUSH indicates a new entry being added to the history stack, such as when\n   * a link is clicked and a new page loads. When this happens, all subsequent\n   * entries in the stack are lost.\n   */\n\n  Action[\"Push\"] = \"PUSH\";\n  /**\n   * A REPLACE indicates the entry at the current index in the history stack\n   * being replaced by a new one.\n   */\n\n  Action[\"Replace\"] = \"REPLACE\";\n})(Action || (Action = {}));\nconst PopStateEventType = \"popstate\";\n/**\n * Memory history stores the current location in memory. It is designed for use\n * in stateful non-browser environments like tests and React Native.\n */\n\nfunction createMemoryHistory(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  let {\n    initialEntries = [\"/\"],\n    initialIndex,\n    v5Compat = false\n  } = options;\n  let entries; // Declare so we can access from createMemoryLocation\n\n  entries = initialEntries.map((entry, index) => createMemoryLocation(entry, typeof entry === \"string\" ? null : entry.state, index === 0 ? \"default\" : undefined));\n  let index = clampIndex(initialIndex == null ? entries.length - 1 : initialIndex);\n  let action = Action.Pop;\n  let listener = null;\n  function clampIndex(n) {\n    return Math.min(Math.max(n, 0), entries.length - 1);\n  }\n  function getCurrentLocation() {\n    return entries[index];\n  }\n  function createMemoryLocation(to, state, key) {\n    if (state === void 0) {\n      state = null;\n    }\n    let location = createLocation(entries ? getCurrentLocation().pathname : \"/\", to, state, key);\n    warning$1(location.pathname.charAt(0) === \"/\", \"relative pathnames are not supported in memory history: \" + JSON.stringify(to));\n    return location;\n  }\n  let history = {\n    get index() {\n      return index;\n    },\n    get action() {\n      return action;\n    },\n    get location() {\n      return getCurrentLocation();\n    },\n    createHref(to) {\n      return typeof to === \"string\" ? to : createPath(to);\n    },\n    encodeLocation(location) {\n      return location;\n    },\n    push(to, state) {\n      action = Action.Push;\n      let nextLocation = createMemoryLocation(to, state);\n      index += 1;\n      entries.splice(index, entries.length, nextLocation);\n      if (v5Compat && listener) {\n        listener({\n          action,\n          location: nextLocation\n        });\n      }\n    },\n    replace(to, state) {\n      action = Action.Replace;\n      let nextLocation = createMemoryLocation(to, state);\n      entries[index] = nextLocation;\n      if (v5Compat && listener) {\n        listener({\n          action,\n          location: nextLocation\n        });\n      }\n    },\n    go(delta) {\n      action = Action.Pop;\n      index = clampIndex(index + delta);\n      if (listener) {\n        listener({\n          action,\n          location: getCurrentLocation()\n        });\n      }\n    },\n    listen(fn) {\n      listener = fn;\n      return () => {\n        listener = null;\n      };\n    }\n  };\n  return history;\n}\n/**\n * Browser history stores the location in regular URLs. This is the standard for\n * most web apps, but it requires some configuration on the server to ensure you\n * serve the same app at multiple URLs.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createbrowserhistory\n */\n\nfunction createBrowserHistory(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  function createBrowserLocation(window, globalHistory) {\n    let {\n      pathname,\n      search,\n      hash\n    } = window.location;\n    return createLocation(\"\", {\n      pathname,\n      search,\n      hash\n    },\n    // state defaults to `null` because `window.history.state` does\n    globalHistory.state && globalHistory.state.usr || null, globalHistory.state && globalHistory.state.key || \"default\");\n  }\n  function createBrowserHref(window, to) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n  return getUrlBasedHistory(createBrowserLocation, createBrowserHref, null, options);\n}\n/**\n * Hash history stores the location in window.location.hash. This makes it ideal\n * for situations where you don't want to send the location to the server for\n * some reason, either because you do cannot configure it or the URL space is\n * reserved for something else.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createhashhistory\n */\n\nfunction createHashHistory(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  function createHashLocation(window, globalHistory) {\n    let {\n      pathname = \"/\",\n      search = \"\",\n      hash = \"\"\n    } = parsePath(window.location.hash.substr(1));\n    return createLocation(\"\", {\n      pathname,\n      search,\n      hash\n    },\n    // state defaults to `null` because `window.history.state` does\n    globalHistory.state && globalHistory.state.usr || null, globalHistory.state && globalHistory.state.key || \"default\");\n  }\n  function createHashHref(window, to) {\n    let base = window.document.querySelector(\"base\");\n    let href = \"\";\n    if (base && base.getAttribute(\"href\")) {\n      let url = window.location.href;\n      let hashIndex = url.indexOf(\"#\");\n      href = hashIndex === -1 ? url : url.slice(0, hashIndex);\n    }\n    return href + \"#\" + (typeof to === \"string\" ? to : createPath(to));\n  }\n  function validateHashLocation(location, to) {\n    warning$1(location.pathname.charAt(0) === \"/\", \"relative pathnames are not supported in hash history.push(\" + JSON.stringify(to) + \")\");\n  }\n  return getUrlBasedHistory(createHashLocation, createHashHref, validateHashLocation, options);\n} //#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region UTILS\n////////////////////////////////////////////////////////////////////////////////\n\nfunction warning$1(cond, message) {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n    try {\n      // Welcome to debugging history!\n      //\n      // This error is thrown as a convenience so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message); // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\nfunction createKey() {\n  return Math.random().toString(36).substr(2, 8);\n}\n/**\n * For browser-based histories, we combine the state and key into an object\n */\n\nfunction getHistoryState(location) {\n  return {\n    usr: location.state,\n    key: location.key\n  };\n}\n/**\n * Creates a Location object with a unique key from the given Path\n */\n\nfunction createLocation(current, to, state, key) {\n  if (state === void 0) {\n    state = null;\n  }\n  let location = _extends({\n    pathname: typeof current === \"string\" ? current : current.pathname,\n    search: \"\",\n    hash: \"\"\n  }, typeof to === \"string\" ? parsePath(to) : to, {\n    state,\n    // TODO: This could be cleaned up.  push/replace should probably just take\n    // full Locations now and avoid the need to run through this flow at all\n    // But that's a pretty big refactor to the current test suite so going to\n    // keep as is for the time being and just let any incoming keys take precedence\n    key: to && to.key || key || createKey()\n  });\n  return location;\n}\n/**\n * Creates a string URL path from the given pathname, search, and hash components.\n */\n\nfunction createPath(_ref) {\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\"\n  } = _ref;\n  if (search && search !== \"?\") pathname += search.charAt(0) === \"?\" ? search : \"?\" + search;\n  if (hash && hash !== \"#\") pathname += hash.charAt(0) === \"#\" ? hash : \"#\" + hash;\n  return pathname;\n}\n/**\n * Parses a string URL path into its separate pathname, search, and hash components.\n */\n\nfunction parsePath(path) {\n  let parsedPath = {};\n  if (path) {\n    let hashIndex = path.indexOf(\"#\");\n    if (hashIndex >= 0) {\n      parsedPath.hash = path.substr(hashIndex);\n      path = path.substr(0, hashIndex);\n    }\n    let searchIndex = path.indexOf(\"?\");\n    if (searchIndex >= 0) {\n      parsedPath.search = path.substr(searchIndex);\n      path = path.substr(0, searchIndex);\n    }\n    if (path) {\n      parsedPath.pathname = path;\n    }\n  }\n  return parsedPath;\n}\nfunction createURL(location) {\n  // window.location.origin is \"null\" (the literal string value) in Firefox\n  // under certain conditions, notably when serving from a local HTML file\n  // See https://bugzilla.mozilla.org/show_bug.cgi?id=878297\n  let base = typeof window !== \"undefined\" && typeof window.location !== \"undefined\" && window.location.origin !== \"null\" ? window.location.origin : \"unknown://unknown\";\n  let href = typeof location === \"string\" ? location : createPath(location);\n  return new URL(href, base);\n}\nfunction getUrlBasedHistory(getLocation, createHref, validateLocation, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  let {\n    window = document.defaultView,\n    v5Compat = false\n  } = options;\n  let globalHistory = window.history;\n  let action = Action.Pop;\n  let listener = null;\n  function handlePop() {\n    action = Action.Pop;\n    if (listener) {\n      listener({\n        action,\n        location: history.location\n      });\n    }\n  }\n  function push(to, state) {\n    action = Action.Push;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n    let historyState = getHistoryState(location);\n    let url = history.createHref(location); // try...catch because iOS limits us to 100 pushState calls :/\n\n    try {\n      globalHistory.pushState(historyState, \"\", url);\n    } catch (error) {\n      // They are going to lose state here, but there is no real\n      // way to warn them about it since the page will refresh...\n      window.location.assign(url);\n    }\n    if (v5Compat && listener) {\n      listener({\n        action,\n        location: history.location\n      });\n    }\n  }\n  function replace(to, state) {\n    action = Action.Replace;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n    let historyState = getHistoryState(location);\n    let url = history.createHref(location);\n    globalHistory.replaceState(historyState, \"\", url);\n    if (v5Compat && listener) {\n      listener({\n        action,\n        location: history.location\n      });\n    }\n  }\n  let history = {\n    get action() {\n      return action;\n    },\n    get location() {\n      return getLocation(window, globalHistory);\n    },\n    listen(fn) {\n      if (listener) {\n        throw new Error(\"A history only accepts one active listener\");\n      }\n      window.addEventListener(PopStateEventType, handlePop);\n      listener = fn;\n      return () => {\n        window.removeEventListener(PopStateEventType, handlePop);\n        listener = null;\n      };\n    },\n    createHref(to) {\n      return createHref(window, to);\n    },\n    encodeLocation(location) {\n      // Encode a Location the same way window.location would\n      let url = createURL(createPath(location));\n      return _extends({}, location, {\n        pathname: url.pathname,\n        search: url.search,\n        hash: url.hash\n      });\n    },\n    push,\n    replace,\n    go(n) {\n      return globalHistory.go(n);\n    }\n  };\n  return history;\n} //#endregion\n\nvar ResultType;\n(function (ResultType) {\n  ResultType[\"data\"] = \"data\";\n  ResultType[\"deferred\"] = \"deferred\";\n  ResultType[\"redirect\"] = \"redirect\";\n  ResultType[\"error\"] = \"error\";\n})(ResultType || (ResultType = {}));\nfunction isIndexRoute(route) {\n  return route.index === true;\n} // Walk the route tree generating unique IDs where necessary so we are working\n// solely with AgnosticDataRouteObject's within the Router\n\nfunction convertRoutesToDataRoutes(routes, parentPath, allIds) {\n  if (parentPath === void 0) {\n    parentPath = [];\n  }\n  if (allIds === void 0) {\n    allIds = new Set();\n  }\n  return routes.map((route, index) => {\n    let treePath = [...parentPath, index];\n    let id = typeof route.id === \"string\" ? route.id : treePath.join(\"-\");\n    invariant(route.index !== true || !route.children, \"Cannot specify children on an index route\");\n    invariant(!allIds.has(id), \"Found a route id collision on id \\\"\" + id + \"\\\".  Route \" + \"id's must be globally unique within Data Router usages\");\n    allIds.add(id);\n    if (isIndexRoute(route)) {\n      let indexRoute = _extends({}, route, {\n        id\n      });\n      return indexRoute;\n    } else {\n      let pathOrLayoutRoute = _extends({}, route, {\n        id,\n        children: route.children ? convertRoutesToDataRoutes(route.children, treePath, allIds) : undefined\n      });\n      return pathOrLayoutRoute;\n    }\n  });\n}\n/**\n * Matches the given routes to a location and returns the match data.\n *\n * @see https://reactrouter.com/docs/en/v6/utils/match-routes\n */\n\nfunction matchRoutes(routes, locationArg, basename) {\n  if (basename === void 0) {\n    basename = \"/\";\n  }\n  let location = typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n  let pathname = stripBasename(location.pathname || \"/\", basename);\n  if (pathname == null) {\n    return null;\n  }\n  let branches = flattenRoutes(routes);\n  rankRouteBranches(branches);\n  let matches = null;\n  for (let i = 0; matches == null && i < branches.length; ++i) {\n    matches = matchRouteBranch(branches[i],\n    // Incoming pathnames are generally encoded from either window.location\n    // or from router.navigate, but we want to match against the unencoded\n    // paths in the route definitions.  Memory router locations won't be\n    // encoded here but there also shouldn't be anything to decode so this\n    // should be a safe operation.  This avoids needing matchRoutes to be\n    // history-aware.\n    safelyDecodeURI(pathname));\n  }\n  return matches;\n}\nfunction flattenRoutes(routes, branches, parentsMeta, parentPath) {\n  if (branches === void 0) {\n    branches = [];\n  }\n  if (parentsMeta === void 0) {\n    parentsMeta = [];\n  }\n  if (parentPath === void 0) {\n    parentPath = \"\";\n  }\n  routes.forEach((route, index) => {\n    let meta = {\n      relativePath: route.path || \"\",\n      caseSensitive: route.caseSensitive === true,\n      childrenIndex: index,\n      route\n    };\n    if (meta.relativePath.startsWith(\"/\")) {\n      invariant(meta.relativePath.startsWith(parentPath), \"Absolute route path \\\"\" + meta.relativePath + \"\\\" nested under path \" + (\"\\\"\" + parentPath + \"\\\" is not valid. An absolute child route path \") + \"must start with the combined path of all its parent routes.\");\n      meta.relativePath = meta.relativePath.slice(parentPath.length);\n    }\n    let path = joinPaths([parentPath, meta.relativePath]);\n    let routesMeta = parentsMeta.concat(meta); // Add the children before adding this route to the array so we traverse the\n    // route tree depth-first and child routes appear before their parents in\n    // the \"flattened\" version.\n\n    if (route.children && route.children.length > 0) {\n      invariant(\n      // Our types know better, but runtime JS may not!\n      // @ts-expect-error\n      route.index !== true, \"Index routes must not have child routes. Please remove \" + (\"all child routes from route path \\\"\" + path + \"\\\".\"));\n      flattenRoutes(route.children, branches, routesMeta, path);\n    } // Routes without a path shouldn't ever match by themselves unless they are\n    // index routes, so don't add them to the list of possible branches.\n\n    if (route.path == null && !route.index) {\n      return;\n    }\n    branches.push({\n      path,\n      score: computeScore(path, route.index),\n      routesMeta\n    });\n  });\n  return branches;\n}\nfunction rankRouteBranches(branches) {\n  branches.sort((a, b) => a.score !== b.score ? b.score - a.score // Higher score first\n  : compareIndexes(a.routesMeta.map(meta => meta.childrenIndex), b.routesMeta.map(meta => meta.childrenIndex)));\n}\nconst paramRe = /^:\\w+$/;\nconst dynamicSegmentValue = 3;\nconst indexRouteValue = 2;\nconst emptySegmentValue = 1;\nconst staticSegmentValue = 10;\nconst splatPenalty = -2;\nconst isSplat = s => s === \"*\";\nfunction computeScore(path, index) {\n  let segments = path.split(\"/\");\n  let initialScore = segments.length;\n  if (segments.some(isSplat)) {\n    initialScore += splatPenalty;\n  }\n  if (index) {\n    initialScore += indexRouteValue;\n  }\n  return segments.filter(s => !isSplat(s)).reduce((score, segment) => score + (paramRe.test(segment) ? dynamicSegmentValue : segment === \"\" ? emptySegmentValue : staticSegmentValue), initialScore);\n}\nfunction compareIndexes(a, b) {\n  let siblings = a.length === b.length && a.slice(0, -1).every((n, i) => n === b[i]);\n  return siblings ?\n  // If two routes are siblings, we should try to match the earlier sibling\n  // first. This allows people to have fine-grained control over the matching\n  // behavior by simply putting routes with identical paths in the order they\n  // want them tried.\n  a[a.length - 1] - b[b.length - 1] :\n  // Otherwise, it doesn't really make sense to rank non-siblings by index,\n  // so they sort equally.\n  0;\n}\nfunction matchRouteBranch(branch, pathname) {\n  let {\n    routesMeta\n  } = branch;\n  let matchedParams = {};\n  let matchedPathname = \"/\";\n  let matches = [];\n  for (let i = 0; i < routesMeta.length; ++i) {\n    let meta = routesMeta[i];\n    let end = i === routesMeta.length - 1;\n    let remainingPathname = matchedPathname === \"/\" ? pathname : pathname.slice(matchedPathname.length) || \"/\";\n    let match = matchPath({\n      path: meta.relativePath,\n      caseSensitive: meta.caseSensitive,\n      end\n    }, remainingPathname);\n    if (!match) return null;\n    Object.assign(matchedParams, match.params);\n    let route = meta.route;\n    matches.push({\n      // TODO: Can this as be avoided?\n      params: matchedParams,\n      pathname: joinPaths([matchedPathname, match.pathname]),\n      pathnameBase: normalizePathname(joinPaths([matchedPathname, match.pathnameBase])),\n      route\n    });\n    if (match.pathnameBase !== \"/\") {\n      matchedPathname = joinPaths([matchedPathname, match.pathnameBase]);\n    }\n  }\n  return matches;\n}\n/**\n * Returns a path with params interpolated.\n *\n * @see https://reactrouter.com/docs/en/v6/utils/generate-path\n */\n\nfunction generatePath(path, params) {\n  if (params === void 0) {\n    params = {};\n  }\n  return path.replace(/:(\\w+)/g, (_, key) => {\n    invariant(params[key] != null, \"Missing \\\":\" + key + \"\\\" param\");\n    return params[key];\n  }).replace(/(\\/?)\\*/, (_, prefix, __, str) => {\n    const star = \"*\";\n    if (params[star] == null) {\n      // If no splat was provided, trim the trailing slash _unless_ it's\n      // the entire path\n      return str === \"/*\" ? \"/\" : \"\";\n    } // Apply the splat\n\n    return \"\" + prefix + params[star];\n  });\n}\n/**\n * Performs pattern matching on a URL pathname and returns information about\n * the match.\n *\n * @see https://reactrouter.com/docs/en/v6/utils/match-path\n */\n\nfunction matchPath(pattern, pathname) {\n  if (typeof pattern === \"string\") {\n    pattern = {\n      path: pattern,\n      caseSensitive: false,\n      end: true\n    };\n  }\n  let [matcher, paramNames] = compilePath(pattern.path, pattern.caseSensitive, pattern.end);\n  let match = pathname.match(matcher);\n  if (!match) return null;\n  let matchedPathname = match[0];\n  let pathnameBase = matchedPathname.replace(/(.)\\/+$/, \"$1\");\n  let captureGroups = match.slice(1);\n  let params = paramNames.reduce((memo, paramName, index) => {\n    // We need to compute the pathnameBase here using the raw splat value\n    // instead of using params[\"*\"] later because it will be decoded then\n    if (paramName === \"*\") {\n      let splatValue = captureGroups[index] || \"\";\n      pathnameBase = matchedPathname.slice(0, matchedPathname.length - splatValue.length).replace(/(.)\\/+$/, \"$1\");\n    }\n    memo[paramName] = safelyDecodeURIComponent(captureGroups[index] || \"\", paramName);\n    return memo;\n  }, {});\n  return {\n    params,\n    pathname: matchedPathname,\n    pathnameBase,\n    pattern\n  };\n}\nfunction compilePath(path, caseSensitive, end) {\n  if (caseSensitive === void 0) {\n    caseSensitive = false;\n  }\n  if (end === void 0) {\n    end = true;\n  }\n  warning(path === \"*\" || !path.endsWith(\"*\") || path.endsWith(\"/*\"), \"Route path \\\"\" + path + \"\\\" will be treated as if it were \" + (\"\\\"\" + path.replace(/\\*$/, \"/*\") + \"\\\" because the `*` character must \") + \"always follow a `/` in the pattern. To get rid of this warning, \" + (\"please change the route path to \\\"\" + path.replace(/\\*$/, \"/*\") + \"\\\".\"));\n  let paramNames = [];\n  let regexpSource = \"^\" + path.replace(/\\/*\\*?$/, \"\") // Ignore trailing / and /*, we'll handle it below\n  .replace(/^\\/*/, \"/\") // Make sure it has a leading /\n  .replace(/[\\\\.*+^$?{}|()[\\]]/g, \"\\\\$&\") // Escape special regex chars\n  .replace(/:(\\w+)/g, (_, paramName) => {\n    paramNames.push(paramName);\n    return \"([^\\\\/]+)\";\n  });\n  if (path.endsWith(\"*\")) {\n    paramNames.push(\"*\");\n    regexpSource += path === \"*\" || path === \"/*\" ? \"(.*)$\" // Already matched the initial /, just match the rest\n    : \"(?:\\\\/(.+)|\\\\/*)$\"; // Don't include the / in params[\"*\"]\n  } else if (end) {\n    // When matching to the end, ignore trailing slashes\n    regexpSource += \"\\\\/*$\";\n  } else if (path !== \"\" && path !== \"/\") {\n    // If our path is non-empty and contains anything beyond an initial slash,\n    // then we have _some_ form of path in our regex so we should expect to\n    // match only if we find the end of this path segment.  Look for an optional\n    // non-captured trailing slash (to match a portion of the URL) or the end\n    // of the path (if we've matched to the end).  We used to do this with a\n    // word boundary but that gives false positives on routes like\n    // /user-preferences since `-` counts as a word boundary.\n    regexpSource += \"(?:(?=\\\\/|$))\";\n  } else ;\n  let matcher = new RegExp(regexpSource, caseSensitive ? undefined : \"i\");\n  return [matcher, paramNames];\n}\nfunction safelyDecodeURI(value) {\n  try {\n    return decodeURI(value);\n  } catch (error) {\n    warning(false, \"The URL path \\\"\" + value + \"\\\" could not be decoded because it is is a \" + \"malformed URL segment. This is probably due to a bad percent \" + (\"encoding (\" + error + \").\"));\n    return value;\n  }\n}\nfunction safelyDecodeURIComponent(value, paramName) {\n  try {\n    return decodeURIComponent(value);\n  } catch (error) {\n    warning(false, \"The value for the URL param \\\"\" + paramName + \"\\\" will not be decoded because\" + (\" the string \\\"\" + value + \"\\\" is a malformed URL segment. This is probably\") + (\" due to a bad percent encoding (\" + error + \").\"));\n    return value;\n  }\n}\n/**\n * @private\n */\n\nfunction stripBasename(pathname, basename) {\n  if (basename === \"/\") return pathname;\n  if (!pathname.toLowerCase().startsWith(basename.toLowerCase())) {\n    return null;\n  } // We want to leave trailing slash behavior in the user's control, so if they\n  // specify a basename with a trailing slash, we should support it\n\n  let startIndex = basename.endsWith(\"/\") ? basename.length - 1 : basename.length;\n  let nextChar = pathname.charAt(startIndex);\n  if (nextChar && nextChar !== \"/\") {\n    // pathname does not start with basename/\n    return null;\n  }\n  return pathname.slice(startIndex) || \"/\";\n}\nfunction invariant(value, message) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    throw new Error(message);\n  }\n}\n/**\n * @private\n */\n\nfunction warning(cond, message) {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n    try {\n      // Welcome to debugging React Router!\n      //\n      // This error is thrown as a convenience so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message); // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\n/**\n * Returns a resolved path object relative to the given pathname.\n *\n * @see https://reactrouter.com/docs/en/v6/utils/resolve-path\n */\n\nfunction resolvePath(to, fromPathname) {\n  if (fromPathname === void 0) {\n    fromPathname = \"/\";\n  }\n  let {\n    pathname: toPathname,\n    search = \"\",\n    hash = \"\"\n  } = typeof to === \"string\" ? parsePath(to) : to;\n  let pathname = toPathname ? toPathname.startsWith(\"/\") ? toPathname : resolvePathname(toPathname, fromPathname) : fromPathname;\n  return {\n    pathname,\n    search: normalizeSearch(search),\n    hash: normalizeHash(hash)\n  };\n}\nfunction resolvePathname(relativePath, fromPathname) {\n  let segments = fromPathname.replace(/\\/+$/, \"\").split(\"/\");\n  let relativeSegments = relativePath.split(\"/\");\n  relativeSegments.forEach(segment => {\n    if (segment === \"..\") {\n      // Keep the root \"\" segment so the pathname starts at /\n      if (segments.length > 1) segments.pop();\n    } else if (segment !== \".\") {\n      segments.push(segment);\n    }\n  });\n  return segments.length > 1 ? segments.join(\"/\") : \"/\";\n}\nfunction getInvalidPathError(char, field, dest, path) {\n  return \"Cannot include a '\" + char + \"' character in a manually specified \" + (\"`to.\" + field + \"` field [\" + JSON.stringify(path) + \"].  Please separate it out to the \") + (\"`to.\" + dest + \"` field. Alternatively you may provide the full path as \") + \"a string in <Link to=\\\"...\\\"> and the router will parse it for you.\";\n}\n/**\n * @private\n *\n * When processing relative navigation we want to ignore ancestor routes that\n * do not contribute to the path, such that index/pathless layout routes don't\n * interfere.\n *\n * For example, when moving a route element into an index route and/or a\n * pathless layout route, relative link behavior contained within should stay\n * the same.  Both of the following examples should link back to the root:\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\" element={<Link to=\"..\"}>\n *   </Route>\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\">\n *       <Route element={<AccountsLayout />}>       // <-- Does not contribute\n *         <Route index element={<Link to=\"..\"} />  // <-- Does not contribute\n *       </Route\n *     </Route>\n *   </Route>\n */\n\nfunction getPathContributingMatches(matches) {\n  return matches.filter((match, index) => index === 0 || match.route.path && match.route.path.length > 0);\n}\n/**\n * @private\n */\n\nfunction resolveTo(toArg, routePathnames, locationPathname, isPathRelative) {\n  if (isPathRelative === void 0) {\n    isPathRelative = false;\n  }\n  let to;\n  if (typeof toArg === \"string\") {\n    to = parsePath(toArg);\n  } else {\n    to = _extends({}, toArg);\n    invariant(!to.pathname || !to.pathname.includes(\"?\"), getInvalidPathError(\"?\", \"pathname\", \"search\", to));\n    invariant(!to.pathname || !to.pathname.includes(\"#\"), getInvalidPathError(\"#\", \"pathname\", \"hash\", to));\n    invariant(!to.search || !to.search.includes(\"#\"), getInvalidPathError(\"#\", \"search\", \"hash\", to));\n  }\n  let isEmptyPath = toArg === \"\" || to.pathname === \"\";\n  let toPathname = isEmptyPath ? \"/\" : to.pathname;\n  let from; // Routing is relative to the current pathname if explicitly requested.\n  //\n  // If a pathname is explicitly provided in `to`, it should be relative to the\n  // route context. This is explained in `Note on `<Link to>` values` in our\n  // migration guide from v5 as a means of disambiguation between `to` values\n  // that begin with `/` and those that do not. However, this is problematic for\n  // `to` values that do not provide a pathname. `to` can simply be a search or\n  // hash string, in which case we should assume that the navigation is relative\n  // to the current location's pathname and *not* the route pathname.\n\n  if (isPathRelative || toPathname == null) {\n    from = locationPathname;\n  } else {\n    let routePathnameIndex = routePathnames.length - 1;\n    if (toPathname.startsWith(\"..\")) {\n      let toSegments = toPathname.split(\"/\"); // Each leading .. segment means \"go up one route\" instead of \"go up one\n      // URL segment\".  This is a key difference from how <a href> works and a\n      // major reason we call this a \"to\" value instead of a \"href\".\n\n      while (toSegments[0] === \"..\") {\n        toSegments.shift();\n        routePathnameIndex -= 1;\n      }\n      to.pathname = toSegments.join(\"/\");\n    } // If there are more \"..\" segments than parent routes, resolve relative to\n    // the root / URL.\n\n    from = routePathnameIndex >= 0 ? routePathnames[routePathnameIndex] : \"/\";\n  }\n  let path = resolvePath(to, from); // Ensure the pathname has a trailing slash if the original \"to\" had one\n\n  let hasExplicitTrailingSlash = toPathname && toPathname !== \"/\" && toPathname.endsWith(\"/\"); // Or if this was a link to the current path which has a trailing slash\n\n  let hasCurrentTrailingSlash = (isEmptyPath || toPathname === \".\") && locationPathname.endsWith(\"/\");\n  if (!path.pathname.endsWith(\"/\") && (hasExplicitTrailingSlash || hasCurrentTrailingSlash)) {\n    path.pathname += \"/\";\n  }\n  return path;\n}\n/**\n * @private\n */\n\nfunction getToPathname(to) {\n  // Empty strings should be treated the same as / paths\n  return to === \"\" || to.pathname === \"\" ? \"/\" : typeof to === \"string\" ? parsePath(to).pathname : to.pathname;\n}\n/**\n * @private\n */\n\nconst joinPaths = paths => paths.join(\"/\").replace(/\\/\\/+/g, \"/\");\n/**\n * @private\n */\n\nconst normalizePathname = pathname => pathname.replace(/\\/+$/, \"\").replace(/^\\/*/, \"/\");\n/**\n * @private\n */\n\nconst normalizeSearch = search => !search || search === \"?\" ? \"\" : search.startsWith(\"?\") ? search : \"?\" + search;\n/**\n * @private\n */\n\nconst normalizeHash = hash => !hash || hash === \"#\" ? \"\" : hash.startsWith(\"#\") ? hash : \"#\" + hash;\n/**\n * This is a shortcut for creating `application/json` responses. Converts `data`\n * to JSON and sets the `Content-Type` header.\n */\n\nconst json = function json(data, init) {\n  if (init === void 0) {\n    init = {};\n  }\n  let responseInit = typeof init === \"number\" ? {\n    status: init\n  } : init;\n  let headers = new Headers(responseInit.headers);\n  if (!headers.has(\"Content-Type\")) {\n    headers.set(\"Content-Type\", \"application/json; charset=utf-8\");\n  }\n  return new Response(JSON.stringify(data), _extends({}, responseInit, {\n    headers\n  }));\n};\nclass AbortedDeferredError extends Error {}\nclass DeferredData {\n  constructor(data) {\n    this.pendingKeys = new Set();\n    this.subscriber = undefined;\n    invariant(data && typeof data === \"object\" && !Array.isArray(data), \"defer() only accepts plain objects\"); // Set up an AbortController + Promise we can race against to exit early\n    // cancellation\n\n    let reject;\n    this.abortPromise = new Promise((_, r) => reject = r);\n    this.controller = new AbortController();\n    let onAbort = () => reject(new AbortedDeferredError(\"Deferred data aborted\"));\n    this.unlistenAbortSignal = () => this.controller.signal.removeEventListener(\"abort\", onAbort);\n    this.controller.signal.addEventListener(\"abort\", onAbort);\n    this.data = Object.entries(data).reduce((acc, _ref) => {\n      let [key, value] = _ref;\n      return Object.assign(acc, {\n        [key]: this.trackPromise(key, value)\n      });\n    }, {});\n  }\n  trackPromise(key, value) {\n    if (!(value instanceof Promise)) {\n      return value;\n    }\n    this.pendingKeys.add(key); // We store a little wrapper promise that will be extended with\n    // _data/_error props upon resolve/reject\n\n    let promise = Promise.race([value, this.abortPromise]).then(data => this.onSettle(promise, key, null, data), error => this.onSettle(promise, key, error)); // Register rejection listeners to avoid uncaught promise rejections on\n    // errors or aborted deferred values\n\n    promise.catch(() => {});\n    Object.defineProperty(promise, \"_tracked\", {\n      get: () => true\n    });\n    return promise;\n  }\n  onSettle(promise, key, error, data) {\n    if (this.controller.signal.aborted && error instanceof AbortedDeferredError) {\n      this.unlistenAbortSignal();\n      Object.defineProperty(promise, \"_error\", {\n        get: () => error\n      });\n      return Promise.reject(error);\n    }\n    this.pendingKeys.delete(key);\n    if (this.done) {\n      // Nothing left to abort!\n      this.unlistenAbortSignal();\n    }\n    const subscriber = this.subscriber;\n    if (error) {\n      Object.defineProperty(promise, \"_error\", {\n        get: () => error\n      });\n      subscriber && subscriber(false);\n      return Promise.reject(error);\n    }\n    Object.defineProperty(promise, \"_data\", {\n      get: () => data\n    });\n    subscriber && subscriber(false);\n    return data;\n  }\n  subscribe(fn) {\n    this.subscriber = fn;\n  }\n  cancel() {\n    this.controller.abort();\n    this.pendingKeys.forEach((v, k) => this.pendingKeys.delete(k));\n    let subscriber = this.subscriber;\n    subscriber && subscriber(true);\n  }\n  async resolveData(signal) {\n    let aborted = false;\n    if (!this.done) {\n      let onAbort = () => this.cancel();\n      signal.addEventListener(\"abort\", onAbort);\n      aborted = await new Promise(resolve => {\n        this.subscribe(aborted => {\n          signal.removeEventListener(\"abort\", onAbort);\n          if (aborted || this.done) {\n            resolve(aborted);\n          }\n        });\n      });\n    }\n    return aborted;\n  }\n  get done() {\n    return this.pendingKeys.size === 0;\n  }\n  get unwrappedData() {\n    invariant(this.data !== null && this.done, \"Can only unwrap data on initialized and settled deferreds\");\n    return Object.entries(this.data).reduce((acc, _ref2) => {\n      let [key, value] = _ref2;\n      return Object.assign(acc, {\n        [key]: unwrapTrackedPromise(value)\n      });\n    }, {});\n  }\n}\nfunction isTrackedPromise(value) {\n  return value instanceof Promise && value._tracked === true;\n}\nfunction unwrapTrackedPromise(value) {\n  if (!isTrackedPromise(value)) {\n    return value;\n  }\n  if (value._error) {\n    throw value._error;\n  }\n  return value._data;\n}\nfunction defer(data) {\n  return new DeferredData(data);\n}\n/**\n * A redirect response. Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\n\nconst redirect = function redirect(url, init) {\n  if (init === void 0) {\n    init = 302;\n  }\n  let responseInit = init;\n  if (typeof responseInit === \"number\") {\n    responseInit = {\n      status: responseInit\n    };\n  } else if (typeof responseInit.status === \"undefined\") {\n    responseInit.status = 302;\n  }\n  let headers = new Headers(responseInit.headers);\n  headers.set(\"Location\", url);\n  return new Response(null, _extends({}, responseInit, {\n    headers\n  }));\n};\n/**\n * @private\n * Utility class we use to hold auto-unwrapped 4xx/5xx Response bodies\n */\n\nclass ErrorResponse {\n  constructor(status, statusText, data) {\n    this.status = status;\n    this.statusText = statusText || \"\";\n    this.data = data;\n  }\n}\n/**\n * Check if the given error is an ErrorResponse generated from a 4xx/5xx\n * Response throw from an action/loader\n */\n\nfunction isRouteErrorResponse(e) {\n  return e instanceof ErrorResponse;\n}\nconst IDLE_NAVIGATION = {\n  state: \"idle\",\n  location: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined\n};\nconst IDLE_FETCHER = {\n  state: \"idle\",\n  data: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined\n};\nconst isBrowser = typeof window !== \"undefined\" && typeof window.document !== \"undefined\" && typeof window.document.createElement !== \"undefined\";\nconst isServer = !isBrowser; //#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region createRouter\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Create a router and listen to history POP navigations\n */\n\nfunction createRouter(init) {\n  invariant(init.routes.length > 0, \"You must provide a non-empty routes array to createRouter\");\n  let dataRoutes = convertRoutesToDataRoutes(init.routes); // Cleanup function for history\n\n  let unlistenHistory = null; // Externally-provided functions to call on all state changes\n\n  let subscribers = new Set(); // Externally-provided object to hold scroll restoration locations during routing\n\n  let savedScrollPositions = null; // Externally-provided function to get scroll restoration keys\n\n  let getScrollRestorationKey = null; // Externally-provided function to get current scroll position\n\n  let getScrollPosition = null; // One-time flag to control the initial hydration scroll restoration.  Because\n  // we don't get the saved positions from <ScrollRestoration /> until _after_\n  // the initial render, we need to manually trigger a separate updateState to\n  // send along the restoreScrollPosition\n\n  let initialScrollRestored = false;\n  let initialMatches = matchRoutes(dataRoutes, init.history.location, init.basename);\n  let initialErrors = null;\n  if (initialMatches == null) {\n    // If we do not match a user-provided-route, fall back to the root\n    // to allow the error boundary to take over\n    let {\n      matches,\n      route,\n      error\n    } = getNotFoundMatches(dataRoutes);\n    initialMatches = matches;\n    initialErrors = {\n      [route.id]: error\n    };\n  }\n  let initialized = !initialMatches.some(m => m.route.loader) || init.hydrationData != null;\n  let router;\n  let state = {\n    historyAction: init.history.action,\n    location: init.history.location,\n    matches: initialMatches,\n    initialized,\n    navigation: IDLE_NAVIGATION,\n    restoreScrollPosition: null,\n    preventScrollReset: false,\n    revalidation: \"idle\",\n    loaderData: init.hydrationData && init.hydrationData.loaderData || {},\n    actionData: init.hydrationData && init.hydrationData.actionData || null,\n    errors: init.hydrationData && init.hydrationData.errors || initialErrors,\n    fetchers: new Map()\n  }; // -- Stateful internal variables to manage navigations --\n  // Current navigation in progress (to be committed in completeNavigation)\n\n  let pendingAction = Action.Pop; // Should the current navigation prevent the scroll reset if scroll cannot\n  // be restored?\n\n  let pendingPreventScrollReset = false; // AbortController for the active navigation\n\n  let pendingNavigationController; // We use this to avoid touching history in completeNavigation if a\n  // revalidation is entirely uninterrupted\n\n  let isUninterruptedRevalidation = false; // Use this internal flag to force revalidation of all loaders:\n  //  - submissions (completed or interrupted)\n  //  - useRevalidate()\n  //  - X-Remix-Revalidate (from redirect)\n\n  let isRevalidationRequired = false; // Use this internal array to capture routes that require revalidation due\n  // to a cancelled deferred on action submission\n\n  let cancelledDeferredRoutes = []; // Use this internal array to capture fetcher loads that were cancelled by an\n  // action navigation and require revalidation\n\n  let cancelledFetcherLoads = []; // AbortControllers for any in-flight fetchers\n\n  let fetchControllers = new Map(); // Track loads based on the order in which they started\n\n  let incrementingLoadId = 0; // Track the outstanding pending navigation data load to be compared against\n  // the globally incrementing load when a fetcher load lands after a completed\n  // navigation\n\n  let pendingNavigationLoadId = -1; // Fetchers that triggered data reloads as a result of their actions\n\n  let fetchReloadIds = new Map(); // Fetchers that triggered redirect navigations from their actions\n\n  let fetchRedirectIds = new Set(); // Most recent href/match for fetcher.load calls for fetchers\n\n  let fetchLoadMatches = new Map(); // Store DeferredData instances for active route matches.  When a\n  // route loader returns defer() we stick one in here.  Then, when a nested\n  // promise resolves we update loaderData.  If a new navigation starts we\n  // cancel active deferreds for eliminated routes.\n\n  let activeDeferreds = new Map(); // Initialize the router, all side effects should be kicked off from here.\n  // Implemented as a Fluent API for ease of:\n  //   let router = createRouter(init).initialize();\n\n  function initialize() {\n    // If history informs us of a POP navigation, start the navigation but do not update\n    // state.  We'll update our own state once the navigation completes\n    unlistenHistory = init.history.listen(_ref => {\n      let {\n        action: historyAction,\n        location\n      } = _ref;\n      return startNavigation(historyAction, location);\n    }); // Kick off initial data load if needed.  Use Pop to avoid modifying history\n\n    if (!state.initialized) {\n      startNavigation(Action.Pop, state.location);\n    }\n    return router;\n  } // Clean up a router and it's side effects\n\n  function dispose() {\n    if (unlistenHistory) {\n      unlistenHistory();\n    }\n    subscribers.clear();\n    pendingNavigationController && pendingNavigationController.abort();\n    state.fetchers.forEach((_, key) => deleteFetcher(key));\n  } // Subscribe to state updates for the router\n\n  function subscribe(fn) {\n    subscribers.add(fn);\n    return () => subscribers.delete(fn);\n  } // Update our state and notify the calling context of the change\n\n  function updateState(newState) {\n    state = _extends({}, state, newState);\n    subscribers.forEach(subscriber => subscriber(state));\n  } // Complete a navigation returning the state.navigation back to the IDLE_NAVIGATION\n  // and setting state.[historyAction/location/matches] to the new route.\n  // - Location is a required param\n  // - Navigation will always be set to IDLE_NAVIGATION\n  // - Can pass any other state in newState\n\n  function completeNavigation(location, newState) {\n    var _state$navigation$for;\n\n    // Deduce if we're in a loading/actionReload state:\n    // - We have committed actionData in the store\n    // - The current navigation was a submission\n    // - We're past the submitting state and into the loading state\n    // - The location we've finished loading is different from the submission\n    //   location, indicating we redirected from the action (avoids false\n    //   positives for loading/submissionRedirect when actionData returned\n    //   on a prior submission)\n    let isActionReload = state.actionData != null && state.navigation.formMethod != null && state.navigation.state === \"loading\" && ((_state$navigation$for = state.navigation.formAction) == null ? void 0 : _state$navigation$for.split(\"?\")[0]) === location.pathname; // Always preserve any existing loaderData from re-used routes\n\n    let newLoaderData = newState.loaderData ? {\n      loaderData: mergeLoaderData(state.loaderData, newState.loaderData, newState.matches || [])\n    } : {};\n    updateState(_extends({}, isActionReload ? {} : {\n      actionData: null\n    }, newState, newLoaderData, {\n      historyAction: pendingAction,\n      location,\n      initialized: true,\n      navigation: IDLE_NAVIGATION,\n      revalidation: \"idle\",\n      // Don't restore on submission navigations\n      restoreScrollPosition: state.navigation.formData ? false : getSavedScrollPosition(location, newState.matches || state.matches),\n      preventScrollReset: pendingPreventScrollReset\n    }));\n    if (isUninterruptedRevalidation) ;else if (pendingAction === Action.Pop) ;else if (pendingAction === Action.Push) {\n      init.history.push(location, location.state);\n    } else if (pendingAction === Action.Replace) {\n      init.history.replace(location, location.state);\n    } // Reset stateful navigation vars\n\n    pendingAction = Action.Pop;\n    pendingPreventScrollReset = false;\n    isUninterruptedRevalidation = false;\n    isRevalidationRequired = false;\n    cancelledDeferredRoutes = [];\n    cancelledFetcherLoads = [];\n  } // Trigger a navigation event, which can either be a numerical POP or a PUSH\n  // replace with an optional submission\n\n  async function navigate(to, opts) {\n    if (typeof to === \"number\") {\n      init.history.go(to);\n      return;\n    }\n    let {\n      path,\n      submission,\n      error\n    } = normalizeNavigateOptions(to, opts);\n    let location = createLocation(state.location, path, opts && opts.state); // When using navigate as a PUSH/REPLACE we aren't reading an already-encoded\n    // URL from window.location, so we need to encode it here so the behavior\n    // remains the same as POP and non-data-router usages.  new URL() does all\n    // the same encoding we'd get from a history.pushState/window.location read\n    // without having to touch history\n\n    location = init.history.encodeLocation(location);\n    let historyAction = (opts && opts.replace) === true || submission != null ? Action.Replace : Action.Push;\n    let preventScrollReset = opts && \"preventScrollReset\" in opts ? opts.preventScrollReset === true : undefined;\n    return await startNavigation(historyAction, location, {\n      submission,\n      // Send through the formData serialization error if we have one so we can\n      // render at the right error boundary after we match routes\n      pendingError: error,\n      preventScrollReset,\n      replace: opts && opts.replace\n    });\n  } // Revalidate all current loaders.  If a navigation is in progress or if this\n  // is interrupted by a navigation, allow this to \"succeed\" by calling all\n  // loaders during the next loader round\n\n  function revalidate() {\n    interruptActiveLoads();\n    updateState({\n      revalidation: \"loading\"\n    }); // If we're currently submitting an action, we don't need to start a new\n    // navigation, we'll just let the follow up loader execution call all loaders\n\n    if (state.navigation.state === \"submitting\") {\n      return;\n    } // If we're currently in an idle state, start a new navigation for the current\n    // action/location and mark it as uninterrupted, which will skip the history\n    // update in completeNavigation\n\n    if (state.navigation.state === \"idle\") {\n      startNavigation(state.historyAction, state.location, {\n        startUninterruptedRevalidation: true\n      });\n      return;\n    } // Otherwise, if we're currently in a loading state, just start a new\n    // navigation to the navigation.location but do not trigger an uninterrupted\n    // revalidation so that history correctly updates once the navigation completes\n\n    startNavigation(pendingAction || state.historyAction, state.navigation.location, {\n      overrideNavigation: state.navigation\n    });\n  } // Start a navigation to the given action/location.  Can optionally provide a\n  // overrideNavigation which will override the normalLoad in the case of a redirect\n  // navigation\n\n  async function startNavigation(historyAction, location, opts) {\n    // Abort any in-progress navigations and start a new one. Unset any ongoing\n    // uninterrupted revalidations unless told otherwise, since we want this\n    // new navigation to update history normally\n    pendingNavigationController && pendingNavigationController.abort();\n    pendingNavigationController = null;\n    pendingAction = historyAction;\n    isUninterruptedRevalidation = (opts && opts.startUninterruptedRevalidation) === true; // Save the current scroll position every time we start a new navigation,\n    // and track whether we should reset scroll on completion\n\n    saveScrollPosition(state.location, state.matches);\n    pendingPreventScrollReset = (opts && opts.preventScrollReset) === true;\n    let loadingNavigation = opts && opts.overrideNavigation;\n    let matches = matchRoutes(dataRoutes, location, init.basename); // Short circuit with a 404 on the root error boundary if we match nothing\n\n    if (!matches) {\n      let {\n        matches: notFoundMatches,\n        route,\n        error\n      } = getNotFoundMatches(dataRoutes); // Cancel all pending deferred on 404s since we don't keep any routes\n\n      cancelActiveDeferreds();\n      completeNavigation(location, {\n        matches: notFoundMatches,\n        loaderData: {},\n        errors: {\n          [route.id]: error\n        }\n      });\n      return;\n    } // Short circuit if it's only a hash change\n\n    if (isHashChangeOnly(state.location, location)) {\n      completeNavigation(location, {\n        matches\n      });\n      return;\n    } // Create a controller/Request for this navigation\n\n    pendingNavigationController = new AbortController();\n    let request = createRequest(location, pendingNavigationController.signal, opts && opts.submission);\n    let pendingActionData;\n    let pendingError;\n    if (opts && opts.pendingError) {\n      // If we have a pendingError, it means the user attempted a GET submission\n      // with binary FormData so assign here and skip to handleLoaders.  That\n      // way we handle calling loaders above the boundary etc.  It's not really\n      // different from an actionError in that sense.\n      pendingError = {\n        [findNearestBoundary(matches).route.id]: opts.pendingError\n      };\n    } else if (opts && opts.submission) {\n      // Call action if we received an action submission\n      let actionOutput = await handleAction(request, location, opts.submission, matches, {\n        replace: opts.replace\n      });\n      if (actionOutput.shortCircuited) {\n        return;\n      }\n      pendingActionData = actionOutput.pendingActionData;\n      pendingError = actionOutput.pendingActionError;\n      let navigation = _extends({\n        state: \"loading\",\n        location\n      }, opts.submission);\n      loadingNavigation = navigation;\n    } // Call loaders\n\n    let {\n      shortCircuited,\n      loaderData,\n      errors\n    } = await handleLoaders(request, location, matches, loadingNavigation, opts && opts.submission, opts && opts.replace, pendingActionData, pendingError);\n    if (shortCircuited) {\n      return;\n    } // Clean up now that the action/loaders have completed.  Don't clean up if\n    // we short circuited because pendingNavigationController will have already\n    // been assigned to a new controller for the next navigation\n\n    pendingNavigationController = null;\n    completeNavigation(location, {\n      matches,\n      loaderData,\n      errors\n    });\n  } // Call the action matched by the leaf route for this navigation and handle\n  // redirects/errors\n\n  async function handleAction(request, location, submission, matches, opts) {\n    interruptActiveLoads(); // Put us in a submitting state\n\n    let navigation = _extends({\n      state: \"submitting\",\n      location\n    }, submission);\n    updateState({\n      navigation\n    }); // Call our action and get the result\n\n    let result;\n    let actionMatch = getTargetMatch(matches, location);\n    if (!actionMatch.route.action) {\n      result = getMethodNotAllowedResult(location);\n    } else {\n      result = await callLoaderOrAction(\"action\", request, actionMatch, matches, router.basename);\n      if (request.signal.aborted) {\n        return {\n          shortCircuited: true\n        };\n      }\n    }\n    if (isRedirectResult(result)) {\n      let redirectNavigation = _extends({\n        state: \"loading\",\n        location: createLocation(state.location, result.location)\n      }, submission);\n      await startRedirectNavigation(result, redirectNavigation, opts && opts.replace);\n      return {\n        shortCircuited: true\n      };\n    }\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = findNearestBoundary(matches, actionMatch.route.id); // By default, all submissions are REPLACE navigations, but if the\n      // action threw an error that'll be rendered in an errorElement, we fall\n      // back to PUSH so that the user can use the back button to get back to\n      // the pre-submission form location to try again\n\n      if ((opts && opts.replace) !== true) {\n        pendingAction = Action.Push;\n      }\n      return {\n        pendingActionError: {\n          [boundaryMatch.route.id]: result.error\n        }\n      };\n    }\n    if (isDeferredResult(result)) {\n      throw new Error(\"defer() is not supported in actions\");\n    }\n    return {\n      pendingActionData: {\n        [actionMatch.route.id]: result.data\n      }\n    };\n  } // Call all applicable loaders for the given matches, handling redirects,\n  // errors, etc.\n\n  async function handleLoaders(request, location, matches, overrideNavigation, submission, replace, pendingActionData, pendingError) {\n    // Figure out the right navigation we want to use for data loading\n    let loadingNavigation = overrideNavigation;\n    if (!loadingNavigation) {\n      let navigation = {\n        state: \"loading\",\n        location,\n        formMethod: undefined,\n        formAction: undefined,\n        formEncType: undefined,\n        formData: undefined\n      };\n      loadingNavigation = navigation;\n    }\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(state, matches, submission, location, isRevalidationRequired, cancelledDeferredRoutes, cancelledFetcherLoads, pendingActionData, pendingError, fetchLoadMatches); // Cancel pending deferreds for no-longer-matched routes or routes we're\n    // about to reload.  Note that if this is an action reload we would have\n    // already cancelled all pending deferreds so this would be a no-op\n\n    cancelActiveDeferreds(routeId => !(matches && matches.some(m => m.route.id === routeId)) || matchesToLoad && matchesToLoad.some(m => m.route.id === routeId)); // Short circuit if we have no loaders to run\n\n    if (matchesToLoad.length === 0 && revalidatingFetchers.length === 0) {\n      completeNavigation(location, {\n        matches,\n        loaderData: mergeLoaderData(state.loaderData, {}, matches),\n        // Commit pending error if we're short circuiting\n        errors: pendingError || null,\n        actionData: pendingActionData || null\n      });\n      return {\n        shortCircuited: true\n      };\n    } // If this is an uninterrupted revalidation, we remain in our current idle\n    // state.  If not, we need to switch to our loading state and load data,\n    // preserving any new action data or existing action data (in the case of\n    // a revalidation interrupting an actionReload)\n\n    if (!isUninterruptedRevalidation) {\n      revalidatingFetchers.forEach(_ref2 => {\n        let [key] = _ref2;\n        let fetcher = state.fetchers.get(key);\n        let revalidatingFetcher = {\n          state: \"loading\",\n          data: fetcher && fetcher.data,\n          formMethod: undefined,\n          formAction: undefined,\n          formEncType: undefined,\n          formData: undefined\n        };\n        state.fetchers.set(key, revalidatingFetcher);\n      });\n      updateState(_extends({\n        navigation: loadingNavigation,\n        actionData: pendingActionData || state.actionData || null\n      }, revalidatingFetchers.length > 0 ? {\n        fetchers: new Map(state.fetchers)\n      } : {}));\n    }\n    pendingNavigationLoadId = ++incrementingLoadId;\n    revalidatingFetchers.forEach(_ref3 => {\n      let [key] = _ref3;\n      return fetchControllers.set(key, pendingNavigationController);\n    });\n    let {\n      results,\n      loaderResults,\n      fetcherResults\n    } = await callLoadersAndMaybeResolveData(state.matches, matches, matchesToLoad, revalidatingFetchers, request);\n    if (request.signal.aborted) {\n      return {\n        shortCircuited: true\n      };\n    } // Clean up _after_ loaders have completed.  Don't clean up if we short\n    // circuited because fetchControllers would have been aborted and\n    // reassigned to new controllers for the next navigation\n\n    revalidatingFetchers.forEach(_ref4 => {\n      let [key] = _ref4;\n      return fetchControllers.delete(key);\n    }); // If any loaders returned a redirect Response, start a new REPLACE navigation\n\n    let redirect = findRedirect(results);\n    if (redirect) {\n      let redirectNavigation = getLoaderRedirect(state, redirect);\n      await startRedirectNavigation(redirect, redirectNavigation, replace);\n      return {\n        shortCircuited: true\n      };\n    } // Process and commit output from loaders\n\n    let {\n      loaderData,\n      errors\n    } = processLoaderData(state, matches, matchesToLoad, loaderResults, pendingError, revalidatingFetchers, fetcherResults, activeDeferreds); // Wire up subscribers to update loaderData as promises settle\n\n    activeDeferreds.forEach((deferredData, routeId) => {\n      deferredData.subscribe(aborted => {\n        // Note: No need to updateState here since the TrackedPromise on\n        // loaderData is stable across resolve/reject\n        // Remove this instance if we were aborted or if promises have settled\n        if (aborted || deferredData.done) {\n          activeDeferreds.delete(routeId);\n        }\n      });\n    });\n    markFetchRedirectsDone();\n    let didAbortFetchLoads = abortStaleFetchLoads(pendingNavigationLoadId);\n    return _extends({\n      loaderData,\n      errors\n    }, didAbortFetchLoads || revalidatingFetchers.length > 0 ? {\n      fetchers: new Map(state.fetchers)\n    } : {});\n  }\n  function getFetcher(key) {\n    return state.fetchers.get(key) || IDLE_FETCHER;\n  } // Trigger a fetcher load/submit for the given fetcher key\n\n  function fetch(key, routeId, href, opts) {\n    if (isServer) {\n      throw new Error(\"router.fetch() was called during the server render, but it shouldn't be. \" + \"You are likely calling a useFetcher() method in the body of your component. \" + \"Try moving it to a useEffect or a callback.\");\n    }\n    if (fetchControllers.has(key)) abortFetcher(key);\n    let matches = matchRoutes(dataRoutes, href, init.basename);\n    if (!matches) {\n      setFetcherError(key, routeId, new ErrorResponse(404, \"Not Found\", null));\n      return;\n    }\n    let {\n      path,\n      submission\n    } = normalizeNavigateOptions(href, opts, true);\n    let match = getTargetMatch(matches, path);\n    if (submission) {\n      handleFetcherAction(key, routeId, path, match, matches, submission);\n      return;\n    } // Store off the match so we can call it's shouldRevalidate on subsequent\n    // revalidations\n\n    fetchLoadMatches.set(key, [path, match, matches]);\n    handleFetcherLoader(key, routeId, path, match, matches);\n  } // Call the action for the matched fetcher.submit(), and then handle redirects,\n  // errors, and revalidation\n\n  async function handleFetcherAction(key, routeId, path, match, requestMatches, submission) {\n    interruptActiveLoads();\n    fetchLoadMatches.delete(key);\n    if (!match.route.action) {\n      let {\n        error\n      } = getMethodNotAllowedResult(path);\n      setFetcherError(key, routeId, error);\n      return;\n    } // Put this fetcher into it's submitting state\n\n    let existingFetcher = state.fetchers.get(key);\n    let fetcher = _extends({\n      state: \"submitting\"\n    }, submission, {\n      data: existingFetcher && existingFetcher.data\n    });\n    state.fetchers.set(key, fetcher);\n    updateState({\n      fetchers: new Map(state.fetchers)\n    }); // Call the action for the fetcher\n\n    let abortController = new AbortController();\n    let fetchRequest = createRequest(path, abortController.signal, submission);\n    fetchControllers.set(key, abortController);\n    let actionResult = await callLoaderOrAction(\"action\", fetchRequest, match, requestMatches, router.basename);\n    if (fetchRequest.signal.aborted) {\n      // We can delete this so long as we weren't aborted by ou our own fetcher\n      // re-submit which would have put _new_ controller is in fetchControllers\n      if (fetchControllers.get(key) === abortController) {\n        fetchControllers.delete(key);\n      }\n      return;\n    }\n    if (isRedirectResult(actionResult)) {\n      fetchControllers.delete(key);\n      fetchRedirectIds.add(key);\n      let loadingFetcher = _extends({\n        state: \"loading\"\n      }, submission, {\n        data: undefined\n      });\n      state.fetchers.set(key, loadingFetcher);\n      updateState({\n        fetchers: new Map(state.fetchers)\n      });\n      let redirectNavigation = _extends({\n        state: \"loading\",\n        location: createLocation(state.location, actionResult.location)\n      }, submission);\n      await startRedirectNavigation(actionResult, redirectNavigation);\n      return;\n    } // Process any non-redirect errors thrown\n\n    if (isErrorResult(actionResult)) {\n      setFetcherError(key, routeId, actionResult.error);\n      return;\n    }\n    if (isDeferredResult(actionResult)) {\n      invariant(false, \"defer() is not supported in actions\");\n    } // Start the data load for current matches, or the next location if we're\n    // in the middle of a navigation\n\n    let nextLocation = state.navigation.location || state.location;\n    let revalidationRequest = createRequest(nextLocation, abortController.signal);\n    let matches = state.navigation.state !== \"idle\" ? matchRoutes(dataRoutes, state.navigation.location, init.basename) : state.matches;\n    invariant(matches, \"Didn't find any matches after fetcher action\");\n    let loadId = ++incrementingLoadId;\n    fetchReloadIds.set(key, loadId);\n    let loadFetcher = _extends({\n      state: \"loading\",\n      data: actionResult.data\n    }, submission);\n    state.fetchers.set(key, loadFetcher);\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(state, matches, submission, nextLocation, isRevalidationRequired, cancelledDeferredRoutes, cancelledFetcherLoads, {\n      [match.route.id]: actionResult.data\n    }, undefined,\n    // No need to send through errors since we short circuit above\n    fetchLoadMatches); // Put all revalidating fetchers into the loading state, except for the\n    // current fetcher which we want to keep in it's current loading state which\n    // contains it's action submission info + action data\n\n    revalidatingFetchers.filter(_ref5 => {\n      let [staleKey] = _ref5;\n      return staleKey !== key;\n    }).forEach(_ref6 => {\n      let [staleKey] = _ref6;\n      let existingFetcher = state.fetchers.get(staleKey);\n      let revalidatingFetcher = {\n        state: \"loading\",\n        data: existingFetcher && existingFetcher.data,\n        formMethod: undefined,\n        formAction: undefined,\n        formEncType: undefined,\n        formData: undefined\n      };\n      state.fetchers.set(staleKey, revalidatingFetcher);\n      fetchControllers.set(staleKey, abortController);\n    });\n    updateState({\n      fetchers: new Map(state.fetchers)\n    });\n    let {\n      results,\n      loaderResults,\n      fetcherResults\n    } = await callLoadersAndMaybeResolveData(state.matches, matches, matchesToLoad, revalidatingFetchers, revalidationRequest);\n    if (abortController.signal.aborted) {\n      return;\n    }\n    fetchReloadIds.delete(key);\n    fetchControllers.delete(key);\n    revalidatingFetchers.forEach(_ref7 => {\n      let [staleKey] = _ref7;\n      return fetchControllers.delete(staleKey);\n    });\n    let redirect = findRedirect(results);\n    if (redirect) {\n      let redirectNavigation = getLoaderRedirect(state, redirect);\n      await startRedirectNavigation(redirect, redirectNavigation);\n      return;\n    } // Process and commit output from loaders\n\n    let {\n      loaderData,\n      errors\n    } = processLoaderData(state, state.matches, matchesToLoad, loaderResults, undefined, revalidatingFetchers, fetcherResults, activeDeferreds);\n    let doneFetcher = {\n      state: \"idle\",\n      data: actionResult.data,\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined\n    };\n    state.fetchers.set(key, doneFetcher);\n    let didAbortFetchLoads = abortStaleFetchLoads(loadId); // If we are currently in a navigation loading state and this fetcher is\n    // more recent than the navigation, we want the newer data so abort the\n    // navigation and complete it with the fetcher data\n\n    if (state.navigation.state === \"loading\" && loadId > pendingNavigationLoadId) {\n      invariant(pendingAction, \"Expected pending action\");\n      pendingNavigationController && pendingNavigationController.abort();\n      completeNavigation(state.navigation.location, {\n        matches,\n        loaderData,\n        errors,\n        fetchers: new Map(state.fetchers)\n      });\n    } else {\n      // otherwise just update with the fetcher data, preserving any existing\n      // loaderData for loaders that did not need to reload.  We have to\n      // manually merge here since we aren't going through completeNavigation\n      updateState(_extends({\n        errors,\n        loaderData: mergeLoaderData(state.loaderData, loaderData, matches)\n      }, didAbortFetchLoads ? {\n        fetchers: new Map(state.fetchers)\n      } : {}));\n      isRevalidationRequired = false;\n    }\n  } // Call the matched loader for fetcher.load(), handling redirects, errors, etc.\n\n  async function handleFetcherLoader(key, routeId, path, match, matches) {\n    let existingFetcher = state.fetchers.get(key); // Put this fetcher into it's loading state\n\n    let loadingFetcher = {\n      state: \"loading\",\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      data: existingFetcher && existingFetcher.data\n    };\n    state.fetchers.set(key, loadingFetcher);\n    updateState({\n      fetchers: new Map(state.fetchers)\n    }); // Call the loader for this fetcher route match\n\n    let abortController = new AbortController();\n    let fetchRequest = createRequest(path, abortController.signal);\n    fetchControllers.set(key, abortController);\n    let result = await callLoaderOrAction(\"loader\", fetchRequest, match, matches, router.basename); // Deferred isn't supported or fetcher loads, await everything and treat it\n    // as a normal load.  resolveDeferredData will return undefined if this\n    // fetcher gets aborted, so we just leave result untouched and short circuit\n    // below if that happens\n\n    if (isDeferredResult(result)) {\n      result = (await resolveDeferredData(result, fetchRequest.signal, true)) || result;\n    } // We can delete this so long as we weren't aborted by ou our own fetcher\n    // re-load which would have put _new_ controller is in fetchControllers\n\n    if (fetchControllers.get(key) === abortController) {\n      fetchControllers.delete(key);\n    }\n    if (fetchRequest.signal.aborted) {\n      return;\n    } // If the loader threw a redirect Response, start a new REPLACE navigation\n\n    if (isRedirectResult(result)) {\n      let redirectNavigation = getLoaderRedirect(state, result);\n      await startRedirectNavigation(result, redirectNavigation);\n      return;\n    } // Process any non-redirect errors thrown\n\n    if (isErrorResult(result)) {\n      let boundaryMatch = findNearestBoundary(state.matches, routeId);\n      state.fetchers.delete(key); // TODO: In remix, this would reset to IDLE_NAVIGATION if it was a catch -\n      // do we need to behave any differently with our non-redirect errors?\n      // What if it was a non-redirect Response?\n\n      updateState({\n        fetchers: new Map(state.fetchers),\n        errors: {\n          [boundaryMatch.route.id]: result.error\n        }\n      });\n      return;\n    }\n    invariant(!isDeferredResult(result), \"Unhandled fetcher deferred data\"); // Put the fetcher back into an idle state\n\n    let doneFetcher = {\n      state: \"idle\",\n      data: result.data,\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined\n    };\n    state.fetchers.set(key, doneFetcher);\n    updateState({\n      fetchers: new Map(state.fetchers)\n    });\n  }\n  /**\n   * Utility function to handle redirects returned from an action or loader.\n   * Normally, a redirect \"replaces\" the navigation that triggered it.  So, for\n   * example:\n   *\n   *  - user is on /a\n   *  - user clicks a link to /b\n   *  - loader for /b redirects to /c\n   *\n   * In a non-JS app the browser would track the in-flight navigation to /b and\n   * then replace it with /c when it encountered the redirect response.  In\n   * the end it would only ever update the URL bar with /c.\n   *\n   * In client-side routing using pushState/replaceState, we aim to emulate\n   * this behavior and we also do not update history until the end of the\n   * navigation (including processed redirects).  This means that we never\n   * actually touch history until we've processed redirects, so we just use\n   * the history action from the original navigation (PUSH or REPLACE).\n   */\n\n  async function startRedirectNavigation(redirect, navigation, replace) {\n    if (redirect.revalidate) {\n      isRevalidationRequired = true;\n    }\n    invariant(navigation.location, \"Expected a location on the redirect navigation\"); // There's no need to abort on redirects, since we don't detect the\n    // redirect until the action/loaders have settled\n\n    pendingNavigationController = null;\n    let redirectHistoryAction = replace === true ? Action.Replace : Action.Push;\n    await startNavigation(redirectHistoryAction, navigation.location, {\n      overrideNavigation: navigation\n    });\n  }\n  async function callLoadersAndMaybeResolveData(currentMatches, matches, matchesToLoad, fetchersToLoad, request) {\n    // Call all navigation loaders and revalidating fetcher loaders in parallel,\n    // then slice off the results into separate arrays so we can handle them\n    // accordingly\n    let results = await Promise.all([...matchesToLoad.map(match => callLoaderOrAction(\"loader\", request, match, matches, router.basename)), ...fetchersToLoad.map(_ref8 => {\n      let [, href, match, fetchMatches] = _ref8;\n      return callLoaderOrAction(\"loader\", createRequest(href, request.signal), match, fetchMatches, router.basename);\n    })]);\n    let loaderResults = results.slice(0, matchesToLoad.length);\n    let fetcherResults = results.slice(matchesToLoad.length);\n    await Promise.all([resolveDeferredResults(currentMatches, matchesToLoad, loaderResults, request.signal, false, state.loaderData), resolveDeferredResults(currentMatches, fetchersToLoad.map(_ref9 => {\n      let [,, match] = _ref9;\n      return match;\n    }), fetcherResults, request.signal, true)]);\n    return {\n      results,\n      loaderResults,\n      fetcherResults\n    };\n  }\n  function interruptActiveLoads() {\n    // Every interruption triggers a revalidation\n    isRevalidationRequired = true; // Cancel pending route-level deferreds and mark cancelled routes for\n    // revalidation\n\n    cancelledDeferredRoutes.push(...cancelActiveDeferreds()); // Abort in-flight fetcher loads\n\n    fetchLoadMatches.forEach((_, key) => {\n      if (fetchControllers.has(key)) {\n        cancelledFetcherLoads.push(key);\n        abortFetcher(key);\n      }\n    });\n  }\n  function setFetcherError(key, routeId, error) {\n    let boundaryMatch = findNearestBoundary(state.matches, routeId);\n    deleteFetcher(key);\n    updateState({\n      errors: {\n        [boundaryMatch.route.id]: error\n      },\n      fetchers: new Map(state.fetchers)\n    });\n  }\n  function deleteFetcher(key) {\n    if (fetchControllers.has(key)) abortFetcher(key);\n    fetchLoadMatches.delete(key);\n    fetchReloadIds.delete(key);\n    fetchRedirectIds.delete(key);\n    state.fetchers.delete(key);\n  }\n  function abortFetcher(key) {\n    let controller = fetchControllers.get(key);\n    invariant(controller, \"Expected fetch controller: \" + key);\n    controller.abort();\n    fetchControllers.delete(key);\n  }\n  function markFetchersDone(keys) {\n    for (let key of keys) {\n      let fetcher = getFetcher(key);\n      let doneFetcher = {\n        state: \"idle\",\n        data: fetcher.data,\n        formMethod: undefined,\n        formAction: undefined,\n        formEncType: undefined,\n        formData: undefined\n      };\n      state.fetchers.set(key, doneFetcher);\n    }\n  }\n  function markFetchRedirectsDone() {\n    let doneKeys = [];\n    for (let key of fetchRedirectIds) {\n      let fetcher = state.fetchers.get(key);\n      invariant(fetcher, \"Expected fetcher: \" + key);\n      if (fetcher.state === \"loading\") {\n        fetchRedirectIds.delete(key);\n        doneKeys.push(key);\n      }\n    }\n    markFetchersDone(doneKeys);\n  }\n  function abortStaleFetchLoads(landedId) {\n    let yeetedKeys = [];\n    for (let [key, id] of fetchReloadIds) {\n      if (id < landedId) {\n        let fetcher = state.fetchers.get(key);\n        invariant(fetcher, \"Expected fetcher: \" + key);\n        if (fetcher.state === \"loading\") {\n          abortFetcher(key);\n          fetchReloadIds.delete(key);\n          yeetedKeys.push(key);\n        }\n      }\n    }\n    markFetchersDone(yeetedKeys);\n    return yeetedKeys.length > 0;\n  }\n  function cancelActiveDeferreds(predicate) {\n    let cancelledRouteIds = [];\n    activeDeferreds.forEach((dfd, routeId) => {\n      if (!predicate || predicate(routeId)) {\n        // Cancel the deferred - but do not remove from activeDeferreds here -\n        // we rely on the subscribers to do that so our tests can assert proper\n        // cleanup via _internalActiveDeferreds\n        dfd.cancel();\n        cancelledRouteIds.push(routeId);\n        activeDeferreds.delete(routeId);\n      }\n    });\n    return cancelledRouteIds;\n  } // Opt in to capturing and reporting scroll positions during navigations,\n  // used by the <ScrollRestoration> component\n\n  function enableScrollRestoration(positions, getPosition, getKey) {\n    savedScrollPositions = positions;\n    getScrollPosition = getPosition;\n    getScrollRestorationKey = getKey || (location => location.key); // Perform initial hydration scroll restoration, since we miss the boat on\n    // the initial updateState() because we've not yet rendered <ScrollRestoration/>\n    // and therefore have no savedScrollPositions available\n\n    if (!initialScrollRestored && state.navigation === IDLE_NAVIGATION) {\n      initialScrollRestored = true;\n      let y = getSavedScrollPosition(state.location, state.matches);\n      if (y != null) {\n        updateState({\n          restoreScrollPosition: y\n        });\n      }\n    }\n    return () => {\n      savedScrollPositions = null;\n      getScrollPosition = null;\n      getScrollRestorationKey = null;\n    };\n  }\n  function saveScrollPosition(location, matches) {\n    if (savedScrollPositions && getScrollRestorationKey && getScrollPosition) {\n      let userMatches = matches.map(m => createUseMatchesMatch(m, state.loaderData));\n      let key = getScrollRestorationKey(location, userMatches) || location.key;\n      savedScrollPositions[key] = getScrollPosition();\n    }\n  }\n  function getSavedScrollPosition(location, matches) {\n    if (savedScrollPositions && getScrollRestorationKey && getScrollPosition) {\n      let userMatches = matches.map(m => createUseMatchesMatch(m, state.loaderData));\n      let key = getScrollRestorationKey(location, userMatches) || location.key;\n      let y = savedScrollPositions[key];\n      if (typeof y === \"number\") {\n        return y;\n      }\n    }\n    return null;\n  }\n  router = {\n    get basename() {\n      return init.basename;\n    },\n    get state() {\n      return state;\n    },\n    get routes() {\n      return dataRoutes;\n    },\n    initialize,\n    subscribe,\n    enableScrollRestoration,\n    navigate,\n    fetch,\n    revalidate,\n    // Passthrough to history-aware createHref used by useHref so we get proper\n    // hash-aware URLs in DOM paths\n    createHref: to => init.history.createHref(to),\n    getFetcher,\n    deleteFetcher,\n    dispose,\n    _internalFetchControllers: fetchControllers,\n    _internalActiveDeferreds: activeDeferreds\n  };\n  return router;\n} //#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region createStaticHandler\n////////////////////////////////////////////////////////////////////////////////\n\nconst validActionMethods = new Set([\"POST\", \"PUT\", \"PATCH\", \"DELETE\"]);\nconst validRequestMethods = new Set([\"GET\", \"HEAD\", ...validActionMethods]);\nfunction unstable_createStaticHandler(routes) {\n  invariant(routes.length > 0, \"You must provide a non-empty routes array to unstable_createStaticHandler\");\n  let dataRoutes = convertRoutesToDataRoutes(routes);\n  /**\n   * The query() method is intended for document requests, in which we want to\n   * call an optional action and potentially multiple loaders for all nested\n   * routes.  It returns a StaticHandlerContext object, which is very similar\n   * to the router state (location, loaderData, actionData, errors, etc.) and\n   * also adds SSR-specific information such as the statusCode and headers\n   * from action/loaders Responses.\n   *\n   * It _should_ never throw and should report all errors through the\n   * returned context.errors object, properly associating errors to their error\n   * boundary.  Additionally, it tracks _deepestRenderedBoundaryId which can be\n   * used to emulate React error boundaries during SSr by performing a second\n   * pass only down to the boundaryId.\n   *\n   * The one exception where we do not return a StaticHandlerContext is when a\n   * redirect response is returned or thrown from any action/loader.  We\n   * propagate that out and return the raw Response so the HTTP server can\n   * return it directly.\n   */\n\n  async function query(request) {\n    let url = new URL(request.url);\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location);\n    if (!validRequestMethods.has(request.method)) {\n      let {\n        matches: methodNotAllowedMatches,\n        route,\n        error\n      } = getMethodNotAllowedMatches(dataRoutes);\n      return {\n        location,\n        matches: methodNotAllowedMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {}\n      };\n    } else if (!matches) {\n      let {\n        matches: notFoundMatches,\n        route,\n        error\n      } = getNotFoundMatches(dataRoutes);\n      return {\n        location,\n        matches: notFoundMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {}\n      };\n    }\n    let result = await queryImpl(request, location, matches);\n    if (result instanceof Response) {\n      return result;\n    } // When returning StaticHandlerContext, we patch back in the location here\n    // since we need it for React Context.  But this helps keep our submit and\n    // loadRouteData operating on a Request instead of a Location\n\n    return _extends({\n      location\n    }, result);\n  }\n  /**\n   * The queryRoute() method is intended for targeted route requests, either\n   * for fetch ?_data requests or resource route requests.  In this case, we\n   * are only ever calling a single action or loader, and we are returning the\n   * returned value directly.  In most cases, this will be a Response returned\n   * from the action/loader, but it may be a primitive or other value as well -\n   * and in such cases the calling context should handle that accordingly.\n   *\n   * We do respect the throw/return differentiation, so if an action/loader\n   * throws, then this method will throw the value.  This is important so we\n   * can do proper boundary identification in Remix where a thrown Response\n   * must go to the Catch Boundary but a returned Response is happy-path.\n   *\n   * One thing to note is that any Router-initiated thrown Response (such as a\n   * 404 or 405) will have a custom X-Remix-Router-Error: \"yes\" header on it\n   * in order to differentiate from responses thrown from user actions/loaders.\n   */\n\n  async function queryRoute(request, routeId) {\n    let url = new URL(request.url);\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location);\n    if (!validRequestMethods.has(request.method)) {\n      throw createRouterErrorResponse(null, {\n        status: 405,\n        statusText: \"Method Not Allowed\"\n      });\n    } else if (!matches) {\n      throw createRouterErrorResponse(null, {\n        status: 404,\n        statusText: \"Not Found\"\n      });\n    }\n    let match = routeId ? matches.find(m => m.route.id === routeId) : getTargetMatch(matches, location);\n    if (!match) {\n      throw createRouterErrorResponse(null, {\n        status: 404,\n        statusText: \"Not Found\"\n      });\n    }\n    let result = await queryImpl(request, location, matches, match);\n    if (result instanceof Response) {\n      return result;\n    }\n    let error = result.errors ? Object.values(result.errors)[0] : undefined;\n    if (error !== undefined) {\n      // If we got back result.errors, that means the loader/action threw\n      // _something_ that wasn't a Response, but it's not guaranteed/required\n      // to be an `instanceof Error` either, so we have to use throw here to\n      // preserve the \"error\" state outside of queryImpl.\n      throw error;\n    } // Pick off the right state value to return\n\n    let routeData = [result.actionData, result.loaderData].find(v => v);\n    return Object.values(routeData || {})[0];\n  }\n  async function queryImpl(request, location, matches, routeMatch) {\n    invariant(request.signal, \"query()/queryRoute() requests must contain an AbortController signal\");\n    try {\n      if (validActionMethods.has(request.method)) {\n        let result = await submit(request, matches, routeMatch || getTargetMatch(matches, location), routeMatch != null);\n        return result;\n      }\n      let result = await loadRouteData(request, matches, routeMatch);\n      return result instanceof Response ? result : _extends({}, result, {\n        actionData: null,\n        actionHeaders: {}\n      });\n    } catch (e) {\n      // If the user threw/returned a Response in callLoaderOrAction, we throw\n      // it to bail out and then return or throw here based on whether the user\n      // returned or threw\n      if (isQueryRouteResponse(e)) {\n        if (e.type === ResultType.error && !isRedirectResponse(e.response)) {\n          throw e.response;\n        }\n        return e.response;\n      } // Redirects are always returned since they don't propagate to catch\n      // boundaries\n\n      if (isRedirectResponse(e)) {\n        return e;\n      }\n      throw e;\n    }\n  }\n  async function submit(request, matches, actionMatch, isRouteRequest) {\n    let result;\n    if (!actionMatch.route.action) {\n      if (isRouteRequest) {\n        throw createRouterErrorResponse(null, {\n          status: 405,\n          statusText: \"Method Not Allowed\"\n        });\n      }\n      result = getMethodNotAllowedResult(request.url);\n    } else {\n      result = await callLoaderOrAction(\"action\", request, actionMatch, matches, undefined,\n      // Basename not currently supported in static handlers\n      true, isRouteRequest);\n      if (request.signal.aborted) {\n        let method = isRouteRequest ? \"queryRoute\" : \"query\";\n        throw new Error(method + \"() call aborted\");\n      }\n    }\n    if (isRedirectResult(result)) {\n      // Uhhhh - this should never happen, we should always throw these from\n      // callLoaderOrAction, but the type narrowing here keeps TS happy and we\n      // can get back on the \"throw all redirect responses\" train here should\n      // this ever happen :/\n      throw new Response(null, {\n        status: result.status,\n        headers: {\n          Location: result.location\n        }\n      });\n    }\n    if (isDeferredResult(result)) {\n      throw new Error(\"defer() is not supported in actions\");\n    }\n    if (isRouteRequest) {\n      // Note: This should only be non-Response values if we get here, since\n      // isRouteRequest should throw any Response received in callLoaderOrAction\n      if (isErrorResult(result)) {\n        let boundaryMatch = findNearestBoundary(matches, actionMatch.route.id);\n        return {\n          matches: [actionMatch],\n          loaderData: {},\n          actionData: null,\n          errors: {\n            [boundaryMatch.route.id]: result.error\n          },\n          // Note: statusCode + headers are unused here since queryRoute will\n          // return the raw Response or value\n          statusCode: 500,\n          loaderHeaders: {},\n          actionHeaders: {}\n        };\n      }\n      return {\n        matches: [actionMatch],\n        loaderData: {},\n        actionData: {\n          [actionMatch.route.id]: result.data\n        },\n        errors: null,\n        // Note: statusCode + headers are unused here since queryRoute will\n        // return the raw Response or value\n        statusCode: 200,\n        loaderHeaders: {},\n        actionHeaders: {}\n      };\n    }\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = findNearestBoundary(matches, actionMatch.route.id);\n      let context = await loadRouteData(request, matches, undefined, {\n        [boundaryMatch.route.id]: result.error\n      }); // action status codes take precedence over loader status codes\n\n      return _extends({}, context, {\n        statusCode: isRouteErrorResponse(result.error) ? result.error.status : 500,\n        actionData: null,\n        actionHeaders: _extends({}, result.headers ? {\n          [actionMatch.route.id]: result.headers\n        } : {})\n      });\n    }\n    let context = await loadRouteData(request, matches);\n    return _extends({}, context, result.statusCode ? {\n      statusCode: result.statusCode\n    } : {}, {\n      actionData: {\n        [actionMatch.route.id]: result.data\n      },\n      actionHeaders: _extends({}, result.headers ? {\n        [actionMatch.route.id]: result.headers\n      } : {})\n    });\n  }\n  async function loadRouteData(request, matches, routeMatch, pendingActionError) {\n    let isRouteRequest = routeMatch != null;\n    let requestMatches = routeMatch ? [routeMatch] : getLoaderMatchesUntilBoundary(matches, Object.keys(pendingActionError || {})[0]);\n    let matchesToLoad = requestMatches.filter(m => m.route.loader); // Short circuit if we have no loaders to run\n\n    if (matchesToLoad.length === 0) {\n      return {\n        matches,\n        loaderData: {},\n        errors: pendingActionError || null,\n        statusCode: 200,\n        loaderHeaders: {}\n      };\n    }\n    let results = await Promise.all([...matchesToLoad.map(match => callLoaderOrAction(\"loader\", request, match, matches, undefined,\n    // Basename not currently supported in static handlers\n    true, isRouteRequest))]);\n    if (request.signal.aborted) {\n      let method = isRouteRequest ? \"queryRoute\" : \"query\";\n      throw new Error(method + \"() call aborted\");\n    } // Can't do anything with these without the Remix side of things, so just\n    // cancel them for now\n\n    results.forEach(result => {\n      if (isDeferredResult(result)) {\n        result.deferredData.cancel();\n      }\n    }); // Process and commit output from loaders\n\n    let context = processRouteLoaderData(matches, matchesToLoad, results, pendingActionError);\n    return _extends({}, context, {\n      matches\n    });\n  }\n  function createRouterErrorResponse(body, init) {\n    return new Response(body, _extends({}, init, {\n      headers: _extends({}, init.headers, {\n        \"X-Remix-Router-Error\": \"yes\"\n      })\n    }));\n  }\n  return {\n    dataRoutes,\n    query,\n    queryRoute\n  };\n} //#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region Helpers\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Given an existing StaticHandlerContext and an error thrown at render time,\n * provide an updated StaticHandlerContext suitable for a second SSR render\n */\n\nfunction getStaticContextFromError(routes, context, error) {\n  let newContext = _extends({}, context, {\n    statusCode: 500,\n    errors: {\n      [context._deepestRenderedBoundaryId || routes[0].id]: error\n    }\n  });\n  return newContext;\n} // Normalize navigation options by converting formMethod=GET formData objects to\n// URLSearchParams so they behave identically to links with query params\n\nfunction normalizeNavigateOptions(to, opts, isFetcher) {\n  if (isFetcher === void 0) {\n    isFetcher = false;\n  }\n  let path = typeof to === \"string\" ? to : createPath(to); // Return location verbatim on non-submission navigations\n\n  if (!opts || !(\"formMethod\" in opts) && !(\"formData\" in opts)) {\n    return {\n      path\n    };\n  } // Create a Submission on non-GET navigations\n\n  if (opts.formMethod != null && opts.formMethod !== \"get\") {\n    return {\n      path,\n      submission: {\n        formMethod: opts.formMethod,\n        formAction: stripHashFromPath(path),\n        formEncType: opts && opts.formEncType || \"application/x-www-form-urlencoded\",\n        formData: opts.formData\n      }\n    };\n  } // No formData to flatten for GET submission\n\n  if (!opts.formData) {\n    return {\n      path\n    };\n  } // Flatten submission onto URLSearchParams for GET submissions\n\n  let parsedPath = parsePath(path);\n  try {\n    let searchParams = convertFormDataToSearchParams(opts.formData); // Since fetcher GET submissions only run a single loader (as opposed to\n    // navigation GET submissions which run all loaders), we need to preserve\n    // any incoming ?index params\n\n    if (isFetcher && parsedPath.search && hasNakedIndexQuery(parsedPath.search)) {\n      searchParams.append(\"index\", \"\");\n    }\n    parsedPath.search = \"?\" + searchParams;\n  } catch (e) {\n    return {\n      path,\n      error: new ErrorResponse(400, \"Bad Request\", \"Cannot submit binary form data using GET\")\n    };\n  }\n  return {\n    path: createPath(parsedPath)\n  };\n}\nfunction getLoaderRedirect(state, redirect) {\n  let {\n    formMethod,\n    formAction,\n    formEncType,\n    formData\n  } = state.navigation;\n  let navigation = {\n    state: \"loading\",\n    location: createLocation(state.location, redirect.location),\n    formMethod: formMethod || undefined,\n    formAction: formAction || undefined,\n    formEncType: formEncType || undefined,\n    formData: formData || undefined\n  };\n  return navigation;\n} // Filter out all routes below any caught error as they aren't going to\n// render so we don't need to load them\n\nfunction getLoaderMatchesUntilBoundary(matches, boundaryId) {\n  let boundaryMatches = matches;\n  if (boundaryId) {\n    let index = matches.findIndex(m => m.route.id === boundaryId);\n    if (index >= 0) {\n      boundaryMatches = matches.slice(0, index);\n    }\n  }\n  return boundaryMatches;\n}\nfunction getMatchesToLoad(state, matches, submission, location, isRevalidationRequired, cancelledDeferredRoutes, cancelledFetcherLoads, pendingActionData, pendingError, fetchLoadMatches) {\n  let actionResult = pendingError ? Object.values(pendingError)[0] : pendingActionData ? Object.values(pendingActionData)[0] : null; // Pick navigation matches that are net-new or qualify for revalidation\n\n  let boundaryId = pendingError ? Object.keys(pendingError)[0] : undefined;\n  let boundaryMatches = getLoaderMatchesUntilBoundary(matches, boundaryId);\n  let navigationMatches = boundaryMatches.filter((match, index) => match.route.loader != null && (isNewLoader(state.loaderData, state.matches[index], match) ||\n  // If this route had a pending deferred cancelled it must be revalidated\n  cancelledDeferredRoutes.some(id => id === match.route.id) || shouldRevalidateLoader(state.location, state.matches[index], submission, location, match, isRevalidationRequired, actionResult))); // Pick fetcher.loads that need to be revalidated\n\n  let revalidatingFetchers = [];\n  fetchLoadMatches && fetchLoadMatches.forEach((_ref10, key) => {\n    let [href, match, fetchMatches] = _ref10;\n\n    // This fetcher was cancelled from a prior action submission - force reload\n    if (cancelledFetcherLoads.includes(key)) {\n      revalidatingFetchers.push([key, href, match, fetchMatches]);\n    } else if (isRevalidationRequired) {\n      let shouldRevalidate = shouldRevalidateLoader(href, match, submission, href, match, isRevalidationRequired, actionResult);\n      if (shouldRevalidate) {\n        revalidatingFetchers.push([key, href, match, fetchMatches]);\n      }\n    }\n  });\n  return [navigationMatches, revalidatingFetchers];\n}\nfunction isNewLoader(currentLoaderData, currentMatch, match) {\n  let isNew =\n  // [a] -> [a, b]\n  !currentMatch ||\n  // [a, b] -> [a, c]\n  match.route.id !== currentMatch.route.id; // Handle the case that we don't have data for a re-used route, potentially\n  // from a prior error or from a cancelled pending deferred\n\n  let isMissingData = currentLoaderData[match.route.id] === undefined; // Always load if this is a net-new route or we don't yet have data\n\n  return isNew || isMissingData;\n}\nfunction isNewRouteInstance(currentMatch, match) {\n  let currentPath = currentMatch.route.path;\n  return (\n    // param change for this match, /users/123 -> /users/456\n    currentMatch.pathname !== match.pathname ||\n    // splat param changed, which is not present in match.path\n    // e.g. /files/images/avatar.jpg -> files/finances.xls\n    currentPath && currentPath.endsWith(\"*\") && currentMatch.params[\"*\"] !== match.params[\"*\"]\n  );\n}\nfunction shouldRevalidateLoader(currentLocation, currentMatch, submission, location, match, isRevalidationRequired, actionResult) {\n  let currentUrl = createURL(currentLocation);\n  let currentParams = currentMatch.params;\n  let nextUrl = createURL(location);\n  let nextParams = match.params; // This is the default implementation as to when we revalidate.  If the route\n  // provides it's own implementation, then we give them full control but\n  // provide this value so they can leverage it if needed after they check\n  // their own specific use cases\n  // Note that fetchers always provide the same current/next locations so the\n  // URL-based checks here don't apply to fetcher shouldRevalidate calls\n\n  let defaultShouldRevalidate = isNewRouteInstance(currentMatch, match) ||\n  // Clicked the same link, resubmitted a GET form\n  currentUrl.toString() === nextUrl.toString() ||\n  // Search params affect all loaders\n  currentUrl.search !== nextUrl.search ||\n  // Forced revalidation due to submission, useRevalidate, or X-Remix-Revalidate\n  isRevalidationRequired;\n  if (match.route.shouldRevalidate) {\n    let routeChoice = match.route.shouldRevalidate(_extends({\n      currentUrl,\n      currentParams,\n      nextUrl,\n      nextParams\n    }, submission, {\n      actionResult,\n      defaultShouldRevalidate\n    }));\n    if (typeof routeChoice === \"boolean\") {\n      return routeChoice;\n    }\n  }\n  return defaultShouldRevalidate;\n}\nasync function callLoaderOrAction(type, request, match, matches, basename, isStaticRequest, isRouteRequest) {\n  if (isStaticRequest === void 0) {\n    isStaticRequest = false;\n  }\n  if (isRouteRequest === void 0) {\n    isRouteRequest = false;\n  }\n  let resultType;\n  let result; // Setup a promise we can race against so that abort signals short circuit\n\n  let reject;\n  let abortPromise = new Promise((_, r) => reject = r);\n  let onReject = () => reject();\n  request.signal.addEventListener(\"abort\", onReject);\n  try {\n    let handler = match.route[type];\n    invariant(handler, \"Could not find the \" + type + \" to run on the \\\"\" + match.route.id + \"\\\" route\");\n    result = await Promise.race([handler({\n      request,\n      params: match.params\n    }), abortPromise]);\n  } catch (e) {\n    resultType = ResultType.error;\n    result = e;\n  } finally {\n    request.signal.removeEventListener(\"abort\", onReject);\n  }\n  if (result instanceof Response) {\n    let status = result.status; // Process redirects\n\n    if (status >= 300 && status <= 399) {\n      let location = result.headers.get(\"Location\");\n      invariant(location, \"Redirects returned/thrown from loaders/actions must have a Location header\"); // Support relative routing in redirects\n\n      let activeMatches = matches.slice(0, matches.indexOf(match) + 1);\n      let routePathnames = getPathContributingMatches(activeMatches).map(match => match.pathnameBase);\n      let requestPath = createURL(request.url).pathname;\n      let resolvedLocation = resolveTo(location, routePathnames, requestPath);\n      invariant(createPath(resolvedLocation), \"Unable to resolve redirect location: \" + result.headers.get(\"Location\")); // Prepend the basename to the redirect location if we have one\n\n      if (basename) {\n        let path = resolvedLocation.pathname;\n        resolvedLocation.pathname = path === \"/\" ? basename : joinPaths([basename, path]);\n      }\n      location = createPath(resolvedLocation); // Don't process redirects in the router during static requests requests.\n      // Instead, throw the Response and let the server handle it with an HTTP\n      // redirect.  We also update the Location header in place in this flow so\n      // basename and relative routing is taken into account\n\n      if (isStaticRequest) {\n        result.headers.set(\"Location\", location);\n        throw result;\n      }\n      return {\n        type: ResultType.redirect,\n        status,\n        location,\n        revalidate: result.headers.get(\"X-Remix-Revalidate\") !== null\n      };\n    } // For SSR single-route requests, we want to hand Responses back directly\n    // without unwrapping.  We do this with the QueryRouteResponse wrapper\n    // interface so we can know whether it was returned or thrown\n\n    if (isRouteRequest) {\n      // eslint-disable-next-line no-throw-literal\n      throw {\n        type: resultType || ResultType.data,\n        response: result\n      };\n    }\n    let data;\n    let contentType = result.headers.get(\"Content-Type\");\n    if (contentType && contentType.startsWith(\"application/json\")) {\n      data = await result.json();\n    } else {\n      data = await result.text();\n    }\n    if (resultType === ResultType.error) {\n      return {\n        type: resultType,\n        error: new ErrorResponse(status, result.statusText, data),\n        headers: result.headers\n      };\n    }\n    return {\n      type: ResultType.data,\n      data,\n      statusCode: result.status,\n      headers: result.headers\n    };\n  }\n  if (resultType === ResultType.error) {\n    return {\n      type: resultType,\n      error: result\n    };\n  }\n  if (result instanceof DeferredData) {\n    return {\n      type: ResultType.deferred,\n      deferredData: result\n    };\n  }\n  return {\n    type: ResultType.data,\n    data: result\n  };\n}\nfunction createRequest(location, signal, submission) {\n  let url = createURL(stripHashFromPath(location)).toString();\n  let init = {\n    signal\n  };\n  if (submission) {\n    let {\n      formMethod,\n      formEncType,\n      formData\n    } = submission;\n    init.method = formMethod.toUpperCase();\n    init.body = formEncType === \"application/x-www-form-urlencoded\" ? convertFormDataToSearchParams(formData) : formData;\n  } // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n\n  return new Request(url, init);\n}\nfunction convertFormDataToSearchParams(formData) {\n  let searchParams = new URLSearchParams();\n  for (let [key, value] of formData.entries()) {\n    invariant(typeof value === \"string\", 'File inputs are not supported with encType \"application/x-www-form-urlencoded\", ' + 'please use \"multipart/form-data\" instead.');\n    searchParams.append(key, value);\n  }\n  return searchParams;\n}\nfunction processRouteLoaderData(matches, matchesToLoad, results, pendingError, activeDeferreds) {\n  // Fill in loaderData/errors from our loaders\n  let loaderData = {};\n  let errors = null;\n  let statusCode;\n  let foundError = false;\n  let loaderHeaders = {}; // Process loader results into state.loaderData/state.errors\n\n  results.forEach((result, index) => {\n    let id = matchesToLoad[index].route.id;\n    invariant(!isRedirectResult(result), \"Cannot handle redirect results in processLoaderData\");\n    if (isErrorResult(result)) {\n      // Look upwards from the matched route for the closest ancestor\n      // error boundary, defaulting to the root match\n      let boundaryMatch = findNearestBoundary(matches, id);\n      let error = result.error; // If we have a pending action error, we report it at the highest-route\n      // that throws a loader error, and then clear it out to indicate that\n      // it was consumed\n\n      if (pendingError) {\n        error = Object.values(pendingError)[0];\n        pendingError = undefined;\n      }\n      errors = Object.assign(errors || {}, {\n        [boundaryMatch.route.id]: error\n      }); // Once we find our first (highest) error, we set the status code and\n      // prevent deeper status codes from overriding\n\n      if (!foundError) {\n        foundError = true;\n        statusCode = isRouteErrorResponse(result.error) ? result.error.status : 500;\n      }\n      if (result.headers) {\n        loaderHeaders[id] = result.headers;\n      }\n    } else if (isDeferredResult(result)) {\n      activeDeferreds && activeDeferreds.set(id, result.deferredData);\n      loaderData[id] = result.deferredData.data; // TODO: Add statusCode/headers once we wire up streaming in Remix\n    } else {\n      loaderData[id] = result.data; // Error status codes always override success status codes, but if all\n      // loaders are successful we take the deepest status code.\n\n      if (result.statusCode != null && result.statusCode !== 200 && !foundError) {\n        statusCode = result.statusCode;\n      }\n      if (result.headers) {\n        loaderHeaders[id] = result.headers;\n      }\n    }\n  }); // If we didn't consume the pending action error (i.e., all loaders\n  // resolved), then consume it here\n\n  if (pendingError) {\n    errors = pendingError;\n  }\n  return {\n    loaderData,\n    errors,\n    statusCode: statusCode || 200,\n    loaderHeaders\n  };\n}\nfunction processLoaderData(state, matches, matchesToLoad, results, pendingError, revalidatingFetchers, fetcherResults, activeDeferreds) {\n  let {\n    loaderData,\n    errors\n  } = processRouteLoaderData(matches, matchesToLoad, results, pendingError, activeDeferreds); // Process results from our revalidating fetchers\n\n  for (let index = 0; index < revalidatingFetchers.length; index++) {\n    let [key,, match] = revalidatingFetchers[index];\n    invariant(fetcherResults !== undefined && fetcherResults[index] !== undefined, \"Did not find corresponding fetcher result\");\n    let result = fetcherResults[index]; // Process fetcher non-redirect errors\n\n    if (isErrorResult(result)) {\n      let boundaryMatch = findNearestBoundary(state.matches, match.route.id);\n      if (!(errors && errors[boundaryMatch.route.id])) {\n        errors = _extends({}, errors, {\n          [boundaryMatch.route.id]: result.error\n        });\n      }\n      state.fetchers.delete(key);\n    } else if (isRedirectResult(result)) {\n      // Should never get here, redirects should get processed above, but we\n      // keep this to type narrow to a success result in the else\n      throw new Error(\"Unhandled fetcher revalidation redirect\");\n    } else if (isDeferredResult(result)) {\n      // Should never get here, deferred data should be awaited for fetchers\n      // in resolveDeferredResults\n      throw new Error(\"Unhandled fetcher deferred data\");\n    } else {\n      let doneFetcher = {\n        state: \"idle\",\n        data: result.data,\n        formMethod: undefined,\n        formAction: undefined,\n        formEncType: undefined,\n        formData: undefined\n      };\n      state.fetchers.set(key, doneFetcher);\n    }\n  }\n  return {\n    loaderData,\n    errors\n  };\n}\nfunction mergeLoaderData(loaderData, newLoaderData, matches) {\n  let mergedLoaderData = _extends({}, newLoaderData);\n  matches.forEach(match => {\n    let id = match.route.id;\n    if (newLoaderData[id] === undefined && loaderData[id] !== undefined) {\n      mergedLoaderData[id] = loaderData[id];\n    }\n  });\n  return mergedLoaderData;\n} // Find the nearest error boundary, looking upwards from the leaf route (or the\n// route specified by routeId) for the closest ancestor error boundary,\n// defaulting to the root match\n\nfunction findNearestBoundary(matches, routeId) {\n  let eligibleMatches = routeId ? matches.slice(0, matches.findIndex(m => m.route.id === routeId) + 1) : [...matches];\n  return eligibleMatches.reverse().find(m => m.route.hasErrorBoundary === true) || matches[0];\n}\nfunction getShortCircuitMatches(routes, status, statusText) {\n  // Prefer a root layout route if present, otherwise shim in a route object\n  let route = routes.find(r => r.index || !r.path || r.path === \"/\") || {\n    id: \"__shim-\" + status + \"-route__\"\n  };\n  return {\n    matches: [{\n      params: {},\n      pathname: \"\",\n      pathnameBase: \"\",\n      route\n    }],\n    route,\n    error: new ErrorResponse(status, statusText, null)\n  };\n}\nfunction getNotFoundMatches(routes) {\n  return getShortCircuitMatches(routes, 404, \"Not Found\");\n}\nfunction getMethodNotAllowedMatches(routes) {\n  return getShortCircuitMatches(routes, 405, \"Method Not Allowed\");\n}\nfunction getMethodNotAllowedResult(path) {\n  let href = typeof path === \"string\" ? path : createPath(path);\n  console.warn(\"You're trying to submit to a route that does not have an action.  To \" + \"fix this, please add an `action` function to the route for \" + (\"[\" + href + \"]\"));\n  return {\n    type: ResultType.error,\n    error: new ErrorResponse(405, \"Method Not Allowed\", \"\")\n  };\n} // Find any returned redirect errors, starting from the lowest match\n\nfunction findRedirect(results) {\n  for (let i = results.length - 1; i >= 0; i--) {\n    let result = results[i];\n    if (isRedirectResult(result)) {\n      return result;\n    }\n  }\n}\nfunction stripHashFromPath(path) {\n  let parsedPath = typeof path === \"string\" ? parsePath(path) : path;\n  return createPath(_extends({}, parsedPath, {\n    hash: \"\"\n  }));\n}\nfunction isHashChangeOnly(a, b) {\n  return a.pathname === b.pathname && a.search === b.search && a.hash !== b.hash;\n}\nfunction isDeferredResult(result) {\n  return result.type === ResultType.deferred;\n}\nfunction isErrorResult(result) {\n  return result.type === ResultType.error;\n}\nfunction isRedirectResult(result) {\n  return (result && result.type) === ResultType.redirect;\n}\nfunction isRedirectResponse(result) {\n  if (!(result instanceof Response)) {\n    return false;\n  }\n  let status = result.status;\n  let location = result.headers.get(\"Location\");\n  return status >= 300 && status <= 399 && location != null;\n}\nfunction isQueryRouteResponse(obj) {\n  return obj && obj.response instanceof Response && (obj.type === ResultType.data || ResultType.error);\n}\nasync function resolveDeferredResults(currentMatches, matchesToLoad, results, signal, isFetcher, currentLoaderData) {\n  for (let index = 0; index < results.length; index++) {\n    let result = results[index];\n    let match = matchesToLoad[index];\n    let currentMatch = currentMatches.find(m => m.route.id === match.route.id);\n    let isRevalidatingLoader = currentMatch != null && !isNewRouteInstance(currentMatch, match) && (currentLoaderData && currentLoaderData[match.route.id]) !== undefined;\n    if (isDeferredResult(result) && (isFetcher || isRevalidatingLoader)) {\n      // Note: we do not have to touch activeDeferreds here since we race them\n      // against the signal in resolveDeferredData and they'll get aborted\n      // there if needed\n      await resolveDeferredData(result, signal, isFetcher).then(result => {\n        if (result) {\n          results[index] = result || results[index];\n        }\n      });\n    }\n  }\n}\nasync function resolveDeferredData(result, signal, unwrap) {\n  if (unwrap === void 0) {\n    unwrap = false;\n  }\n  let aborted = await result.deferredData.resolveData(signal);\n  if (aborted) {\n    return;\n  }\n  if (unwrap) {\n    try {\n      return {\n        type: ResultType.data,\n        data: result.deferredData.unwrappedData\n      };\n    } catch (e) {\n      // Handle any TrackedPromise._error values encountered while unwrapping\n      return {\n        type: ResultType.error,\n        error: e\n      };\n    }\n  }\n  return {\n    type: ResultType.data,\n    data: result.deferredData.data\n  };\n}\nfunction hasNakedIndexQuery(search) {\n  return new URLSearchParams(search).getAll(\"index\").some(v => v === \"\");\n} // Note: This should match the format exported by useMatches, so if you change\n// this please also change that :)  Eventually we'll DRY this up\n\nfunction createUseMatchesMatch(match, loaderData) {\n  let {\n    route,\n    pathname,\n    params\n  } = match;\n  return {\n    id: route.id,\n    pathname,\n    params,\n    data: loaderData[route.id],\n    handle: route.handle\n  };\n}\nfunction getTargetMatch(matches, location) {\n  let search = typeof location === \"string\" ? parsePath(location).search : location.search;\n  if (matches[matches.length - 1].route.index && hasNakedIndexQuery(search || \"\")) {\n    // Return the leaf index route when index is present\n    return matches[matches.length - 1];\n  } // Otherwise grab the deepest \"path contributing\" match (ignoring index and\n  // pathless layout routes)\n\n  let pathMatches = getPathContributingMatches(matches);\n  return pathMatches[pathMatches.length - 1];\n} //#endregion\n\nexport { AbortedDeferredError, Action, ErrorResponse, IDLE_FETCHER, IDLE_NAVIGATION, convertRoutesToDataRoutes as UNSAFE_convertRoutesToDataRoutes, getPathContributingMatches as UNSAFE_getPathContributingMatches, createBrowserHistory, createHashHistory, createMemoryHistory, createPath, createRouter, defer, generatePath, getStaticContextFromError, getToPathname, invariant, isRouteErrorResponse, joinPaths, json, matchPath, matchRoutes, normalizePathname, parsePath, redirect, resolvePath, resolveTo, stripBasename, unstable_createStaticHandler, warning };", "map": {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;;AAEA;;AAEG;IACSA;AAAZ,WAAYA,MAAZ,EAAkB;EAChB;;;;;;AAMG;EACHA;EAEA;;;;AAIG;;EACHA;EAEA;;;AAGG;;EACHA;AACD,CAtBD,EAAYA,MAAM,KAANA,MAAM,GAsBjB,EAtBiB,CAAlB;AA2KA,MAAMC,iBAAiB,GAAG,UAA1B;AA+BA;;;AAGG;;AACa,6BACdC,OADc,EACoB;EAAA,IAAlCA,OAAkC;IAAlCA,OAAkC,GAAF,EAAE;EAAA;EAElC,IAAI;IAAEC,cAAc,GAAG,CAAC,GAAD,CAAnB;IAA0BC,YAA1B;IAAwCC,QAAQ,GAAG;EAAnD,IAA6DH,OAAjE;EACA,IAAII,OAAJ,CAHkC;;EAIlCA,OAAO,GAAGH,cAAc,CAACI,GAAf,CAAmB,CAACC,KAAD,EAAQC,KAAR,KAC3BC,oBAAoB,CAClBF,KADkB,EAElB,OAAOA,KAAP,KAAiB,QAAjB,GAA4B,IAA5B,GAAmCA,KAAK,CAACG,KAFvB,EAGlBF,KAAK,KAAK,CAAV,GAAc,SAAd,GAA0BG,SAHR,CADZ,CAAV;EAOA,IAAIH,KAAK,GAAGI,UAAU,CACpBT,YAAY,IAAI,IAAhB,GAAuBE,OAAO,CAACQ,MAAR,GAAiB,CAAxC,GAA4CV,YADxB,CAAtB;EAGA,IAAIW,MAAM,GAAGf,MAAM,CAACgB,GAApB;EACA,IAAIC,QAAQ,GAAoB,IAAhC;EAEA,SAASJ,UAAT,CAAoBK,CAApB,EAA6B;IAC3B,OAAOC,IAAI,CAACC,GAAL,CAASD,IAAI,CAACE,GAAL,CAASH,CAAT,EAAY,CAAZ,CAAT,EAAyBZ,OAAO,CAACQ,MAAR,GAAiB,CAA1C,CAAP;EACD;EACD,SAASQ,kBAAT,GAA2B;IACzB,OAAOhB,OAAO,CAACG,KAAD,CAAd;EACD;EACD,SAASC,oBAAT,CACEa,EADF,EAEEZ,KAFF,EAGEa,GAHF,EAGc;IAAA,IADZb,KACY;MADZA,KACY,GADC,IACD;IAAA;IAEZ,IAAIc,QAAQ,GAAGC,cAAc,CAC3BpB,OAAO,GAAGgB,kBAAkB,GAAGK,QAAxB,GAAmC,GADf,EAE3BJ,EAF2B,EAG3BZ,KAH2B,EAI3Ba,GAJ2B,CAA7B;IAMAI,SAAO,CACLH,QAAQ,CAACE,QAAT,CAAkBE,MAAlB,CAAyB,CAAzB,CAAgC,QAD3B,+DAEsDC,IAAI,CAACC,SAAL,CACzDR,EADyD,CAFtD,CAAP;IAMA,OAAOE,QAAP;EACD;EAED,IAAIO,OAAO,GAAkB;IAC3B,IAAIvB,KAAJ,GAAS;MACP,OAAOA,KAAP;KAFyB;IAI3B,IAAIM,MAAJ,GAAU;MACR,OAAOA,MAAP;KALyB;IAO3B,IAAIU,QAAJ,GAAY;MACV,OAAOH,kBAAkB,EAAzB;KARyB;IAU3BW,UAAU,CAACV,EAAD,EAAG;MACX,OAAO,OAAOA,EAAP,KAAc,QAAd,GAAyBA,EAAzB,GAA8BW,UAAU,CAACX,EAAD,CAA/C;KAXyB;IAa3BY,cAAc,CAACV,QAAD,EAAS;MACrB,OAAOA,QAAP;KAdyB;IAgB3BW,IAAI,CAACb,EAAD,EAAKZ,KAAL,EAAU;MACZI,MAAM,GAAGf,MAAM,CAACqC,IAAhB;MACA,IAAIC,YAAY,GAAG5B,oBAAoB,CAACa,EAAD,EAAKZ,KAAL,CAAvC;MACAF,KAAK,IAAI,CAAT;MACAH,OAAO,CAACiC,MAAR,CAAe9B,KAAf,EAAsBH,OAAO,CAACQ,MAA9B,EAAsCwB,YAAtC;MACA,IAAIjC,QAAQ,IAAIY,QAAhB,EAA0B;QACxBA,QAAQ,CAAC;UAAEF,MAAF;UAAUU,QAAQ,EAAEa;QAApB,CAAD,CAAR;MACD;KAvBwB;IAyB3BE,OAAO,CAACjB,EAAD,EAAKZ,KAAL,EAAU;MACfI,MAAM,GAAGf,MAAM,CAACyC,OAAhB;MACA,IAAIH,YAAY,GAAG5B,oBAAoB,CAACa,EAAD,EAAKZ,KAAL,CAAvC;MACAL,OAAO,CAACG,KAAD,CAAP,GAAiB6B,YAAjB;MACA,IAAIjC,QAAQ,IAAIY,QAAhB,EAA0B;QACxBA,QAAQ,CAAC;UAAEF,MAAF;UAAUU,QAAQ,EAAEa;QAApB,CAAD,CAAR;MACD;KA/BwB;IAiC3BI,EAAE,CAACC,KAAD,EAAM;MACN5B,MAAM,GAAGf,MAAM,CAACgB,GAAhB;MACAP,KAAK,GAAGI,UAAU,CAACJ,KAAK,GAAGkC,KAAT,CAAlB;MACA,IAAI1B,QAAJ,EAAc;QACZA,QAAQ,CAAC;UAAEF,MAAF;UAAUU,QAAQ,EAAEH,kBAAkB;QAAtC,CAAD,CAAR;MACD;KAtCwB;IAwC3BsB,MAAM,CAACC,EAAD,EAAa;MACjB5B,QAAQ,GAAG4B,EAAX;MACA,OAAO,MAAK;QACV5B,QAAQ,GAAG,IAAX;OADF;IAGD;GA7CH;EAgDA,OAAOe,OAAP;AACD;AAkBD;;;;;;AAMG;;AACa,8BACd9B,OADc,EACqB;EAAA,IAAnCA,OAAmC;IAAnCA,OAAmC,GAAF,EAAE;EAAA;EAEnC,SAAS4C,qBAAT,CACEC,MADF,EAEEC,aAFF,EAEkC;IAEhC,IAAI;MAAErB,QAAF;MAAYsB,MAAZ;MAAoBC;KAASH,SAAM,CAACtB,QAAxC;IACA,OAAOC,cAAc,CACnB,EADmB,EAEnB;MAAEC,QAAF;MAAYsB,MAAZ;MAAoBC;IAApB,CAFmB;IAAA;IAIlBF,aAAa,CAACrC,KAAd,IAAuBqC,aAAa,CAACrC,KAAd,CAAoBwC,GAA5C,IAAoD,IAJjC,EAKlBH,aAAa,CAACrC,KAAd,IAAuBqC,aAAa,CAACrC,KAAd,CAAoBa,GAA5C,IAAoD,SALjC,CAArB;EAOD;EAED,SAAS4B,iBAAT,CAA2BL,MAA3B,EAA2CxB,EAA3C,EAAiD;IAC/C,OAAO,OAAOA,EAAP,KAAc,QAAd,GAAyBA,EAAzB,GAA8BW,UAAU,CAACX,EAAD,CAA/C;EACD;EAED,OAAO8B,kBAAkB,CACvBP,qBADuB,EAEvBM,iBAFuB,EAGvB,IAHuB,EAIvBlD,OAJuB,CAAzB;AAMD;AAsBD;;;;;;;AAOG;;AACa,2BACdA,OADc,EACkB;EAAA,IAAhCA,OAAgC;IAAhCA,OAAgC,GAAF,EAAE;EAAA;EAEhC,SAASoD,kBAAT,CACEP,MADF,EAEEC,aAFF,EAEkC;IAEhC,IAAI;MACFrB,QAAQ,GAAG,GADT;MAEFsB,MAAM,GAAG,EAFP;MAGFC,IAAI,GAAG;IAHL,IAIAK,SAAS,CAACR,MAAM,CAACtB,QAAP,CAAgByB,IAAhB,CAAqBM,MAArB,CAA4B,CAA5B,CAAD,CAJb;IAKA,OAAO9B,cAAc,CACnB,EADmB,EAEnB;MAAEC,QAAF;MAAYsB,MAAZ;MAAoBC;IAApB,CAFmB;IAAA;IAIlBF,aAAa,CAACrC,KAAd,IAAuBqC,aAAa,CAACrC,KAAd,CAAoBwC,GAA5C,IAAoD,IAJjC,EAKlBH,aAAa,CAACrC,KAAd,IAAuBqC,aAAa,CAACrC,KAAd,CAAoBa,GAA5C,IAAoD,SALjC,CAArB;EAOD;EAED,SAASiC,cAAT,CAAwBV,MAAxB,EAAwCxB,EAAxC,EAA8C;IAC5C,IAAImC,IAAI,GAAGX,MAAM,CAACY,QAAP,CAAgBC,aAAhB,CAA8B,MAA9B,CAAX;IACA,IAAIC,IAAI,GAAG,EAAX;IAEA,IAAIH,IAAI,IAAIA,IAAI,CAACI,YAAL,CAAkB,MAAlB,CAAZ,EAAuC;MACrC,IAAIC,GAAG,GAAGhB,MAAM,CAACtB,QAAP,CAAgBoC,IAA1B;MACA,IAAIG,SAAS,GAAGD,GAAG,CAACE,OAAJ,CAAY,GAAZ,CAAhB;MACAJ,IAAI,GAAGG,SAAS,KAAK,CAAC,CAAf,GAAmBD,GAAnB,GAAyBA,GAAG,CAACG,KAAJ,CAAU,CAAV,EAAaF,SAAb,CAAhC;IACD;IAED,OAAOH,IAAI,GAAG,GAAP,IAAc,OAAOtC,EAAP,KAAc,QAAd,GAAyBA,EAAzB,GAA8BW,UAAU,CAACX,EAAD,CAAtD,CAAP;EACD;EAED,SAAS4C,oBAAT,CAA8B1C,QAA9B,EAAkDF,EAAlD,EAAwD;IACtDK,SAAO,CACLH,QAAQ,CAACE,QAAT,CAAkBE,MAAlB,CAAyB,CAAzB,CAAgC,QAD3B,iEAEwDC,IAAI,CAACC,SAAL,CAC3DR,EAD2D,CAFxD,GAAP;EAMD;EAED,OAAO8B,kBAAkB,CACvBC,kBADuB,EAEvBG,cAFuB,EAGvBU,oBAHuB,EAIvBjE,OAJuB,CAAzB;AAMD;AAGD;AACA;AACA;;AAEA,SAAS0B,SAAT,CAAiBwC,IAAjB,EAA4BC,OAA5B,EAA2C;EACzC,IAAI,CAACD,IAAL,EAAW;IACT;IACA,IAAI,OAAOE,OAAP,KAAmB,WAAvB,EAAoCA,OAAO,CAACC,IAAR,CAAaF,OAAb;IAEpC,IAAI;MACF;MACA;MACA;MACA;MACA;MACA,MAAM,IAAIG,KAAJ,CAAUH,OAAV,CAAN,CANE;IAQH,CARD,CAQE,OAAOI,CAAP,EAAU;EACb;AACF;AAED,SAASC,SAAT,GAAkB;EAChB,OAAOvD,IAAI,CAACwD,MAAL,GAAcC,QAAd,CAAuB,EAAvB,EAA2BpB,MAA3B,CAAkC,CAAlC,EAAqC,CAArC,CAAP;AACD;AAED;;AAEG;;AACH,SAASqB,eAAT,CAAyBpD,QAAzB,EAA2C;EACzC,OAAO;IACL0B,GAAG,EAAE1B,QAAQ,CAACd,KADT;IAELa,GAAG,EAAEC,QAAQ,CAACD;GAFhB;AAID;AAED;;AAEG;;AACG,SAAUE,cAAV,CACJoD,OADI,EAEJvD,EAFI,EAGJZ,KAHI,EAIJa,GAJI,EAIQ;EAAA,IADZb,KACY;IADZA,KACY,GADC,IACD;EAAA;EAEZ,IAAIc,QAAQ;IACVE,QAAQ,EAAE,OAAOmD,OAAP,KAAmB,QAAnB,GAA8BA,OAA9B,GAAwCA,OAAO,CAACnD,QADhD;IAEVsB,MAAM,EAAE,EAFE;IAGVC,IAAI,EAAE;GACF,SAAO3B,EAAP,KAAc,QAAd,GAAyBgC,SAAS,CAAChC,EAAD,CAAlC,GAAyCA,EAJnC;IAKVZ,KALU;IAMV;IACA;IACA;IACA;IACAa,GAAG,EAAGD,EAAE,IAAKA,EAAe,CAACC,GAAxB,IAAgCA,GAAhC,IAAuCkD,SAAS;GAVvD;EAYA,OAAOjD,QAAP;AACD;AAED;;AAEG;;AACa,oBAIAsD;EAAA,IAJW;IACzBpD,QAAQ,GAAG,GADc;IAEzBsB,MAAM,GAAG,EAFgB;IAGzBC,IAAI,GAAG;GACO;EACd,IAAID,MAAM,IAAIA,MAAM,KAAK,GAAzB,EACEtB,QAAQ,IAAIsB,MAAM,CAACpB,MAAP,CAAc,CAAd,CAAqB,QAArB,GAA2BoB,MAA3B,GAAoC,MAAMA,MAAtD;EACF,IAAIC,IAAI,IAAIA,IAAI,KAAK,GAArB,EACEvB,QAAQ,IAAIuB,IAAI,CAACrB,MAAL,CAAY,CAAZ,CAAmB,QAAnB,GAAyBqB,IAAzB,GAAgC,MAAMA,IAAlD;EACF,OAAOvB,QAAP;AACD;AAED;;AAEG;;AACG,SAAU4B,SAAV,CAAoByB,IAApB,EAAgC;EACpC,IAAIC,UAAU,GAAkB,EAAhC;EAEA,IAAID,IAAJ,EAAU;IACR,IAAIhB,SAAS,GAAGgB,IAAI,CAACf,OAAL,CAAa,GAAb,CAAhB;IACA,IAAID,SAAS,IAAI,CAAjB,EAAoB;MAClBiB,UAAU,CAAC/B,IAAX,GAAkB8B,IAAI,CAACxB,MAAL,CAAYQ,SAAZ,CAAlB;MACAgB,IAAI,GAAGA,IAAI,CAACxB,MAAL,CAAY,CAAZ,EAAeQ,SAAf,CAAP;IACD;IAED,IAAIkB,WAAW,GAAGF,IAAI,CAACf,OAAL,CAAa,GAAb,CAAlB;IACA,IAAIiB,WAAW,IAAI,CAAnB,EAAsB;MACpBD,UAAU,CAAChC,MAAX,GAAoB+B,IAAI,CAACxB,MAAL,CAAY0B,WAAZ,CAApB;MACAF,IAAI,GAAGA,IAAI,CAACxB,MAAL,CAAY,CAAZ,EAAe0B,WAAf,CAAP;IACD;IAED,IAAIF,IAAJ,EAAU;MACRC,UAAU,CAACtD,QAAX,GAAsBqD,IAAtB;IACD;EACF;EAED,OAAOC,UAAP;AACD;AAEK,SAAUE,SAAV,CAAoB1D,QAApB,EAA+C;EACnD;EACA;EACA;EACA,IAAIiC,IAAI,GACN,OAAOX,MAAP,KAAkB,WAAlB,IACA,OAAOA,MAAM,CAACtB,QAAd,KAA2B,WAD3B,IAEAsB,MAAM,CAACtB,QAAP,CAAgB2D,MAAhB,KAA2B,MAF3B,GAGIrC,MAAM,CAACtB,QAAP,CAAgB2D,MAHpB,GAII,mBALN;EAMA,IAAIvB,IAAI,GAAG,OAAOpC,QAAP,KAAoB,QAApB,GAA+BA,QAA/B,GAA0CS,UAAU,CAACT,QAAD,CAA/D;EACA,OAAO,IAAI4D,GAAJ,CAAQxB,IAAR,EAAcH,IAAd,CAAP;AACD;AASD,SAASL,kBAAT,CACEiC,WADF,EAEErD,UAFF,EAGEsD,gBAHF,EAIErF,OAJF,EAIiC;EAAA,IAA/BA,OAA+B;IAA/BA,OAA+B,GAAF,EAAE;EAAA;EAE/B,IAAI;IAAE6C,MAAM,GAAGY,QAAQ,CAAC6B,WAApB;IAAkCnF,QAAQ,GAAG;EAA7C,IAAuDH,OAA3D;EACA,IAAI8C,aAAa,GAAGD,MAAM,CAACf,OAA3B;EACA,IAAIjB,MAAM,GAAGf,MAAM,CAACgB,GAApB;EACA,IAAIC,QAAQ,GAAoB,IAAhC;EAEA,SAASwE,SAAT,GAAkB;IAChB1E,MAAM,GAAGf,MAAM,CAACgB,GAAhB;IACA,IAAIC,QAAJ,EAAc;MACZA,QAAQ,CAAC;QAAEF,MAAF;QAAUU,QAAQ,EAAEO,OAAO,CAACP;MAA5B,CAAD,CAAR;IACD;EACF;EAED,SAASW,IAAT,CAAcb,EAAd,EAAsBZ,KAAtB,EAAiC;IAC/BI,MAAM,GAAGf,MAAM,CAACqC,IAAhB;IACA,IAAIZ,QAAQ,GAAGC,cAAc,CAACM,OAAO,CAACP,QAAT,EAAmBF,EAAnB,EAAuBZ,KAAvB,CAA7B;IACA,IAAI4E,gBAAJ,EAAsBA,gBAAgB,CAAC9D,QAAD,EAAWF,EAAX,CAAhB;IAEtB,IAAImE,YAAY,GAAGb,eAAe,CAACpD,QAAD,CAAlC;IACA,IAAIsC,GAAG,GAAG/B,OAAO,CAACC,UAAR,CAAmBR,QAAnB,CAAV,CAN+B;;IAS/B,IAAI;MACFuB,aAAa,CAAC2C,SAAd,CAAwBD,YAAxB,EAAsC,EAAtC,EAA0C3B,GAA1C;KADF,CAEE,OAAO6B,KAAP,EAAc;MACd;MACA;MACA7C,MAAM,CAACtB,QAAP,CAAgBoE,MAAhB,CAAuB9B,GAAvB;IACD;IAED,IAAI1D,QAAQ,IAAIY,QAAhB,EAA0B;MACxBA,QAAQ,CAAC;QAAEF,MAAF;QAAUU,QAAQ,EAAEO,OAAO,CAACP;MAA5B,CAAD,CAAR;IACD;EACF;EAED,SAASe,OAAT,CAAiBjB,EAAjB,EAAyBZ,KAAzB,EAAoC;IAClCI,MAAM,GAAGf,MAAM,CAACyC,OAAhB;IACA,IAAIhB,QAAQ,GAAGC,cAAc,CAACM,OAAO,CAACP,QAAT,EAAmBF,EAAnB,EAAuBZ,KAAvB,CAA7B;IACA,IAAI4E,gBAAJ,EAAsBA,gBAAgB,CAAC9D,QAAD,EAAWF,EAAX,CAAhB;IAEtB,IAAImE,YAAY,GAAGb,eAAe,CAACpD,QAAD,CAAlC;IACA,IAAIsC,GAAG,GAAG/B,OAAO,CAACC,UAAR,CAAmBR,QAAnB,CAAV;IACAuB,aAAa,CAAC8C,YAAd,CAA2BJ,YAA3B,EAAyC,EAAzC,EAA6C3B,GAA7C;IAEA,IAAI1D,QAAQ,IAAIY,QAAhB,EAA0B;MACxBA,QAAQ,CAAC;QAAEF,MAAF;QAAUU,QAAQ,EAAEO,OAAO,CAACP;MAA5B,CAAD,CAAR;IACD;EACF;EAED,IAAIO,OAAO,GAAY;IACrB,IAAIjB,MAAJ,GAAU;MACR,OAAOA,MAAP;KAFmB;IAIrB,IAAIU,QAAJ,GAAY;MACV,OAAO6D,WAAW,CAACvC,MAAD,EAASC,aAAT,CAAlB;KALmB;IAOrBJ,MAAM,CAACC,EAAD,EAAa;MACjB,IAAI5B,QAAJ,EAAc;QACZ,MAAM,IAAIuD,KAAJ,CAAU,4CAAV,CAAN;MACD;MACDzB,MAAM,CAACgD,gBAAP,CAAwB9F,iBAAxB,EAA2CwF,SAA3C;MACAxE,QAAQ,GAAG4B,EAAX;MAEA,OAAO,MAAK;QACVE,MAAM,CAACiD,mBAAP,CAA2B/F,iBAA3B,EAA8CwF,SAA9C;QACAxE,QAAQ,GAAG,IAAX;OAFF;KAdmB;IAmBrBgB,UAAU,CAACV,EAAD,EAAG;MACX,OAAOU,UAAU,CAACc,MAAD,EAASxB,EAAT,CAAjB;KApBmB;IAsBrBY,cAAc,CAACV,QAAD,EAAS;MACrB;MACA,IAAIsC,GAAG,GAAGoB,SAAS,CAACjD,UAAU,CAACT,QAAD,CAAX,CAAnB;MACA,oBACKA,QADL;QAEEE,QAAQ,EAAEoC,GAAG,CAACpC,QAFhB;QAGEsB,MAAM,EAAEc,GAAG,CAACd,MAHd;QAIEC,IAAI,EAAEa,GAAG,CAACb;MAJZ;KAzBmB;IAgCrBd,IAhCqB;IAiCrBI,OAjCqB;IAkCrBE,EAAE,CAACxB,CAAD,EAAE;MACF,OAAO8B,aAAa,CAACN,EAAd,CAAiBxB,CAAjB,CAAP;IACD;GApCH;EAuCA,OAAOc,OAAP;AACD;;ACtoBD,IAAYiE,UAAZ;AAAA,WAAYA,UAAZ,EAAsB;EACpBA;EACAA;EACAA;EACAA;AACD,CALD,EAAYA,UAAU,KAAVA,UAAU,GAKrB,EALqB,CAAtB;AA4PA,SAASC,YAAT,CACEC,KADF,EAC4B;EAE1B,OAAOA,KAAK,CAAC1F,KAAN,KAAgB,IAAvB;AACD;AAGD;;AACM,SAAU2F,yBAAV,CACJC,MADI,EAEJC,UAFI,EAGJC,MAHI,EAGmC;EAAA,IADvCD,UACuC;IADvCA,UACuC,GADhB,EACgB;EAAA;EAAA,IAAvCC,MAAuC;IAAvCA,MAAuC,GAAjB,IAAIC,GAAJ,EAAiB;EAAA;EAEvC,OAAOH,MAAM,CAAC9F,GAAP,CAAW,CAAC4F,KAAD,EAAQ1F,KAAR,KAAiB;IACjC,IAAIgG,QAAQ,GAAG,CAAC,GAAGH,UAAJ,EAAgB7F,KAAhB,CAAf;IACA,IAAIiG,EAAE,GAAG,OAAOP,KAAK,CAACO,EAAb,KAAoB,QAApB,GAA+BP,KAAK,CAACO,EAArC,GAA0CD,QAAQ,CAACE,IAAT,CAAc,GAAd,CAAnD;IACAC,SAAS,CACPT,KAAK,CAAC1F,KAAN,KAAgB,IAAhB,IAAwB,CAAC0F,KAAK,CAACU,QADxB,EAAT;IAIAD,SAAS,CACP,CAACL,MAAM,CAACO,GAAP,CAAWJ,EAAX,CADM,EAEP,wCAAqCA,EAArC,mBACE,wDAHK,CAAT;IAKAH,MAAM,CAACQ,GAAP,CAAWL,EAAX;IAEA,IAAIR,YAAY,CAACC,KAAD,CAAhB,EAAyB;MACvB,IAAIa,UAAU,gBAAsCb,KAAtC;QAA6CO;OAA3D;MACA,OAAOM,UAAP;IACD,CAHD,MAGO;MACL,IAAIC,iBAAiB,gBAChBd,KADgB;QAEnBO,EAFmB;QAGnBG,QAAQ,EAAEV,KAAK,CAACU,QAAN,GACNT,yBAAyB,CAACD,KAAK,CAACU,QAAP,EAAiBJ,QAAjB,EAA2BF,MAA3B,CADnB,GAEN3F;OALN;MAOA,OAAOqG,iBAAP;IACD;EACF,CA3BM,CAAP;AA4BD;AAED;;;;AAIG;;AACG,SAAUC,WAAV,CAGJb,MAHI,EAIJc,WAJI,EAKJC,QALI,EAKU;EAAA,IAAdA,QAAc;IAAdA,QAAc,GAAH,GAAG;EAAA;EAEd,IAAI3F,QAAQ,GACV,OAAO0F,WAAP,KAAuB,QAAvB,GAAkC5D,SAAS,CAAC4D,WAAD,CAA3C,GAA2DA,WAD7D;EAGA,IAAIxF,QAAQ,GAAG0F,aAAa,CAAC5F,QAAQ,CAACE,QAAT,IAAqB,GAAtB,EAA2ByF,QAA3B,CAA5B;EAEA,IAAIzF,QAAQ,IAAI,IAAhB,EAAsB;IACpB,OAAO,IAAP;EACD;EAED,IAAI2F,QAAQ,GAAGC,aAAa,CAAClB,MAAD,CAA5B;EACAmB,iBAAiB,CAACF,QAAD,CAAjB;EAEA,IAAIG,OAAO,GAAG,IAAd;EACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBD,OAAO,IAAI,IAAX,IAAmBC,CAAC,GAAGJ,QAAQ,CAACxG,MAAhD,EAAwD,EAAE4G,CAA1D,EAA6D;IAC3DD,OAAO,GAAGE,gBAAgB,CACxBL,QAAQ,CAACI,CAAD,CADgB;IAAA;IAGxB;IACA;IACA;IACA;IACA;IACAE,eAAe,CAACjG,QAAD,CARS,CAA1B;EAUD;EAED,OAAO8F,OAAP;AACD;AAmBD,SAASF,aAAT,CAGElB,MAHF,EAIEiB,QAJF,EAKEO,WALF,EAMEvB,UANF,EAMiB;EAAA,IAFfgB,QAEe;IAFfA,QAEe,GAF4B,EAE5B;EAAA;EAAA,IADfO,WACe;IADfA,WACe,GAD6B,EAC7B;EAAA;EAAA,IAAfvB,UAAe;IAAfA,UAAe,GAAF,EAAE;EAAA;EAEfD,MAAM,CAACyB,OAAP,CAAe,CAAC3B,KAAD,EAAQ1F,KAAR,KAAiB;IAC9B,IAAIsH,IAAI,GAA+B;MACrCC,YAAY,EAAE7B,KAAK,CAACnB,IAAN,IAAc,EADS;MAErCiD,aAAa,EAAE9B,KAAK,CAAC8B,aAAN,KAAwB,IAFF;MAGrCC,aAAa,EAAEzH,KAHsB;MAIrC0F;KAJF;IAOA,IAAI4B,IAAI,CAACC,YAAL,CAAkBG,UAAlB,CAA6B,GAA7B,CAAJ,EAAuC;MACrCvB,SAAS,CACPmB,IAAI,CAACC,YAAL,CAAkBG,UAAlB,CAA6B7B,UAA7B,CADO,EAEP,2BAAwByB,IAAI,CAACC,YAA7B,GACM1B,4CADN,oHAFO,CAAT;MAOAyB,IAAI,CAACC,YAAL,GAAoBD,IAAI,CAACC,YAAL,CAAkB9D,KAAlB,CAAwBoC,UAAU,CAACxF,MAAnC,CAApB;IACD;IAED,IAAIkE,IAAI,GAAGoD,SAAS,CAAC,CAAC9B,UAAD,EAAayB,IAAI,CAACC,YAAlB,CAAD,CAApB;IACA,IAAIK,UAAU,GAAGR,WAAW,CAACS,MAAZ,CAAmBP,IAAnB,CAAjB,CApB8B;IAuB9B;IACA;;IACA,IAAI5B,KAAK,CAACU,QAAN,IAAkBV,KAAK,CAACU,QAAN,CAAe/F,MAAf,GAAwB,CAA9C,EAAiD;MAC/C8F,SAAS;MAAA;MAEP;MACAT,KAAK,CAAC1F,KAAN,KAAgB,IAHT,EAIP,yDACuCuE,gDADvC,SAJO,CAAT;MAQAuC,aAAa,CAACpB,KAAK,CAACU,QAAP,EAAiBS,QAAjB,EAA2Be,UAA3B,EAAuCrD,IAAvC,CAAb;IACD,CAnC6B;IAsC9B;;IACA,IAAImB,KAAK,CAACnB,IAAN,IAAc,IAAd,IAAsB,CAACmB,KAAK,CAAC1F,KAAjC,EAAwC;MACtC;IACD;IAED6G,QAAQ,CAAClF,IAAT,CAAc;MAAE4C,IAAF;MAAQuD,KAAK,EAAEC,YAAY,CAACxD,IAAD,EAAOmB,KAAK,CAAC1F,KAAb,CAA3B;MAAgD4H;KAA9D;GA3CF;EA8CA,OAAOf,QAAP;AACD;AAED,SAASE,iBAAT,CAA2BF,QAA3B,EAAkD;EAChDA,QAAQ,CAACmB,IAAT,CAAc,CAACC,CAAD,EAAIC,CAAJ,KACZD,CAAC,CAACH,KAAF,KAAYI,CAAC,CAACJ,KAAd,GACII,CAAC,CAACJ,KAAF,GAAUG,CAAC,CAACH,KADhB;EAAA,EAEIK,cAAc,CACZF,CAAC,CAACL,UAAF,CAAa9H,GAAb,CAAkBwH,IAAD,IAAUA,IAAI,CAACG,aAAhC,CADY,EAEZS,CAAC,CAACN,UAAF,CAAa9H,GAAb,CAAkBwH,IAAD,IAAUA,IAAI,CAACG,aAAhC,CAFY,CAHpB;AAQD;AAED,MAAMW,OAAO,GAAG,QAAhB;AACA,MAAMC,mBAAmB,GAAG,CAA5B;AACA,MAAMC,eAAe,GAAG,CAAxB;AACA,MAAMC,iBAAiB,GAAG,CAA1B;AACA,MAAMC,kBAAkB,GAAG,EAA3B;AACA,MAAMC,YAAY,GAAG,CAAC,CAAtB;AACA,MAAMC,OAAO,GAAIC,CAAD,IAAeA,CAAC,KAAK,GAArC;AAEA,SAASZ,YAAT,CAAsBxD,IAAtB,EAAoCvE,KAApC,EAA8D;EAC5D,IAAI4I,QAAQ,GAAGrE,IAAI,CAACsE,KAAL,CAAW,GAAX,CAAf;EACA,IAAIC,YAAY,GAAGF,QAAQ,CAACvI,MAA5B;EACA,IAAIuI,QAAQ,CAACG,IAAT,CAAcL,OAAd,CAAJ,EAA4B;IAC1BI,YAAY,IAAIL,YAAhB;EACD;EAED,IAAIzI,KAAJ,EAAW;IACT8I,YAAY,IAAIR,eAAhB;EACD;EAED,OAAOM,QAAQ,CACZI,MADI,CACIL,CAAD,IAAO,CAACD,OAAO,CAACC,CAAD,CADlB,CAEJM,OAFI,CAGH,CAACnB,KAAD,EAAQoB,OAAR,KACEpB,KAAK,IACJM,OAAO,CAACe,IAAR,CAAaD,OAAb,IACGb,mBADH,GAEGa,OAAO,KAAK,EAAZ,GACAX,iBADA,GAEAC,kBALC,CAJJ,EAUHM,YAVG,CAAP;AAYD;AAED,SAASX,cAAT,CAAwBF,CAAxB,EAAqCC,CAArC,EAAgD;EAC9C,IAAIkB,QAAQ,GACVnB,CAAC,CAAC5H,MAAF,KAAa6H,CAAC,CAAC7H,MAAf,IAAyB4H,CAAC,CAACxE,KAAF,CAAQ,CAAR,EAAW,CAAC,CAAZ,CAAe4F,MAAf,CAAqB,CAAC5I,CAAD,EAAIwG,CAAJ,KAAUxG,CAAC,KAAKyH,CAAC,CAACjB,CAAD,CAAtC,CAD3B;EAGA,OAAOmC,QAAQ;EAAA;EAEX;EACA;EACA;EACAnB,CAAC,CAACA,CAAC,CAAC5H,MAAF,GAAW,CAAZ,CAAD,GAAkB6H,CAAC,CAACA,CAAC,CAAC7H,MAAF,GAAW,CAAZ,CALR;EAAA;EAOX;EACA,CARJ;AASD;AAED,SAAS6G,gBAAT,CAIEoC,MAJF,EAKEpI,QALF,EAKkB;EAEhB,IAAI;IAAE0G;EAAF,IAAiB0B,MAArB;EAEA,IAAIC,aAAa,GAAG,EAApB;EACA,IAAIC,eAAe,GAAG,GAAtB;EACA,IAAIxC,OAAO,GAAoD,EAA/D;EACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGW,UAAU,CAACvH,MAA/B,EAAuC,EAAE4G,CAAzC,EAA4C;IAC1C,IAAIK,IAAI,GAAGM,UAAU,CAACX,CAAD,CAArB;IACA,IAAIwC,GAAG,GAAGxC,CAAC,KAAKW,UAAU,CAACvH,MAAX,GAAoB,CAApC;IACA,IAAIqJ,iBAAiB,GACnBF,eAAe,KAAK,GAApB,GACItI,QADJ,GAEIA,QAAQ,CAACuC,KAAT,CAAe+F,eAAe,CAACnJ,MAA/B,KAA0C,GAHhD;IAIA,IAAIsJ,KAAK,GAAGC,SAAS,CACnB;MAAErF,IAAI,EAAE+C,IAAI,CAACC,YAAb;MAA2BC,aAAa,EAAEF,IAAI,CAACE,aAA/C;MAA8DiC;KAD3C,EAEnBC,iBAFmB,CAArB;IAKA,IAAI,CAACC,KAAL,EAAY,OAAO,IAAP;IAEZE,MAAM,CAACzE,MAAP,CAAcmE,aAAd,EAA6BI,KAAK,CAACG,MAAnC;IAEA,IAAIpE,KAAK,GAAG4B,IAAI,CAAC5B,KAAjB;IAEAsB,OAAO,CAACrF,IAAR,CAAa;MACX;MACAmI,MAAM,EAAEP,aAFG;MAGXrI,QAAQ,EAAEyG,SAAS,CAAC,CAAC6B,eAAD,EAAkBG,KAAK,CAACzI,QAAxB,CAAD,CAHR;MAIX6I,YAAY,EAAEC,iBAAiB,CAC7BrC,SAAS,CAAC,CAAC6B,eAAD,EAAkBG,KAAK,CAACI,YAAxB,CAAD,CADoB,CAJpB;MAOXrE;KAPF;IAUA,IAAIiE,KAAK,CAACI,YAAN,KAAuB,GAA3B,EAAgC;MAC9BP,eAAe,GAAG7B,SAAS,CAAC,CAAC6B,eAAD,EAAkBG,KAAK,CAACI,YAAxB,CAAD,CAA3B;IACD;EACF;EAED,OAAO/C,OAAP;AACD;AAED;;;;AAIG;;SACaiD,aACd1F,MACAuF,QAEa;EAAA,IAFbA,MAEa;IAFbA,MAEa,GAAT,EAAS;EAAA;EAEb,OAAOvF,IAAI,CACRxC,OADI,CACI,SADJ,EACe,CAACmI,CAAD,EAAInJ,GAAJ,KAA4B;IAC9CoF,SAAS,CAAC2D,MAAM,CAAC/I,GAAD,CAAN,IAAe,IAAhB,EAAmCA,mBAAnC,GAAT;IACA,OAAO+I,MAAM,CAAC/I,GAAD,CAAb;EACD,CAJI,CAKJgB,QALI,CAKI,SALJ,EAKe,CAACmI,CAAD,EAAIC,MAAJ,EAAYC,EAAZ,EAAgBC,GAAhB,KAAuB;IACzC,MAAMC,IAAI,GAAG,GAAb;IAEA,IAAIR,MAAM,CAACQ,IAAD,CAAN,IAAgB,IAApB,EAA0B;MACxB;MACA;MACA,OAAOD,GAAG,KAAK,IAAR,GAAe,GAAf,GAAqB,EAA5B;IACD,CAPwC;;IAUzC,YAAUF,MAAV,GAAmBL,MAAM,CAACQ,IAAD,CAAzB;EACD,CAhBI,CAAP;AAiBD;AAiDD;;;;;AAKG;;AACa,mBAIdC,OAJc,EAKdrJ,QALc,EAKE;EAEhB,IAAI,OAAOqJ,OAAP,KAAmB,QAAvB,EAAiC;IAC/BA,OAAO,GAAG;MAAEhG,IAAI,EAAEgG,OAAR;MAAiB/C,aAAa,EAAE,KAAhC;MAAuCiC,GAAG,EAAE;KAAtD;EACD;EAED,IAAI,CAACe,OAAD,EAAUC,UAAV,CAAwBC,cAAW,CACrCH,OAAO,CAAChG,IAD6B,EAErCgG,OAAO,CAAC/C,aAF6B,EAGrC+C,OAAO,CAACd,GAH6B,CAAvC;EAMA,IAAIE,KAAK,GAAGzI,QAAQ,CAACyI,KAAT,CAAea,OAAf,CAAZ;EACA,IAAI,CAACb,KAAL,EAAY,OAAO,IAAP;EAEZ,IAAIH,eAAe,GAAGG,KAAK,CAAC,CAAD,CAA3B;EACA,IAAII,YAAY,GAAGP,eAAe,CAACzH,OAAhB,CAAwB,SAAxB,EAAmC,IAAnC,CAAnB;EACA,IAAI4I,aAAa,GAAGhB,KAAK,CAAClG,KAAN,CAAY,CAAZ,CAApB;EACA,IAAIqG,MAAM,GAAWW,UAAU,CAACxB,MAAX,CACnB,CAAC2B,IAAD,EAAOC,SAAP,EAAkB7K,KAAlB,KAA2B;IACzB;IACA;IACA,IAAI6K,SAAS,KAAK,GAAlB,EAAuB;MACrB,IAAIC,UAAU,GAAGH,aAAa,CAAC3K,KAAD,CAAb,IAAwB,EAAzC;MACA+J,YAAY,GAAGP,eAAe,CAC3B/F,KADY,CACN,CADM,EACH+F,eAAe,CAACnJ,MAAhB,GAAyByK,UAAU,CAACzK,MADjC,CAEZ0B,QAFY,CAEJ,SAFI,EAEO,IAFP,CAAf;IAGD;IAED6I,IAAI,CAACC,SAAD,CAAJ,GAAkBE,wBAAwB,CACxCJ,aAAa,CAAC3K,KAAD,CAAb,IAAwB,EADgB,EAExC6K,SAFwC,CAA1C;IAIA,OAAOD,IAAP;GAfiB,EAiBnB,EAjBmB,CAArB;EAoBA,OAAO;IACLd,MADK;IAEL5I,QAAQ,EAAEsI,eAFL;IAGLO,YAHK;IAILQ;GAJF;AAMD;AAED,SAASG,WAAT,CACEnG,IADF,EAEEiD,aAFF,EAGEiC,GAHF,EAGY;EAAA,IADVjC,aACU;IADVA,aACU,GADM,KACN;EAAA;EAAA,IAAViC,GAAU;IAAVA,GAAU,GAAJ,IAAI;EAAA;EAEVtI,OAAO,CACLoD,IAAI,KAAK,GAAT,IAAgB,CAACA,IAAI,CAACyG,QAAL,CAAc,GAAd,CAAjB,IAAuCzG,IAAI,CAACyG,QAAL,CAAc,IAAd,CADlC,EAEL,eAAezG,OAAf,iDACMA,IAAI,CAACxC,OAAL,CAAa,KAAb,EAAoB,IAApB,CADN,wJAGsCwC,IAAI,CAACxC,OAAL,CAAa,KAAb,EAAoB,IAApB,CAHtC,SAFK,CAAP;EAQA,IAAI0I,UAAU,GAAa,EAA3B;EACA,IAAIQ,YAAY,GACd,MACA1G,IAAI,CACDxC,OADH,CACW,SADX,EACsB,EADtB,CAC0B;EAAA,CACvBA,OAFH,CAEW,MAFX,EAEmB,GAFnB,CAEwB;EAAA,CACrBA,OAHH,CAGW,qBAHX,EAGkC,MAHlC,CAG0C;EAAA,CACvCA,OAJH,CAIW,SAJX,EAIsB,CAACmI,CAAD,EAAYW,SAAZ,KAAiC;IACnDJ,UAAU,CAAC9I,IAAX,CAAgBkJ,SAAhB;IACA,OAAO,WAAP;EACD,CAPH,CAFF;EAWA,IAAItG,IAAI,CAACyG,QAAL,CAAc,GAAd,CAAJ,EAAwB;IACtBP,UAAU,CAAC9I,IAAX,CAAgB,GAAhB;IACAsJ,YAAY,IACV1G,IAAI,KAAK,GAAT,IAAgBA,IAAI,KAAK,IAAzB,GACI,OADJ;IAAA,EAEI,mBAHN,CAFsB;GAAxB,MAMO,IAAIkF,GAAJ,EAAS;IACd;IACAwB,YAAY,IAAI,OAAhB;GAFK,MAGA,IAAI1G,IAAI,KAAK,EAAT,IAAeA,IAAI,KAAK,GAA5B,EAAiC;IACtC;IACA;IACA;IACA;IACA;IACA;IACA;IACA0G,YAAY,IAAI,eAAhB;EACD,CATM,MASA;EAIP,IAAIT,OAAO,GAAG,IAAIU,MAAJ,CAAWD,YAAX,EAAyBzD,aAAa,GAAGrH,SAAH,GAAe,GAArD,CAAd;EAEA,OAAO,CAACqK,OAAD,EAAUC,UAAV,CAAP;AACD;AAED,SAAStD,eAAT,CAAyBgE,KAAzB,EAAsC;EACpC,IAAI;IACF,OAAOC,SAAS,CAACD,KAAD,CAAhB;GADF,CAEE,OAAOhG,KAAP,EAAc;IACdhE,OAAO,CACL,KADK,EAEL,oBAAiBgK,KAAjB,GAEehG,uIAFf,QAFK,CAAP;IAOA,OAAOgG,KAAP;EACD;AACF;AAED,SAASJ,wBAAT,CAAkCI,KAAlC,EAAiDN,SAAjD,EAAkE;EAChE,IAAI;IACF,OAAOQ,kBAAkB,CAACF,KAAD,CAAzB;GADF,CAEE,OAAOhG,KAAP,EAAc;IACdhE,OAAO,CACL,KADK,EAEL,gCAAgC0J,YAAhC,0DACkBM,KADlB,8FAEqChG,KAFrC,QAFK,CAAP;IAOA,OAAOgG,KAAP;EACD;AACF;AAED;;AAEG;;AACa,uBACdjK,QADc,EAEdyF,QAFc,EAEE;EAEhB,IAAIA,QAAQ,KAAK,GAAjB,EAAsB,OAAOzF,QAAP;EAEtB,IAAI,CAACA,QAAQ,CAACoK,WAAT,EAAuB5D,WAAvB,CAAkCf,QAAQ,CAAC2E,WAAT,EAAlC,CAAL,EAAgE;IAC9D,OAAO,IAAP;EACD,CANe;EAShB;;EACA,IAAIC,UAAU,GAAG5E,QAAQ,CAACqE,QAAT,CAAkB,GAAlB,IACbrE,QAAQ,CAACtG,MAAT,GAAkB,CADL,GAEbsG,QAAQ,CAACtG,MAFb;EAGA,IAAImL,QAAQ,GAAGtK,QAAQ,CAACE,MAAT,CAAgBmK,UAAhB,CAAf;EACA,IAAIC,QAAQ,IAAIA,QAAQ,KAAK,GAA7B,EAAkC;IAChC;IACA,OAAO,IAAP;EACD;EAED,OAAOtK,QAAQ,CAACuC,KAAT,CAAe8H,UAAf,KAA8B,GAArC;AACD;AAUe,mBAAUJ,KAAV,EAAsBvH,OAAtB,EAAsC;EACpD,IAAIuH,KAAK,KAAK,KAAV,IAAmBA,KAAK,KAAK,IAA7B,IAAqC,OAAOA,KAAP,KAAiB,WAA1D,EAAuE;IACrE,MAAM,IAAIpH,KAAJ,CAAUH,OAAV,CAAN;EACD;AACF;AAED;;AAEG;;AACa,iBAAQD,IAAR,EAAmBC,OAAnB,EAAkC;EAChD,IAAI,CAACD,IAAL,EAAW;IACT;IACA,IAAI,OAAOE,OAAP,KAAmB,WAAvB,EAAoCA,OAAO,CAACC,IAAR,CAAaF,OAAb;IAEpC,IAAI;MACF;MACA;MACA;MACA;MACA;MACA,MAAM,IAAIG,KAAJ,CAAUH,OAAV,CAAN,CANE;IAQH,CARD,CAQE,OAAOI,CAAP,EAAU;EACb;AACF;AAED;;;;AAIG;;SACayH,YAAY3K,IAAQ4K,cAAkB;EAAA,IAAlBA,YAAkB;IAAlBA,YAAkB,GAAH,GAAG;EAAA;EACpD,IAAI;IACFxK,QAAQ,EAAEyK,UADR;IAEFnJ,MAAM,GAAG,EAFP;IAGFC,IAAI,GAAG;GACL,UAAO3B,EAAP,KAAc,QAAd,GAAyBgC,SAAS,CAAChC,EAAD,CAAlC,GAAyCA,EAJ7C;EAMA,IAAII,QAAQ,GAAGyK,UAAU,GACrBA,UAAU,CAACjE,UAAX,CAAsB,GAAtB,IACEiE,UADF,GAEEC,eAAe,CAACD,UAAD,EAAaD,YAAb,CAHI,GAIrBA,YAJJ;EAMA,OAAO;IACLxK,QADK;IAELsB,MAAM,EAAEqJ,eAAe,CAACrJ,MAAD,CAFlB;IAGLC,IAAI,EAAEqJ,aAAa,CAACrJ,IAAD;GAHrB;AAKD;AAED,SAASmJ,eAAT,CAAyBrE,YAAzB,EAA+CmE,YAA/C,EAAmE;EACjE,IAAI9C,QAAQ,GAAG8C,YAAY,CAAC3J,OAAb,CAAqB,MAArB,EAA6B,EAA7B,EAAiC8G,KAAjC,CAAuC,GAAvC,CAAf;EACA,IAAIkD,gBAAgB,GAAGxE,YAAY,CAACsB,KAAb,CAAmB,GAAnB,CAAvB;EAEAkD,gBAAgB,CAAC1E,OAAjB,CAA0B6B,OAAD,IAAY;IACnC,IAAIA,OAAO,KAAK,IAAhB,EAAsB;MACpB;MACA,IAAIN,QAAQ,CAACvI,MAAT,GAAkB,CAAtB,EAAyBuI,QAAQ,CAACoD,GAAT;IAC1B,CAHD,MAGO,IAAI9C,OAAO,KAAK,GAAhB,EAAqB;MAC1BN,QAAQ,CAACjH,IAAT,CAAcuH,OAAd;IACD;GANH;EASA,OAAON,QAAQ,CAACvI,MAAT,GAAkB,CAAlB,GAAsBuI,QAAQ,CAAC1C,IAAT,CAAc,GAAd,CAAtB,GAA2C,GAAlD;AACD;AAED,SAAS+F,mBAAT,CACEC,IADF,EAEEC,KAFF,EAGEC,IAHF,EAIE7H,IAJF,EAIqB;EAEnB,OACE,oBAAqB2H,OAArB,GACQC,wDADR,GAC0B9K,kBAAI,CAACC,SAAL,CACxBiD,IADwB,CAD1B,qDAIQ6H,IAJR,GADF;AAQD;AAED;;;;;;;;;;;;;;;;;;;;;;AAsBG;;AACG,SAAUC,0BAAV,CAEJrF,OAFI,EAEQ;EACZ,OAAOA,OAAO,CAACgC,MAAR,CACL,CAACW,KAAD,EAAQ3J,KAAR,KACEA,KAAK,KAAK,CAAV,IAAgB2J,KAAK,CAACjE,KAAN,CAAYnB,IAAZ,IAAoBoF,KAAK,CAACjE,KAAN,CAAYnB,IAAZ,CAAiBlE,MAAjB,GAA0B,CAF3D,CAAP;AAID;AAED;;AAEG;;AACG,SAAUiM,SAAV,CACJC,KADI,EAEJC,cAFI,EAGJC,gBAHI,EAIJC,cAJI,EAIkB;EAAA,IAAtBA,cAAsB;IAAtBA,cAAsB,GAAL,KAAK;EAAA;EAEtB,IAAI5L,EAAJ;EACA,IAAI,OAAOyL,KAAP,KAAiB,QAArB,EAA+B;IAC7BzL,EAAE,GAAGgC,SAAS,CAACyJ,KAAD,CAAd;EACD,CAFD,MAEO;IACLzL,EAAE,gBAAQyL,KAAR,CAAF;IAEApG,SAAS,CACP,CAACrF,EAAE,CAACI,QAAJ,IAAgB,CAACJ,EAAE,CAACI,QAAH,CAAYyL,QAAZ,CAAqB,GAArB,CADV,EAEPV,mBAAmB,CAAC,GAAD,EAAM,UAAN,EAAkB,QAAlB,EAA4BnL,EAA5B,CAFZ,CAAT;IAIAqF,SAAS,CACP,CAACrF,EAAE,CAACI,QAAJ,IAAgB,CAACJ,EAAE,CAACI,QAAH,CAAYyL,QAAZ,CAAqB,GAArB,CADV,EAEPV,mBAAmB,CAAC,GAAD,EAAM,UAAN,EAAkB,MAAlB,EAA0BnL,EAA1B,CAFZ,CAAT;IAIAqF,SAAS,CACP,CAACrF,EAAE,CAAC0B,MAAJ,IAAc,CAAC1B,EAAE,CAAC0B,MAAH,CAAUmK,QAAV,CAAmB,GAAnB,CADR,EAEPV,mBAAmB,CAAC,GAAD,EAAM,QAAN,EAAgB,MAAhB,EAAwBnL,EAAxB,CAFZ,CAAT;EAID;EAED,IAAI8L,WAAW,GAAGL,KAAK,KAAK,EAAV,IAAgBzL,EAAE,CAACI,QAAH,KAAgB,EAAlD;EACA,IAAIyK,UAAU,GAAGiB,WAAW,GAAG,GAAH,GAAS9L,EAAE,CAACI,QAAxC;EAEA,IAAI2L,IAAJ,CAzBsB;EA4BtB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACA,IAAIH,cAAc,IAAIf,UAAU,IAAI,IAApC,EAA0C;IACxCkB,IAAI,GAAGJ,gBAAP;EACD,CAFD,MAEO;IACL,IAAIK,kBAAkB,GAAGN,cAAc,CAACnM,MAAf,GAAwB,CAAjD;IAEA,IAAIsL,UAAU,CAACjE,UAAX,CAAsB,IAAtB,CAAJ,EAAiC;MAC/B,IAAIqF,UAAU,GAAGpB,UAAU,CAAC9C,KAAX,CAAiB,GAAjB,CAAjB,CAD+B;MAI/B;MACA;;MACA,OAAOkE,UAAU,CAAC,CAAD,CAAV,KAAkB,IAAzB,EAA+B;QAC7BA,UAAU,CAACC,KAAX;QACAF,kBAAkB,IAAI,CAAtB;MACD;MAEDhM,EAAE,CAACI,QAAH,GAAc6L,UAAU,CAAC7G,IAAX,CAAgB,GAAhB,CAAd;IACD,CAfI;IAkBL;;IACA2G,IAAI,GAAGC,kBAAkB,IAAI,CAAtB,GAA0BN,cAAc,CAACM,kBAAD,CAAxC,GAA+D,GAAtE;EACD;EAED,IAAIvI,IAAI,GAAGkH,WAAW,CAAC3K,EAAD,EAAK+L,IAAL,CAAtB,CA5DsB;;EA+DtB,IAAII,wBAAwB,GAC1BtB,UAAU,IAAIA,UAAU,KAAK,GAA7B,IAAoCA,UAAU,CAACX,QAAX,CAAoB,GAApB,CADtC,CA/DsB;;EAkEtB,IAAIkC,uBAAuB,GACzB,CAACN,WAAW,IAAIjB,UAAU,KAAK,GAA/B,KAAuCc,gBAAgB,CAACzB,QAAjB,CAA0B,GAA1B,CADzC;EAEA,IACE,CAACzG,IAAI,CAACrD,QAAL,CAAc8J,QAAd,CAAuB,GAAvB,CAAD,KACCiC,wBAAwB,IAAIC,uBAD7B,CADF,EAGE;IACA3I,IAAI,CAACrD,QAAL,IAAiB,GAAjB;EACD;EAED,OAAOqD,IAAP;AACD;AAED;;AAEG;;AACG,SAAU4I,aAAV,CAAwBrM,EAAxB,EAA8B;EAClC;EACA,OAAOA,EAAE,KAAK,EAAP,IAAcA,EAAW,CAACI,QAAZ,KAAyB,EAAvC,GACH,GADG,GAEH,OAAOJ,EAAP,KAAc,QAAd,GACAgC,SAAS,CAAChC,EAAD,CAAT,CAAcI,QADd,GAEAJ,EAAE,CAACI,QAJP;AAKD;AAED;;AAEG;;MACUyG,SAAS,GAAIyF,KAAD,IACvBA,KAAK,CAAClH,IAAN,CAAW,GAAX,EAAgBnE,OAAhB,CAAwB,QAAxB,EAAkC,GAAlC;AAEF;;AAEG;;MACUiI,iBAAiB,GAAI9I,QAAD,IAC/BA,QAAQ,CAACa,OAAT,CAAiB,MAAjB,EAAyB,EAAzB,CAA6BA,QAA7B,CAAqC,MAArC,EAA6C,GAA7C;AAEF;;AAEG;;AACI,MAAM8J,eAAe,GAAIrJ,MAAD,IAC7B,CAACA,MAAD,IAAWA,MAAM,KAAK,GAAtB,GACI,EADJ,GAEIA,MAAM,CAACkF,UAAP,CAAkB,GAAlB,CACAlF,SADA,GAEA,MAAMA,MALL;AAOP;;AAEG;;AACI,MAAMsJ,aAAa,GAAIrJ,IAAD,IAC3B,CAACA,IAAD,IAASA,IAAI,KAAK,GAAlB,GAAwB,EAAxB,GAA6BA,IAAI,CAACiF,UAAL,CAAgB,GAAhB,CAAuBjF,OAAvB,GAA8B,MAAMA,IAD5D;AAQP;;;AAGG;;AACI,MAAM4K,IAAI,GAAiB,SAArBA,IAAqB,CAACC,IAAD,EAAOC,IAAP,EAAoB;EAAA,IAAbA,IAAa;IAAbA,IAAa,GAAN,EAAM;EAAA;EACpD,IAAIC,YAAY,GAAG,OAAOD,IAAP,KAAgB,QAAhB,GAA2B;IAAEE,MAAM,EAAEF;EAAV,CAA3B,GAA8CA,IAAjE;EAEA,IAAIG,OAAO,GAAG,IAAIC,OAAJ,CAAYH,YAAY,CAACE,OAAzB,CAAd;EACA,IAAI,CAACA,OAAO,CAACrH,GAAR,CAAY,cAAZ,CAAL,EAAkC;IAChCqH,OAAO,CAACE,GAAR,CAAY,cAAZ,EAA4B,iCAA5B;EACD;EAED,OAAO,IAAIC,QAAJ,CAAaxM,IAAI,CAACC,SAAL,CAAegM,IAAf,CAAb,eACFE,YADE;IAELE;GAFF;AAID;AAQK,MAAOI,oBAAP,SAAoC/J,KAApC,CAAyC;MAElCgK,aAAY;EAQvBC,YAAYV,IAAZ,EAAyC;IAPjC,mBAAoC,IAAIvH,GAAJ,EAApC;IAIA,IAAUkI,WAAV,GAA0C9N,SAA1C;IAINgG,SAAS,CACPmH,IAAI,IAAI,OAAOA,IAAP,KAAgB,QAAxB,IAAoC,CAACY,KAAK,CAACC,OAAN,CAAcb,IAAd,CAD9B,EAEP,oCAFO,CAAT,CADuC;IAOvC;;IACA,IAAIc,MAAJ;IACA,KAAKC,YAAL,GAAoB,IAAIC,OAAJ,CAAY,CAACpE,CAAD,EAAIqE,CAAJ,KAAWH,MAAM,GAAGG,CAAhC,CAApB;IACA,KAAKC,UAAL,GAAkB,IAAIC,eAAJ,EAAlB;IACA,IAAIC,OAAO,GAAG,MACZN,MAAM,CAAC,IAAIN,oBAAJ,CAAyB,uBAAzB,CAAD,CADR;IAEA,KAAKa,mBAAL,GAA2B,MACzB,KAAKH,UAAL,CAAgBI,MAAhB,CAAuBrJ,mBAAvB,CAA2C,OAA3C,EAAoDmJ,OAApD,CADF;IAEA,IAAKF,WAAL,CAAgBI,MAAhB,CAAuBtJ,gBAAvB,CAAwC,OAAxC,EAAiDoJ,OAAjD;IAEA,IAAKpB,KAAL,GAAYzD,MAAM,CAAChK,OAAP,CAAeyN,IAAf,CAAqBrE,OAArB,CACV,CAAC4F,GAAD;MAAA,IAAM,CAAC9N,GAAD,EAAMoK,KAAN,CAAN;MAAA,OACEtB,MAAM,CAACzE,MAAP,CAAcyJ,GAAd,EAAmB;QACjB,CAAC9N,GAAD,GAAO,KAAK+N,YAAL,CAAkB/N,GAAlB,EAAuBoK,KAAvB;MADU,CAAnB,CADF;KADU,EAKV,EALU,CAAZ;EAOD;EAEO2D,YAAY,CAClB/N,GADkB,EAElBoK,KAFkB,EAEe;IAEjC,IAAI,EAAEA,KAAK,YAAYmD,OAAnB,CAAJ,EAAiC;MAC/B,OAAOnD,KAAP;IACD;IAED,KAAK4D,WAAL,CAAiBzI,GAAjB,CAAqBvF,GAArB,EANiC;IASjC;;IACA,IAAIiO,OAAO,GAAmBV,OAAO,CAACW,IAAR,CAAa,CAAC9D,KAAD,EAAQ,KAAKkD,YAAb,CAAb,EAAyCa,IAAzC,CAC3B5B,IAAD,IAAU,KAAK6B,QAAL,CAAcH,OAAd,EAAuBjO,GAAvB,EAA4B,IAA5B,EAAkCuM,IAAlC,CADkB,EAE3BnI,KAAD,IAAW,KAAKgK,QAAL,CAAcH,OAAd,EAAuBjO,GAAvB,EAA4BoE,KAA5B,CAFiB,CAA9B,CAViC;IAgBjC;;IACA6J,OAAO,CAACI,KAAR,CAAc,MAAO,EAArB;IAEAvF,MAAM,CAACwF,cAAP,CAAsBL,OAAtB,EAA+B,UAA/B,EAA2C;MAAEM,GAAG,EAAE,MAAM;KAAxD;IACA,OAAON,OAAP;EACD;EAEOG,QAAQ,CACdH,OADc,EAEdjO,GAFc,EAGdoE,KAHc,EAIdmI,IAJc,EAIA;IAEd,IACE,KAAKkB,UAAL,CAAgBI,MAAhB,CAAuBW,OAAvB,IACApK,KAAK,YAAY2I,oBAFnB,EAGE;MACA,KAAKa,mBAAL;MACA9E,MAAM,CAACwF,cAAP,CAAsBL,OAAtB,EAA+B,QAA/B,EAAyC;QAAEM,GAAG,EAAE,MAAMnK;OAAtD;MACA,OAAOmJ,OAAO,CAACF,MAAR,CAAejJ,KAAf,CAAP;IACD;IAED,KAAK4J,WAAL,CAAiBS,MAAjB,CAAwBzO,GAAxB;IAEA,IAAI,KAAK0O,IAAT,EAAe;MACb;MACA,KAAKd,mBAAL;IACD;IAED,MAAMV,UAAU,GAAG,KAAKA,UAAxB;IACA,IAAI9I,KAAJ,EAAW;MACT0E,MAAM,CAACwF,cAAP,CAAsBL,OAAtB,EAA+B,QAA/B,EAAyC;QAAEM,GAAG,EAAE,MAAMnK;OAAtD;MACA8I,UAAU,IAAIA,UAAU,CAAC,KAAD,CAAxB;MACA,OAAOK,OAAO,CAACF,MAAR,CAAejJ,KAAf,CAAP;IACD;IAED0E,MAAM,CAACwF,cAAP,CAAsBL,OAAtB,EAA+B,OAA/B,EAAwC;MAAEM,GAAG,EAAE,MAAMhC;KAArD;IACAW,UAAU,IAAIA,UAAU,CAAC,KAAD,CAAxB;IACA,OAAOX,IAAP;EACD;EAEDoC,SAAS,CAACtN,EAAD,EAA+B;IACtC,IAAK6L,WAAL,GAAkB7L,EAAlB;EACD;EAEDuN,MAAM;IACJ,IAAKnB,WAAL,CAAgBoB,KAAhB;IACA,KAAKb,WAAL,CAAiB1H,OAAjB,CAAyB,CAACwI,CAAD,EAAIC,CAAJ,KAAU,KAAKf,WAAL,CAAiBS,MAAjB,CAAwBM,CAAxB,CAAnC;IACA,IAAI7B,UAAU,GAAG,KAAKA,UAAtB;IACAA,UAAU,IAAIA,UAAU,CAAC,IAAD,CAAxB;EACD;EAEgB,MAAX8B,WAAW,CAACnB,MAAD,EAAoB;IACnC,IAAIW,OAAO,GAAG,KAAd;IACA,IAAI,CAAC,IAAKE,KAAV,EAAgB;MACd,IAAIf,OAAO,GAAG,MAAM,KAAKiB,MAAL,EAApB;MACAf,MAAM,CAACtJ,gBAAP,CAAwB,OAAxB,EAAiCoJ,OAAjC;MACAa,OAAO,GAAG,MAAM,IAAIjB,OAAJ,CAAa0B,OAAD,IAAY;QACtC,IAAKN,UAAL,CAAgBH,OAAD,IAAY;UACzBX,MAAM,CAACrJ,mBAAP,CAA2B,OAA3B,EAAoCmJ,OAApC;UACA,IAAIa,OAAO,IAAI,IAAKE,KAApB,EAA0B;YACxBO,OAAO,CAACT,OAAD,CAAP;UACD;SAJH;MAMD,CAPe,CAAhB;IAQD;IACD,OAAOA,OAAP;EACD;EAEO,IAAJE,IAAI;IACN,OAAO,IAAKV,YAAL,CAAiBkB,IAAjB,KAA0B,CAAjC;EACD;EAEgB,IAAbC,aAAa;IACf/J,SAAS,CACP,IAAKmH,KAAL,KAAc,IAAd,IAAsB,IAAKmC,KADpB,EAEP,2DAFO,CAAT;IAKA,OAAO5F,MAAM,CAAChK,OAAP,CAAe,KAAKyN,IAApB,CAA0BrE,OAA1B,CACL,CAAC4F,GAAD;MAAA,IAAM,CAAC9N,GAAD,EAAMoK,KAAN,CAAN;MAAA,OACEtB,MAAM,CAACzE,MAAP,CAAcyJ,GAAd,EAAmB;QACjB,CAAC9N,GAAD,GAAOoP,oBAAoB,CAAChF,KAAD;MADV,CAAnB,CADF;KADK,EAKL,EALK,CAAP;EAOD;AA1IsB;AA6IzB,SAASiF,gBAAT,CAA0BjF,KAA1B,EAAoC;EAClC,OACEA,KAAK,YAAYmD,OAAjB,IAA6BnD,KAAwB,CAACkF,QAAzB,KAAsC,IADrE;AAGD;AAED,SAASF,oBAAT,CAA8BhF,KAA9B,EAAwC;EACtC,IAAI,CAACiF,gBAAgB,CAACjF,KAAD,CAArB,EAA8B;IAC5B,OAAOA,KAAP;EACD;EAED,IAAIA,KAAK,CAACmF,MAAV,EAAkB;IAChB,MAAMnF,KAAK,CAACmF,MAAZ;EACD;EACD,OAAOnF,KAAK,CAACoF,KAAb;AACD;AAEK,SAAUC,KAAV,CAAgBlD,IAAhB,EAA6C;EACjD,OAAO,IAAIS,YAAJ,CAAiBT,IAAjB,CAAP;AACD;AAOD;;;AAGG;;AACI,MAAMmD,QAAQ,GAAqB,SAA7BA,QAA6B,CAACnN,GAAD,EAAMiK,IAAN,EAAoB;EAAA,IAAdA,IAAc;IAAdA,IAAc,GAAP,GAAO;EAAA;EAC5D,IAAIC,YAAY,GAAGD,IAAnB;EACA,IAAI,OAAOC,YAAP,KAAwB,QAA5B,EAAsC;IACpCA,YAAY,GAAG;MAAEC,MAAM,EAAED;KAAzB;GADF,MAEO,IAAI,OAAOA,YAAY,CAACC,MAApB,KAA+B,WAAnC,EAAgD;IACrDD,YAAY,CAACC,MAAb,GAAsB,GAAtB;EACD;EAED,IAAIC,OAAO,GAAG,IAAIC,OAAJ,CAAYH,YAAY,CAACE,OAAzB,CAAd;EACAA,OAAO,CAACE,GAAR,CAAY,UAAZ,EAAwBtK,GAAxB;EAEA,OAAO,IAAIuK,QAAJ,CAAa,IAAb,eACFL,YADE;IAELE;GAFF;AAID;AAED;;;AAGG;;MACUgD,cAAa;EAKxB1C,YAAYP,MAAZ,EAA4BkD,UAA5B,EAA4DrD,IAA5D,EAAqE;IACnE,IAAKG,OAAL,GAAcA,MAAd;IACA,KAAKkD,UAAL,GAAkBA,UAAU,IAAI,EAAhC;IACA,IAAKrD,KAAL,GAAYA,IAAZ;EACD;AATuB;AAY1B;;;AAGG;;AACG,SAAUsD,oBAAV,CAA+B5M,CAA/B,EAAqC;EACzC,OAAOA,CAAC,YAAY0M,aAApB;AACD;AClvBM,MAAMG,eAAe,GAA6B;EACvD3Q,KAAK,EAAE,MADgD;EAEvDc,QAAQ,EAAEb,SAF6C;EAGvD2Q,UAAU,EAAE3Q,SAH2C;EAIvD4Q,UAAU,EAAE5Q,SAJ2C;EAKvD6Q,WAAW,EAAE7Q,SAL0C;EAMvD8Q,QAAQ,EAAE9Q;AAN6C;AASlD,MAAM+Q,YAAY,GAA0B;EACjDhR,KAAK,EAAE,MAD0C;EAEjDoN,IAAI,EAAEnN,SAF2C;EAGjD2Q,UAAU,EAAE3Q,SAHqC;EAIjD4Q,UAAU,EAAE5Q,SAJqC;EAKjD6Q,WAAW,EAAE7Q,SALoC;EAMjD8Q,QAAQ,EAAE9Q;AANuC;AASnD,MAAMgR,SAAS,GACb,OAAO7O,MAAP,KAAkB,WAAlB,IACA,OAAOA,MAAM,CAACY,QAAd,KAA2B,WAD3B,IAEA,OAAOZ,MAAM,CAACY,QAAP,CAAgBkO,aAAvB,KAAyC,WAH3C;AAIA,MAAMC,QAAQ,GAAG,CAACF,SAAlB;AAGA;AACA;AACA;;AAEA;;AAEG;;AACG,SAAUG,YAAV,CAAuB/D,IAAvB,EAAuC;EAC3CpH,SAAS,CACPoH,IAAI,CAAC3H,MAAL,CAAYvF,MAAZ,GAAqB,CADd,EAEP,2DAFO,CAAT;EAKA,IAAIkR,UAAU,GAAG5L,yBAAyB,CAAC4H,IAAI,CAAC3H,MAAN,CAA1C,CAN2C;;EAQ3C,IAAI4L,eAAe,GAAwB,IAA3C,CAR2C;;EAU3C,IAAIC,WAAW,GAAG,IAAI1L,GAAJ,EAAlB,CAV2C;;EAY3C,IAAI2L,oBAAoB,GAAkC,IAA1D,CAZ2C;;EAc3C,IAAIC,uBAAuB,GAA2C,IAAtE,CAd2C;;EAgB3C,IAAIC,iBAAiB,GAAqC,IAA1D,CAhB2C;EAkB3C;EACA;EACA;;EACA,IAAIC,qBAAqB,GAAG,KAA5B;EAEA,IAAIC,cAAc,GAAGrL,WAAW,CAC9B8K,UAD8B,EAE9BhE,IAAI,CAAChM,OAAL,CAAaP,QAFiB,EAG9BuM,IAAI,CAAC5G,QAHyB,CAAhC;EAKA,IAAIoL,aAAa,GAAqB,IAAtC;EAEA,IAAID,cAAc,IAAI,IAAtB,EAA4B;IAC1B;IACA;IACA,IAAI;MAAE9K,OAAF;MAAWtB,KAAX;MAAkBP;KAAU6M,qBAAkB,CAACT,UAAD,CAAlD;IACAO,cAAc,GAAG9K,OAAjB;IACA+K,aAAa,GAAG;MAAE,CAACrM,KAAK,CAACO,EAAP,GAAYd;KAA9B;EACD;EAED,IAAI8M,WAAW,GACb,CAACH,cAAc,CAAC/I,IAAf,CAAqBmJ,CAAD,IAAOA,CAAC,CAACxM,KAAF,CAAQyM,MAAnC,CAAD,IAA+C5E,IAAI,CAAC6E,aAAL,IAAsB,IADvE;EAGA,IAAIC,MAAJ;EACA,IAAInS,KAAK,GAAgB;IACvBoS,aAAa,EAAE/E,IAAI,CAAChM,OAAL,CAAajB,MADL;IAEvBU,QAAQ,EAAEuM,IAAI,CAAChM,OAAL,CAAaP,QAFA;IAGvBgG,OAAO,EAAE8K,cAHc;IAIvBG,WAJuB;IAKvBM,UAAU,EAAE1B,eALW;IAMvB2B,qBAAqB,EAAE,IANA;IAOvBC,kBAAkB,EAAE,KAPG;IAQvBC,YAAY,EAAE,MARS;IASvBC,UAAU,EAAGpF,IAAI,CAAC6E,aAAL,IAAsB7E,IAAI,CAAC6E,aAAL,CAAmBO,UAA1C,IAAyD,EAT9C;IAUvBC,UAAU,EAAGrF,IAAI,CAAC6E,aAAL,IAAsB7E,IAAI,CAAC6E,aAAL,CAAmBQ,UAA1C,IAAyD,IAV9C;IAWvBC,MAAM,EAAGtF,IAAI,CAAC6E,aAAL,IAAsB7E,IAAI,CAAC6E,aAAL,CAAmBS,MAA1C,IAAqDd,aAXtC;IAYvBe,QAAQ,EAAE,IAAIC,GAAJ;EAZa,CAAzB,CA1C2C;EA0D3C;;EACA,IAAIC,aAAa,GAAkBC,MAAa,CAAC1S,GAAjD,CA3D2C;EA6D3C;;EACA,IAAI2S,yBAAyB,GAAG,KAAhC,CA9D2C;;EAgE3C,IAAIC,2BAAJ,CAhE2C;EAkE3C;;EACA,IAAIC,2BAA2B,GAAG,KAAlC,CAnE2C;EAqE3C;EACA;EACA;;EACA,IAAIC,sBAAsB,GAAG,KAA7B,CAxE2C;EA0E3C;;EACA,IAAIC,uBAAuB,GAAa,EAAxC,CA3E2C;EA6E3C;;EACA,IAAIC,qBAAqB,GAAa,EAAtC,CA9E2C;;EAgF3C,IAAIC,gBAAgB,GAAG,IAAIT,GAAJ,EAAvB,CAhF2C;;EAkF3C,IAAIU,kBAAkB,GAAG,CAAzB,CAlF2C;EAoF3C;EACA;;EACA,IAAIC,uBAAuB,GAAG,CAAC,CAA/B,CAtF2C;;EAwF3C,IAAIC,cAAc,GAAG,IAAIZ,GAAJ,EAArB,CAxF2C;;EA0F3C,IAAIa,gBAAgB,GAAG,IAAI7N,GAAJ,EAAvB,CA1F2C;;EA4F3C,IAAI8N,gBAAgB,GAAG,IAAId,GAAJ,EAAvB,CA5F2C;EA8F3C;EACA;EACA;;EACA,IAAIe,eAAe,GAAG,IAAIf,GAAJ,EAAtB,CAjG2C;EAoG3C;EACA;;EACA,SAASgB,UAAT,GAAmB;IACjB;IACA;IACAvC,eAAe,GAAGjE,IAAI,CAAChM,OAAL,CAAaY,MAAb,CAChBmC;MAAA,IAAC;QAAEhE,MAAM,EAAEgS,aAAV;QAAyBtR;OAA1B;MAAA,OACEgT,eAAe,CAAC1B,aAAD,EAAgBtR,QAAhB,CADjB;KADgB,CAAlB,CAHiB;;IASjB,IAAI,CAACd,KAAK,CAAC+R,WAAX,EAAwB;MACtB+B,eAAe,CAACf,MAAa,CAAC1S,GAAf,EAAoBL,KAAK,CAACc,QAA1B,CAAf;IACD;IAED,OAAOqR,MAAP;EACD,CApH0C;;EAuH3C,SAAS4B,OAAT,GAAgB;IACd,IAAIzC,eAAJ,EAAqB;MACnBA,eAAe;IAChB;IACDC,WAAW,CAACyC,KAAZ;IACAf,2BAA2B,IAAIA,2BAA2B,CAACvD,KAA5B,EAA/B;IACA1P,KAAK,CAAC4S,QAAN,CAAezL,OAAf,CAAuB,CAAC6C,CAAD,EAAInJ,GAAJ,KAAYoT,aAAa,CAACpT,GAAD,CAAhD;EACD,CA9H0C;;EAiI3C,SAAS2O,SAAT,CAAmBtN,EAAnB,EAAuC;IACrCqP,WAAW,CAACnL,GAAZ,CAAgBlE,EAAhB;IACA,OAAO,MAAMqP,WAAW,CAACjC,MAAZ,CAAmBpN,EAAnB,CAAb;EACD,CApI0C;;EAuI3C,SAASgS,WAAT,CAAqBC,QAArB,EAAmD;IACjDnU,KAAK,GACAA,kBADA,EAEAmU,QAFA,CAAL;IAIA5C,WAAW,CAACpK,OAAZ,CAAqB4G,UAAD,IAAgBA,UAAU,CAAC/N,KAAD,CAA9C;EACD,CA7I0C;EAgJ3C;EACA;EACA;EACA;;EACA,SAASoU,kBAAT,CACEtT,QADF,EAEEqT,QAFF,EAE4E;IAAA;;IAE1E;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIE,cAAc,GAChBrU,KAAK,CAAC0S,UAAN,IAAoB,IAApB,IACA1S,KAAK,CAACqS,UAAN,CAAiBzB,UAAjB,IAA+B,IAD/B,IAEA5Q,KAAK,CAACqS,UAAN,CAAiBrS,KAAjB,KAA2B,SAF3B,IAGA,+BAAK,CAACqS,UAAN,CAAiBxB,UAAjB,2CAA6BlI,KAA7B,CAAmC,GAAnC,EAAwC,CAAxC,OAA+C7H,QAAQ,CAACE,QAJ1D,CAV0E;;IAiB1E,IAAIsT,aAAa,GAAGH,QAAQ,CAAC1B,UAAT,GAChB;MACEA,UAAU,EAAE8B,eAAe,CACzBvU,KAAK,CAACyS,UADmB,EAEzB0B,QAAQ,CAAC1B,UAFgB,EAGzB0B,QAAQ,CAACrN,OAAT,IAAoB,EAHK;IAD7B,CADgB,GAQhB,EARJ;IAUAoN,WAAW,CAILG,2BAAc,GAAG,EAAH,GAAQ;MAAE3B,UAAU,EAAE;KAJ/B,EAKNyB,QALM,EAMNG,aANM;MAOTlC,aAAa,EAAEU,aAPN;MAQThS,QARS;MASTiR,WAAW,EAAE,IATJ;MAUTM,UAAU,EAAE1B,eAVH;MAWT6B,YAAY,EAAE,MAXL;MAYT;MACAF,qBAAqB,EAAEtS,KAAK,CAACqS,UAAN,CAAiBtB,QAAjB,GACnB,KADmB,GAEnByD,sBAAsB,CAAC1T,QAAD,EAAWqT,QAAQ,CAACrN,OAAT,IAAoB9G,KAAK,CAAC8G,OAArC,CAfjB;MAgBTyL,kBAAkB,EAAES;KAhBtB;IAmBA,IAAIE,2BAAJ,EAAiC,CAAjC,KAEO,IAAIJ,aAAa,KAAKC,MAAa,CAAC1S,GAApC,EAAyC,CAAzC,KAEA,IAAIyS,aAAa,KAAKC,MAAa,CAACrR,IAApC,EAA0C;MAC/C2L,IAAI,CAAChM,OAAL,CAAaI,IAAb,CAAkBX,QAAlB,EAA4BA,QAAQ,CAACd,KAArC;IACD,CAFM,MAEA,IAAI8S,aAAa,KAAKC,MAAa,CAACjR,OAApC,EAA6C;MAClDuL,IAAI,CAAChM,OAAL,CAAaQ,OAAb,CAAqBf,QAArB,EAA+BA,QAAQ,CAACd,KAAxC;IACD,CAtDyE;;IAyD1E8S,aAAa,GAAGC,MAAa,CAAC1S,GAA9B;IACA2S,yBAAyB,GAAG,KAA5B;IACAE,2BAA2B,GAAG,KAA9B;IACAC,sBAAsB,GAAG,KAAzB;IACAC,uBAAuB,GAAG,EAA1B;IACAC,qBAAqB,GAAG,EAAxB;EACD,CArN0C;EAwN3C;;EACA,eAAeoB,QAAf,CACE7T,EADF,EAEE8T,IAFF,EAE8B;IAE5B,IAAI,OAAO9T,EAAP,KAAc,QAAlB,EAA4B;MAC1ByM,IAAI,CAAChM,OAAL,CAAaU,EAAb,CAAgBnB,EAAhB;MACA;IACD;IAED,IAAI;MAAEyD,IAAF;MAAQsQ,UAAR;MAAoB1P;IAApB,IAA8B2P,wBAAwB,CAAChU,EAAD,EAAK8T,IAAL,CAA1D;IAEA,IAAI5T,QAAQ,GAAGC,cAAc,CAACf,KAAK,CAACc,QAAP,EAAiBuD,IAAjB,EAAuBqQ,IAAI,IAAIA,IAAI,CAAC1U,KAApC,CAA7B,CAT4B;IAY5B;IACA;IACA;IACA;;IACAc,QAAQ,GAAGuM,IAAI,CAAChM,OAAL,CAAaG,cAAb,CAA4BV,QAA5B,CAAX;IAEA,IAAIsR,aAAa,GACf,CAACsC,IAAI,IAAIA,IAAI,CAAC7S,OAAd,MAA2B,IAA3B,IAAmC8S,UAAU,IAAI,IAAjD,GACI5B,MAAa,CAACjR,OADlB,GAEIiR,MAAa,CAACrR,IAHpB;IAIA,IAAI6Q,kBAAkB,GACpBmC,IAAI,IAAI,oBAAwBA,QAAhC,GACIA,IAAI,CAACnC,kBAAL,KAA4B,IADhC,GAEItS,SAHN;IAKA,OAAO,MAAM6T,eAAe,CAAC1B,aAAD,EAAgBtR,QAAhB,EAA0B;MACpD6T,UADoD;MAEpD;MACA;MACAE,YAAY,EAAE5P,KAJsC;MAKpDsN,kBALoD;MAMpD1Q,OAAO,EAAE6S,IAAI,IAAIA,IAAI,CAAC7S;IAN8B,CAA1B,CAA5B;EAQD,CA9P0C;EAiQ3C;EACA;;EACA,SAASiT,UAAT,GAAmB;IACjBC,oBAAoB;IACpBb,WAAW,CAAC;MAAE1B,YAAY,EAAE;KAAjB,CAAX,CAFiB;IAKjB;;IACA,IAAIxS,KAAK,CAACqS,UAAN,CAAiBrS,KAAjB,KAA2B,YAA/B,EAA6C;MAC3C;IACD,CARgB;IAWjB;IACA;;IACA,IAAIA,KAAK,CAACqS,UAAN,CAAiBrS,KAAjB,KAA2B,MAA/B,EAAuC;MACrC8T,eAAe,CAAC9T,KAAK,CAACoS,aAAP,EAAsBpS,KAAK,CAACc,QAA5B,EAAsC;QACnDkU,8BAA8B,EAAE;MADmB,CAAtC,CAAf;MAGA;IACD,CAlBgB;IAqBjB;IACA;;IACAlB,eAAe,CACbhB,aAAa,IAAI9S,KAAK,CAACoS,aADV,EAEbpS,KAAK,CAACqS,UAAN,CAAiBvR,QAFJ,EAGb;MAAEmU,kBAAkB,EAAEjV,KAAK,CAACqS;IAA5B,CAHa,CAAf;EAKD,CA/R0C;EAkS3C;EACA;;EACA,eAAeyB,eAAf,CACE1B,aADF,EAEEtR,QAFF,EAGE4T,IAHF,EAUG;IAED;IACA;IACA;IACAzB,2BAA2B,IAAIA,2BAA2B,CAACvD,KAA5B,EAA/B;IACAuD,2BAA2B,GAAG,IAA9B;IACAH,aAAa,GAAGV,aAAhB;IACAc,2BAA2B,GACzB,CAACwB,IAAI,IAAIA,IAAI,CAACM,8BAAd,MAAkD,IADpD,CARC;IAYD;;IACAE,kBAAkB,CAAClV,KAAK,CAACc,QAAP,EAAiBd,KAAK,CAAC8G,OAAvB,CAAlB;IACAkM,yBAAyB,GAAG,CAAC0B,IAAI,IAAIA,IAAI,CAACnC,kBAAd,MAAsC,IAAlE;IAEA,IAAI4C,iBAAiB,GAAGT,IAAI,IAAIA,IAAI,CAACO,kBAArC;IACA,IAAInO,OAAO,GAAGP,WAAW,CAAC8K,UAAD,EAAavQ,QAAb,EAAuBuM,IAAI,CAAC5G,QAA5B,CAAzB,CAjBC;;IAoBD,IAAI,CAACK,OAAL,EAAc;MACZ,IAAI;QACFA,OAAO,EAAEsO,eADP;QAEF5P,KAFE;QAGFP;MAHE,IAIA6M,kBAAkB,CAACT,UAAD,CAJtB,CADY;;MAOZgE,qBAAqB;MACrBjB,kBAAkB,CAACtT,QAAD,EAAW;QAC3BgG,OAAO,EAAEsO,eADkB;QAE3B3C,UAAU,EAAE,EAFe;QAG3BE,MAAM,EAAE;UACN,CAACnN,KAAK,CAACO,EAAP,GAAYd;QADN;MAHmB,CAAX,CAAlB;MAOA;IACD,CApCA;;IAuCD,IAAIqQ,gBAAgB,CAACtV,KAAK,CAACc,QAAP,EAAiBA,QAAjB,CAApB,EAAgD;MAC9CsT,kBAAkB,CAACtT,QAAD,EAAW;QAAEgG;MAAF,CAAX,CAAlB;MACA;IACD,CA1CA;;IA6CDmM,2BAA2B,GAAG,IAAI1E,eAAJ,EAA9B;IACA,IAAIgH,OAAO,GAAGC,aAAa,CACzB1U,QADyB,EAEzBmS,2BAA2B,CAACvE,MAFH,EAGzBgG,IAAI,IAAIA,IAAI,CAACC,UAHY,CAA3B;IAKA,IAAIc,iBAAJ;IACA,IAAIZ,YAAJ;IAEA,IAAIH,IAAI,IAAIA,IAAI,CAACG,YAAjB,EAA+B;MAC7B;MACA;MACA;MACA;MACAA,YAAY,GAAG;QACb,CAACa,mBAAmB,CAAC5O,OAAD,CAAnB,CAA6BtB,KAA7B,CAAmCO,EAApC,GAAyC2O,IAAI,CAACG;OADhD;IAGD,CARD,MAQO,IAAIH,IAAI,IAAIA,IAAI,CAACC,UAAjB,EAA6B;MAClC;MACA,IAAIgB,YAAY,GAAG,MAAMC,YAAY,CACnCL,OADmC,EAEnCzU,QAFmC,EAGnC4T,IAAI,CAACC,UAH8B,EAInC7N,OAJmC,EAKnC;QAAEjF,OAAO,EAAE6S,IAAI,CAAC7S;MAAhB,CALmC,CAArC;MAQA,IAAI8T,YAAY,CAACE,cAAjB,EAAiC;QAC/B;MACD;MAEDJ,iBAAiB,GAAGE,YAAY,CAACF,iBAAjC;MACAZ,YAAY,GAAGc,YAAY,CAACG,kBAA5B;MAEA,IAAIzD,UAAU;QACZrS,KAAK,EAAE,SADK;QAEZc;OACG4T,MAAI,CAACC,UAHI,CAAd;MAKAQ,iBAAiB,GAAG9C,UAApB;IACD,CArFA;;IAwFD,IAAI;MAAEwD,cAAF;MAAkBpD,UAAlB;MAA8BE;KAAW,SAAMoD,aAAa,CAC9DR,OAD8D,EAE9DzU,QAF8D,EAG9DgG,OAH8D,EAI9DqO,iBAJ8D,EAK9DT,IAAI,IAAIA,IAAI,CAACC,UALiD,EAM9DD,IAAI,IAAIA,IAAI,CAAC7S,OANiD,EAO9D4T,iBAP8D,EAQ9DZ,YAR8D,CAAhE;IAWA,IAAIgB,cAAJ,EAAoB;MAClB;IACD,CArGA;IAwGD;IACA;;IACA5C,2BAA2B,GAAG,IAA9B;IAEAmB,kBAAkB,CAACtT,QAAD,EAAW;MAC3BgG,OAD2B;MAE3B2L,UAF2B;MAG3BE;IAH2B,CAAX,CAAlB;EAKD,CA/Z0C;EAka3C;;EACA,eAAeiD,YAAf,CACEL,OADF,EAEEzU,QAFF,EAGE6T,UAHF,EAIE7N,OAJF,EAKE4N,IALF,EAK8B;IAE5BK,oBAAoB,GAFQ;;IAK5B,IAAI1C,UAAU;MACZrS,KAAK,EAAE,YADK;MAEZc;IAFY,GAGT6T,UAHS,CAAd;IAKAT,WAAW,CAAC;MAAE7B;KAAH,CAAX,CAV4B;;IAa5B,IAAI2D,MAAJ;IACA,IAAIC,WAAW,GAAGC,cAAc,CAACpP,OAAD,EAAUhG,QAAV,CAAhC;IAEA,IAAI,CAACmV,WAAW,CAACzQ,KAAZ,CAAkBpF,MAAvB,EAA+B;MAC7B4V,MAAM,GAAGG,yBAAyB,CAACrV,QAAD,CAAlC;IACD,CAFD,MAEO;MACLkV,MAAM,GAAG,MAAMI,kBAAkB,CAC/B,QAD+B,EAE/Bb,OAF+B,EAG/BU,WAH+B,EAI/BnP,OAJ+B,EAK/BqL,MAAM,CAAC1L,QALwB,CAAjC;MAQA,IAAI8O,OAAO,CAAC7G,MAAR,CAAeW,OAAnB,EAA4B;QAC1B,OAAO;UAAEwG,cAAc,EAAE;SAAzB;MACD;IACF;IAED,IAAIQ,gBAAgB,CAACL,MAAD,CAApB,EAA8B;MAC5B,IAAIM,kBAAkB;QACpBtW,KAAK,EAAE,SADa;QAEpBc,QAAQ,EAAEC,cAAc,CAACf,KAAK,CAACc,QAAP,EAAiBkV,MAAM,CAAClV,QAAxB;MAFJ,GAGjB6T,UAHiB,CAAtB;MAKA,MAAM4B,uBAAuB,CAC3BP,MAD2B,EAE3BM,kBAF2B,EAG3B5B,IAAI,IAAIA,IAAI,CAAC7S,OAHc,CAA7B;MAKA,OAAO;QAAEgU,cAAc,EAAE;OAAzB;IACD;IAED,IAAIW,aAAa,CAACR,MAAD,CAAjB,EAA2B;MACzB;MACA;MACA,IAAIS,aAAa,GAAGf,mBAAmB,CAAC5O,OAAD,EAAUmP,WAAW,CAACzQ,KAAZ,CAAkBO,EAA5B,CAAvC,CAHyB;MAMzB;MACA;MACA;;MACA,IAAI,CAAC2O,IAAI,IAAIA,IAAI,CAAC7S,OAAd,MAA2B,IAA/B,EAAqC;QACnCiR,aAAa,GAAGC,MAAa,CAACrR,IAA9B;MACD;MAED,OAAO;QACLoU,kBAAkB,EAAE;UAAE,CAACW,aAAa,CAACjR,KAAd,CAAoBO,EAArB,GAA0BiQ,MAAM,CAAC/Q;QAAnC;OADtB;IAGD;IAED,IAAIyR,gBAAgB,CAACV,MAAD,CAApB,EAA8B;MAC5B,MAAM,IAAInS,KAAJ,CAAU,qCAAV,CAAN;IACD;IAED,OAAO;MACL4R,iBAAiB,EAAE;QAAE,CAACQ,WAAW,CAACzQ,KAAZ,CAAkBO,EAAnB,GAAwBiQ,MAAM,CAAC5I;MAAjC;KADrB;EAGD,CA/e0C;EAkf3C;;EACA,eAAe2I,aAAf,CACER,OADF,EAEEzU,QAFF,EAGEgG,OAHF,EAIEmO,kBAJF,EAKEN,UALF,EAME9S,OANF,EAOE4T,iBAPF,EAQEZ,YARF,EAQ0B;IAExB;IACA,IAAIM,iBAAiB,GAAGF,kBAAxB;IACA,IAAI,CAACE,iBAAL,EAAwB;MACtB,IAAI9C,UAAU,GAAgC;QAC5CrS,KAAK,EAAE,SADqC;QAE5Cc,QAF4C;QAG5C8P,UAAU,EAAE3Q,SAHgC;QAI5C4Q,UAAU,EAAE5Q,SAJgC;QAK5C6Q,WAAW,EAAE7Q,SAL+B;QAM5C8Q,QAAQ,EAAE9Q;OANZ;MAQAkV,iBAAiB,GAAG9C,UAApB;IACD;IAED,IAAI,CAACsE,aAAD,EAAgBC,oBAAhB,CAAwCC,mBAAgB,CAC1D7W,KAD0D,EAE1D8G,OAF0D,EAG1D6N,UAH0D,EAI1D7T,QAJ0D,EAK1DqS,sBAL0D,EAM1DC,uBAN0D,EAO1DC,qBAP0D,EAQ1DoC,iBAR0D,EAS1DZ,YAT0D,EAU1DlB,gBAV0D,CAA5D,CAhBwB;IA8BxB;IACA;;IACA0B,qBAAqB,CAClByB,OAAD,IACE,EAAEhQ,OAAO,IAAIA,OAAO,CAAC+B,IAAR,CAAcmJ,CAAD,IAAOA,CAAC,CAACxM,KAAF,CAAQO,EAAR,KAAe+Q,OAAnC,CAAb,KACCH,aAAa,IAAIA,aAAa,CAAC9N,IAAd,CAAoBmJ,CAAD,IAAOA,CAAC,CAACxM,KAAF,CAAQO,EAAR,KAAe+Q,OAAzC,CAHD,CAArB,CAhCwB;;IAuCxB,IAAIH,aAAa,CAACxW,MAAd,KAAyB,CAAzB,IAA8ByW,oBAAoB,CAACzW,MAArB,KAAgC,CAAlE,EAAqE;MACnEiU,kBAAkB,CAACtT,QAAD,EAAW;QAC3BgG,OAD2B;QAE3B2L,UAAU,EAAE8B,eAAe,CAACvU,KAAK,CAACyS,UAAP,EAAmB,EAAnB,EAAuB3L,OAAvB,CAFA;QAG3B;QACA6L,MAAM,EAAEkC,YAAY,IAAI,IAJG;QAK3BnC,UAAU,EAAE+C,iBAAiB,IAAI;MALN,CAAX,CAAlB;MAOA,OAAO;QAAEI,cAAc,EAAE;OAAzB;IACD,CAhDuB;IAmDxB;IACA;IACA;;IACA,IAAI,CAAC3C,2BAAL,EAAkC;MAChC0D,oBAAoB,CAACzP,OAArB,CAA6B4P,KAAU;QAAA,IAAT,CAAClW,GAAD,CAAS;QACrC,IAAImW,OAAO,GAAGhX,KAAK,CAAC4S,QAAN,CAAexD,GAAf,CAAmBvO,GAAnB,CAAd;QACA,IAAIoW,mBAAmB,GAA6B;UAClDjX,KAAK,EAAE,SAD2C;UAElDoN,IAAI,EAAE4J,OAAO,IAAIA,OAAO,CAAC5J,IAFyB;UAGlDwD,UAAU,EAAE3Q,SAHsC;UAIlD4Q,UAAU,EAAE5Q,SAJsC;UAKlD6Q,WAAW,EAAE7Q,SALqC;UAMlD8Q,QAAQ,EAAE9Q;SANZ;QAQAD,KAAK,CAAC4S,QAAN,CAAelF,GAAf,CAAmB7M,GAAnB,EAAwBoW,mBAAxB;OAVF;MAYA/C,WAAW;QACT7B,UAAU,EAAE8C,iBADH;QAETzC,UAAU,EAAE+C,iBAAiB,IAAIzV,KAAK,CAAC0S,UAA3B,IAAyC;MAF5C,GAGLkE,oBAAoB,CAACzW,MAArB,GAA8B,CAA9B,GACA;QAAEyS,QAAQ,EAAE,IAAIC,GAAJ,CAAQ7S,KAAK,CAAC4S,QAAd;OADZ,GAEA,EALK,CAAX;IAOD;IAEDY,uBAAuB,GAAG,EAAED,kBAA5B;IACAqD,oBAAoB,CAACzP,OAArB,CAA6B+P;MAAA,IAAC,CAACrW,GAAD,CAAD;MAAA,OAC3ByS,gBAAgB,CAAC5F,GAAjB,CAAqB7M,GAArB,EAA0BoS,2BAA1B,CAD2B;KAA7B;IAIA,IAAI;MAAEkE,OAAF;MAAWC,aAAX;MAA0BC;IAA1B,IACF,MAAMC,8BAA8B,CAClCtX,KAAK,CAAC8G,OAD4B,EAElCA,OAFkC,EAGlC6P,aAHkC,EAIlCC,oBAJkC,EAKlCrB,OALkC,CADtC;IASA,IAAIA,OAAO,CAAC7G,MAAR,CAAeW,OAAnB,EAA4B;MAC1B,OAAO;QAAEwG,cAAc,EAAE;OAAzB;IACD,CA5FuB;IA+FxB;IACA;;IACAe,oBAAoB,CAACzP,OAArB,CAA6BoQ;MAAA,IAAC,CAAC1W,GAAD,CAAD;MAAA,OAAWyS,gBAAgB,CAAChE,MAAjB,CAAwBzO,GAAxB,CAAX;IAAA,CAA7B,EAjGwB;;IAoGxB,IAAI0P,QAAQ,GAAGiH,YAAY,CAACL,OAAD,CAA3B;IACA,IAAI5G,QAAJ,EAAc;MACZ,IAAI+F,kBAAkB,GAAGmB,iBAAiB,CAACzX,KAAD,EAAQuQ,QAAR,CAA1C;MACA,MAAMgG,uBAAuB,CAAChG,QAAD,EAAW+F,kBAAX,EAA+BzU,OAA/B,CAA7B;MACA,OAAO;QAAEgU,cAAc,EAAE;OAAzB;IACD,CAzGuB;;IA4GxB,IAAI;MAAEpD,UAAF;MAAcE;IAAd,IAAyB+E,iBAAiB,CAC5C1X,KAD4C,EAE5C8G,OAF4C,EAG5C6P,aAH4C,EAI5CS,aAJ4C,EAK5CvC,YAL4C,EAM5C+B,oBAN4C,EAO5CS,cAP4C,EAQ5CzD,eAR4C,CAA9C,CA5GwB;;IAwHxBA,eAAe,CAACzM,OAAhB,CAAwB,CAACwQ,YAAD,EAAeb,OAAf,KAA0B;MAChDa,YAAY,CAACnI,SAAb,CAAwBH,OAAD,IAAY;QACjC;QACA;QACA;QACA,IAAIA,OAAO,IAAIsI,YAAY,CAACpI,IAA5B,EAAkC;UAChCqE,eAAe,CAACtE,MAAhB,CAAuBwH,OAAvB;QACD;OANH;KADF;IAWAc,sBAAsB;IACtB,IAAIC,kBAAkB,GAAGC,oBAAoB,CAACtE,uBAAD,CAA7C;IAEA;MACEf,UADF;MAEEE;IAFF,GAGMkF,kBAAkB,IAAIjB,oBAAoB,CAACzW,MAArB,GAA8B,CAApD,GACA;MAAEyS,QAAQ,EAAE,IAAIC,GAAJ,CAAQ7S,KAAK,CAAC4S,QAAd;IAAZ,CADA,GAEA,EALN;EAOD;EAED,SAASmF,UAAT,CAAiClX,GAAjC,EAA4C;IAC1C,OAAOb,KAAK,CAAC4S,QAAN,CAAexD,GAAf,CAAmBvO,GAAnB,KAA2BmQ,YAAlC;EACD,CA5oB0C;;EA+oB3C,SAASgH,KAAT,CACEnX,GADF,EAEEiW,OAFF,EAGE5T,IAHF,EAIEwR,IAJF,EAI2B;IAEzB,IAAIvD,QAAJ,EAAc;MACZ,MAAM,IAAItN,KAAJ,CACJ,8EACE,8EADF,GAEE,6CAHE,CAAN;IAKD;IAED,IAAIyP,gBAAgB,CAACnN,GAAjB,CAAqBtF,GAArB,CAAJ,EAA+BoX,YAAY,CAACpX,GAAD,CAAZ;IAE/B,IAAIiG,OAAO,GAAGP,WAAW,CAAC8K,UAAD,EAAanO,IAAb,EAAmBmK,IAAI,CAAC5G,QAAxB,CAAzB;IACA,IAAI,CAACK,OAAL,EAAc;MACZoR,eAAe,CAACrX,GAAD,EAAMiW,OAAN,EAAe,IAAItG,aAAJ,CAAkB,GAAlB,EAAuB,WAAvB,EAAoC,IAApC,CAAf,CAAf;MACA;IACD;IAED,IAAI;MAAEnM,IAAF;MAAQsQ;IAAR,IAAuBC,wBAAwB,CAAC1R,IAAD,EAAOwR,IAAP,EAAa,IAAb,CAAnD;IACA,IAAIjL,KAAK,GAAGyM,cAAc,CAACpP,OAAD,EAAUzC,IAAV,CAA1B;IAEA,IAAIsQ,UAAJ,EAAgB;MACdwD,mBAAmB,CAACtX,GAAD,EAAMiW,OAAN,EAAezS,IAAf,EAAqBoF,KAArB,EAA4B3C,OAA5B,EAAqC6N,UAArC,CAAnB;MACA;IACD,CAxBwB;IA2BzB;;IACAhB,gBAAgB,CAACjG,GAAjB,CAAqB7M,GAArB,EAA0B,CAACwD,IAAD,EAAOoF,KAAP,EAAc3C,OAAd,CAA1B;IACAsR,mBAAmB,CAACvX,GAAD,EAAMiW,OAAN,EAAezS,IAAf,EAAqBoF,KAArB,EAA4B3C,OAA5B,CAAnB;EACD,CAjrB0C;EAorB3C;;EACA,eAAeqR,mBAAf,CACEtX,GADF,EAEEiW,OAFF,EAGEzS,IAHF,EAIEoF,KAJF,EAKE4O,cALF,EAME1D,UANF,EAMwB;IAEtBI,oBAAoB;IACpBpB,gBAAgB,CAACrE,MAAjB,CAAwBzO,GAAxB;IAEA,IAAI,CAAC4I,KAAK,CAACjE,KAAN,CAAYpF,MAAjB,EAAyB;MACvB,IAAI;QAAE6E;OAAUkR,4BAAyB,CAAC9R,IAAD,CAAzC;MACA6T,eAAe,CAACrX,GAAD,EAAMiW,OAAN,EAAe7R,KAAf,CAAf;MACA;IACD,CATqB;;IAYtB,IAAIqT,eAAe,GAAGtY,KAAK,CAAC4S,QAAN,CAAexD,GAAf,CAAmBvO,GAAnB,CAAtB;IACA,IAAImW,OAAO;MACThX,KAAK,EAAE;IADE,GAEN2U,UAFM;MAGTvH,IAAI,EAAEkL,eAAe,IAAIA,eAAe,CAAClL;KAH3C;IAKApN,KAAK,CAAC4S,QAAN,CAAelF,GAAf,CAAmB7M,GAAnB,EAAwBmW,OAAxB;IACA9C,WAAW,CAAC;MAAEtB,QAAQ,EAAE,IAAIC,GAAJ,CAAQ7S,KAAK,CAAC4S,QAAd;KAAb,CAAX,CAnBsB;;IAsBtB,IAAI2F,eAAe,GAAG,IAAIhK,eAAJ,EAAtB;IACA,IAAIiK,YAAY,GAAGhD,aAAa,CAACnR,IAAD,EAAOkU,eAAe,CAAC7J,MAAvB,EAA+BiG,UAA/B,CAAhC;IACArB,gBAAgB,CAAC5F,GAAjB,CAAqB7M,GAArB,EAA0B0X,eAA1B;IAEA,IAAIE,YAAY,GAAG,MAAMrC,kBAAkB,CACzC,QADyC,EAEzCoC,YAFyC,EAGzC/O,KAHyC,EAIzC4O,cAJyC,EAKzClG,MAAM,CAAC1L,QALkC,CAA3C;IAQA,IAAI+R,YAAY,CAAC9J,MAAb,CAAoBW,OAAxB,EAAiC;MAC/B;MACA;MACA,IAAIiE,gBAAgB,CAAClE,GAAjB,CAAqBvO,GAArB,MAA8B0X,eAAlC,EAAmD;QACjDjF,gBAAgB,CAAChE,MAAjB,CAAwBzO,GAAxB;MACD;MACD;IACD;IAED,IAAIwV,gBAAgB,CAACoC,YAAD,CAApB,EAAoC;MAClCnF,gBAAgB,CAAChE,MAAjB,CAAwBzO,GAAxB;MACA6S,gBAAgB,CAACtN,GAAjB,CAAqBvF,GAArB;MACA,IAAI6X,cAAc;QAChB1Y,KAAK,EAAE;MADS,GAEb2U,UAFa;QAGhBvH,IAAI,EAAEnN;OAHR;MAKAD,KAAK,CAAC4S,QAAN,CAAelF,GAAf,CAAmB7M,GAAnB,EAAwB6X,cAAxB;MACAxE,WAAW,CAAC;QAAEtB,QAAQ,EAAE,IAAIC,GAAJ,CAAQ7S,KAAK,CAAC4S,QAAd;MAAZ,CAAD,CAAX;MAEA,IAAI0D,kBAAkB;QACpBtW,KAAK,EAAE,SADa;QAEpBc,QAAQ,EAAEC,cAAc,CAACf,KAAK,CAACc,QAAP,EAAiB2X,YAAY,CAAC3X,QAA9B;MAFJ,GAGjB6T,UAHiB,CAAtB;MAKA,MAAM4B,uBAAuB,CAACkC,YAAD,EAAenC,kBAAf,CAA7B;MACA;IACD,CA7DqB;;IAgEtB,IAAIE,aAAa,CAACiC,YAAD,CAAjB,EAAiC;MAC/BP,eAAe,CAACrX,GAAD,EAAMiW,OAAN,EAAe2B,YAAY,CAACxT,KAA5B,CAAf;MACA;IACD;IAED,IAAIyR,gBAAgB,CAAC+B,YAAD,CAApB,EAAoC;MAClCxS,SAAS,CAAC,KAAD,EAAQ,qCAAR,CAAT;IACD,CAvEqB;IA0EtB;;IACA,IAAItE,YAAY,GAAG3B,KAAK,CAACqS,UAAN,CAAiBvR,QAAjB,IAA6Bd,KAAK,CAACc,QAAtD;IACA,IAAI6X,mBAAmB,GAAGnD,aAAa,CACrC7T,YADqC,EAErC4W,eAAe,CAAC7J,MAFqB,CAAvC;IAIA,IAAI5H,OAAO,GACT9G,KAAK,CAACqS,UAAN,CAAiBrS,KAAjB,KAA2B,MAA3B,GACIuG,WAAW,CAAC8K,UAAD,EAAarR,KAAK,CAACqS,UAAN,CAAiBvR,QAA9B,EAAwCuM,IAAI,CAAC5G,QAA7C,CADf,GAEIzG,KAAK,CAAC8G,OAHZ;IAKAb,SAAS,CAACa,OAAD,EAAU,8CAAV,CAAT;IAEA,IAAI8R,MAAM,GAAG,EAAErF,kBAAf;IACAE,cAAc,CAAC/F,GAAf,CAAmB7M,GAAnB,EAAwB+X,MAAxB;IAEA,IAAIC,WAAW;MACb7Y,KAAK,EAAE,SADM;MAEboN,IAAI,EAAEqL,YAAY,CAACrL;IAFN,GAGVuH,UAHU,CAAf;IAKA3U,KAAK,CAAC4S,QAAN,CAAelF,GAAf,CAAmB7M,GAAnB,EAAwBgY,WAAxB;IAEA,IAAI,CAAClC,aAAD,EAAgBC,oBAAhB,IAAwCC,gBAAgB,CAC1D7W,KAD0D,EAE1D8G,OAF0D,EAG1D6N,UAH0D,EAI1DhT,YAJ0D,EAK1DwR,sBAL0D,EAM1DC,uBAN0D,EAO1DC,qBAP0D,EAQ1D;MAAE,CAAC5J,KAAK,CAACjE,KAAN,CAAYO,EAAb,GAAkB0S,YAAY,CAACrL;KARyB,EAS1DnN,SAT0D;IAAA;IAU1D0T,gBAV0D,CAA5D,CAjGsB;IA+GtB;IACA;;IACAiD,oBAAoB,CACjB9N,MADH,CACUgQ;MAAA,IAAC,CAACC,QAAD,CAAD;MAAA,OAAgBA,QAAQ,KAAKlY,GAA7B;KADV,EAEGsG,OAFH,CAEW6R,KAAe;MAAA,IAAd,CAACD,QAAD,CAAc;MACtB,IAAIT,eAAe,GAAGtY,KAAK,CAAC4S,QAAN,CAAexD,GAAf,CAAmB2J,QAAnB,CAAtB;MACA,IAAI9B,mBAAmB,GAA6B;QAClDjX,KAAK,EAAE,SAD2C;QAElDoN,IAAI,EAAEkL,eAAe,IAAIA,eAAe,CAAClL,IAFS;QAGlDwD,UAAU,EAAE3Q,SAHsC;QAIlD4Q,UAAU,EAAE5Q,SAJsC;QAKlD6Q,WAAW,EAAE7Q,SALqC;QAMlD8Q,QAAQ,EAAE9Q;OANZ;MAQAD,KAAK,CAAC4S,QAAN,CAAelF,GAAf,CAAmBqL,QAAnB,EAA6B9B,mBAA7B;MACA3D,gBAAgB,CAAC5F,GAAjB,CAAqBqL,QAArB,EAA+BR,eAA/B;KAbJ;IAgBArE,WAAW,CAAC;MAAEtB,QAAQ,EAAE,IAAIC,GAAJ,CAAQ7S,KAAK,CAAC4S,QAAd;IAAZ,CAAD,CAAX;IAEA,IAAI;MAAEuE,OAAF;MAAWC,aAAX;MAA0BC;IAA1B,IACF,MAAMC,8BAA8B,CAClCtX,KAAK,CAAC8G,OAD4B,EAElCA,OAFkC,EAGlC6P,aAHkC,EAIlCC,oBAJkC,EAKlC+B,mBALkC,CADtC;IASA,IAAIJ,eAAe,CAAC7J,MAAhB,CAAuBW,OAA3B,EAAoC;MAClC;IACD;IAEDoE,cAAc,CAACnE,MAAf,CAAsBzO,GAAtB;IACAyS,gBAAgB,CAAChE,MAAjB,CAAwBzO,GAAxB;IACA+V,oBAAoB,CAACzP,OAArB,CAA6B8R;MAAA,IAAC,CAACF,QAAD,CAAD;MAAA,OAC3BzF,gBAAgB,CAAChE,MAAjB,CAAwByJ,QAAxB,CAD2B;KAA7B;IAIA,IAAIxI,QAAQ,GAAGiH,YAAY,CAACL,OAAD,CAA3B;IACA,IAAI5G,QAAJ,EAAc;MACZ,IAAI+F,kBAAkB,GAAGmB,iBAAiB,CAACzX,KAAD,EAAQuQ,QAAR,CAA1C;MACA,MAAMgG,uBAAuB,CAAChG,QAAD,EAAW+F,kBAAX,CAA7B;MACA;IACD,CA3JqB;;IA8JtB,IAAI;MAAE7D,UAAF;MAAcE;IAAd,IAAyB+E,iBAAiB,CAC5C1X,KAD4C,EAE5CA,KAAK,CAAC8G,OAFsC,EAG5C6P,aAH4C,EAI5CS,aAJ4C,EAK5CnX,SAL4C,EAM5C2W,oBAN4C,EAO5CS,cAP4C,EAQ5CzD,eAR4C,CAA9C;IAWA,IAAIsF,WAAW,GAA0B;MACvClZ,KAAK,EAAE,MADgC;MAEvCoN,IAAI,EAAEqL,YAAY,CAACrL,IAFoB;MAGvCwD,UAAU,EAAE3Q,SAH2B;MAIvC4Q,UAAU,EAAE5Q,SAJ2B;MAKvC6Q,WAAW,EAAE7Q,SAL0B;MAMvC8Q,QAAQ,EAAE9Q;KANZ;IAQAD,KAAK,CAAC4S,QAAN,CAAelF,GAAf,CAAmB7M,GAAnB,EAAwBqY,WAAxB;IAEA,IAAIrB,kBAAkB,GAAGC,oBAAoB,CAACc,MAAD,CAA7C,CAnLsB;IAsLtB;IACA;;IACA,IACE5Y,KAAK,CAACqS,UAAN,CAAiBrS,KAAjB,KAA2B,SAA3B,IACA4Y,MAAM,GAAGpF,uBAFX,EAGE;MACAvN,SAAS,CAAC6M,aAAD,EAAgB,yBAAhB,CAAT;MACAG,2BAA2B,IAAIA,2BAA2B,CAACvD,KAA5B,EAA/B;MAEA0E,kBAAkB,CAACpU,KAAK,CAACqS,UAAN,CAAiBvR,QAAlB,EAA4B;QAC5CgG,OAD4C;QAE5C2L,UAF4C;QAG5CE,MAH4C;QAI5CC,QAAQ,EAAE,IAAIC,GAAJ,CAAQ7S,KAAK,CAAC4S,QAAd;MAJkC,CAA5B,CAAlB;IAMD,CAbD,MAaO;MACL;MACA;MACA;MACAsB,WAAW;QACTvB,MADS;QAETF,UAAU,EAAE8B,eAAe,CAACvU,KAAK,CAACyS,UAAP,EAAmBA,UAAnB,EAA+B3L,OAA/B;MAFlB,GAGL+Q,kBAAkB,GAAG;QAAEjF,QAAQ,EAAE,IAAIC,GAAJ,CAAQ7S,KAAK,CAAC4S,QAAd;OAAf,GAA2C,EAHxD,CAAX;MAKAO,sBAAsB,GAAG,KAAzB;IACD;EACF,CA34B0C;;EA84B3C,eAAeiF,mBAAf,CACEvX,GADF,EAEEiW,OAFF,EAGEzS,IAHF,EAIEoF,KAJF,EAKE3C,OALF,EAKmC;IAEjC,IAAIwR,eAAe,GAAGtY,KAAK,CAAC4S,QAAN,CAAexD,GAAf,CAAmBvO,GAAnB,CAAtB,CAFiC;;IAIjC,IAAI6X,cAAc,GAA6B;MAC7C1Y,KAAK,EAAE,SADsC;MAE7C4Q,UAAU,EAAE3Q,SAFiC;MAG7C4Q,UAAU,EAAE5Q,SAHiC;MAI7C6Q,WAAW,EAAE7Q,SAJgC;MAK7C8Q,QAAQ,EAAE9Q,SALmC;MAM7CmN,IAAI,EAAEkL,eAAe,IAAIA,eAAe,CAAClL;KAN3C;IAQApN,KAAK,CAAC4S,QAAN,CAAelF,GAAf,CAAmB7M,GAAnB,EAAwB6X,cAAxB;IACAxE,WAAW,CAAC;MAAEtB,QAAQ,EAAE,IAAIC,GAAJ,CAAQ7S,KAAK,CAAC4S,QAAd;KAAb,CAAX,CAbiC;;IAgBjC,IAAI2F,eAAe,GAAG,IAAIhK,eAAJ,EAAtB;IACA,IAAIiK,YAAY,GAAGhD,aAAa,CAACnR,IAAD,EAAOkU,eAAe,CAAC7J,MAAvB,CAAhC;IACA4E,gBAAgB,CAAC5F,GAAjB,CAAqB7M,GAArB,EAA0B0X,eAA1B;IACA,IAAIvC,MAAM,GAAe,MAAMI,kBAAkB,CAC/C,QAD+C,EAE/CoC,YAF+C,EAG/C/O,KAH+C,EAI/C3C,OAJ+C,EAK/CqL,MAAM,CAAC1L,QALwC,CAAjD,CAnBiC;IA4BjC;IACA;IACA;;IACA,IAAIiQ,gBAAgB,CAACV,MAAD,CAApB,EAA8B;MAC5BA,MAAM,GACJ,CAAC,MAAMmD,mBAAmB,CAACnD,MAAD,EAASwC,YAAY,CAAC9J,MAAtB,EAA8B,IAA9B,CAA1B,KACAsH,MAFF;IAGD,CAnCgC;IAsCjC;;IACA,IAAI1C,gBAAgB,CAAClE,GAAjB,CAAqBvO,GAArB,MAA8B0X,eAAlC,EAAmD;MACjDjF,gBAAgB,CAAChE,MAAjB,CAAwBzO,GAAxB;IACD;IAED,IAAI2X,YAAY,CAAC9J,MAAb,CAAoBW,OAAxB,EAAiC;MAC/B;IACD,CA7CgC;;IAgDjC,IAAIgH,gBAAgB,CAACL,MAAD,CAApB,EAA8B;MAC5B,IAAIM,kBAAkB,GAAGmB,iBAAiB,CAACzX,KAAD,EAAQgW,MAAR,CAA1C;MACA,MAAMO,uBAAuB,CAACP,MAAD,EAASM,kBAAT,CAA7B;MACA;IACD,CApDgC;;IAuDjC,IAAIE,aAAa,CAACR,MAAD,CAAjB,EAA2B;MACzB,IAAIS,aAAa,GAAGf,mBAAmB,CAAC1V,KAAK,CAAC8G,OAAP,EAAgBgQ,OAAhB,CAAvC;MACA9W,KAAK,CAAC4S,QAAN,CAAetD,MAAf,CAAsBzO,GAAtB,EAFyB;MAIzB;MACA;;MACAqT,WAAW,CAAC;QACVtB,QAAQ,EAAE,IAAIC,GAAJ,CAAQ7S,KAAK,CAAC4S,QAAd,CADA;QAEVD,MAAM,EAAE;UACN,CAAC8D,aAAa,CAACjR,KAAd,CAAoBO,EAArB,GAA0BiQ,MAAM,CAAC/Q;QAD3B;MAFE,CAAD,CAAX;MAMA;IACD;IAEDgB,SAAS,CAAC,CAACyQ,gBAAgB,CAACV,MAAD,CAAlB,EAA4B,iCAA5B,CAAT,CAtEiC;;IAyEjC,IAAIkD,WAAW,GAA0B;MACvClZ,KAAK,EAAE,MADgC;MAEvCoN,IAAI,EAAE4I,MAAM,CAAC5I,IAF0B;MAGvCwD,UAAU,EAAE3Q,SAH2B;MAIvC4Q,UAAU,EAAE5Q,SAJ2B;MAKvC6Q,WAAW,EAAE7Q,SAL0B;MAMvC8Q,QAAQ,EAAE9Q;KANZ;IAQAD,KAAK,CAAC4S,QAAN,CAAelF,GAAf,CAAmB7M,GAAnB,EAAwBqY,WAAxB;IACAhF,WAAW,CAAC;MAAEtB,QAAQ,EAAE,IAAIC,GAAJ,CAAQ7S,KAAK,CAAC4S,QAAd;IAAZ,CAAD,CAAX;EACD;EAED;;;;;;;;;;;;;;;;;;AAkBG;;EACH,eAAe2D,uBAAf,CACEhG,QADF,EAEE8B,UAFF,EAGExQ,OAHF,EAGmB;IAEjB,IAAI0O,QAAQ,CAACuE,UAAb,EAAyB;MACvB3B,sBAAsB,GAAG,IAAzB;IACD;IACDlN,SAAS,CACPoM,UAAU,CAACvR,QADJ,EAEP,gDAFO,CAAT,CALiB;IAUjB;;IACAmS,2BAA2B,GAAG,IAA9B;IAEA,IAAImG,qBAAqB,GACvBvX,OAAO,KAAK,IAAZ,GAAmBkR,MAAa,CAACjR,OAAjC,GAA2CiR,MAAa,CAACrR,IAD3D;IAGA,MAAMoS,eAAe,CAACsF,qBAAD,EAAwB/G,UAAU,CAACvR,QAAnC,EAA6C;MAChEmU,kBAAkB,EAAE5C;IAD4C,CAA7C,CAArB;EAGD;EAED,eAAeiF,8BAAf,CACE+B,cADF,EAEEvS,OAFF,EAGE6P,aAHF,EAIE2C,cAJF,EAKE/D,OALF,EAKkB;IAEhB;IACA;IACA;IACA,IAAI4B,OAAO,GAAG,MAAM/I,OAAO,CAACmL,GAAR,CAAY,CAC9B,GAAG5C,aAAa,CAAC/W,GAAd,CAAmB6J,KAAD,IACnB2M,kBAAkB,CAAC,QAAD,EAAWb,OAAX,EAAoB9L,KAApB,EAA2B3C,OAA3B,EAAoCqL,MAAM,CAAC1L,QAA3C,CADjB,CAD2B,EAI9B,GAAG6S,cAAc,CAAC1Z,GAAf,CAAmB4Z;MAAA,IAAC,GAAGtW,IAAH,EAASuG,KAAT,EAAgBgQ,YAAhB,CAAD;MAAA,OACpBrD,kBAAkB,CAChB,QADgB,EAEhBZ,aAAa,CAACtS,IAAD,EAAOqS,OAAO,CAAC7G,MAAf,CAFG,EAGhBjF,KAHgB,EAIhBgQ,YAJgB,EAKhBtH,MAAM,CAAC1L,QALS,CADE;KAAnB,CAJ2B,CAAZ,CAApB;IAcA,IAAI2Q,aAAa,GAAGD,OAAO,CAAC5T,KAAR,CAAc,CAAd,EAAiBoT,aAAa,CAACxW,MAA/B,CAApB;IACA,IAAIkX,cAAc,GAAGF,OAAO,CAAC5T,KAAR,CAAcoT,aAAa,CAACxW,MAA5B,CAArB;IAEA,MAAMiO,OAAO,CAACmL,GAAR,CAAY,CAChBG,sBAAsB,CACpBL,cADoB,EAEpB1C,aAFoB,EAGpBS,aAHoB,EAIpB7B,OAAO,CAAC7G,MAJY,EAKpB,KALoB,EAMpB1O,KAAK,CAACyS,UANc,CADN,EAShBiH,sBAAsB,CACpBL,cADoB,EAEpBC,cAAc,CAAC1Z,GAAf,CAAmB+Z;MAAA,IAAC,IAAKlQ,KAAL,CAAD;MAAA,OAAiBA,KAAjB;KAAnB,CAFoB,EAGpB4N,cAHoB,EAIpB9B,OAAO,CAAC7G,MAJY,EAKpB,IALoB,CATN,CAAZ,CAAN;IAkBA,OAAO;MAAEyI,OAAF;MAAWC,aAAX;MAA0BC;KAAjC;EACD;EAED,SAAStC,oBAAT,GAA6B;IAC3B;IACA5B,sBAAsB,GAAG,IAAzB,CAF2B;IAK3B;;IACAC,uBAAuB,CAAC3R,IAAxB,CAA6B,GAAG4T,qBAAqB,EAArD,EAN2B;;IAS3B1B,gBAAgB,CAACxM,OAAjB,CAAyB,CAAC6C,CAAD,EAAInJ,GAAJ,KAAW;MAClC,IAAIyS,gBAAgB,CAACnN,GAAjB,CAAqBtF,GAArB,CAAJ,EAA+B;QAC7BwS,qBAAqB,CAAC5R,IAAtB,CAA2BZ,GAA3B;QACAoX,YAAY,CAACpX,GAAD,CAAZ;MACD;KAJH;EAMD;EAED,SAASqX,eAAT,CAAyBrX,GAAzB,EAAsCiW,OAAtC,EAAuD7R,KAAvD,EAAiE;IAC/D,IAAIwR,aAAa,GAAGf,mBAAmB,CAAC1V,KAAK,CAAC8G,OAAP,EAAgBgQ,OAAhB,CAAvC;IACA7C,aAAa,CAACpT,GAAD,CAAb;IACAqT,WAAW,CAAC;MACVvB,MAAM,EAAE;QACN,CAAC8D,aAAa,CAACjR,KAAd,CAAoBO,EAArB,GAA0Bd;OAFlB;MAIV2N,QAAQ,EAAE,IAAIC,GAAJ,CAAQ7S,KAAK,CAAC4S,QAAd;IAJA,CAAD,CAAX;EAMD;EAED,SAASqB,aAAT,CAAuBpT,GAAvB,EAAkC;IAChC,IAAIyS,gBAAgB,CAACnN,GAAjB,CAAqBtF,GAArB,CAAJ,EAA+BoX,YAAY,CAACpX,GAAD,CAAZ;IAC/B8S,gBAAgB,CAACrE,MAAjB,CAAwBzO,GAAxB;IACA4S,cAAc,CAACnE,MAAf,CAAsBzO,GAAtB;IACA6S,gBAAgB,CAACpE,MAAjB,CAAwBzO,GAAxB;IACAb,KAAK,CAAC4S,QAAN,CAAetD,MAAf,CAAsBzO,GAAtB;EACD;EAED,SAASoX,YAAT,CAAsBpX,GAAtB,EAAiC;IAC/B,IAAIyN,UAAU,GAAGgF,gBAAgB,CAAClE,GAAjB,CAAqBvO,GAArB,CAAjB;IACAoF,SAAS,CAACqI,UAAD,EAA2CzN,mCAA3C,CAAT;IACAyN,UAAU,CAACoB,KAAX;IACA4D,gBAAgB,CAAChE,MAAjB,CAAwBzO,GAAxB;EACD;EAED,SAAS+Y,gBAAT,CAA0BC,IAA1B,EAAwC;IACtC,KAAK,IAAIhZ,GAAT,IAAgBgZ,IAAhB,EAAsB;MACpB,IAAI7C,OAAO,GAAGe,UAAU,CAAClX,GAAD,CAAxB;MACA,IAAIqY,WAAW,GAA0B;QACvClZ,KAAK,EAAE,MADgC;QAEvCoN,IAAI,EAAE4J,OAAO,CAAC5J,IAFyB;QAGvCwD,UAAU,EAAE3Q,SAH2B;QAIvC4Q,UAAU,EAAE5Q,SAJ2B;QAKvC6Q,WAAW,EAAE7Q,SAL0B;QAMvC8Q,QAAQ,EAAE9Q;OANZ;MAQAD,KAAK,CAAC4S,QAAN,CAAelF,GAAf,CAAmB7M,GAAnB,EAAwBqY,WAAxB;IACD;EACF;EAED,SAAStB,sBAAT,GAA+B;IAC7B,IAAIkC,QAAQ,GAAG,EAAf;IACA,KAAK,IAAIjZ,GAAT,IAAgB6S,gBAAhB,EAAkC;MAChC,IAAIsD,OAAO,GAAGhX,KAAK,CAAC4S,QAAN,CAAexD,GAAf,CAAmBvO,GAAnB,CAAd;MACAoF,SAAS,CAAC+Q,OAAD,EAA+BnW,0BAA/B,CAAT;MACA,IAAImW,OAAO,CAAChX,KAAR,KAAkB,SAAtB,EAAiC;QAC/B0T,gBAAgB,CAACpE,MAAjB,CAAwBzO,GAAxB;QACAiZ,QAAQ,CAACrY,IAAT,CAAcZ,GAAd;MACD;IACF;IACD+Y,gBAAgB,CAACE,QAAD,CAAhB;EACD;EAED,SAAShC,oBAAT,CAA8BiC,QAA9B,EAA8C;IAC5C,IAAIC,UAAU,GAAG,EAAjB;IACA,KAAK,IAAI,CAACnZ,GAAD,EAAMkF,EAAN,CAAT,IAAsB0N,cAAtB,EAAsC;MACpC,IAAI1N,EAAE,GAAGgU,QAAT,EAAmB;QACjB,IAAI/C,OAAO,GAAGhX,KAAK,CAAC4S,QAAN,CAAexD,GAAf,CAAmBvO,GAAnB,CAAd;QACAoF,SAAS,CAAC+Q,OAAD,EAA+BnW,0BAA/B,CAAT;QACA,IAAImW,OAAO,CAAChX,KAAR,KAAkB,SAAtB,EAAiC;UAC/BiY,YAAY,CAACpX,GAAD,CAAZ;UACA4S,cAAc,CAACnE,MAAf,CAAsBzO,GAAtB;UACAmZ,UAAU,CAACvY,IAAX,CAAgBZ,GAAhB;QACD;MACF;IACF;IACD+Y,gBAAgB,CAACI,UAAD,CAAhB;IACA,OAAOA,UAAU,CAAC7Z,MAAX,GAAoB,CAA3B;EACD;EAED,SAASkV,qBAAT,CACE4E,SADF,EAC0C;IAExC,IAAIC,iBAAiB,GAAa,EAAlC;IACAtG,eAAe,CAACzM,OAAhB,CAAwB,CAACgT,GAAD,EAAMrD,OAAN,KAAiB;MACvC,IAAI,CAACmD,SAAD,IAAcA,SAAS,CAACnD,OAAD,CAA3B,EAAsC;QACpC;QACA;QACA;QACAqD,GAAG,CAAC1K,MAAJ;QACAyK,iBAAiB,CAACzY,IAAlB,CAAuBqV,OAAvB;QACAlD,eAAe,CAACtE,MAAhB,CAAuBwH,OAAvB;MACD;KARH;IAUA,OAAOoD,iBAAP;EACD,CA1qC0C;EA6qC3C;;EACA,SAASE,uBAAT,CACEC,SADF,EAEEC,WAFF,EAGEC,MAHF,EAG0C;IAExC/I,oBAAoB,GAAG6I,SAAvB;IACA3I,iBAAiB,GAAG4I,WAApB;IACA7I,uBAAuB,GAAG8I,MAAM,KAAMzZ,QAAD,IAAcA,QAAQ,CAACD,GAA5B,CAAhC,CAJwC;IAOxC;IACA;;IACA,IAAI,CAAC8Q,qBAAD,IAA0B3R,KAAK,CAACqS,UAAN,KAAqB1B,eAAnD,EAAoE;MAClEgB,qBAAqB,GAAG,IAAxB;MACA,IAAI6I,CAAC,GAAGhG,sBAAsB,CAACxU,KAAK,CAACc,QAAP,EAAiBd,KAAK,CAAC8G,OAAvB,CAA9B;MACA,IAAI0T,CAAC,IAAI,IAAT,EAAe;QACbtG,WAAW,CAAC;UAAE5B,qBAAqB,EAAEkI;QAAzB,CAAD,CAAX;MACD;IACF;IAED,OAAO,MAAK;MACVhJ,oBAAoB,GAAG,IAAvB;MACAE,iBAAiB,GAAG,IAApB;MACAD,uBAAuB,GAAG,IAA1B;KAHF;EAKD;EAED,SAASyD,kBAAT,CACEpU,QADF,EAEEgG,OAFF,EAEmC;IAEjC,IAAI0K,oBAAoB,IAAIC,uBAAxB,IAAmDC,iBAAvD,EAA0E;MACxE,IAAI+I,WAAW,GAAG3T,OAAO,CAAClH,GAAR,CAAaoS,CAAD,IAC5B0I,qBAAqB,CAAC1I,CAAD,EAAIhS,KAAK,CAACyS,UAAV,CADL,CAAlB;MAGA,IAAI5R,GAAG,GAAG4Q,uBAAuB,CAAC3Q,QAAD,EAAW2Z,WAAX,CAAvB,IAAkD3Z,QAAQ,CAACD,GAArE;MACA2Q,oBAAoB,CAAC3Q,GAAD,CAApB,GAA4B6Q,iBAAiB,EAA7C;IACD;EACF;EAED,SAAS8C,sBAAT,CACE1T,QADF,EAEEgG,OAFF,EAEmC;IAEjC,IAAI0K,oBAAoB,IAAIC,uBAAxB,IAAmDC,iBAAvD,EAA0E;MACxE,IAAI+I,WAAW,GAAG3T,OAAO,CAAClH,GAAR,CAAaoS,CAAD,IAC5B0I,qBAAqB,CAAC1I,CAAD,EAAIhS,KAAK,CAACyS,UAAV,CADL,CAAlB;MAGA,IAAI5R,GAAG,GAAG4Q,uBAAuB,CAAC3Q,QAAD,EAAW2Z,WAAX,CAAvB,IAAkD3Z,QAAQ,CAACD,GAArE;MACA,IAAI2Z,CAAC,GAAGhJ,oBAAoB,CAAC3Q,GAAD,CAA5B;MACA,IAAI,OAAO2Z,CAAP,KAAa,QAAjB,EAA2B;QACzB,OAAOA,CAAP;MACD;IACF;IACD,OAAO,IAAP;EACD;EAEDrI,MAAM,GAAG;IACP,IAAI1L,QAAJ,GAAY;MACV,OAAO4G,IAAI,CAAC5G,QAAZ;KAFK;IAIP,IAAIzG,KAAJ,GAAS;MACP,OAAOA,KAAP;KALK;IAOP,IAAI0F,MAAJ,GAAU;MACR,OAAO2L,UAAP;KARK;IAUPwC,UAVO;IAWPrE,SAXO;IAYP4K,uBAZO;IAaP3F,QAbO;IAcPuD,KAdO;IAePlD,UAfO;IAgBP;IACA;IACAxT,UAAU,EAAGV,EAAD,IAAYyM,IAAI,CAAChM,OAAL,CAAaC,UAAb,CAAwBV,EAAxB,CAlBjB;IAmBPmX,UAnBO;IAoBP9D,aApBO;IAqBPF,OArBO;IAsBP4G,yBAAyB,EAAErH,gBAtBpB;IAuBPsH,wBAAwB,EAAEhH;GAvB5B;EA0BA,OAAOzB,MAAP;AACD;AAGD;AACA;AACA;;AAEA,MAAM0I,kBAAkB,GAAG,IAAIhV,GAAJ,CAAQ,CAAC,MAAD,EAAS,KAAT,EAAgB,OAAhB,EAAyB,QAAzB,CAAR,CAA3B;AACA,MAAMiV,mBAAmB,GAAG,IAAIjV,GAAJ,CAAQ,CAAC,KAAD,EAAQ,MAAR,EAAgB,GAAGgV,kBAAnB,CAAR,CAA5B;AAEM,SAAUE,4BAAV,CACJrV,MADI,EACyB;EAE7BO,SAAS,CACPP,MAAM,CAACvF,MAAP,GAAgB,CADT,EAEP,2EAFO,CAAT;EAKA,IAAIkR,UAAU,GAAG5L,yBAAyB,CAACC,MAAD,CAA1C;EAEA;;;;;;;;;;;;;;;;;;AAkBG;;EACH,eAAesV,KAAf,CACEzF,OADF,EACkB;IAEhB,IAAInS,GAAG,GAAG,IAAIsB,GAAJ,CAAQ6Q,OAAO,CAACnS,GAAhB,CAAV;IACA,IAAItC,QAAQ,GAAGC,cAAc,CAAC,EAAD,EAAKQ,UAAU,CAAC6B,GAAD,CAAf,EAAsB,IAAtB,EAA4B,SAA5B,CAA7B;IACA,IAAI0D,OAAO,GAAGP,WAAW,CAAC8K,UAAD,EAAavQ,QAAb,CAAzB;IAEA,IAAI,CAACga,mBAAmB,CAAC3U,GAApB,CAAwBoP,OAAO,CAAC0F,MAAhC,CAAL,EAA8C;MAC5C,IAAI;QACFnU,OAAO,EAAEoU,uBADP;QAEF1V,KAFE;QAGFP;OACEkW,6BAA0B,CAAC9J,UAAD,CAJ9B;MAKA,OAAO;QACLvQ,QADK;QAELgG,OAAO,EAAEoU,uBAFJ;QAGLzI,UAAU,EAAE,EAHP;QAILC,UAAU,EAAE,IAJP;QAKLC,MAAM,EAAE;UACN,CAACnN,KAAK,CAACO,EAAP,GAAYd;SANT;QAQLmW,UAAU,EAAEnW,KAAK,CAACsI,MARb;QASL8N,aAAa,EAAE,EATV;QAULC,aAAa,EAAE;OAVjB;IAYD,CAlBD,MAkBO,IAAI,CAACxU,OAAL,EAAc;MACnB,IAAI;QACFA,OAAO,EAAEsO,eADP;QAEF5P,KAFE;QAGFP;OACE6M,qBAAkB,CAACT,UAAD,CAJtB;MAKA,OAAO;QACLvQ,QADK;QAELgG,OAAO,EAAEsO,eAFJ;QAGL3C,UAAU,EAAE,EAHP;QAILC,UAAU,EAAE,IAJP;QAKLC,MAAM,EAAE;UACN,CAACnN,KAAK,CAACO,EAAP,GAAYd;SANT;QAQLmW,UAAU,EAAEnW,KAAK,CAACsI,MARb;QASL8N,aAAa,EAAE,EATV;QAULC,aAAa,EAAE;OAVjB;IAYD;IAED,IAAItF,MAAM,GAAG,MAAMuF,SAAS,CAAChG,OAAD,EAAUzU,QAAV,EAAoBgG,OAApB,CAA5B;IACA,IAAIkP,MAAM,YAAYrI,QAAtB,EAAgC;MAC9B,OAAOqI,MAAP;IACD,CA/Ce;IAkDhB;IACA;;IACA;MAASlV;IAAT,GAAsBkV,MAAtB;EACD;EAED;;;;;;;;;;;;;;;;AAgBG;;EACH,eAAewF,UAAf,CAA0BjG,OAA1B,EAA4CuB,OAA5C,EAA4D;IAC1D,IAAI1T,GAAG,GAAG,IAAIsB,GAAJ,CAAQ6Q,OAAO,CAACnS,GAAhB,CAAV;IACA,IAAItC,QAAQ,GAAGC,cAAc,CAAC,EAAD,EAAKQ,UAAU,CAAC6B,GAAD,CAAf,EAAsB,IAAtB,EAA4B,SAA5B,CAA7B;IACA,IAAI0D,OAAO,GAAGP,WAAW,CAAC8K,UAAD,EAAavQ,QAAb,CAAzB;IAEA,IAAI,CAACga,mBAAmB,CAAC3U,GAApB,CAAwBoP,OAAO,CAAC0F,MAAhC,CAAL,EAA8C;MAC5C,MAAMQ,yBAAyB,CAAC,IAAD,EAAO;QACpClO,MAAM,EAAE,GAD4B;QAEpCkD,UAAU,EAAE;MAFwB,CAAP,CAA/B;IAID,CALD,MAKO,IAAI,CAAC3J,OAAL,EAAc;MACnB,MAAM2U,yBAAyB,CAAC,IAAD,EAAO;QACpClO,MAAM,EAAE,GAD4B;QAEpCkD,UAAU,EAAE;MAFwB,CAAP,CAA/B;IAID;IAED,IAAIhH,KAAK,GAAGqN,OAAO,GACfhQ,OAAO,CAAC4U,IAAR,CAAc1J,CAAD,IAAOA,CAAC,CAACxM,KAAF,CAAQO,EAAR,KAAe+Q,OAAnC,CADe,GAEfZ,cAAc,CAACpP,OAAD,EAAUhG,QAAV,CAFlB;IAIA,IAAI,CAAC2I,KAAL,EAAY;MACV,MAAMgS,yBAAyB,CAAC,IAAD,EAAO;QACpClO,MAAM,EAAE,GAD4B;QAEpCkD,UAAU,EAAE;MAFwB,CAAP,CAA/B;IAID;IAED,IAAIuF,MAAM,GAAG,MAAMuF,SAAS,CAAChG,OAAD,EAAUzU,QAAV,EAAoBgG,OAApB,EAA6B2C,KAA7B,CAA5B;IACA,IAAIuM,MAAM,YAAYrI,QAAtB,EAAgC;MAC9B,OAAOqI,MAAP;IACD;IAED,IAAI/Q,KAAK,GAAG+Q,MAAM,CAACrD,MAAP,GAAgBhJ,MAAM,CAACgS,MAAP,CAAc3F,MAAM,CAACrD,MAArB,EAA6B,CAA7B,CAAhB,GAAkD1S,SAA9D;IACA,IAAIgF,KAAK,KAAKhF,SAAd,EAAyB;MACvB;MACA;MACA;MACA;MACA,MAAMgF,KAAN;IACD,CAxCyD;;IA2C1D,IAAI2W,SAAS,GAAG,CAAC5F,MAAM,CAACtD,UAAR,EAAoBsD,MAAM,CAACvD,UAA3B,EAAuCiJ,IAAvC,CAA6C/L,CAAD,IAAOA,CAAnD,CAAhB;IACA,OAAOhG,MAAM,CAACgS,MAAP,CAAcC,SAAS,IAAI,EAA3B,CAA+B,EAA/B,CAAP;EACD;EAED,eAAeL,SAAf,CACEhG,OADF,EAEEzU,QAFF,EAGEgG,OAHF,EAIE+U,UAJF,EAIqC;IAEnC5V,SAAS,CACPsP,OAAO,CAAC7G,MADD,EAEP,sEAFO,CAAT;IAKA,IAAI;MACF,IAAImM,kBAAkB,CAAC1U,GAAnB,CAAuBoP,OAAO,CAAC0F,MAA/B,CAAJ,EAA4C;QAC1C,IAAIjF,MAAM,GAAG,MAAM8F,MAAM,CACvBvG,OADuB,EAEvBzO,OAFuB,EAGvB+U,UAAU,IAAI3F,cAAc,CAACpP,OAAD,EAAUhG,QAAV,CAHL,EAIvB+a,UAAU,IAAI,IAJS,CAAzB;QAMA,OAAO7F,MAAP;MACD;MAED,IAAIA,MAAM,GAAG,MAAM+F,aAAa,CAACxG,OAAD,EAAUzO,OAAV,EAAmB+U,UAAnB,CAAhC;MACA,OAAO7F,MAAM,YAAYrI,QAAlB,GACHqI,MADG,gBAGEA,MAHF;QAIDtD,UAAU,EAAE,IAJX;QAKD4I,aAAa,EAAE;OALrB;KAZF,CAmBE,OAAOxX,CAAP,EAAU;MACV;MACA;MACA;MACA,IAAIkY,oBAAoB,CAAClY,CAAD,CAAxB,EAA6B;QAC3B,IAAIA,CAAC,CAACmY,IAAF,KAAW3W,UAAU,CAACL,KAAtB,IAA+B,CAACiX,kBAAkB,CAACpY,CAAC,CAACqY,QAAH,CAAtD,EAAoE;UAClE,MAAMrY,CAAC,CAACqY,QAAR;QACD;QACD,OAAOrY,CAAC,CAACqY,QAAT;MACD,CATS;MAWV;;MACA,IAAID,kBAAkB,CAACpY,CAAD,CAAtB,EAA2B;QACzB,OAAOA,CAAP;MACD;MACD,MAAMA,CAAN;IACD;EACF;EAED,eAAegY,MAAf,CACEvG,OADF,EAEEzO,OAFF,EAGEmP,WAHF,EAIEmG,cAJF,EAIyB;IAEvB,IAAIpG,MAAJ;IACA,IAAI,CAACC,WAAW,CAACzQ,KAAZ,CAAkBpF,MAAvB,EAA+B;MAC7B,IAAIgc,cAAJ,EAAoB;QAClB,MAAMX,yBAAyB,CAAC,IAAD,EAAO;UACpClO,MAAM,EAAE,GAD4B;UAEpCkD,UAAU,EAAE;QAFwB,CAAP,CAA/B;MAID;MACDuF,MAAM,GAAGG,yBAAyB,CAACZ,OAAO,CAACnS,GAAT,CAAlC;IACD,CARD,MAQO;MACL4S,MAAM,GAAG,MAAMI,kBAAkB,CAC/B,QAD+B,EAE/Bb,OAF+B,EAG/BU,WAH+B,EAI/BnP,OAJ+B,EAK/B7G,SAL+B;MAAA;MAM/B,IAN+B,EAO/Bmc,cAP+B,CAAjC;MAUA,IAAI7G,OAAO,CAAC7G,MAAR,CAAeW,OAAnB,EAA4B;QAC1B,IAAI4L,MAAM,GAAGmB,cAAc,GAAG,YAAH,GAAkB,OAA7C;QACA,MAAM,IAAIvY,KAAJ,CAAaoX,MAAb,GAAN;MACD;IACF;IAED,IAAI5E,gBAAgB,CAACL,MAAD,CAApB,EAA8B;MAC5B;MACA;MACA;MACA;MACA,MAAM,IAAIrI,QAAJ,CAAa,IAAb,EAAmB;QACvBJ,MAAM,EAAEyI,MAAM,CAACzI,MADQ;QAEvBC,OAAO,EAAE;UACP6O,QAAQ,EAAErG,MAAM,CAAClV;QADV;MAFc,CAAnB,CAAN;IAMD;IAED,IAAI4V,gBAAgB,CAACV,MAAD,CAApB,EAA8B;MAC5B,MAAM,IAAInS,KAAJ,CAAU,qCAAV,CAAN;IACD;IAED,IAAIuY,cAAJ,EAAoB;MAClB;MACA;MACA,IAAI5F,aAAa,CAACR,MAAD,CAAjB,EAA2B;QACzB,IAAIS,aAAa,GAAGf,mBAAmB,CAAC5O,OAAD,EAAUmP,WAAW,CAACzQ,KAAZ,CAAkBO,EAA5B,CAAvC;QACA,OAAO;UACLe,OAAO,EAAE,CAACmP,WAAD,CADJ;UAELxD,UAAU,EAAE,EAFP;UAGLC,UAAU,EAAE,IAHP;UAILC,MAAM,EAAE;YACN,CAAC8D,aAAa,CAACjR,KAAd,CAAoBO,EAArB,GAA0BiQ,MAAM,CAAC/Q;WAL9B;UAOL;UACA;UACAmW,UAAU,EAAE,GATP;UAULC,aAAa,EAAE,EAVV;UAWLC,aAAa,EAAE;SAXjB;MAaD;MAED,OAAO;QACLxU,OAAO,EAAE,CAACmP,WAAD,CADJ;QAELxD,UAAU,EAAE,EAFP;QAGLC,UAAU,EAAE;UAAE,CAACuD,WAAW,CAACzQ,KAAZ,CAAkBO,EAAnB,GAAwBiQ,MAAM,CAAC5I;SAHxC;QAILuF,MAAM,EAAE,IAJH;QAKL;QACA;QACAyI,UAAU,EAAE,GAPP;QAQLC,aAAa,EAAE,EARV;QASLC,aAAa,EAAE;OATjB;IAWD;IAED,IAAI9E,aAAa,CAACR,MAAD,CAAjB,EAA2B;MACzB;MACA;MACA,IAAIS,aAAa,GAAGf,mBAAmB,CAAC5O,OAAD,EAAUmP,WAAW,CAACzQ,KAAZ,CAAkBO,EAA5B,CAAvC;MACA,IAAIuW,OAAO,GAAG,MAAMP,aAAa,CAACxG,OAAD,EAAUzO,OAAV,EAAmB7G,SAAnB,EAA8B;QAC7D,CAACwW,aAAa,CAACjR,KAAd,CAAoBO,EAArB,GAA0BiQ,MAAM,CAAC/Q;OADF,CAAjC,CAJyB;;MASzB,oBACKqX,OADL;QAEElB,UAAU,EAAE1K,oBAAoB,CAACsF,MAAM,CAAC/Q,KAAR,CAApB,GACR+Q,MAAM,CAAC/Q,KAAP,CAAasI,MADL,GAER,GAJN;QAKEmF,UAAU,EAAE,IALd;QAME4I,aAAa,EACPtF,mBAAM,CAACxI,OAAP,GAAiB;UAAE,CAACyI,WAAW,CAACzQ,KAAZ,CAAkBO,EAAnB,GAAwBiQ,MAAM,CAACxI;QAAjC,CAAjB,GAA8D,EADvD;MANf;IAUD;IAED,IAAI8O,OAAO,GAAG,MAAMP,aAAa,CAACxG,OAAD,EAAUzO,OAAV,CAAjC;IAEA,oBACKwV,OADL,EAGMtG,MAAM,CAACoF,UAAP,GAAoB;MAAEA,UAAU,EAAEpF,MAAM,CAACoF;IAArB,CAApB,GAAwD,EAH9D;MAIE1I,UAAU,EAAE;QACV,CAACuD,WAAW,CAACzQ,KAAZ,CAAkBO,EAAnB,GAAwBiQ,MAAM,CAAC5I;OALnC;MAOEkO,aAAa,EACPtF,mBAAM,CAACxI,OAAP,GAAiB;QAAE,CAACyI,WAAW,CAACzQ,KAAZ,CAAkBO,EAAnB,GAAwBiQ,MAAM,CAACxI;MAAjC,CAAjB,GAA8D,EADvD;IAPf;EAWD;EAED,eAAeuO,aAAf,CACExG,OADF,EAEEzO,OAFF,EAGE+U,UAHF,EAIE/F,kBAJF,EAIgC;IAK9B,IAAIsG,cAAc,GAAGP,UAAU,IAAI,IAAnC;IACA,IAAIxD,cAAc,GAAGwD,UAAU,GAC3B,CAACA,UAAD,CAD2B,GAE3BU,6BAA6B,CAC3BzV,OAD2B,EAE3B6C,MAAM,CAACkQ,IAAP,CAAY/D,kBAAkB,IAAI,EAAlC,EAAsC,CAAtC,CAF2B,CAFjC;IAMA,IAAIa,aAAa,GAAG0B,cAAc,CAACvP,MAAf,CAAuBkJ,CAAD,IAAOA,CAAC,CAACxM,KAAF,CAAQyM,MAArC,CAApB,CAZ8B;;IAe9B,IAAI0E,aAAa,CAACxW,MAAd,KAAyB,CAA7B,EAAgC;MAC9B,OAAO;QACL2G,OADK;QAEL2L,UAAU,EAAE,EAFP;QAGLE,MAAM,EAAEmD,kBAAkB,IAAI,IAHzB;QAILsF,UAAU,EAAE,GAJP;QAKLC,aAAa,EAAE;OALjB;IAOD;IAED,IAAIlE,OAAO,GAAG,MAAM/I,OAAO,CAACmL,GAAR,CAAY,CAC9B,GAAG5C,aAAa,CAAC/W,GAAd,CAAmB6J,KAAD,IACnB2M,kBAAkB,CAChB,QADgB,EAEhBb,OAFgB,EAGhB9L,KAHgB,EAIhB3C,OAJgB,EAKhB7G,SALgB;IAAA;IAMhB,IANgB,EAOhBmc,cAPgB,CADjB,CAD2B,CAAZ,CAApB;IAcA,IAAI7G,OAAO,CAAC7G,MAAR,CAAeW,OAAnB,EAA4B;MAC1B,IAAI4L,MAAM,GAAGmB,cAAc,GAAG,YAAH,GAAkB,OAA7C;MACA,MAAM,IAAIvY,KAAJ,CAAaoX,MAAb,GAAN;IACD,CA1C6B;IA6C9B;;IACA9D,OAAO,CAAChQ,OAAR,CAAiB6O,MAAD,IAAW;MACzB,IAAIU,gBAAgB,CAACV,MAAD,CAApB,EAA8B;QAC5BA,MAAM,CAAC2B,YAAP,CAAoBlI,MAApB;MACD;IACF,CAJD,EA9C8B;;IAqD9B,IAAI6M,OAAO,GAAGE,sBAAsB,CAClC1V,OADkC,EAElC6P,aAFkC,EAGlCQ,OAHkC,EAIlCrB,kBAJkC,CAApC;IAOA,oBACKwG,OADL;MAEExV;IAFF;EAID;EAED,SAAS2U,yBAAT,CACEgB,IADF,EAEEpP,IAFF,EAEoB;IAElB,OAAO,IAAIM,QAAJ,CAAa8O,IAAb,eACFpP,IADE;MAELG,OAAO,eACFH,IAAI,CAACG,OADH;QAEL,sBAAwB;MAFnB;KAFT;EAOD;EAED,OAAO;IACL6D,UADK;IAEL2J,KAFK;IAGLQ;GAHF;AAKD;AAID;AACA;AACA;;AAEA;;;AAGG;;SACakB,0BACdhX,QACA4W,SACArX,OAAU;EAEV,IAAI0X,UAAU,gBACTL,OADS;IAEZlB,UAAU,EAAE,GAFA;IAGZzI,MAAM,EAAE;MACN,CAAC2J,OAAO,CAACM,0BAAR,IAAsClX,MAAM,CAAC,CAAD,CAAN,CAAUK,EAAjD,GAAsDd;IADhD;GAHV;EAOA,OAAO0X,UAAP;AACD;AAGD;;AACA,SAAS/H,wBAAT,CACEhU,EADF,EAEE8T,IAFF,EAGEmI,SAHF,EAGmB;EAAA,IAAjBA,SAAiB;IAAjBA,SAAiB,GAAL,KAAK;EAAA;EAMjB,IAAIxY,IAAI,GAAG,OAAOzD,EAAP,KAAc,QAAd,GAAyBA,EAAzB,GAA8BW,UAAU,CAACX,EAAD,CAAnD,CANiB;;EASjB,IAAI,CAAC8T,IAAD,IAAU,EAAE,gBAAgBA,IAAlB,KAA2B,EAAE,cAAcA,IAAhB,CAAzC,EAAiE;IAC/D,OAAO;MAAErQ;KAAT;EACD,CAXgB;;EAcjB,IAAIqQ,IAAI,CAAC9D,UAAL,IAAmB,IAAnB,IAA2B8D,IAAI,CAAC9D,UAAL,KAAoB,KAAnD,EAA0D;IACxD,OAAO;MACLvM,IADK;MAELsQ,UAAU,EAAE;QACV/D,UAAU,EAAE8D,IAAI,CAAC9D,UADP;QAEVC,UAAU,EAAEiM,iBAAiB,CAACzY,IAAD,CAFnB;QAGVyM,WAAW,EACR4D,IAAI,IAAIA,IAAI,CAAC5D,WAAd,IAA8B,mCAJtB;QAKVC,QAAQ,EAAE2D,IAAI,CAAC3D;MALL;KAFd;EAUD,CAzBgB;;EA4BjB,IAAI,CAAC2D,IAAI,CAAC3D,QAAV,EAAoB;IAClB,OAAO;MAAE1M;KAAT;EACD,CA9BgB;;EAiCjB,IAAIC,UAAU,GAAG1B,SAAS,CAACyB,IAAD,CAA1B;EACA,IAAI;IACF,IAAI0Y,YAAY,GAAGC,6BAA6B,CAACtI,IAAI,CAAC3D,QAAN,CAAhD,CADE;IAGF;IACA;;IACA,IACE8L,SAAS,IACTvY,UAAU,CAAChC,MADX,IAEA2a,kBAAkB,CAAC3Y,UAAU,CAAChC,MAAZ,CAHpB,EAIE;MACAya,YAAY,CAACG,MAAb,CAAoB,OAApB,EAA6B,EAA7B;IACD;IACD5Y,UAAU,CAAChC,MAAX,SAAwBya,YAAxB;GAZF,CAaE,OAAOjZ,CAAP,EAAU;IACV,OAAO;MACLO,IADK;MAELY,KAAK,EAAE,IAAIuL,aAAJ,CACL,GADK,EAEL,aAFK,EAGL,0CAHK;KAFT;EAQD;EAED,OAAO;IAAEnM,IAAI,EAAE9C,UAAU,CAAC+C,UAAD;GAAzB;AACD;AAED,SAASmT,iBAAT,CACEzX,KADF,EAEEuQ,QAFF,EAE0B;EAExB,IAAI;IAAEK,UAAF;IAAcC,UAAd;IAA0BC,WAA1B;IAAuCC;GAAa/Q,QAAK,CAACqS,UAA9D;EACA,IAAIA,UAAU,GAAgC;IAC5CrS,KAAK,EAAE,SADqC;IAE5Cc,QAAQ,EAAEC,cAAc,CAACf,KAAK,CAACc,QAAP,EAAiByP,QAAQ,CAACzP,QAA1B,CAFoB;IAG5C8P,UAAU,EAAEA,UAAU,IAAI3Q,SAHkB;IAI5C4Q,UAAU,EAAEA,UAAU,IAAI5Q,SAJkB;IAK5C6Q,WAAW,EAAEA,WAAW,IAAI7Q,SALgB;IAM5C8Q,QAAQ,EAAEA,QAAQ,IAAI9Q;GANxB;EAQA,OAAOoS,UAAP;AACD;AAGD;;AACA,SAASkK,6BAAT,CACEzV,OADF,EAEEqW,UAFF,EAEqB;EAEnB,IAAIC,eAAe,GAAGtW,OAAtB;EACA,IAAIqW,UAAJ,EAAgB;IACd,IAAIrd,KAAK,GAAGgH,OAAO,CAACuW,SAAR,CAAmBrL,CAAD,IAAOA,CAAC,CAACxM,KAAF,CAAQO,EAAR,KAAeoX,UAAxC,CAAZ;IACA,IAAIrd,KAAK,IAAI,CAAb,EAAgB;MACdsd,eAAe,GAAGtW,OAAO,CAACvD,KAAR,CAAc,CAAd,EAAiBzD,KAAjB,CAAlB;IACD;EACF;EACD,OAAOsd,eAAP;AACD;AAED,SAASvG,gBAAT,CACE7W,KADF,EAEE8G,OAFF,EAGE6N,UAHF,EAIE7T,QAJF,EAKEqS,sBALF,EAMEC,uBANF,EAOEC,qBAPF,EAQEoC,iBARF,EASEZ,YATF,EAUElB,gBAVF,EAUgD;EAE9C,IAAI8E,YAAY,GAAG5D,YAAY,GAC3BlL,MAAM,CAACgS,MAAP,CAAc9G,YAAd,CAA4B,EAA5B,CAD2B,GAE3BY,iBAAiB,GACjB9L,MAAM,CAACgS,MAAP,CAAclG,iBAAd,CAAiC,EAAjC,CADiB,GAEjB,IAJJ,CAF8C;;EAS9C,IAAI0H,UAAU,GAAGtI,YAAY,GAAGlL,MAAM,CAACkQ,IAAP,CAAYhF,YAAZ,EAA0B,CAA1B,CAAH,GAAkC5U,SAA/D;EACA,IAAImd,eAAe,GAAGb,6BAA6B,CAACzV,OAAD,EAAUqW,UAAV,CAAnD;EACA,IAAIG,iBAAiB,GAAGF,eAAe,CAACtU,MAAhB,CACtB,CAACW,KAAD,EAAQ3J,KAAR,KACE2J,KAAK,CAACjE,KAAN,CAAYyM,MAAZ,IAAsB,IAAtB,KACCsL,WAAW,CAACvd,KAAK,CAACyS,UAAP,EAAmBzS,KAAK,CAAC8G,OAAN,CAAchH,KAAd,CAAnB,EAAyC2J,KAAzC,CAAX;EAAA;EAEC2J,uBAAuB,CAACvK,IAAxB,CAA8B9C,EAAD,IAAQA,EAAE,KAAK0D,KAAK,CAACjE,KAAN,CAAYO,EAAxD,CAFD,IAGCyX,sBAAsB,CACpBxd,KAAK,CAACc,QADc,EAEpBd,KAAK,CAAC8G,OAAN,CAAchH,KAAd,CAFoB,EAGpB6U,UAHoB,EAIpB7T,QAJoB,EAKpB2I,KALoB,EAMpB0J,sBANoB,EAOpBsF,YAPoB,CAJxB,CAFoB,CAAxB,CAX8C;;EA6B9C,IAAI7B,oBAAoB,GAA0B,EAAlD;EACAjD,gBAAgB,IACdA,gBAAgB,CAACxM,OAAjB,CAAyB,SAA8BtG,GAA9B,KAAqC;IAAA,IAApC,CAACqC,IAAD,EAAOuG,KAAP,EAAcgQ,YAAd,CAAoC;;IAC5D;IACA,IAAIpG,qBAAqB,CAAC5G,QAAtB,CAA+B5L,GAA/B,CAAJ,EAAyC;MACvC+V,oBAAoB,CAACnV,IAArB,CAA0B,CAACZ,GAAD,EAAMqC,IAAN,EAAYuG,KAAZ,EAAmBgQ,YAAnB,CAA1B;KADF,MAEO,IAAItG,sBAAJ,EAA4B;MACjC,IAAIsK,gBAAgB,GAAGD,sBAAsB,CAC3Cta,IAD2C,EAE3CuG,KAF2C,EAG3CkL,UAH2C,EAI3CzR,IAJ2C,EAK3CuG,KAL2C,EAM3C0J,sBAN2C,EAO3CsF,YAP2C,CAA7C;MASA,IAAIgF,gBAAJ,EAAsB;QACpB7G,oBAAoB,CAACnV,IAArB,CAA0B,CAACZ,GAAD,EAAMqC,IAAN,EAAYuG,KAAZ,EAAmBgQ,YAAnB,CAA1B;MACD;IACF;EACF,CAlBD,CADF;EAqBA,OAAO,CAAC6D,iBAAD,EAAoB1G,oBAApB,CAAP;AACD;AAED,SAAS2G,WAAT,CACEG,iBADF,EAEEC,YAFF,EAGElU,KAHF,EAG+B;EAE7B,IAAImU,KAAK;EAAA;EAEP,CAACD,YAAD;EAAA;EAEAlU,KAAK,CAACjE,KAAN,CAAYO,EAAZ,KAAmB4X,YAAY,CAACnY,KAAb,CAAmBO,EAJxC,CAF6B;EAS7B;;EACA,IAAI8X,aAAa,GAAGH,iBAAiB,CAACjU,KAAK,CAACjE,KAAN,CAAYO,EAAb,CAAjB,KAAsC9F,SAA1D,CAV6B;;EAa7B,OAAO2d,KAAK,IAAIC,aAAhB;AACD;AAED,SAASC,kBAAT,CACEH,YADF,EAEElU,KAFF,EAE+B;EAE7B,IAAIsU,WAAW,GAAGJ,YAAY,CAACnY,KAAb,CAAmBnB,IAArC;EACA;IAAA;IAEEsZ,YAAY,CAAC3c,QAAb,KAA0ByI,KAAK,CAACzI,QAAhC;IAAA;IAEA;IACC+c,WAAW,IACVA,WAAW,CAACjT,QAAZ,CAAqB,GAArB,CADD,IAEC6S,YAAY,CAAC/T,MAAb,CAAoB,GAApB,MAA6BH,KAAK,CAACG,MAAN,CAAa,GAAb;EAAA;AAElC;AAED,SAAS4T,sBAAT,CACEQ,eADF,EAEEL,YAFF,EAGEhJ,UAHF,EAIE7T,QAJF,EAKE2I,KALF,EAME0J,sBANF,EAOEsF,YAPF,EAOsC;EAEpC,IAAIwF,UAAU,GAAGzZ,SAAS,CAACwZ,eAAD,CAA1B;EACA,IAAIE,aAAa,GAAGP,YAAY,CAAC/T,MAAjC;EACA,IAAIuU,OAAO,GAAG3Z,SAAS,CAAC1D,QAAD,CAAvB;EACA,IAAIsd,UAAU,GAAG3U,KAAK,CAACG,MAAvB,CALoC;EAQpC;EACA;EACA;EACA;EACA;;EACA,IAAIyU,uBAAuB,GACzBP,kBAAkB,CAACH,YAAD,EAAelU,KAAf,CAAlB;EAAA;EAEAwU,UAAU,CAACha,QAAX,OAA0Bka,OAAO,CAACla,QAAR,EAF1B;EAAA;EAIAga,UAAU,CAAC3b,MAAX,KAAsB6b,OAAO,CAAC7b,MAJ9B;EAAA;EAMA6Q,sBAPF;EASA,IAAI1J,KAAK,CAACjE,KAAN,CAAYiY,gBAAhB,EAAkC;IAChC,IAAIa,WAAW,GAAG7U,KAAK,CAACjE,KAAN,CAAYiY,gBAAZ;MAChBQ,UADgB;MAEhBC,aAFgB;MAGhBC,OAHgB;MAIhBC;IAJgB,GAKbzJ,UALa;MAMhB8D,YANgB;MAOhB4F;KAPF;IASA,IAAI,OAAOC,WAAP,KAAuB,SAA3B,EAAsC;MACpC,OAAOA,WAAP;IACD;EACF;EAED,OAAOD,uBAAP;AACD;AAED,eAAejI,kBAAf,CACE6F,IADF,EAEE1G,OAFF,EAGE9L,KAHF,EAIE3C,OAJF,EAKEL,QALF,EAME8X,eANF,EAOEnC,cAPF,EAOiC;EAAA,IAD/BmC,eAC+B;IAD/BA,eAC+B,GADJ,KACI;EAAA;EAAA,IAA/BnC,cAA+B;IAA/BA,cAA+B,GAAL,KAAK;EAAA;EAE/B,IAAIoC,UAAJ;EACA,IAAIxI,MAAJ,CAH+B;;EAM/B,IAAI9H,MAAJ;EACA,IAAIC,YAAY,GAAG,IAAIC,OAAJ,CAAY,CAACpE,CAAD,EAAIqE,CAAJ,KAAWH,MAAM,GAAGG,CAAhC,CAAnB;EACA,IAAIoQ,QAAQ,GAAG,MAAMvQ,MAAM,EAA3B;EACAqH,OAAO,CAAC7G,MAAR,CAAetJ,gBAAf,CAAgC,OAAhC,EAAyCqZ,QAAzC;EAEA,IAAI;IACF,IAAIC,OAAO,GAAGjV,KAAK,CAACjE,KAAN,CAAYyW,IAAZ,CAAd;IACAhW,SAAS,CACPyY,OADO,0BAEezC,IAFf,yBAEsCxS,KAAK,CAACjE,KAAN,CAAYO,EAFlD,GAAT;IAKAiQ,MAAM,GAAG,MAAM5H,OAAO,CAACW,IAAR,CAAa,CAC1B2P,OAAO,CAAC;MAAEnJ,OAAF;MAAW3L,MAAM,EAAEH,KAAK,CAACG;IAAzB,CAAD,CADmB,EAE1BuE,YAF0B,CAAb,CAAf;GAPF,CAWE,OAAOrK,CAAP,EAAU;IACV0a,UAAU,GAAGlZ,UAAU,CAACL,KAAxB;IACA+Q,MAAM,GAAGlS,CAAT;EACD,CAdD,SAcU;IACRyR,OAAO,CAAC7G,MAAR,CAAerJ,mBAAf,CAAmC,OAAnC,EAA4CoZ,QAA5C;EACD;EAED,IAAIzI,MAAM,YAAYrI,QAAtB,EAAgC;IAC9B,IAAIJ,MAAM,GAAGyI,MAAM,CAACzI,MAApB,CAD8B;;IAI9B,IAAIA,MAAM,IAAI,GAAV,IAAiBA,MAAM,IAAI,GAA/B,EAAoC;MAClC,IAAIzM,QAAQ,GAAGkV,MAAM,CAACxI,OAAP,CAAe4B,GAAf,CAAmB,UAAnB,CAAf;MACAnJ,SAAS,CACPnF,QADO,EAEP,4EAFO,CAAT,CAFkC;;MAQlC,IAAI6d,aAAa,GAAG7X,OAAO,CAACvD,KAAR,CAAc,CAAd,EAAiBuD,OAAO,CAACxD,OAAR,CAAgBmG,KAAhB,IAAyB,CAA1C,CAApB;MACA,IAAI6C,cAAc,GAAGH,0BAA0B,CAACwS,aAAD,CAA1B,CAA0C/e,GAA1C,CAClB6J,KAAD,IAAWA,KAAK,CAACI,YADE,CAArB;MAGA,IAAI+U,WAAW,GAAGpa,SAAS,CAAC+Q,OAAO,CAACnS,GAAT,CAAT,CAAuBpC,QAAzC;MACA,IAAI6d,gBAAgB,GAAGzS,SAAS,CAACtL,QAAD,EAAWwL,cAAX,EAA2BsS,WAA3B,CAAhC;MACA3Y,SAAS,CACP1E,UAAU,CAACsd,gBAAD,CADH,4CAEiC7I,MAAM,CAACxI,OAAP,CAAe4B,GAAf,CAAmB,UAAnB,CAFjC,CAAT,CAdkC;;MAoBlC,IAAI3I,QAAJ,EAAc;QACZ,IAAIpC,IAAI,GAAGwa,gBAAgB,CAAC7d,QAA5B;QACA6d,gBAAgB,CAAC7d,QAAjB,GACEqD,IAAI,KAAK,GAAT,GAAeoC,QAAf,GAA0BgB,SAAS,CAAC,CAAChB,QAAD,EAAWpC,IAAX,CAAD,CADrC;MAED;MAEDvD,QAAQ,GAAGS,UAAU,CAACsd,gBAAD,CAArB,CA1BkC;MA6BlC;MACA;MACA;;MACA,IAAIN,eAAJ,EAAqB;QACnBvI,MAAM,CAACxI,OAAP,CAAeE,GAAf,CAAmB,UAAnB,EAA+B5M,QAA/B;QACA,MAAMkV,MAAN;MACD;MAED,OAAO;QACLiG,IAAI,EAAE3W,UAAU,CAACiL,QADZ;QAELhD,MAFK;QAGLzM,QAHK;QAILgU,UAAU,EAAEkB,MAAM,CAACxI,OAAP,CAAe4B,GAAf,CAAmB,oBAAnB,CAA6C;OAJ3D;IAMD,CA/C6B;IAkD9B;IACA;;IACA,IAAIgN,cAAJ,EAAoB;MAClB;MACA,MAAM;QACJH,IAAI,EAAEuC,UAAU,IAAIlZ,UAAU,CAAC8H,IAD3B;QAEJ+O,QAAQ,EAAEnG;OAFZ;IAID;IAED,IAAI5I,IAAJ;IACA,IAAI0R,WAAW,GAAG9I,MAAM,CAACxI,OAAP,CAAe4B,GAAf,CAAmB,cAAnB,CAAlB;IACA,IAAI0P,WAAW,IAAIA,WAAW,CAACtX,UAAZ,CAAuB,kBAAvB,CAAnB,EAA+D;MAC7D4F,IAAI,GAAG,MAAM4I,MAAM,CAAC7I,IAAP,EAAb;IACD,CAFD,MAEO;MACLC,IAAI,GAAG,MAAM4I,MAAM,CAAC+I,IAAP,EAAb;IACD;IAED,IAAIP,UAAU,KAAKlZ,UAAU,CAACL,KAA9B,EAAqC;MACnC,OAAO;QACLgX,IAAI,EAAEuC,UADD;QAELvZ,KAAK,EAAE,IAAIuL,aAAJ,CAAkBjD,MAAlB,EAA0ByI,MAAM,CAACvF,UAAjC,EAA6CrD,IAA7C,CAFF;QAGLI,OAAO,EAAEwI,MAAM,CAACxI;OAHlB;IAKD;IAED,OAAO;MACLyO,IAAI,EAAE3W,UAAU,CAAC8H,IADZ;MAELA,IAFK;MAGLgO,UAAU,EAAEpF,MAAM,CAACzI,MAHd;MAILC,OAAO,EAAEwI,MAAM,CAACxI;KAJlB;EAMD;EAED,IAAIgR,UAAU,KAAKlZ,UAAU,CAACL,KAA9B,EAAqC;IACnC,OAAO;MAAEgX,IAAI,EAAEuC,UAAR;MAAoBvZ,KAAK,EAAE+Q;KAAlC;EACD;EAED,IAAIA,MAAM,YAAYnI,YAAtB,EAAoC;IAClC,OAAO;MAAEoO,IAAI,EAAE3W,UAAU,CAAC0Z,QAAnB;MAA6BrH,YAAY,EAAE3B;KAAlD;EACD;EAED,OAAO;IAAEiG,IAAI,EAAE3W,UAAU,CAAC8H,IAAnB;IAAyBA,IAAI,EAAE4I;GAAtC;AACD;AAED,SAASR,aAAT,CACE1U,QADF,EAEE4N,MAFF,EAGEiG,UAHF,EAGyB;EAEvB,IAAIvR,GAAG,GAAGoB,SAAS,CAACsY,iBAAiB,CAAChc,QAAD,CAAlB,CAAT,CAAuCmD,QAAvC,EAAV;EACA,IAAIoJ,IAAI,GAAgB;IAAEqB;GAA1B;EAEA,IAAIiG,UAAJ,EAAgB;IACd,IAAI;MAAE/D,UAAF;MAAcE,WAAd;MAA2BC;IAA3B,IAAwC4D,UAA5C;IACAtH,IAAI,CAAC4N,MAAL,GAAcrK,UAAU,CAACqO,WAAX,EAAd;IACA5R,IAAI,CAACoP,IAAL,GACE3L,WAAW,KAAK,mCAAhB,GACIkM,6BAA6B,CAACjM,QAAD,CADjC,GAEIA,QAHN;EAID,CAZsB;;EAevB,OAAO,IAAImO,OAAJ,CAAY9b,GAAZ,EAAiBiK,IAAjB,CAAP;AACD;AAED,SAAS2P,6BAAT,CAAuCjM,QAAvC,EAAyD;EACvD,IAAIgM,YAAY,GAAG,IAAIoC,eAAJ,EAAnB;EAEA,KAAK,IAAI,CAACte,GAAD,EAAMoK,KAAN,CAAT,IAAyB8F,QAAQ,CAACpR,OAAT,EAAzB,EAA6C;IAC3CsG,SAAS,CACP,OAAOgF,KAAP,KAAiB,QADV,EAEP,qFACE,2CAHK,CAAT;IAKA8R,YAAY,CAACG,MAAb,CAAoBrc,GAApB,EAAyBoK,KAAzB;EACD;EAED,OAAO8R,YAAP;AACD;AAED,SAASP,sBAAT,CACE1V,OADF,EAEE6P,aAFF,EAGEQ,OAHF,EAIEtC,YAJF,EAKEjB,eALF,EAK6C;EAO3C;EACA,IAAInB,UAAU,GAA8B,EAA5C;EACA,IAAIE,MAAM,GAAiC,IAA3C;EACA,IAAIyI,UAAJ;EACA,IAAIgE,UAAU,GAAG,KAAjB;EACA,IAAI/D,aAAa,GAA4B,EAA7C,CAZ2C;;EAe3ClE,OAAO,CAAChQ,OAAR,CAAgB,CAAC6O,MAAD,EAASlW,KAAT,KAAkB;IAChC,IAAIiG,EAAE,GAAG4Q,aAAa,CAAC7W,KAAD,CAAb,CAAqB0F,KAArB,CAA2BO,EAApC;IACAE,SAAS,CACP,CAACoQ,gBAAgB,CAACL,MAAD,CADV,EAEP,qDAFO,CAAT;IAIA,IAAIQ,aAAa,CAACR,MAAD,CAAjB,EAA2B;MACzB;MACA;MACA,IAAIS,aAAa,GAAGf,mBAAmB,CAAC5O,OAAD,EAAUf,EAAV,CAAvC;MACA,IAAId,KAAK,GAAG+Q,MAAM,CAAC/Q,KAAnB,CAJyB;MAMzB;MACA;;MACA,IAAI4P,YAAJ,EAAkB;QAChB5P,KAAK,GAAG0E,MAAM,CAACgS,MAAP,CAAc9G,YAAd,EAA4B,CAA5B,CAAR;QACAA,YAAY,GAAG5U,SAAf;MACD;MACD0S,MAAM,GAAGhJ,MAAM,CAACzE,MAAP,CAAcyN,MAAM,IAAI,EAAxB,EAA4B;QACnC,CAAC8D,aAAa,CAACjR,KAAd,CAAoBO,EAArB,GAA0Bd;OADnB,CAAT,CAZyB;MAgBzB;;MACA,IAAI,CAACma,UAAL,EAAiB;QACfA,UAAU,GAAG,IAAb;QACAhE,UAAU,GAAG1K,oBAAoB,CAACsF,MAAM,CAAC/Q,KAAR,CAApB,GACT+Q,MAAM,CAAC/Q,KAAP,CAAasI,MADJ,GAET,GAFJ;MAGD;MACD,IAAIyI,MAAM,CAACxI,OAAX,EAAoB;QAClB6N,aAAa,CAACtV,EAAD,CAAb,GAAoBiQ,MAAM,CAACxI,OAA3B;MACD;IACF,CA1BD,MA0BO,IAAIkJ,gBAAgB,CAACV,MAAD,CAApB,EAA8B;MACnCpC,eAAe,IAAIA,eAAe,CAAClG,GAAhB,CAAoB3H,EAApB,EAAwBiQ,MAAM,CAAC2B,YAA/B,CAAnB;MACAlF,UAAU,CAAC1M,EAAD,CAAV,GAAiBiQ,MAAM,CAAC2B,YAAP,CAAoBvK,IAArC,CAFmC;IAIpC,CAJM,MAIA;MACLqF,UAAU,CAAC1M,EAAD,CAAV,GAAiBiQ,MAAM,CAAC5I,IAAxB,CADK;MAGL;;MACA,IACE4I,MAAM,CAACoF,UAAP,IAAqB,IAArB,IACApF,MAAM,CAACoF,UAAP,KAAsB,GADtB,IAEA,CAACgE,UAHH,EAIE;QACAhE,UAAU,GAAGpF,MAAM,CAACoF,UAApB;MACD;MACD,IAAIpF,MAAM,CAACxI,OAAX,EAAoB;QAClB6N,aAAa,CAACtV,EAAD,CAAb,GAAoBiQ,MAAM,CAACxI,OAA3B;MACD;IACF;EACF,CAnDD,EAf2C;EAqE3C;;EACA,IAAIqH,YAAJ,EAAkB;IAChBlC,MAAM,GAAGkC,YAAT;EACD;EAED,OAAO;IACLpC,UADK;IAELE,MAFK;IAGLyI,UAAU,EAAEA,UAAU,IAAI,GAHrB;IAILC;GAJF;AAMD;AAED,SAAS3D,iBAAT,CACE1X,KADF,EAEE8G,OAFF,EAGE6P,aAHF,EAIEQ,OAJF,EAKEtC,YALF,EAME+B,oBANF,EAOES,cAPF,EAQEzD,eARF,EAQ4C;EAK1C,IAAI;IAAEnB,UAAF;IAAcE;EAAd,IAAyB6J,sBAAsB,CACjD1V,OADiD,EAEjD6P,aAFiD,EAGjDQ,OAHiD,EAIjDtC,YAJiD,EAKjDjB,eALiD,CAAnD,CAL0C;;EAc1C,KAAK,IAAI9T,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAG8W,oBAAoB,CAACzW,MAAjD,EAAyDL,KAAK,EAA9D,EAAkE;IAChE,IAAI,CAACe,GAAD,GAAQ4I,KAAR,IAAiBmN,oBAAoB,CAAC9W,KAAD,CAAzC;IACAmG,SAAS,CACPoR,cAAc,KAAKpX,SAAnB,IAAgCoX,cAAc,CAACvX,KAAD,CAAd,KAA0BG,SADnD,EAEP,2CAFO,CAAT;IAIA,IAAI+V,MAAM,GAAGqB,cAAc,CAACvX,KAAD,CAA3B,CANgE;;IAShE,IAAI0W,aAAa,CAACR,MAAD,CAAjB,EAA2B;MACzB,IAAIS,aAAa,GAAGf,mBAAmB,CAAC1V,KAAK,CAAC8G,OAAP,EAAgB2C,KAAK,CAACjE,KAAN,CAAYO,EAA5B,CAAvC;MACA,IAAI,EAAE4M,MAAM,IAAIA,MAAM,CAAC8D,aAAa,CAACjR,KAAd,CAAoBO,EAArB,CAAlB,CAAJ,EAAiD;QAC/C4M,MAAM,gBACDA,MADC;UAEJ,CAAC8D,aAAa,CAACjR,KAAd,CAAoBO,EAArB,GAA0BiQ,MAAM,CAAC/Q;SAFnC;MAID;MACDjF,KAAK,CAAC4S,QAAN,CAAetD,MAAf,CAAsBzO,GAAtB;IACD,CATD,MASO,IAAIwV,gBAAgB,CAACL,MAAD,CAApB,EAA8B;MACnC;MACA;MACA,MAAM,IAAInS,KAAJ,CAAU,yCAAV,CAAN;IACD,CAJM,MAIA,IAAI6S,gBAAgB,CAACV,MAAD,CAApB,EAA8B;MACnC;MACA;MACA,MAAM,IAAInS,KAAJ,CAAU,iCAAV,CAAN;IACD,CAJM,MAIA;MACL,IAAIqV,WAAW,GAA0B;QACvClZ,KAAK,EAAE,MADgC;QAEvCoN,IAAI,EAAE4I,MAAM,CAAC5I,IAF0B;QAGvCwD,UAAU,EAAE3Q,SAH2B;QAIvC4Q,UAAU,EAAE5Q,SAJ2B;QAKvC6Q,WAAW,EAAE7Q,SAL0B;QAMvC8Q,QAAQ,EAAE9Q;OANZ;MAQAD,KAAK,CAAC4S,QAAN,CAAelF,GAAf,CAAmB7M,GAAnB,EAAwBqY,WAAxB;IACD;EACF;EAED,OAAO;IAAEzG,UAAF;IAAcE;GAArB;AACD;AAED,SAAS4B,eAAT,CACE9B,UADF,EAEE6B,aAFF,EAGExN,OAHF,EAGmC;EAEjC,IAAIuY,gBAAgB,GAAQ/K,0BAAR,CAApB;EACAxN,OAAO,CAACK,OAAR,CAAiBsC,KAAD,IAAU;IACxB,IAAI1D,EAAE,GAAG0D,KAAK,CAACjE,KAAN,CAAYO,EAArB;IACA,IAAIuO,aAAa,CAACvO,EAAD,CAAb,KAAsB9F,SAAtB,IAAmCwS,UAAU,CAAC1M,EAAD,CAAV,KAAmB9F,SAA1D,EAAqE;MACnEof,gBAAgB,CAACtZ,EAAD,CAAhB,GAAuB0M,UAAU,CAAC1M,EAAD,CAAjC;IACD;GAJH;EAMA,OAAOsZ,gBAAP;AACD;AAGD;AACA;;AACA,SAAS3J,mBAAT,CACE5O,OADF,EAEEgQ,OAFF,EAEkB;EAEhB,IAAIwI,eAAe,GAAGxI,OAAO,GACzBhQ,OAAO,CAACvD,KAAR,CAAc,CAAd,EAAiBuD,OAAO,CAACuW,SAAR,CAAmBrL,CAAD,IAAOA,CAAC,CAACxM,KAAF,CAAQO,EAAR,KAAe+Q,OAAxC,CAAmD,IAApE,CADyB,GAEzB,CAAC,GAAGhQ,OAAJ,CAFJ;EAGA,OACEwY,eAAe,CAACC,OAAhB,GAA0B7D,IAA1B,CAAgC1J,CAAD,IAAOA,CAAC,CAACxM,KAAF,CAAQga,gBAAR,KAA6B,IAAnE,KACA1Y,OAAO,CAAC,CAAD,CAFT;AAID;AAED,SAAS2Y,sBAAT,CACE/Z,MADF,EAEE6H,MAFF,EAGEkD,UAHF,EAGoB;EAMlB;EACA,IAAIjL,KAAK,GAAGE,MAAM,CAACgW,IAAP,CAAarN,CAAD,IAAOA,CAAC,CAACvO,KAAF,IAAW,CAACuO,CAAC,CAAChK,IAAd,IAAsBgK,CAAC,CAAChK,IAAF,KAAW,GAApD,CAA4D;IACtE0B,EAAE,cAAYwH,MAAZ;GADJ;EAIA,OAAO;IACLzG,OAAO,EAAE,CACP;MACE8C,MAAM,EAAE,EADV;MAEE5I,QAAQ,EAAE,EAFZ;MAGE6I,YAAY,EAAE,EAHhB;MAIErE;IAJF,CADO,CADJ;IASLA,KATK;IAULP,KAAK,EAAE,IAAIuL,aAAJ,CAAkBjD,MAAlB,EAA0BkD,UAA1B,EAAsC,IAAtC;GAVT;AAYD;AAED,SAASqB,kBAAT,CAA4BpM,MAA5B,EAA6D;EAC3D,OAAO+Z,sBAAsB,CAAC/Z,MAAD,EAAS,GAAT,EAAc,WAAd,CAA7B;AACD;AAED,SAASyV,0BAAT,CAAoCzV,MAApC,EAAqE;EACnE,OAAO+Z,sBAAsB,CAAC/Z,MAAD,EAAS,GAAT,EAAc,oBAAd,CAA7B;AACD;AAED,SAASyQ,yBAAT,CAAmC9R,IAAnC,EAA0D;EACxD,IAAInB,IAAI,GAAG,OAAOmB,IAAP,KAAgB,QAAhB,GAA2BA,IAA3B,GAAkC9C,UAAU,CAAC8C,IAAD,CAAvD;EACAV,OAAO,CAACC,IAAR,CACE,0EACE,6DADF,UAEMV,IAFN,GADF;EAKA,OAAO;IACL+Y,IAAI,EAAE3W,UAAU,CAACL,KADZ;IAELA,KAAK,EAAE,IAAIuL,aAAJ,CAAkB,GAAlB,EAAuB,oBAAvB,EAA6C,EAA7C;GAFT;AAID;;AAGD,SAASgH,YAAT,CAAsBL,OAAtB,EAA2C;EACzC,KAAK,IAAIpQ,CAAC,GAAGoQ,OAAO,CAAChX,MAAR,GAAiB,CAA9B,EAAiC4G,CAAC,IAAI,CAAtC,EAAyCA,CAAC,EAA1C,EAA8C;IAC5C,IAAIiP,MAAM,GAAGmB,OAAO,CAACpQ,CAAD,CAApB;IACA,IAAIsP,gBAAgB,CAACL,MAAD,CAApB,EAA8B;MAC5B,OAAOA,MAAP;IACD;EACF;AACF;AAED,SAAS8G,iBAAT,CAA2BzY,IAA3B,EAAmC;EACjC,IAAIC,UAAU,GAAG,OAAOD,IAAP,KAAgB,QAAhB,GAA2BzB,SAAS,CAACyB,IAAD,CAApC,GAA6CA,IAA9D;EACA,OAAO9C,UAAU,cAAM+C,UAAN;IAAkB/B,IAAI,EAAE;GAAzC;AACD;AAED,SAAS+S,gBAAT,CAA0BvN,CAA1B,EAAuCC,CAAvC,EAAkD;EAChD,OACED,CAAC,CAAC/G,QAAF,KAAegH,CAAC,CAAChH,QAAjB,IAA6B+G,CAAC,CAACzF,MAAF,KAAa0F,CAAC,CAAC1F,MAA5C,IAAsDyF,CAAC,CAACxF,IAAF,KAAWyF,CAAC,CAACzF,IADrE;AAGD;AAED,SAASmU,gBAAT,CAA0BV,MAA1B,EAA4C;EAC1C,OAAOA,MAAM,CAACiG,IAAP,KAAgB3W,UAAU,CAAC0Z,QAAlC;AACD;AAED,SAASxI,aAAT,CAAuBR,MAAvB,EAAyC;EACvC,OAAOA,MAAM,CAACiG,IAAP,KAAgB3W,UAAU,CAACL,KAAlC;AACD;AAED,SAASoR,gBAAT,CAA0BL,MAA1B,EAA6C;EAC3C,OAAO,CAACA,MAAM,IAAIA,MAAM,CAACiG,IAAlB,MAA4B3W,UAAU,CAACiL,QAA9C;AACD;AAED,SAAS2L,kBAAT,CAA4BlG,MAA5B,EAAuC;EACrC,IAAI,EAAEA,MAAM,YAAYrI,QAApB,CAAJ,EAAmC;IACjC,OAAO,KAAP;EACD;EAED,IAAIJ,MAAM,GAAGyI,MAAM,CAACzI,MAApB;EACA,IAAIzM,QAAQ,GAAGkV,MAAM,CAACxI,OAAP,CAAe4B,GAAf,CAAmB,UAAnB,CAAf;EACA,OAAO7B,MAAM,IAAI,GAAV,IAAiBA,MAAM,IAAI,GAA3B,IAAkCzM,QAAQ,IAAI,IAArD;AACD;AAED,SAASkb,oBAAT,CAA8B0D,GAA9B,EAAsC;EACpC,OACEA,GAAG,IACHA,GAAG,CAACvD,QAAJ,YAAwBxO,QADxB,KAEC+R,GAAG,CAACzD,IAAJ,KAAa3W,UAAU,CAAC8H,IAAxB,IAAgC9H,UAAU,CAACL,KAF5C,CADF;AAKD;AAED,eAAeyU,sBAAf,CACEL,cADF,EAEE1C,aAFF,EAGEQ,OAHF,EAIEzI,MAJF,EAKEmO,SALF,EAMEa,iBANF,EAM+B;EAE7B,KAAK,IAAI5d,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGqX,OAAO,CAAChX,MAApC,EAA4CL,KAAK,EAAjD,EAAqD;IACnD,IAAIkW,MAAM,GAAGmB,OAAO,CAACrX,KAAD,CAApB;IACA,IAAI2J,KAAK,GAAGkN,aAAa,CAAC7W,KAAD,CAAzB;IACA,IAAI6d,YAAY,GAAGtE,cAAc,CAACqC,IAAf,CAChB1J,CAAD,IAAOA,CAAC,CAACxM,KAAF,CAAQO,EAAR,KAAe0D,KAAK,CAACjE,KAAN,CAAYO,EADjB,CAAnB;IAGA,IAAI4Z,oBAAoB,GACtBhC,YAAY,IAAI,IAAhB,IACA,CAACG,kBAAkB,CAACH,YAAD,EAAelU,KAAf,CADnB,IAEA,CAACiU,iBAAiB,IAAIA,iBAAiB,CAACjU,KAAK,CAACjE,KAAN,CAAYO,EAAb,CAAvC,MAA6D9F,SAH/D;IAKA,IAAIyW,gBAAgB,CAACV,MAAD,CAAhB,KAA6B6G,SAAS,IAAI8C,oBAA1C,CAAJ,EAAqE;MACnE;MACA;MACA;MACA,MAAMxG,mBAAmB,CAACnD,MAAD,EAAStH,MAAT,EAAiBmO,SAAjB,CAAnB,CAA+C7N,IAA/C,CAAqDgH,MAAD,IAAW;QACnE,IAAIA,MAAJ,EAAY;UACVmB,OAAO,CAACrX,KAAD,CAAP,GAAiBkW,MAAM,IAAImB,OAAO,CAACrX,KAAD,CAAlC;QACD;MACF,CAJK,CAAN;IAKD;EACF;AACF;AAED,eAAeqZ,mBAAf,CACEnD,MADF,EAEEtH,MAFF,EAGEkR,MAHF,EAGgB;EAAA,IAAdA,MAAc;IAAdA,MAAc,GAAL,KAAK;EAAA;EAEd,IAAIvQ,OAAO,GAAG,MAAM2G,MAAM,CAAC2B,YAAP,CAAoB9H,WAApB,CAAgCnB,MAAhC,CAApB;EACA,IAAIW,OAAJ,EAAa;IACX;EACD;EAED,IAAIuQ,MAAJ,EAAY;IACV,IAAI;MACF,OAAO;QACL3D,IAAI,EAAE3W,UAAU,CAAC8H,IADZ;QAELA,IAAI,EAAE4I,MAAM,CAAC2B,YAAP,CAAoB3H;OAF5B;KADF,CAKE,OAAOlM,CAAP,EAAU;MACV;MACA,OAAO;QACLmY,IAAI,EAAE3W,UAAU,CAACL,KADZ;QAELA,KAAK,EAAEnB;OAFT;IAID;EACF;EAED,OAAO;IACLmY,IAAI,EAAE3W,UAAU,CAAC8H,IADZ;IAELA,IAAI,EAAE4I,MAAM,CAAC2B,YAAP,CAAoBvK;GAF5B;AAID;AAED,SAAS6P,kBAAT,CAA4B3a,MAA5B,EAA0C;EACxC,OAAO,IAAI6c,eAAJ,CAAoB7c,MAApB,EAA4Bud,MAA5B,CAAmC,OAAnC,CAA4ChX,KAA5C,CAAkD8G,CAAD,IAAOA,CAAC,KAAK,EAA9D,CAAP;AACD;AAGD;;AACA,SAAS+K,qBAAT,CACEjR,KADF,EAEEgJ,UAFF,EAEuB;EAErB,IAAI;IAAEjN,KAAF;IAASxE,QAAT;IAAmB4I;EAAnB,IAA8BH,KAAlC;EACA,OAAO;IACL1D,EAAE,EAAEP,KAAK,CAACO,EADL;IAEL/E,QAFK;IAGL4I,MAHK;IAILwD,IAAI,EAAEqF,UAAU,CAACjN,KAAK,CAACO,EAAP,CAJX;IAKL+Z,MAAM,EAAEta,KAAK,CAACsa;GALhB;AAOD;AAED,SAAS5J,cAAT,CACEpP,OADF,EAEEhG,QAFF,EAE6B;EAE3B,IAAIwB,MAAM,GACR,OAAOxB,QAAP,KAAoB,QAApB,GAA+B8B,SAAS,CAAC9B,QAAD,CAAT,CAAoBwB,MAAnD,GAA4DxB,QAAQ,CAACwB,MADvE;EAEA,IACEwE,OAAO,CAACA,OAAO,CAAC3G,MAAR,GAAiB,CAAlB,CAAP,CAA4BqF,KAA5B,CAAkC1F,KAAlC,IACAmd,kBAAkB,CAAC3a,MAAM,IAAI,EAAX,CAFpB,EAGE;IACA;IACA,OAAOwE,OAAO,CAACA,OAAO,CAAC3G,MAAR,GAAiB,CAAlB,CAAd;EACD,CAV0B;EAY3B;;EACA,IAAI4f,WAAW,GAAG5T,0BAA0B,CAACrF,OAAD,CAA5C;EACA,OAAOiZ,WAAW,CAACA,WAAW,CAAC5f,MAAZ,GAAqB,CAAtB,CAAlB;AACD", "names": ["Action", "PopStateEventType", "options", "initialEntries", "initialIndex", "v5Compat", "entries", "map", "entry", "index", "createMemoryLocation", "state", "undefined", "clampIndex", "length", "action", "Pop", "listener", "n", "Math", "min", "max", "getCurrentLocation", "to", "key", "location", "createLocation", "pathname", "warning", "char<PERSON>t", "JSON", "stringify", "history", "createHref", "createPath", "encodeLocation", "push", "<PERSON><PERSON>", "nextLocation", "splice", "replace", "Replace", "go", "delta", "listen", "fn", "createBrowserLocation", "window", "globalHistory", "search", "hash", "usr", "createBrowserHref", "getUrlBasedHistory", "createHashLocation", "parsePath", "substr", "createHashHref", "base", "document", "querySelector", "href", "getAttribute", "url", "hashIndex", "indexOf", "slice", "validateHashLocation", "cond", "message", "console", "warn", "Error", "e", "create<PERSON><PERSON>", "random", "toString", "getHistoryState", "current", "_ref", "path", "parsed<PERSON><PERSON>", "searchIndex", "createURL", "origin", "URL", "getLocation", "validateLocation", "defaultView", "handlePop", "historyState", "pushState", "error", "assign", "replaceState", "addEventListener", "removeEventListener", "ResultType", "isIndexRoute", "route", "convertRoutesToDataRoutes", "routes", "parentPath", "allIds", "Set", "treePath", "id", "join", "invariant", "children", "has", "add", "indexRoute", "pathOrLayoutRoute", "matchRoutes", "locationArg", "basename", "stripBasename", "branches", "flattenRoutes", "rankRouteBranches", "matches", "i", "matchRouteBranch", "safelyDecodeURI", "parents<PERSON>eta", "for<PERSON>ach", "meta", "relativePath", "caseSensitive", "childrenIndex", "startsWith", "joinPaths", "routesMeta", "concat", "score", "computeScore", "sort", "a", "b", "compareIndexes", "paramRe", "dynamicSegmentValue", "indexRouteValue", "emptySegmentValue", "staticSegmentValue", "splatPenalty", "isSplat", "s", "segments", "split", "initialScore", "some", "filter", "reduce", "segment", "test", "siblings", "every", "branch", "matchedParams", "matchedPathname", "end", "remainingPathname", "match", "matchPath", "Object", "params", "pathnameBase", "normalizePathname", "generatePath", "_", "prefix", "__", "str", "star", "pattern", "matcher", "paramNames", "compilePath", "captureGroups", "memo", "paramName", "splatValue", "safelyDecodeURIComponent", "endsWith", "regexpSource", "RegExp", "value", "decodeURI", "decodeURIComponent", "toLowerCase", "startIndex", "nextChar", "<PERSON><PERSON><PERSON>", "fromPathname", "toPathname", "resolvePathname", "normalizeSearch", "normalizeHash", "relativeSegments", "pop", "getInvalidPathError", "char", "field", "dest", "getPathContributingMatches", "resolveTo", "to<PERSON><PERSON>", "routePathnames", "locationPathname", "isPathRelative", "includes", "isEmptyPath", "from", "routePathnameIndex", "toSegments", "shift", "hasExplicitTrailingSlash", "hasCurrentTrailingSlash", "getToPathname", "paths", "json", "data", "init", "responseInit", "status", "headers", "Headers", "set", "Response", "Aborted<PERSON>eferredError", "DeferredData", "constructor", "subscriber", "Array", "isArray", "reject", "abortPromise", "Promise", "r", "controller", "AbortController", "onAbort", "unlistenAbortSignal", "signal", "acc", "trackPromise", "<PERSON><PERSON><PERSON><PERSON>", "promise", "race", "then", "onSettle", "catch", "defineProperty", "get", "aborted", "delete", "done", "subscribe", "cancel", "abort", "v", "k", "resolveData", "resolve", "size", "unwrappedData", "unwrapTrackedPromise", "isTrackedPromise", "_tracked", "_error", "_data", "defer", "redirect", "ErrorResponse", "statusText", "isRouteErrorResponse", "IDLE_NAVIGATION", "formMethod", "formAction", "formEncType", "formData", "IDLE_FETCHER", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "isServer", "createRouter", "dataRoutes", "unlistenHistory", "subscribers", "savedScrollPositions", "getScrollRestorationKey", "getScrollPosition", "initialScrollRestored", "initialMatches", "initialErrors", "getNotFoundMatches", "initialized", "m", "loader", "hydrationData", "router", "historyAction", "navigation", "restoreScrollPosition", "preventScrollReset", "revalidation", "loaderData", "actionData", "errors", "fetchers", "Map", "pendingAction", "HistoryAction", "pendingPreventScrollReset", "pendingNavigationController", "isUninterruptedRevalidation", "isRevalidationRequired", "cancelledDeferredRoutes", "cancelledFetcherLoads", "fetchControllers", "incrementingLoadId", "pendingNavigationLoadId", "fetchReloadIds", "fetchRedirectIds", "fetchLoadMatches", "activeDeferreds", "initialize", "startNavigation", "dispose", "clear", "deleteFetcher", "updateState", "newState", "completeNavigation", "isActionReload", "newLoaderData", "mergeLoaderData", "getSavedScrollPosition", "navigate", "opts", "submission", "normalizeNavigateOptions", "pendingError", "revalidate", "interruptActiveLoads", "startUninterruptedRevalidation", "overrideNavigation", "saveScrollPosition", "loadingNavigation", "notFoundMatches", "cancelActiveDeferreds", "isHashChangeOnly", "request", "createRequest", "pendingActionData", "findNearestBoundary", "actionOutput", "handleAction", "shortCircuited", "pendingActionError", "handleLoaders", "result", "actionMatch", "getTargetMatch", "getMethodNotAllowedResult", "callLoaderOrAction", "isRedirectResult", "redirectNavigation", "startRedirectNavigation", "isErrorResult", "boundaryMatch", "isDeferredResult", "matchesToLoad", "revalidatingFetchers", "getMatchesToLoad", "routeId", "_ref2", "fetcher", "revalidatingFetcher", "_ref3", "results", "loaderResults", "fetcherResults", "callLoadersAndMaybeResolveData", "_ref4", "findRedirect", "getLoaderRedirect", "processLoaderData", "deferredData", "markFetchRedirectsDone", "didAbortFetchLoads", "abortStaleFetchLoads", "getFetcher", "fetch", "abort<PERSON><PERSON><PERSON>", "setFetcherError", "handleFetcherAction", "handleFetcherLoader", "requestMatches", "existingFetcher", "abortController", "fetchRequest", "actionResult", "loadingFetcher", "revalidationRequest", "loadId", "loadFetcher", "_ref5", "staleKey", "_ref6", "_ref7", "done<PERSON>etcher", "resolveDeferredData", "redirectHistoryAction", "currentMatches", "fetchersToLoad", "all", "_ref8", "fetchMatches", "resolveDeferredResults", "_ref9", "markFetchersDone", "keys", "done<PERSON><PERSON><PERSON>", "landedId", "yeeted<PERSON><PERSON>s", "predicate", "cancelledRouteIds", "dfd", "enableScrollRestoration", "positions", "getPosition", "<PERSON><PERSON><PERSON>", "y", "userMatches", "createUseMatchesMatch", "_internalFetchControllers", "_internalActiveDeferreds", "validActionMethods", "validRequestMethods", "unstable_createStatic<PERSON>andler", "query", "method", "methodNotAllowedMatches", "getMethodNotAllowedMatches", "statusCode", "loaderHeaders", "actionHeaders", "queryImpl", "queryRoute", "createRouterErrorResponse", "find", "values", "routeData", "routeMatch", "submit", "loadRouteData", "isQueryRouteResponse", "type", "isRedirectResponse", "response", "isRouteRequest", "Location", "context", "getLoaderMatchesUntilBoundary", "processRouteLoaderData", "body", "getStaticContextFromError", "newContext", "_deepestRenderedBoundaryId", "isFetcher", "stripHashFromPath", "searchParams", "convertFormDataToSearchParams", "hasNakedIndexQuery", "append", "boundaryId", "boundaryMatches", "findIndex", "navigationMatches", "is<PERSON>ew<PERSON><PERSON>der", "shouldRevalidateLoader", "shouldRevalidate", "currentLoaderData", "currentMatch", "isNew", "isMissingData", "isNewRouteInstance", "currentPath", "currentLocation", "currentUrl", "currentParams", "nextUrl", "nextParams", "defaultShouldRevalidate", "routeChoice", "isStaticRequest", "resultType", "onReject", "handler", "activeMatches", "requestPath", "resolvedLocation", "contentType", "text", "deferred", "toUpperCase", "Request", "URLSearchParams", "found<PERSON><PERSON>r", "mergedLoaderData", "eligibleMatches", "reverse", "hasErrorBou<PERSON>ry", "getShortCircuitMatches", "obj", "isRevalidatingLoader", "unwrap", "getAll", "handle", "pathMatches"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\@remix-run\\router\\history.ts", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\@remix-run\\router\\utils.ts", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\@remix-run\\router\\router.ts"], "sourcesContent": ["////////////////////////////////////////////////////////////////////////////////\n//#region Types and Constants\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Actions represent the type of change to a location value.\n */\nexport enum Action {\n  /**\n   * A POP indicates a change to an arbitrary index in the history stack, such\n   * as a back or forward navigation. It does not describe the direction of the\n   * navigation, only that the current index changed.\n   *\n   * Note: This is the default action for newly created history objects.\n   */\n  Pop = \"POP\",\n\n  /**\n   * A PUSH indicates a new entry being added to the history stack, such as when\n   * a link is clicked and a new page loads. When this happens, all subsequent\n   * entries in the stack are lost.\n   */\n  Push = \"PUSH\",\n\n  /**\n   * A REPLACE indicates the entry at the current index in the history stack\n   * being replaced by a new one.\n   */\n  Replace = \"REPLACE\",\n}\n\n/**\n * The pathname, search, and hash values of a URL.\n */\nexport interface Path {\n  /**\n   * A URL pathname, beginning with a /.\n   */\n  pathname: string;\n\n  /**\n   * A URL search string, beginning with a ?.\n   */\n  search: string;\n\n  /**\n   * A URL fragment identifier, beginning with a #.\n   */\n  hash: string;\n}\n\n/**\n * An entry in a history stack. A location contains information about the\n * URL path, as well as possibly some arbitrary state and a key.\n */\nexport interface Location extends Path {\n  /**\n   * A value of arbitrary data associated with this location.\n   */\n  state: any;\n\n  /**\n   * A unique string associated with this location. May be used to safely store\n   * and retrieve data in some other storage API, like `localStorage`.\n   *\n   * Note: This value is always \"default\" on the initial location.\n   */\n  key: string;\n}\n\n/**\n * A change to the current location.\n */\nexport interface Update {\n  /**\n   * The action that triggered the change.\n   */\n  action: Action;\n\n  /**\n   * The new location.\n   */\n  location: Location;\n}\n\n/**\n * A function that receives notifications about location changes.\n */\nexport interface Listener {\n  (update: Update): void;\n}\n\n/**\n * Describes a location that is the destination of some navigation, either via\n * `history.push` or `history.replace`. May be either a URL or the pieces of a\n * URL path.\n */\nexport type To = string | Partial<Path>;\n\n/**\n * A history is an interface to the navigation stack. The history serves as the\n * source of truth for the current location, as well as provides a set of\n * methods that may be used to change it.\n *\n * It is similar to the DOM's `window.history` object, but with a smaller, more\n * focused API.\n */\nexport interface History {\n  /**\n   * The last action that modified the current location. This will always be\n   * Action.Pop when a history instance is first created. This value is mutable.\n   */\n  readonly action: Action;\n\n  /**\n   * The current location. This value is mutable.\n   */\n  readonly location: Location;\n\n  /**\n   * Returns a valid href for the given `to` value that may be used as\n   * the value of an <a href> attribute.\n   *\n   * @param to - The destination URL\n   */\n  createHref(to: To): string;\n\n  /**\n   * Encode a location the same way window.history would do (no-op for memory\n   * history) so we ensure our PUSH/REPLAC e navigations for data routers\n   * behave the same as POP\n   *\n   * @param location The incoming location from router.navigate()\n   */\n  encodeLocation(location: Location): Location;\n\n  /**\n   * Pushes a new location onto the history stack, increasing its length by one.\n   * If there were any entries in the stack after the current one, they are\n   * lost.\n   *\n   * @param to - The new URL\n   * @param state - Data to associate with the new location\n   */\n  push(to: To, state?: any): void;\n\n  /**\n   * Replaces the current location in the history stack with a new one.  The\n   * location that was replaced will no longer be available.\n   *\n   * @param to - The new URL\n   * @param state - Data to associate with the new location\n   */\n  replace(to: To, state?: any): void;\n\n  /**\n   * Navigates `n` entries backward/forward in the history stack relative to the\n   * current index. For example, a \"back\" navigation would use go(-1).\n   *\n   * @param delta - The delta in the stack index\n   */\n  go(delta: number): void;\n\n  /**\n   * Sets up a listener that will be called whenever the current location\n   * changes.\n   *\n   * @param listener - A function that will be called when the location changes\n   * @returns unlisten - A function that may be used to stop listening\n   */\n  listen(listener: Listener): () => void;\n}\n\ntype HistoryState = {\n  usr: any;\n  key?: string;\n};\n\nconst PopStateEventType = \"popstate\";\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Memory History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A user-supplied object that describes a location. Used when providing\n * entries to `createMemoryHistory` via its `initialEntries` option.\n */\nexport type InitialEntry = string | Partial<Location>;\n\nexport type MemoryHistoryOptions = {\n  initialEntries?: InitialEntry[];\n  initialIndex?: number;\n  v5Compat?: boolean;\n};\n\n/**\n * A memory history stores locations in memory. This is useful in stateful\n * environments where there is no web browser, such as node tests or React\n * Native.\n */\nexport interface MemoryHistory extends History {\n  /**\n   * The current index in the history stack.\n   */\n  readonly index: number;\n}\n\n/**\n * Memory history stores the current location in memory. It is designed for use\n * in stateful non-browser environments like tests and React Native.\n */\nexport function createMemoryHistory(\n  options: MemoryHistoryOptions = {}\n): MemoryHistory {\n  let { initialEntries = [\"/\"], initialIndex, v5Compat = false } = options;\n  let entries: Location[]; // Declare so we can access from createMemoryLocation\n  entries = initialEntries.map((entry, index) =>\n    createMemoryLocation(\n      entry,\n      typeof entry === \"string\" ? null : entry.state,\n      index === 0 ? \"default\" : undefined\n    )\n  );\n  let index = clampIndex(\n    initialIndex == null ? entries.length - 1 : initialIndex\n  );\n  let action = Action.Pop;\n  let listener: Listener | null = null;\n\n  function clampIndex(n: number): number {\n    return Math.min(Math.max(n, 0), entries.length - 1);\n  }\n  function getCurrentLocation(): Location {\n    return entries[index];\n  }\n  function createMemoryLocation(\n    to: To,\n    state: any = null,\n    key?: string\n  ): Location {\n    let location = createLocation(\n      entries ? getCurrentLocation().pathname : \"/\",\n      to,\n      state,\n      key\n    );\n    warning(\n      location.pathname.charAt(0) === \"/\",\n      `relative pathnames are not supported in memory history: ${JSON.stringify(\n        to\n      )}`\n    );\n    return location;\n  }\n\n  let history: MemoryHistory = {\n    get index() {\n      return index;\n    },\n    get action() {\n      return action;\n    },\n    get location() {\n      return getCurrentLocation();\n    },\n    createHref(to) {\n      return typeof to === \"string\" ? to : createPath(to);\n    },\n    encodeLocation(location) {\n      return location;\n    },\n    push(to, state) {\n      action = Action.Push;\n      let nextLocation = createMemoryLocation(to, state);\n      index += 1;\n      entries.splice(index, entries.length, nextLocation);\n      if (v5Compat && listener) {\n        listener({ action, location: nextLocation });\n      }\n    },\n    replace(to, state) {\n      action = Action.Replace;\n      let nextLocation = createMemoryLocation(to, state);\n      entries[index] = nextLocation;\n      if (v5Compat && listener) {\n        listener({ action, location: nextLocation });\n      }\n    },\n    go(delta) {\n      action = Action.Pop;\n      index = clampIndex(index + delta);\n      if (listener) {\n        listener({ action, location: getCurrentLocation() });\n      }\n    },\n    listen(fn: Listener) {\n      listener = fn;\n      return () => {\n        listener = null;\n      };\n    },\n  };\n\n  return history;\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Browser History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A browser history stores the current location in regular URLs in a web\n * browser environment. This is the standard for most web apps and provides the\n * cleanest URLs the browser's address bar.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#browserhistory\n */\nexport interface BrowserHistory extends UrlHistory {}\n\nexport type BrowserHistoryOptions = UrlHistoryOptions;\n\n/**\n * Browser history stores the location in regular URLs. This is the standard for\n * most web apps, but it requires some configuration on the server to ensure you\n * serve the same app at multiple URLs.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createbrowserhistory\n */\nexport function createBrowserHistory(\n  options: BrowserHistoryOptions = {}\n): BrowserHistory {\n  function createBrowserLocation(\n    window: Window,\n    globalHistory: Window[\"history\"]\n  ) {\n    let { pathname, search, hash } = window.location;\n    return createLocation(\n      \"\",\n      { pathname, search, hash },\n      // state defaults to `null` because `window.history.state` does\n      (globalHistory.state && globalHistory.state.usr) || null,\n      (globalHistory.state && globalHistory.state.key) || \"default\"\n    );\n  }\n\n  function createBrowserHref(window: Window, to: To) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n\n  return getUrlBasedHistory(\n    createBrowserLocation,\n    createBrowserHref,\n    null,\n    options\n  );\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Hash History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A hash history stores the current location in the fragment identifier portion\n * of the URL in a web browser environment.\n *\n * This is ideal for apps that do not control the server for some reason\n * (because the fragment identifier is never sent to the server), including some\n * shared hosting environments that do not provide fine-grained controls over\n * which pages are served at which URLs.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#hashhistory\n */\nexport interface HashHistory extends UrlHistory {}\n\nexport type HashHistoryOptions = UrlHistoryOptions;\n\n/**\n * Hash history stores the location in window.location.hash. This makes it ideal\n * for situations where you don't want to send the location to the server for\n * some reason, either because you do cannot configure it or the URL space is\n * reserved for something else.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createhashhistory\n */\nexport function createHashHistory(\n  options: HashHistoryOptions = {}\n): HashHistory {\n  function createHashLocation(\n    window: Window,\n    globalHistory: Window[\"history\"]\n  ) {\n    let {\n      pathname = \"/\",\n      search = \"\",\n      hash = \"\",\n    } = parsePath(window.location.hash.substr(1));\n    return createLocation(\n      \"\",\n      { pathname, search, hash },\n      // state defaults to `null` because `window.history.state` does\n      (globalHistory.state && globalHistory.state.usr) || null,\n      (globalHistory.state && globalHistory.state.key) || \"default\"\n    );\n  }\n\n  function createHashHref(window: Window, to: To) {\n    let base = window.document.querySelector(\"base\");\n    let href = \"\";\n\n    if (base && base.getAttribute(\"href\")) {\n      let url = window.location.href;\n      let hashIndex = url.indexOf(\"#\");\n      href = hashIndex === -1 ? url : url.slice(0, hashIndex);\n    }\n\n    return href + \"#\" + (typeof to === \"string\" ? to : createPath(to));\n  }\n\n  function validateHashLocation(location: Location, to: To) {\n    warning(\n      location.pathname.charAt(0) === \"/\",\n      `relative pathnames are not supported in hash history.push(${JSON.stringify(\n        to\n      )})`\n    );\n  }\n\n  return getUrlBasedHistory(\n    createHashLocation,\n    createHashHref,\n    validateHashLocation,\n    options\n  );\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region UTILS\n////////////////////////////////////////////////////////////////////////////////\n\nfunction warning(cond: any, message: string) {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n\n    try {\n      // Welcome to debugging history!\n      //\n      // This error is thrown as a convenience so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message);\n      // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\n\nfunction createKey() {\n  return Math.random().toString(36).substr(2, 8);\n}\n\n/**\n * For browser-based histories, we combine the state and key into an object\n */\nfunction getHistoryState(location: Location): HistoryState {\n  return {\n    usr: location.state,\n    key: location.key,\n  };\n}\n\n/**\n * Creates a Location object with a unique key from the given Path\n */\nexport function createLocation(\n  current: string | Location,\n  to: To,\n  state: any = null,\n  key?: string\n): Readonly<Location> {\n  let location: Readonly<Location> = {\n    pathname: typeof current === \"string\" ? current : current.pathname,\n    search: \"\",\n    hash: \"\",\n    ...(typeof to === \"string\" ? parsePath(to) : to),\n    state,\n    // TODO: This could be cleaned up.  push/replace should probably just take\n    // full Locations now and avoid the need to run through this flow at all\n    // But that's a pretty big refactor to the current test suite so going to\n    // keep as is for the time being and just let any incoming keys take precedence\n    key: (to && (to as Location).key) || key || createKey(),\n  };\n  return location;\n}\n\n/**\n * Creates a string URL path from the given pathname, search, and hash components.\n */\nexport function createPath({\n  pathname = \"/\",\n  search = \"\",\n  hash = \"\",\n}: Partial<Path>) {\n  if (search && search !== \"?\")\n    pathname += search.charAt(0) === \"?\" ? search : \"?\" + search;\n  if (hash && hash !== \"#\")\n    pathname += hash.charAt(0) === \"#\" ? hash : \"#\" + hash;\n  return pathname;\n}\n\n/**\n * Parses a string URL path into its separate pathname, search, and hash components.\n */\nexport function parsePath(path: string): Partial<Path> {\n  let parsedPath: Partial<Path> = {};\n\n  if (path) {\n    let hashIndex = path.indexOf(\"#\");\n    if (hashIndex >= 0) {\n      parsedPath.hash = path.substr(hashIndex);\n      path = path.substr(0, hashIndex);\n    }\n\n    let searchIndex = path.indexOf(\"?\");\n    if (searchIndex >= 0) {\n      parsedPath.search = path.substr(searchIndex);\n      path = path.substr(0, searchIndex);\n    }\n\n    if (path) {\n      parsedPath.pathname = path;\n    }\n  }\n\n  return parsedPath;\n}\n\nexport function createURL(location: Location | string): URL {\n  // window.location.origin is \"null\" (the literal string value) in Firefox\n  // under certain conditions, notably when serving from a local HTML file\n  // See https://bugzilla.mozilla.org/show_bug.cgi?id=878297\n  let base =\n    typeof window !== \"undefined\" &&\n    typeof window.location !== \"undefined\" &&\n    window.location.origin !== \"null\"\n      ? window.location.origin\n      : \"unknown://unknown\";\n  let href = typeof location === \"string\" ? location : createPath(location);\n  return new URL(href, base);\n}\n\nexport interface UrlHistory extends History {}\n\nexport type UrlHistoryOptions = {\n  window?: Window;\n  v5Compat?: boolean;\n};\n\nfunction getUrlBasedHistory(\n  getLocation: (window: Window, globalHistory: Window[\"history\"]) => Location,\n  createHref: (window: Window, to: To) => string,\n  validateLocation: ((location: Location, to: To) => void) | null,\n  options: UrlHistoryOptions = {}\n): UrlHistory {\n  let { window = document.defaultView!, v5Compat = false } = options;\n  let globalHistory = window.history;\n  let action = Action.Pop;\n  let listener: Listener | null = null;\n\n  function handlePop() {\n    action = Action.Pop;\n    if (listener) {\n      listener({ action, location: history.location });\n    }\n  }\n\n  function push(to: To, state?: any) {\n    action = Action.Push;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n\n    let historyState = getHistoryState(location);\n    let url = history.createHref(location);\n\n    // try...catch because iOS limits us to 100 pushState calls :/\n    try {\n      globalHistory.pushState(historyState, \"\", url);\n    } catch (error) {\n      // They are going to lose state here, but there is no real\n      // way to warn them about it since the page will refresh...\n      window.location.assign(url);\n    }\n\n    if (v5Compat && listener) {\n      listener({ action, location: history.location });\n    }\n  }\n\n  function replace(to: To, state?: any) {\n    action = Action.Replace;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n\n    let historyState = getHistoryState(location);\n    let url = history.createHref(location);\n    globalHistory.replaceState(historyState, \"\", url);\n\n    if (v5Compat && listener) {\n      listener({ action, location: history.location });\n    }\n  }\n\n  let history: History = {\n    get action() {\n      return action;\n    },\n    get location() {\n      return getLocation(window, globalHistory);\n    },\n    listen(fn: Listener) {\n      if (listener) {\n        throw new Error(\"A history only accepts one active listener\");\n      }\n      window.addEventListener(PopStateEventType, handlePop);\n      listener = fn;\n\n      return () => {\n        window.removeEventListener(PopStateEventType, handlePop);\n        listener = null;\n      };\n    },\n    createHref(to) {\n      return createHref(window, to);\n    },\n    encodeLocation(location) {\n      // Encode a Location the same way window.location would\n      let url = createURL(createPath(location));\n      return {\n        ...location,\n        pathname: url.pathname,\n        search: url.search,\n        hash: url.hash,\n      };\n    },\n    push,\n    replace,\n    go(n) {\n      return globalHistory.go(n);\n    },\n  };\n\n  return history;\n}\n\n//#endregion\n", "import type { Location, Path, To } from \"./history\";\nimport { parsePath } from \"./history\";\n\n/**\n * Map of routeId -> data returned from a loader/action/error\n */\nexport interface RouteData {\n  [routeId: string]: any;\n}\n\nexport enum ResultType {\n  data = \"data\",\n  deferred = \"deferred\",\n  redirect = \"redirect\",\n  error = \"error\",\n}\n\n/**\n * Successful result from a loader or action\n */\nexport interface SuccessResult {\n  type: ResultType.data;\n  data: any;\n  statusCode?: number;\n  headers?: Headers;\n}\n\n/**\n * Successful defer() result from a loader or action\n */\nexport interface DeferredResult {\n  type: ResultType.deferred;\n  deferredData: DeferredData;\n}\n\n/**\n * Redirect result from a loader or action\n */\nexport interface RedirectResult {\n  type: ResultType.redirect;\n  status: number;\n  location: string;\n  revalidate: boolean;\n}\n\n/**\n * Unsuccessful result from a loader or action\n */\nexport interface ErrorResult {\n  type: ResultType.error;\n  error: any;\n  headers?: Headers;\n}\n\n/**\n * Result from a loader or action - potentially successful or unsuccessful\n */\nexport type DataResult =\n  | SuccessResult\n  | DeferredResult\n  | RedirectResult\n  | ErrorResult;\n\nexport type FormMethod = \"get\" | \"post\" | \"put\" | \"patch\" | \"delete\";\nexport type FormEncType =\n  | \"application/x-www-form-urlencoded\"\n  | \"multipart/form-data\";\n\n/**\n * @private\n * Internal interface to pass around for action submissions, not intended for\n * external consumption\n */\nexport interface Submission {\n  formMethod: Exclude<FormMethod, \"get\">;\n  formAction: string;\n  formEncType: FormEncType;\n  formData: FormData;\n}\n\n/**\n * @private\n * Arguments passed to route loader/action functions.  Same for now but we keep\n * this as a private implementation detail in case they diverge in the future.\n */\ninterface DataFunctionArgs {\n  request: Request;\n  params: Params;\n}\n\n/**\n * Arguments passed to loader functions\n */\nexport interface LoaderFunctionArgs extends DataFunctionArgs {}\n\n/**\n * Arguments passed to action functions\n */\nexport interface ActionFunctionArgs extends DataFunctionArgs {}\n\n/**\n * Route loader function signature\n */\nexport interface LoaderFunction {\n  (args: LoaderFunctionArgs): Promise<Response> | Response | Promise<any> | any;\n}\n\n/**\n * Route action function signature\n */\nexport interface ActionFunction {\n  (args: ActionFunctionArgs): Promise<Response> | Response | Promise<any> | any;\n}\n\n/**\n * Route shouldRevalidate function signature.  This runs after any submission\n * (navigation or fetcher), so we flatten the navigation/fetcher submission\n * onto the arguments.  It shouldn't matter whether it came from a navigation\n * or a fetcher, what really matters is the URLs and the formData since loaders\n * have to re-run based on the data models that were potentially mutated.\n */\nexport interface ShouldRevalidateFunction {\n  (args: {\n    currentUrl: URL;\n    currentParams: AgnosticDataRouteMatch[\"params\"];\n    nextUrl: URL;\n    nextParams: AgnosticDataRouteMatch[\"params\"];\n    formMethod?: Submission[\"formMethod\"];\n    formAction?: Submission[\"formAction\"];\n    formEncType?: Submission[\"formEncType\"];\n    formData?: Submission[\"formData\"];\n    actionResult?: DataResult;\n    defaultShouldRevalidate: boolean;\n  }): boolean;\n}\n\n/**\n * Base RouteObject with common props shared by all types of routes\n */\ntype AgnosticBaseRouteObject = {\n  caseSensitive?: boolean;\n  path?: string;\n  id?: string;\n  loader?: LoaderFunction;\n  action?: ActionFunction;\n  hasErrorBoundary?: boolean;\n  shouldRevalidate?: ShouldRevalidateFunction;\n  handle?: any;\n};\n\n/**\n * Index routes must not have children\n */\nexport type AgnosticIndexRouteObject = AgnosticBaseRouteObject & {\n  children?: undefined;\n  index: true;\n};\n\n/**\n * Non-index routes may have children, but cannot have index\n */\nexport type AgnosticNonIndexRouteObject = AgnosticBaseRouteObject & {\n  children?: AgnosticRouteObject[];\n  index?: false;\n};\n\n/**\n * A route object represents a logical route, with (optionally) its child\n * routes organized in a tree-like structure.\n */\nexport type AgnosticRouteObject =\n  | AgnosticIndexRouteObject\n  | AgnosticNonIndexRouteObject;\n\nexport type AgnosticDataIndexRouteObject = AgnosticIndexRouteObject & {\n  id: string;\n};\n\nexport type AgnosticDataNonIndexRouteObject = AgnosticNonIndexRouteObject & {\n  children?: AgnosticDataRouteObject[];\n  id: string;\n};\n\n/**\n * A data route object, which is just a RouteObject with a required unique ID\n */\nexport type AgnosticDataRouteObject =\n  | AgnosticDataIndexRouteObject\n  | AgnosticDataNonIndexRouteObject;\n\n// Recursive helper for finding path parameters in the absence of wildcards\ntype _PathParam<Path extends string> =\n  // split path into individual path segments\n  Path extends `${infer L}/${infer R}`\n    ? _PathParam<L> | _PathParam<R>\n    : // find params after `:`\n    Path extends `${string}:${infer Param}`\n    ? Param\n    : // otherwise, there aren't any params present\n      never;\n\n/**\n * Examples:\n * \"/a/b/*\" -> \"*\"\n * \":a\" -> \"a\"\n * \"/a/:b\" -> \"b\"\n * \"/a/blahblahblah:b\" -> \"b\"\n * \"/:a/:b\" -> \"a\" | \"b\"\n * \"/:a/b/:c/*\" -> \"a\" | \"c\" | \"*\"\n */\ntype PathParam<Path extends string> =\n  // check if path is just a wildcard\n  Path extends \"*\"\n    ? \"*\"\n    : // look for wildcard at the end of the path\n    Path extends `${infer Rest}/*`\n    ? \"*\" | _PathParam<Rest>\n    : // look for params in the absence of wildcards\n      _PathParam<Path>;\n\n// Attempt to parse the given string segment. If it fails, then just return the\n// plain string type as a default fallback. Otherwise return the union of the\n// parsed string literals that were referenced as dynamic segments in the route.\nexport type ParamParseKey<Segment extends string> =\n  // if could not find path params, fallback to `string`\n  [PathParam<Segment>] extends [never] ? string : PathParam<Segment>;\n\n/**\n * The parameters that were parsed from the URL path.\n */\nexport type Params<Key extends string = string> = {\n  readonly [key in Key]: string | undefined;\n};\n\n/**\n * A RouteMatch contains info about how a route matched a URL.\n */\nexport interface AgnosticRouteMatch<\n  ParamKey extends string = string,\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  /**\n   * The names and values of dynamic parameters in the URL.\n   */\n  params: Params<ParamKey>;\n  /**\n   * The portion of the URL pathname that was matched.\n   */\n  pathname: string;\n  /**\n   * The portion of the URL pathname that was matched before child routes.\n   */\n  pathnameBase: string;\n  /**\n   * The route object that was used to match.\n   */\n  route: RouteObjectType;\n}\n\nexport interface AgnosticDataRouteMatch\n  extends AgnosticRouteMatch<string, AgnosticDataRouteObject> {}\n\nfunction isIndexRoute(\n  route: AgnosticRouteObject\n): route is AgnosticIndexRouteObject {\n  return route.index === true;\n}\n\n// Walk the route tree generating unique IDs where necessary so we are working\n// solely with AgnosticDataRouteObject's within the Router\nexport function convertRoutesToDataRoutes(\n  routes: AgnosticRouteObject[],\n  parentPath: number[] = [],\n  allIds: Set<string> = new Set<string>()\n): AgnosticDataRouteObject[] {\n  return routes.map((route, index) => {\n    let treePath = [...parentPath, index];\n    let id = typeof route.id === \"string\" ? route.id : treePath.join(\"-\");\n    invariant(\n      route.index !== true || !route.children,\n      `Cannot specify children on an index route`\n    );\n    invariant(\n      !allIds.has(id),\n      `Found a route id collision on id \"${id}\".  Route ` +\n        \"id's must be globally unique within Data Router usages\"\n    );\n    allIds.add(id);\n\n    if (isIndexRoute(route)) {\n      let indexRoute: AgnosticDataIndexRouteObject = { ...route, id };\n      return indexRoute;\n    } else {\n      let pathOrLayoutRoute: AgnosticDataNonIndexRouteObject = {\n        ...route,\n        id,\n        children: route.children\n          ? convertRoutesToDataRoutes(route.children, treePath, allIds)\n          : undefined,\n      };\n      return pathOrLayoutRoute;\n    }\n  });\n}\n\n/**\n * Matches the given routes to a location and returns the match data.\n *\n * @see https://reactrouter.com/docs/en/v6/utils/match-routes\n */\nexport function matchRoutes<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  routes: RouteObjectType[],\n  locationArg: Partial<Location> | string,\n  basename = \"/\"\n): AgnosticRouteMatch<string, RouteObjectType>[] | null {\n  let location =\n    typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n\n  let pathname = stripBasename(location.pathname || \"/\", basename);\n\n  if (pathname == null) {\n    return null;\n  }\n\n  let branches = flattenRoutes(routes);\n  rankRouteBranches(branches);\n\n  let matches = null;\n  for (let i = 0; matches == null && i < branches.length; ++i) {\n    matches = matchRouteBranch<string, RouteObjectType>(\n      branches[i],\n      // Incoming pathnames are generally encoded from either window.location\n      // or from router.navigate, but we want to match against the unencoded\n      // paths in the route definitions.  Memory router locations won't be\n      // encoded here but there also shouldn't be anything to decode so this\n      // should be a safe operation.  This avoids needing matchRoutes to be\n      // history-aware.\n      safelyDecodeURI(pathname)\n    );\n  }\n\n  return matches;\n}\n\ninterface RouteMeta<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  relativePath: string;\n  caseSensitive: boolean;\n  childrenIndex: number;\n  route: RouteObjectType;\n}\n\ninterface RouteBranch<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  path: string;\n  score: number;\n  routesMeta: RouteMeta<RouteObjectType>[];\n}\n\nfunction flattenRoutes<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  routes: RouteObjectType[],\n  branches: RouteBranch<RouteObjectType>[] = [],\n  parentsMeta: RouteMeta<RouteObjectType>[] = [],\n  parentPath = \"\"\n): RouteBranch<RouteObjectType>[] {\n  routes.forEach((route, index) => {\n    let meta: RouteMeta<RouteObjectType> = {\n      relativePath: route.path || \"\",\n      caseSensitive: route.caseSensitive === true,\n      childrenIndex: index,\n      route,\n    };\n\n    if (meta.relativePath.startsWith(\"/\")) {\n      invariant(\n        meta.relativePath.startsWith(parentPath),\n        `Absolute route path \"${meta.relativePath}\" nested under path ` +\n          `\"${parentPath}\" is not valid. An absolute child route path ` +\n          `must start with the combined path of all its parent routes.`\n      );\n\n      meta.relativePath = meta.relativePath.slice(parentPath.length);\n    }\n\n    let path = joinPaths([parentPath, meta.relativePath]);\n    let routesMeta = parentsMeta.concat(meta);\n\n    // Add the children before adding this route to the array so we traverse the\n    // route tree depth-first and child routes appear before their parents in\n    // the \"flattened\" version.\n    if (route.children && route.children.length > 0) {\n      invariant(\n        // Our types know better, but runtime JS may not!\n        // @ts-expect-error\n        route.index !== true,\n        `Index routes must not have child routes. Please remove ` +\n          `all child routes from route path \"${path}\".`\n      );\n\n      flattenRoutes(route.children, branches, routesMeta, path);\n    }\n\n    // Routes without a path shouldn't ever match by themselves unless they are\n    // index routes, so don't add them to the list of possible branches.\n    if (route.path == null && !route.index) {\n      return;\n    }\n\n    branches.push({ path, score: computeScore(path, route.index), routesMeta });\n  });\n\n  return branches;\n}\n\nfunction rankRouteBranches(branches: RouteBranch[]): void {\n  branches.sort((a, b) =>\n    a.score !== b.score\n      ? b.score - a.score // Higher score first\n      : compareIndexes(\n          a.routesMeta.map((meta) => meta.childrenIndex),\n          b.routesMeta.map((meta) => meta.childrenIndex)\n        )\n  );\n}\n\nconst paramRe = /^:\\w+$/;\nconst dynamicSegmentValue = 3;\nconst indexRouteValue = 2;\nconst emptySegmentValue = 1;\nconst staticSegmentValue = 10;\nconst splatPenalty = -2;\nconst isSplat = (s: string) => s === \"*\";\n\nfunction computeScore(path: string, index: boolean | undefined): number {\n  let segments = path.split(\"/\");\n  let initialScore = segments.length;\n  if (segments.some(isSplat)) {\n    initialScore += splatPenalty;\n  }\n\n  if (index) {\n    initialScore += indexRouteValue;\n  }\n\n  return segments\n    .filter((s) => !isSplat(s))\n    .reduce(\n      (score, segment) =>\n        score +\n        (paramRe.test(segment)\n          ? dynamicSegmentValue\n          : segment === \"\"\n          ? emptySegmentValue\n          : staticSegmentValue),\n      initialScore\n    );\n}\n\nfunction compareIndexes(a: number[], b: number[]): number {\n  let siblings =\n    a.length === b.length && a.slice(0, -1).every((n, i) => n === b[i]);\n\n  return siblings\n    ? // If two routes are siblings, we should try to match the earlier sibling\n      // first. This allows people to have fine-grained control over the matching\n      // behavior by simply putting routes with identical paths in the order they\n      // want them tried.\n      a[a.length - 1] - b[b.length - 1]\n    : // Otherwise, it doesn't really make sense to rank non-siblings by index,\n      // so they sort equally.\n      0;\n}\n\nfunction matchRouteBranch<\n  ParamKey extends string = string,\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  branch: RouteBranch<RouteObjectType>,\n  pathname: string\n): AgnosticRouteMatch<ParamKey, RouteObjectType>[] | null {\n  let { routesMeta } = branch;\n\n  let matchedParams = {};\n  let matchedPathname = \"/\";\n  let matches: AgnosticRouteMatch<ParamKey, RouteObjectType>[] = [];\n  for (let i = 0; i < routesMeta.length; ++i) {\n    let meta = routesMeta[i];\n    let end = i === routesMeta.length - 1;\n    let remainingPathname =\n      matchedPathname === \"/\"\n        ? pathname\n        : pathname.slice(matchedPathname.length) || \"/\";\n    let match = matchPath(\n      { path: meta.relativePath, caseSensitive: meta.caseSensitive, end },\n      remainingPathname\n    );\n\n    if (!match) return null;\n\n    Object.assign(matchedParams, match.params);\n\n    let route = meta.route;\n\n    matches.push({\n      // TODO: Can this as be avoided?\n      params: matchedParams as Params<ParamKey>,\n      pathname: joinPaths([matchedPathname, match.pathname]),\n      pathnameBase: normalizePathname(\n        joinPaths([matchedPathname, match.pathnameBase])\n      ),\n      route,\n    });\n\n    if (match.pathnameBase !== \"/\") {\n      matchedPathname = joinPaths([matchedPathname, match.pathnameBase]);\n    }\n  }\n\n  return matches;\n}\n\n/**\n * Returns a path with params interpolated.\n *\n * @see https://reactrouter.com/docs/en/v6/utils/generate-path\n */\nexport function generatePath<Path extends string>(\n  path: Path,\n  params: {\n    [key in PathParam<Path>]: string;\n  } = {} as any\n): string {\n  return path\n    .replace(/:(\\w+)/g, (_, key: PathParam<Path>) => {\n      invariant(params[key] != null, `Missing \":${key}\" param`);\n      return params[key]!;\n    })\n    .replace(/(\\/?)\\*/, (_, prefix, __, str) => {\n      const star = \"*\" as PathParam<Path>;\n\n      if (params[star] == null) {\n        // If no splat was provided, trim the trailing slash _unless_ it's\n        // the entire path\n        return str === \"/*\" ? \"/\" : \"\";\n      }\n\n      // Apply the splat\n      return `${prefix}${params[star]}`;\n    });\n}\n\n/**\n * A PathPattern is used to match on some portion of a URL pathname.\n */\nexport interface PathPattern<Path extends string = string> {\n  /**\n   * A string to match against a URL pathname. May contain `:id`-style segments\n   * to indicate placeholders for dynamic parameters. May also end with `/*` to\n   * indicate matching the rest of the URL pathname.\n   */\n  path: Path;\n  /**\n   * Should be `true` if the static portions of the `path` should be matched in\n   * the same case.\n   */\n  caseSensitive?: boolean;\n  /**\n   * Should be `true` if this pattern should match the entire URL pathname.\n   */\n  end?: boolean;\n}\n\n/**\n * A PathMatch contains info about how a PathPattern matched on a URL pathname.\n */\nexport interface PathMatch<ParamKey extends string = string> {\n  /**\n   * The names and values of dynamic parameters in the URL.\n   */\n  params: Params<ParamKey>;\n  /**\n   * The portion of the URL pathname that was matched.\n   */\n  pathname: string;\n  /**\n   * The portion of the URL pathname that was matched before child routes.\n   */\n  pathnameBase: string;\n  /**\n   * The pattern that was used to match.\n   */\n  pattern: PathPattern;\n}\n\ntype Mutable<T> = {\n  -readonly [P in keyof T]: T[P];\n};\n\n/**\n * Performs pattern matching on a URL pathname and returns information about\n * the match.\n *\n * @see https://reactrouter.com/docs/en/v6/utils/match-path\n */\nexport function matchPath<\n  ParamKey extends ParamParseKey<Path>,\n  Path extends string\n>(\n  pattern: PathPattern<Path> | Path,\n  pathname: string\n): PathMatch<ParamKey> | null {\n  if (typeof pattern === \"string\") {\n    pattern = { path: pattern, caseSensitive: false, end: true };\n  }\n\n  let [matcher, paramNames] = compilePath(\n    pattern.path,\n    pattern.caseSensitive,\n    pattern.end\n  );\n\n  let match = pathname.match(matcher);\n  if (!match) return null;\n\n  let matchedPathname = match[0];\n  let pathnameBase = matchedPathname.replace(/(.)\\/+$/, \"$1\");\n  let captureGroups = match.slice(1);\n  let params: Params = paramNames.reduce<Mutable<Params>>(\n    (memo, paramName, index) => {\n      // We need to compute the pathnameBase here using the raw splat value\n      // instead of using params[\"*\"] later because it will be decoded then\n      if (paramName === \"*\") {\n        let splatValue = captureGroups[index] || \"\";\n        pathnameBase = matchedPathname\n          .slice(0, matchedPathname.length - splatValue.length)\n          .replace(/(.)\\/+$/, \"$1\");\n      }\n\n      memo[paramName] = safelyDecodeURIComponent(\n        captureGroups[index] || \"\",\n        paramName\n      );\n      return memo;\n    },\n    {}\n  );\n\n  return {\n    params,\n    pathname: matchedPathname,\n    pathnameBase,\n    pattern,\n  };\n}\n\nfunction compilePath(\n  path: string,\n  caseSensitive = false,\n  end = true\n): [RegExp, string[]] {\n  warning(\n    path === \"*\" || !path.endsWith(\"*\") || path.endsWith(\"/*\"),\n    `Route path \"${path}\" will be treated as if it were ` +\n      `\"${path.replace(/\\*$/, \"/*\")}\" because the \\`*\\` character must ` +\n      `always follow a \\`/\\` in the pattern. To get rid of this warning, ` +\n      `please change the route path to \"${path.replace(/\\*$/, \"/*\")}\".`\n  );\n\n  let paramNames: string[] = [];\n  let regexpSource =\n    \"^\" +\n    path\n      .replace(/\\/*\\*?$/, \"\") // Ignore trailing / and /*, we'll handle it below\n      .replace(/^\\/*/, \"/\") // Make sure it has a leading /\n      .replace(/[\\\\.*+^$?{}|()[\\]]/g, \"\\\\$&\") // Escape special regex chars\n      .replace(/:(\\w+)/g, (_: string, paramName: string) => {\n        paramNames.push(paramName);\n        return \"([^\\\\/]+)\";\n      });\n\n  if (path.endsWith(\"*\")) {\n    paramNames.push(\"*\");\n    regexpSource +=\n      path === \"*\" || path === \"/*\"\n        ? \"(.*)$\" // Already matched the initial /, just match the rest\n        : \"(?:\\\\/(.+)|\\\\/*)$\"; // Don't include the / in params[\"*\"]\n  } else if (end) {\n    // When matching to the end, ignore trailing slashes\n    regexpSource += \"\\\\/*$\";\n  } else if (path !== \"\" && path !== \"/\") {\n    // If our path is non-empty and contains anything beyond an initial slash,\n    // then we have _some_ form of path in our regex so we should expect to\n    // match only if we find the end of this path segment.  Look for an optional\n    // non-captured trailing slash (to match a portion of the URL) or the end\n    // of the path (if we've matched to the end).  We used to do this with a\n    // word boundary but that gives false positives on routes like\n    // /user-preferences since `-` counts as a word boundary.\n    regexpSource += \"(?:(?=\\\\/|$))\";\n  } else {\n    // Nothing to match for \"\" or \"/\"\n  }\n\n  let matcher = new RegExp(regexpSource, caseSensitive ? undefined : \"i\");\n\n  return [matcher, paramNames];\n}\n\nfunction safelyDecodeURI(value: string) {\n  try {\n    return decodeURI(value);\n  } catch (error) {\n    warning(\n      false,\n      `The URL path \"${value}\" could not be decoded because it is is a ` +\n        `malformed URL segment. This is probably due to a bad percent ` +\n        `encoding (${error}).`\n    );\n\n    return value;\n  }\n}\n\nfunction safelyDecodeURIComponent(value: string, paramName: string) {\n  try {\n    return decodeURIComponent(value);\n  } catch (error) {\n    warning(\n      false,\n      `The value for the URL param \"${paramName}\" will not be decoded because` +\n        ` the string \"${value}\" is a malformed URL segment. This is probably` +\n        ` due to a bad percent encoding (${error}).`\n    );\n\n    return value;\n  }\n}\n\n/**\n * @private\n */\nexport function stripBasename(\n  pathname: string,\n  basename: string\n): string | null {\n  if (basename === \"/\") return pathname;\n\n  if (!pathname.toLowerCase().startsWith(basename.toLowerCase())) {\n    return null;\n  }\n\n  // We want to leave trailing slash behavior in the user's control, so if they\n  // specify a basename with a trailing slash, we should support it\n  let startIndex = basename.endsWith(\"/\")\n    ? basename.length - 1\n    : basename.length;\n  let nextChar = pathname.charAt(startIndex);\n  if (nextChar && nextChar !== \"/\") {\n    // pathname does not start with basename/\n    return null;\n  }\n\n  return pathname.slice(startIndex) || \"/\";\n}\n\n/**\n * @private\n */\nexport function invariant(value: boolean, message?: string): asserts value;\nexport function invariant<T>(\n  value: T | null | undefined,\n  message?: string\n): asserts value is T;\nexport function invariant(value: any, message?: string) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    throw new Error(message);\n  }\n}\n\n/**\n * @private\n */\nexport function warning(cond: any, message: string): void {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n\n    try {\n      // Welcome to debugging React Router!\n      //\n      // This error is thrown as a convenience so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message);\n      // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\n\n/**\n * Returns a resolved path object relative to the given pathname.\n *\n * @see https://reactrouter.com/docs/en/v6/utils/resolve-path\n */\nexport function resolvePath(to: To, fromPathname = \"/\"): Path {\n  let {\n    pathname: toPathname,\n    search = \"\",\n    hash = \"\",\n  } = typeof to === \"string\" ? parsePath(to) : to;\n\n  let pathname = toPathname\n    ? toPathname.startsWith(\"/\")\n      ? toPathname\n      : resolvePathname(toPathname, fromPathname)\n    : fromPathname;\n\n  return {\n    pathname,\n    search: normalizeSearch(search),\n    hash: normalizeHash(hash),\n  };\n}\n\nfunction resolvePathname(relativePath: string, fromPathname: string): string {\n  let segments = fromPathname.replace(/\\/+$/, \"\").split(\"/\");\n  let relativeSegments = relativePath.split(\"/\");\n\n  relativeSegments.forEach((segment) => {\n    if (segment === \"..\") {\n      // Keep the root \"\" segment so the pathname starts at /\n      if (segments.length > 1) segments.pop();\n    } else if (segment !== \".\") {\n      segments.push(segment);\n    }\n  });\n\n  return segments.length > 1 ? segments.join(\"/\") : \"/\";\n}\n\nfunction getInvalidPathError(\n  char: string,\n  field: string,\n  dest: string,\n  path: Partial<Path>\n) {\n  return (\n    `Cannot include a '${char}' character in a manually specified ` +\n    `\\`to.${field}\\` field [${JSON.stringify(\n      path\n    )}].  Please separate it out to the ` +\n    `\\`to.${dest}\\` field. Alternatively you may provide the full path as ` +\n    `a string in <Link to=\"...\"> and the router will parse it for you.`\n  );\n}\n\n/**\n * @private\n *\n * When processing relative navigation we want to ignore ancestor routes that\n * do not contribute to the path, such that index/pathless layout routes don't\n * interfere.\n *\n * For example, when moving a route element into an index route and/or a\n * pathless layout route, relative link behavior contained within should stay\n * the same.  Both of the following examples should link back to the root:\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\" element={<Link to=\"..\"}>\n *   </Route>\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\">\n *       <Route element={<AccountsLayout />}>       // <-- Does not contribute\n *         <Route index element={<Link to=\"..\"} />  // <-- Does not contribute\n *       </Route\n *     </Route>\n *   </Route>\n */\nexport function getPathContributingMatches<\n  T extends AgnosticRouteMatch = AgnosticRouteMatch\n>(matches: T[]) {\n  return matches.filter(\n    (match, index) =>\n      index === 0 || (match.route.path && match.route.path.length > 0)\n  );\n}\n\n/**\n * @private\n */\nexport function resolveTo(\n  toArg: To,\n  routePathnames: string[],\n  locationPathname: string,\n  isPathRelative = false\n): Path {\n  let to: Partial<Path>;\n  if (typeof toArg === \"string\") {\n    to = parsePath(toArg);\n  } else {\n    to = { ...toArg };\n\n    invariant(\n      !to.pathname || !to.pathname.includes(\"?\"),\n      getInvalidPathError(\"?\", \"pathname\", \"search\", to)\n    );\n    invariant(\n      !to.pathname || !to.pathname.includes(\"#\"),\n      getInvalidPathError(\"#\", \"pathname\", \"hash\", to)\n    );\n    invariant(\n      !to.search || !to.search.includes(\"#\"),\n      getInvalidPathError(\"#\", \"search\", \"hash\", to)\n    );\n  }\n\n  let isEmptyPath = toArg === \"\" || to.pathname === \"\";\n  let toPathname = isEmptyPath ? \"/\" : to.pathname;\n\n  let from: string;\n\n  // Routing is relative to the current pathname if explicitly requested.\n  //\n  // If a pathname is explicitly provided in `to`, it should be relative to the\n  // route context. This is explained in `Note on `<Link to>` values` in our\n  // migration guide from v5 as a means of disambiguation between `to` values\n  // that begin with `/` and those that do not. However, this is problematic for\n  // `to` values that do not provide a pathname. `to` can simply be a search or\n  // hash string, in which case we should assume that the navigation is relative\n  // to the current location's pathname and *not* the route pathname.\n  if (isPathRelative || toPathname == null) {\n    from = locationPathname;\n  } else {\n    let routePathnameIndex = routePathnames.length - 1;\n\n    if (toPathname.startsWith(\"..\")) {\n      let toSegments = toPathname.split(\"/\");\n\n      // Each leading .. segment means \"go up one route\" instead of \"go up one\n      // URL segment\".  This is a key difference from how <a href> works and a\n      // major reason we call this a \"to\" value instead of a \"href\".\n      while (toSegments[0] === \"..\") {\n        toSegments.shift();\n        routePathnameIndex -= 1;\n      }\n\n      to.pathname = toSegments.join(\"/\");\n    }\n\n    // If there are more \"..\" segments than parent routes, resolve relative to\n    // the root / URL.\n    from = routePathnameIndex >= 0 ? routePathnames[routePathnameIndex] : \"/\";\n  }\n\n  let path = resolvePath(to, from);\n\n  // Ensure the pathname has a trailing slash if the original \"to\" had one\n  let hasExplicitTrailingSlash =\n    toPathname && toPathname !== \"/\" && toPathname.endsWith(\"/\");\n  // Or if this was a link to the current path which has a trailing slash\n  let hasCurrentTrailingSlash =\n    (isEmptyPath || toPathname === \".\") && locationPathname.endsWith(\"/\");\n  if (\n    !path.pathname.endsWith(\"/\") &&\n    (hasExplicitTrailingSlash || hasCurrentTrailingSlash)\n  ) {\n    path.pathname += \"/\";\n  }\n\n  return path;\n}\n\n/**\n * @private\n */\nexport function getToPathname(to: To): string | undefined {\n  // Empty strings should be treated the same as / paths\n  return to === \"\" || (to as Path).pathname === \"\"\n    ? \"/\"\n    : typeof to === \"string\"\n    ? parsePath(to).pathname\n    : to.pathname;\n}\n\n/**\n * @private\n */\nexport const joinPaths = (paths: string[]): string =>\n  paths.join(\"/\").replace(/\\/\\/+/g, \"/\");\n\n/**\n * @private\n */\nexport const normalizePathname = (pathname: string): string =>\n  pathname.replace(/\\/+$/, \"\").replace(/^\\/*/, \"/\");\n\n/**\n * @private\n */\nexport const normalizeSearch = (search: string): string =>\n  !search || search === \"?\"\n    ? \"\"\n    : search.startsWith(\"?\")\n    ? search\n    : \"?\" + search;\n\n/**\n * @private\n */\nexport const normalizeHash = (hash: string): string =>\n  !hash || hash === \"#\" ? \"\" : hash.startsWith(\"#\") ? hash : \"#\" + hash;\n\nexport type JsonFunction = <Data>(\n  data: Data,\n  init?: number | ResponseInit\n) => Response;\n\n/**\n * This is a shortcut for creating `application/json` responses. Converts `data`\n * to JSON and sets the `Content-Type` header.\n */\nexport const json: JsonFunction = (data, init = {}) => {\n  let responseInit = typeof init === \"number\" ? { status: init } : init;\n\n  let headers = new Headers(responseInit.headers);\n  if (!headers.has(\"Content-Type\")) {\n    headers.set(\"Content-Type\", \"application/json; charset=utf-8\");\n  }\n\n  return new Response(JSON.stringify(data), {\n    ...responseInit,\n    headers,\n  });\n};\n\nexport interface TrackedPromise extends Promise<any> {\n  _tracked?: boolean;\n  _data?: any;\n  _error?: any;\n}\n\nexport class AbortedDeferredError extends Error {}\n\nexport class DeferredData {\n  private pendingKeys: Set<string | number> = new Set<string | number>();\n  private controller: AbortController;\n  private abortPromise: Promise<void>;\n  private unlistenAbortSignal: () => void;\n  private subscriber?: (aborted: boolean) => void = undefined;\n  data: Record<string, unknown>;\n\n  constructor(data: Record<string, unknown>) {\n    invariant(\n      data && typeof data === \"object\" && !Array.isArray(data),\n      \"defer() only accepts plain objects\"\n    );\n\n    // Set up an AbortController + Promise we can race against to exit early\n    // cancellation\n    let reject: (e: AbortedDeferredError) => void;\n    this.abortPromise = new Promise((_, r) => (reject = r));\n    this.controller = new AbortController();\n    let onAbort = () =>\n      reject(new AbortedDeferredError(\"Deferred data aborted\"));\n    this.unlistenAbortSignal = () =>\n      this.controller.signal.removeEventListener(\"abort\", onAbort);\n    this.controller.signal.addEventListener(\"abort\", onAbort);\n\n    this.data = Object.entries(data).reduce(\n      (acc, [key, value]) =>\n        Object.assign(acc, {\n          [key]: this.trackPromise(key, value),\n        }),\n      {}\n    );\n  }\n\n  private trackPromise(\n    key: string | number,\n    value: Promise<unknown> | unknown\n  ): TrackedPromise | unknown {\n    if (!(value instanceof Promise)) {\n      return value;\n    }\n\n    this.pendingKeys.add(key);\n\n    // We store a little wrapper promise that will be extended with\n    // _data/_error props upon resolve/reject\n    let promise: TrackedPromise = Promise.race([value, this.abortPromise]).then(\n      (data) => this.onSettle(promise, key, null, data as unknown),\n      (error) => this.onSettle(promise, key, error as unknown)\n    );\n\n    // Register rejection listeners to avoid uncaught promise rejections on\n    // errors or aborted deferred values\n    promise.catch(() => {});\n\n    Object.defineProperty(promise, \"_tracked\", { get: () => true });\n    return promise;\n  }\n\n  private onSettle(\n    promise: TrackedPromise,\n    key: string | number,\n    error: unknown,\n    data?: unknown\n  ): unknown {\n    if (\n      this.controller.signal.aborted &&\n      error instanceof AbortedDeferredError\n    ) {\n      this.unlistenAbortSignal();\n      Object.defineProperty(promise, \"_error\", { get: () => error });\n      return Promise.reject(error);\n    }\n\n    this.pendingKeys.delete(key);\n\n    if (this.done) {\n      // Nothing left to abort!\n      this.unlistenAbortSignal();\n    }\n\n    const subscriber = this.subscriber;\n    if (error) {\n      Object.defineProperty(promise, \"_error\", { get: () => error });\n      subscriber && subscriber(false);\n      return Promise.reject(error);\n    }\n\n    Object.defineProperty(promise, \"_data\", { get: () => data });\n    subscriber && subscriber(false);\n    return data;\n  }\n\n  subscribe(fn: (aborted: boolean) => void) {\n    this.subscriber = fn;\n  }\n\n  cancel() {\n    this.controller.abort();\n    this.pendingKeys.forEach((v, k) => this.pendingKeys.delete(k));\n    let subscriber = this.subscriber;\n    subscriber && subscriber(true);\n  }\n\n  async resolveData(signal: AbortSignal) {\n    let aborted = false;\n    if (!this.done) {\n      let onAbort = () => this.cancel();\n      signal.addEventListener(\"abort\", onAbort);\n      aborted = await new Promise((resolve) => {\n        this.subscribe((aborted) => {\n          signal.removeEventListener(\"abort\", onAbort);\n          if (aborted || this.done) {\n            resolve(aborted);\n          }\n        });\n      });\n    }\n    return aborted;\n  }\n\n  get done() {\n    return this.pendingKeys.size === 0;\n  }\n\n  get unwrappedData() {\n    invariant(\n      this.data !== null && this.done,\n      \"Can only unwrap data on initialized and settled deferreds\"\n    );\n\n    return Object.entries(this.data).reduce(\n      (acc, [key, value]) =>\n        Object.assign(acc, {\n          [key]: unwrapTrackedPromise(value),\n        }),\n      {}\n    );\n  }\n}\n\nfunction isTrackedPromise(value: any): value is TrackedPromise {\n  return (\n    value instanceof Promise && (value as TrackedPromise)._tracked === true\n  );\n}\n\nfunction unwrapTrackedPromise(value: any) {\n  if (!isTrackedPromise(value)) {\n    return value;\n  }\n\n  if (value._error) {\n    throw value._error;\n  }\n  return value._data;\n}\n\nexport function defer(data: Record<string, unknown>) {\n  return new DeferredData(data);\n}\n\nexport type RedirectFunction = (\n  url: string,\n  init?: number | ResponseInit\n) => Response;\n\n/**\n * A redirect response. Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nexport const redirect: RedirectFunction = (url, init = 302) => {\n  let responseInit = init;\n  if (typeof responseInit === \"number\") {\n    responseInit = { status: responseInit };\n  } else if (typeof responseInit.status === \"undefined\") {\n    responseInit.status = 302;\n  }\n\n  let headers = new Headers(responseInit.headers);\n  headers.set(\"Location\", url);\n\n  return new Response(null, {\n    ...responseInit,\n    headers,\n  });\n};\n\n/**\n * @private\n * Utility class we use to hold auto-unwrapped 4xx/5xx Response bodies\n */\nexport class ErrorResponse {\n  status: number;\n  statusText: string;\n  data: any;\n\n  constructor(status: number, statusText: string | undefined, data: any) {\n    this.status = status;\n    this.statusText = statusText || \"\";\n    this.data = data;\n  }\n}\n\n/**\n * Check if the given error is an ErrorResponse generated from a 4xx/5xx\n * Response throw from an action/loader\n */\nexport function isRouteErrorResponse(e: any): e is ErrorResponse {\n  return e instanceof ErrorResponse;\n}\n", "import type { History, Location, To } from \"./history\";\nimport {\n  Action as HistoryAction,\n  createLocation,\n  createPath,\n  createURL,\n  parsePath,\n} from \"./history\";\nimport type {\n  DataResult,\n  AgnosticDataRouteMatch,\n  AgnosticDataRouteObject,\n  DeferredResult,\n  ErrorR<PERSON>ult,\n  FormEncType,\n  FormMethod,\n  Redirect<PERSON><PERSON>ult,\n  RouteData,\n  AgnosticRouteObject,\n  Submission,\n  SuccessResult,\n  AgnosticRouteMatch,\n} from \"./utils\";\nimport {\n  DeferredData,\n  ErrorResponse,\n  ResultType,\n  convertRoutesToDataRoutes,\n  getPathContributingMatches,\n  invariant,\n  isRouteErrorResponse,\n  joinPaths,\n  matchRoutes,\n  resolveTo,\n} from \"./utils\";\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Types and Constants\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A Router instance manages all navigation and data loading/mutations\n */\nexport interface Router {\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the basename for the router\n   */\n  get basename(): RouterInit[\"basename\"];\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the current state of the router\n   */\n  get state(): RouterState;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the routes for this router instance\n   */\n  get routes(): AgnosticDataRouteObject[];\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Initialize the router, including adding history listeners and kicking off\n   * initial data fetches.  Returns a function to cleanup listeners and abort\n   * any in-progress loads\n   */\n  initialize(): Router;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Subscribe to router.state updates\n   *\n   * @param fn function to call with the new state\n   */\n  subscribe(fn: RouterSubscriber): () => void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Enable scroll restoration behavior in the router\n   *\n   * @param savedScrollPositions Object that will manage positions, in case\n   *                             it's being restored from sessionStorage\n   * @param getScrollPosition    Function to get the active Y scroll position\n   * @param getKey               Function to get the key to use for restoration\n   */\n  enableScrollRestoration(\n    savedScrollPositions: Record<string, number>,\n    getScrollPosition: GetScrollPositionFunction,\n    getKey?: GetScrollRestorationKeyFunction\n  ): () => void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Navigate forward/backward in the history stack\n   * @param to Delta to move in the history stack\n   */\n  navigate(to: number): void;\n\n  /**\n   * Navigate to the given path\n   * @param to Path to navigate to\n   * @param opts Navigation options (method, submission, etc.)\n   */\n  navigate(to: To, opts?: RouterNavigateOptions): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Trigger a fetcher load/submission\n   *\n   * @param key     Fetcher key\n   * @param routeId Route that owns the fetcher\n   * @param href    href to fetch\n   * @param opts    Fetcher options, (method, submission, etc.)\n   */\n  fetch(\n    key: string,\n    routeId: string,\n    href: string,\n    opts?: RouterNavigateOptions\n  ): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Trigger a revalidation of all current route loaders and fetcher loads\n   */\n  revalidate(): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Utility function to create an href for the given location\n   * @param location\n   */\n  createHref(location: Location | URL): string;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Get/create a fetcher for the given key\n   * @param key\n   */\n  getFetcher<TData = any>(key?: string): Fetcher<TData>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Delete the fetcher for a given key\n   * @param key\n   */\n  deleteFetcher(key?: string): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Cleanup listeners and abort any in-progress loads\n   */\n  dispose(): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Internal fetch AbortControllers accessed by unit tests\n   */\n  _internalFetchControllers: Map<string, AbortController>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Internal pending DeferredData instances accessed by unit tests\n   */\n  _internalActiveDeferreds: Map<string, DeferredData>;\n}\n\n/**\n * State maintained internally by the router.  During a navigation, all states\n * reflect the the \"old\" location unless otherwise noted.\n */\nexport interface RouterState {\n  /**\n   * The action of the most recent navigation\n   */\n  historyAction: HistoryAction;\n\n  /**\n   * The current location reflected by the router\n   */\n  location: Location;\n\n  /**\n   * The current set of route matches\n   */\n  matches: AgnosticDataRouteMatch[];\n\n  /**\n   * Tracks whether we've completed our initial data load\n   */\n  initialized: boolean;\n\n  /**\n   * Current scroll position we should start at for a new view\n   *  - number -> scroll position to restore to\n   *  - false -> do not restore scroll at all (used during submissions)\n   *  - null -> don't have a saved position, scroll to hash or top of page\n   */\n  restoreScrollPosition: number | false | null;\n\n  /**\n   * Indicate whether this navigation should skip resetting the scroll position\n   * if we are unable to restore the scroll position\n   */\n  preventScrollReset: boolean;\n\n  /**\n   * Tracks the state of the current navigation\n   */\n  navigation: Navigation;\n\n  /**\n   * Tracks any in-progress revalidations\n   */\n  revalidation: RevalidationState;\n\n  /**\n   * Data from the loaders for the current matches\n   */\n  loaderData: RouteData;\n\n  /**\n   * Data from the action for the current matches\n   */\n  actionData: RouteData | null;\n\n  /**\n   * Errors caught from loaders for the current matches\n   */\n  errors: RouteData | null;\n\n  /**\n   * Map of current fetchers\n   */\n  fetchers: Map<string, Fetcher>;\n}\n\n/**\n * Data that can be passed into hydrate a Router from SSR\n */\nexport type HydrationState = Partial<\n  Pick<RouterState, \"loaderData\" | \"actionData\" | \"errors\">\n>;\n\n/**\n * Initialization options for createRouter\n */\nexport interface RouterInit {\n  basename?: string;\n  routes: AgnosticRouteObject[];\n  history: History;\n  hydrationData?: HydrationState;\n}\n\n/**\n * State returned from a server-side query() call\n */\nexport interface StaticHandlerContext {\n  location: RouterState[\"location\"];\n  matches: RouterState[\"matches\"];\n  loaderData: RouterState[\"loaderData\"];\n  actionData: RouterState[\"actionData\"];\n  errors: RouterState[\"errors\"];\n  statusCode: number;\n  loaderHeaders: Record<string, Headers>;\n  actionHeaders: Record<string, Headers>;\n  _deepestRenderedBoundaryId?: string | null;\n}\n\n/**\n * A StaticHandler instance manages a singular SSR navigation/fetch event\n */\nexport interface StaticHandler {\n  dataRoutes: AgnosticDataRouteObject[];\n  query(request: Request): Promise<StaticHandlerContext | Response>;\n  queryRoute(request: Request, routeId?: string): Promise<any>;\n}\n\n/**\n * Subscriber function signature for changes to router state\n */\nexport interface RouterSubscriber {\n  (state: RouterState): void;\n}\n\ninterface UseMatchesMatch {\n  id: string;\n  pathname: string;\n  params: AgnosticRouteMatch[\"params\"];\n  data: unknown;\n  handle: unknown;\n}\n\n/**\n * Function signature for determining the key to be used in scroll restoration\n * for a given location\n */\nexport interface GetScrollRestorationKeyFunction {\n  (location: Location, matches: UseMatchesMatch[]): string | null;\n}\n\n/**\n * Function signature for determining the current scroll position\n */\nexport interface GetScrollPositionFunction {\n  (): number;\n}\n\n/**\n * Options for a navigate() call for a Link navigation\n */\ntype LinkNavigateOptions = {\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n};\n\n/**\n * Options for a navigate() call for a Form navigation\n */\ntype SubmissionNavigateOptions = {\n  replace?: boolean;\n  state?: any;\n  formMethod?: FormMethod;\n  formEncType?: FormEncType;\n  formData: FormData;\n};\n\n/**\n * Options to pass to navigate() for either a Link or Form navigation\n */\nexport type RouterNavigateOptions =\n  | LinkNavigateOptions\n  | SubmissionNavigateOptions;\n\n/**\n * Options to pass to fetch()\n */\nexport type RouterFetchOptions =\n  | Omit<LinkNavigateOptions, \"replace\">\n  | Omit<SubmissionNavigateOptions, \"replace\">;\n\n/**\n * Potential states for state.navigation\n */\nexport type NavigationStates = {\n  Idle: {\n    state: \"idle\";\n    location: undefined;\n    formMethod: undefined;\n    formAction: undefined;\n    formEncType: undefined;\n    formData: undefined;\n  };\n  Loading: {\n    state: \"loading\";\n    location: Location;\n    formMethod: FormMethod | undefined;\n    formAction: string | undefined;\n    formEncType: FormEncType | undefined;\n    formData: FormData | undefined;\n  };\n  Submitting: {\n    state: \"submitting\";\n    location: Location;\n    formMethod: FormMethod;\n    formAction: string;\n    formEncType: FormEncType;\n    formData: FormData;\n  };\n};\n\nexport type Navigation = NavigationStates[keyof NavigationStates];\n\nexport type RevalidationState = \"idle\" | \"loading\";\n\n/**\n * Potential states for fetchers\n */\ntype FetcherStates<TData = any> = {\n  Idle: {\n    state: \"idle\";\n    formMethod: undefined;\n    formAction: undefined;\n    formEncType: undefined;\n    formData: undefined;\n    data: TData | undefined;\n  };\n  Loading: {\n    state: \"loading\";\n    formMethod: FormMethod | undefined;\n    formAction: string | undefined;\n    formEncType: FormEncType | undefined;\n    formData: FormData | undefined;\n    data: TData | undefined;\n  };\n  Submitting: {\n    state: \"submitting\";\n    formMethod: FormMethod;\n    formAction: string;\n    formEncType: FormEncType;\n    formData: FormData;\n    data: TData | undefined;\n  };\n};\n\nexport type Fetcher<TData = any> =\n  FetcherStates<TData>[keyof FetcherStates<TData>];\n\ninterface ShortCircuitable {\n  /**\n   * startNavigation does not need to complete the navigation because we\n   * redirected or got interrupted\n   */\n  shortCircuited?: boolean;\n}\n\ninterface HandleActionResult extends ShortCircuitable {\n  /**\n   * Error thrown from the current action, keyed by the route containing the\n   * error boundary to render the error.  To be committed to the state after\n   * loaders have completed\n   */\n  pendingActionError?: RouteData;\n  /**\n   * Data returned from the current action, keyed by the route owning the action.\n   * To be committed to the state after loaders have completed\n   */\n  pendingActionData?: RouteData;\n}\n\ninterface HandleLoadersResult extends ShortCircuitable {\n  /**\n   * loaderData returned from the current set of loaders\n   */\n  loaderData?: RouterState[\"loaderData\"];\n  /**\n   * errors thrown from the current set of loaders\n   */\n  errors?: RouterState[\"errors\"];\n}\n\n/**\n * Tuple of [key, href, DataRouteMatch, DataRouteMatch[]] for a revalidating\n * fetcher.load()\n */\ntype RevalidatingFetcher = [\n  string,\n  string,\n  AgnosticDataRouteMatch,\n  AgnosticDataRouteMatch[]\n];\n\n/**\n * Tuple of [href, DataRouteMatch, DataRouteMatch[]] for an active\n * fetcher.load()\n */\ntype FetchLoadMatch = [\n  string,\n  AgnosticDataRouteMatch,\n  AgnosticDataRouteMatch[]\n];\n\n/**\n * Wrapper object to allow us to throw any response out from callLoaderOrAction\n * for queryRouter while preserving whether or not it was thrown or returned\n * from the loader/action\n */\ninterface QueryRouteResponse {\n  type: ResultType.data | ResultType.error;\n  response: Response;\n}\n\nexport const IDLE_NAVIGATION: NavigationStates[\"Idle\"] = {\n  state: \"idle\",\n  location: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined,\n};\n\nexport const IDLE_FETCHER: FetcherStates[\"Idle\"] = {\n  state: \"idle\",\n  data: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined,\n};\n\nconst isBrowser =\n  typeof window !== \"undefined\" &&\n  typeof window.document !== \"undefined\" &&\n  typeof window.document.createElement !== \"undefined\";\nconst isServer = !isBrowser;\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region createRouter\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Create a router and listen to history POP navigations\n */\nexport function createRouter(init: RouterInit): Router {\n  invariant(\n    init.routes.length > 0,\n    \"You must provide a non-empty routes array to createRouter\"\n  );\n\n  let dataRoutes = convertRoutesToDataRoutes(init.routes);\n  // Cleanup function for history\n  let unlistenHistory: (() => void) | null = null;\n  // Externally-provided functions to call on all state changes\n  let subscribers = new Set<RouterSubscriber>();\n  // Externally-provided object to hold scroll restoration locations during routing\n  let savedScrollPositions: Record<string, number> | null = null;\n  // Externally-provided function to get scroll restoration keys\n  let getScrollRestorationKey: GetScrollRestorationKeyFunction | null = null;\n  // Externally-provided function to get current scroll position\n  let getScrollPosition: GetScrollPositionFunction | null = null;\n  // One-time flag to control the initial hydration scroll restoration.  Because\n  // we don't get the saved positions from <ScrollRestoration /> until _after_\n  // the initial render, we need to manually trigger a separate updateState to\n  // send along the restoreScrollPosition\n  let initialScrollRestored = false;\n\n  let initialMatches = matchRoutes(\n    dataRoutes,\n    init.history.location,\n    init.basename\n  );\n  let initialErrors: RouteData | null = null;\n\n  if (initialMatches == null) {\n    // If we do not match a user-provided-route, fall back to the root\n    // to allow the error boundary to take over\n    let { matches, route, error } = getNotFoundMatches(dataRoutes);\n    initialMatches = matches;\n    initialErrors = { [route.id]: error };\n  }\n\n  let initialized =\n    !initialMatches.some((m) => m.route.loader) || init.hydrationData != null;\n\n  let router: Router;\n  let state: RouterState = {\n    historyAction: init.history.action,\n    location: init.history.location,\n    matches: initialMatches,\n    initialized,\n    navigation: IDLE_NAVIGATION,\n    restoreScrollPosition: null,\n    preventScrollReset: false,\n    revalidation: \"idle\",\n    loaderData: (init.hydrationData && init.hydrationData.loaderData) || {},\n    actionData: (init.hydrationData && init.hydrationData.actionData) || null,\n    errors: (init.hydrationData && init.hydrationData.errors) || initialErrors,\n    fetchers: new Map(),\n  };\n\n  // -- Stateful internal variables to manage navigations --\n  // Current navigation in progress (to be committed in completeNavigation)\n  let pendingAction: HistoryAction = HistoryAction.Pop;\n  // Should the current navigation prevent the scroll reset if scroll cannot\n  // be restored?\n  let pendingPreventScrollReset = false;\n  // AbortController for the active navigation\n  let pendingNavigationController: AbortController | null;\n  // We use this to avoid touching history in completeNavigation if a\n  // revalidation is entirely uninterrupted\n  let isUninterruptedRevalidation = false;\n  // Use this internal flag to force revalidation of all loaders:\n  //  - submissions (completed or interrupted)\n  //  - useRevalidate()\n  //  - X-Remix-Revalidate (from redirect)\n  let isRevalidationRequired = false;\n  // Use this internal array to capture routes that require revalidation due\n  // to a cancelled deferred on action submission\n  let cancelledDeferredRoutes: string[] = [];\n  // Use this internal array to capture fetcher loads that were cancelled by an\n  // action navigation and require revalidation\n  let cancelledFetcherLoads: string[] = [];\n  // AbortControllers for any in-flight fetchers\n  let fetchControllers = new Map<string, AbortController>();\n  // Track loads based on the order in which they started\n  let incrementingLoadId = 0;\n  // Track the outstanding pending navigation data load to be compared against\n  // the globally incrementing load when a fetcher load lands after a completed\n  // navigation\n  let pendingNavigationLoadId = -1;\n  // Fetchers that triggered data reloads as a result of their actions\n  let fetchReloadIds = new Map<string, number>();\n  // Fetchers that triggered redirect navigations from their actions\n  let fetchRedirectIds = new Set<string>();\n  // Most recent href/match for fetcher.load calls for fetchers\n  let fetchLoadMatches = new Map<string, FetchLoadMatch>();\n  // Store DeferredData instances for active route matches.  When a\n  // route loader returns defer() we stick one in here.  Then, when a nested\n  // promise resolves we update loaderData.  If a new navigation starts we\n  // cancel active deferreds for eliminated routes.\n  let activeDeferreds = new Map<string, DeferredData>();\n\n  // Initialize the router, all side effects should be kicked off from here.\n  // Implemented as a Fluent API for ease of:\n  //   let router = createRouter(init).initialize();\n  function initialize() {\n    // If history informs us of a POP navigation, start the navigation but do not update\n    // state.  We'll update our own state once the navigation completes\n    unlistenHistory = init.history.listen(\n      ({ action: historyAction, location }) =>\n        startNavigation(historyAction, location)\n    );\n\n    // Kick off initial data load if needed.  Use Pop to avoid modifying history\n    if (!state.initialized) {\n      startNavigation(HistoryAction.Pop, state.location);\n    }\n\n    return router;\n  }\n\n  // Clean up a router and it's side effects\n  function dispose() {\n    if (unlistenHistory) {\n      unlistenHistory();\n    }\n    subscribers.clear();\n    pendingNavigationController && pendingNavigationController.abort();\n    state.fetchers.forEach((_, key) => deleteFetcher(key));\n  }\n\n  // Subscribe to state updates for the router\n  function subscribe(fn: RouterSubscriber) {\n    subscribers.add(fn);\n    return () => subscribers.delete(fn);\n  }\n\n  // Update our state and notify the calling context of the change\n  function updateState(newState: Partial<RouterState>): void {\n    state = {\n      ...state,\n      ...newState,\n    };\n    subscribers.forEach((subscriber) => subscriber(state));\n  }\n\n  // Complete a navigation returning the state.navigation back to the IDLE_NAVIGATION\n  // and setting state.[historyAction/location/matches] to the new route.\n  // - Location is a required param\n  // - Navigation will always be set to IDLE_NAVIGATION\n  // - Can pass any other state in newState\n  function completeNavigation(\n    location: Location,\n    newState: Partial<Omit<RouterState, \"action\" | \"location\" | \"navigation\">>\n  ): void {\n    // Deduce if we're in a loading/actionReload state:\n    // - We have committed actionData in the store\n    // - The current navigation was a submission\n    // - We're past the submitting state and into the loading state\n    // - The location we've finished loading is different from the submission\n    //   location, indicating we redirected from the action (avoids false\n    //   positives for loading/submissionRedirect when actionData returned\n    //   on a prior submission)\n    let isActionReload =\n      state.actionData != null &&\n      state.navigation.formMethod != null &&\n      state.navigation.state === \"loading\" &&\n      state.navigation.formAction?.split(\"?\")[0] === location.pathname;\n\n    // Always preserve any existing loaderData from re-used routes\n    let newLoaderData = newState.loaderData\n      ? {\n          loaderData: mergeLoaderData(\n            state.loaderData,\n            newState.loaderData,\n            newState.matches || []\n          ),\n        }\n      : {};\n\n    updateState({\n      // Clear existing actionData on any completed navigation beyond the original\n      // action, unless we're currently finishing the loading/actionReload state.\n      // Do this prior to spreading in newState in case we got back to back actions\n      ...(isActionReload ? {} : { actionData: null }),\n      ...newState,\n      ...newLoaderData,\n      historyAction: pendingAction,\n      location,\n      initialized: true,\n      navigation: IDLE_NAVIGATION,\n      revalidation: \"idle\",\n      // Don't restore on submission navigations\n      restoreScrollPosition: state.navigation.formData\n        ? false\n        : getSavedScrollPosition(location, newState.matches || state.matches),\n      preventScrollReset: pendingPreventScrollReset,\n    });\n\n    if (isUninterruptedRevalidation) {\n      // If this was an uninterrupted revalidation then do not touch history\n    } else if (pendingAction === HistoryAction.Pop) {\n      // Do nothing for POP - URL has already been updated\n    } else if (pendingAction === HistoryAction.Push) {\n      init.history.push(location, location.state);\n    } else if (pendingAction === HistoryAction.Replace) {\n      init.history.replace(location, location.state);\n    }\n\n    // Reset stateful navigation vars\n    pendingAction = HistoryAction.Pop;\n    pendingPreventScrollReset = false;\n    isUninterruptedRevalidation = false;\n    isRevalidationRequired = false;\n    cancelledDeferredRoutes = [];\n    cancelledFetcherLoads = [];\n  }\n\n  // Trigger a navigation event, which can either be a numerical POP or a PUSH\n  // replace with an optional submission\n  async function navigate(\n    to: number | To,\n    opts?: RouterNavigateOptions\n  ): Promise<void> {\n    if (typeof to === \"number\") {\n      init.history.go(to);\n      return;\n    }\n\n    let { path, submission, error } = normalizeNavigateOptions(to, opts);\n\n    let location = createLocation(state.location, path, opts && opts.state);\n\n    // When using navigate as a PUSH/REPLACE we aren't reading an already-encoded\n    // URL from window.location, so we need to encode it here so the behavior\n    // remains the same as POP and non-data-router usages.  new URL() does all\n    // the same encoding we'd get from a history.pushState/window.location read\n    // without having to touch history\n    location = init.history.encodeLocation(location);\n\n    let historyAction =\n      (opts && opts.replace) === true || submission != null\n        ? HistoryAction.Replace\n        : HistoryAction.Push;\n    let preventScrollReset =\n      opts && \"preventScrollReset\" in opts\n        ? opts.preventScrollReset === true\n        : undefined;\n\n    return await startNavigation(historyAction, location, {\n      submission,\n      // Send through the formData serialization error if we have one so we can\n      // render at the right error boundary after we match routes\n      pendingError: error,\n      preventScrollReset,\n      replace: opts && opts.replace,\n    });\n  }\n\n  // Revalidate all current loaders.  If a navigation is in progress or if this\n  // is interrupted by a navigation, allow this to \"succeed\" by calling all\n  // loaders during the next loader round\n  function revalidate() {\n    interruptActiveLoads();\n    updateState({ revalidation: \"loading\" });\n\n    // If we're currently submitting an action, we don't need to start a new\n    // navigation, we'll just let the follow up loader execution call all loaders\n    if (state.navigation.state === \"submitting\") {\n      return;\n    }\n\n    // If we're currently in an idle state, start a new navigation for the current\n    // action/location and mark it as uninterrupted, which will skip the history\n    // update in completeNavigation\n    if (state.navigation.state === \"idle\") {\n      startNavigation(state.historyAction, state.location, {\n        startUninterruptedRevalidation: true,\n      });\n      return;\n    }\n\n    // Otherwise, if we're currently in a loading state, just start a new\n    // navigation to the navigation.location but do not trigger an uninterrupted\n    // revalidation so that history correctly updates once the navigation completes\n    startNavigation(\n      pendingAction || state.historyAction,\n      state.navigation.location,\n      { overrideNavigation: state.navigation }\n    );\n  }\n\n  // Start a navigation to the given action/location.  Can optionally provide a\n  // overrideNavigation which will override the normalLoad in the case of a redirect\n  // navigation\n  async function startNavigation(\n    historyAction: HistoryAction,\n    location: Location,\n    opts?: {\n      submission?: Submission;\n      overrideNavigation?: Navigation;\n      pendingError?: ErrorResponse;\n      startUninterruptedRevalidation?: boolean;\n      preventScrollReset?: boolean;\n      replace?: boolean;\n    }\n  ): Promise<void> {\n    // Abort any in-progress navigations and start a new one. Unset any ongoing\n    // uninterrupted revalidations unless told otherwise, since we want this\n    // new navigation to update history normally\n    pendingNavigationController && pendingNavigationController.abort();\n    pendingNavigationController = null;\n    pendingAction = historyAction;\n    isUninterruptedRevalidation =\n      (opts && opts.startUninterruptedRevalidation) === true;\n\n    // Save the current scroll position every time we start a new navigation,\n    // and track whether we should reset scroll on completion\n    saveScrollPosition(state.location, state.matches);\n    pendingPreventScrollReset = (opts && opts.preventScrollReset) === true;\n\n    let loadingNavigation = opts && opts.overrideNavigation;\n    let matches = matchRoutes(dataRoutes, location, init.basename);\n\n    // Short circuit with a 404 on the root error boundary if we match nothing\n    if (!matches) {\n      let {\n        matches: notFoundMatches,\n        route,\n        error,\n      } = getNotFoundMatches(dataRoutes);\n      // Cancel all pending deferred on 404s since we don't keep any routes\n      cancelActiveDeferreds();\n      completeNavigation(location, {\n        matches: notFoundMatches,\n        loaderData: {},\n        errors: {\n          [route.id]: error,\n        },\n      });\n      return;\n    }\n\n    // Short circuit if it's only a hash change\n    if (isHashChangeOnly(state.location, location)) {\n      completeNavigation(location, { matches });\n      return;\n    }\n\n    // Create a controller/Request for this navigation\n    pendingNavigationController = new AbortController();\n    let request = createRequest(\n      location,\n      pendingNavigationController.signal,\n      opts && opts.submission\n    );\n    let pendingActionData: RouteData | undefined;\n    let pendingError: RouteData | undefined;\n\n    if (opts && opts.pendingError) {\n      // If we have a pendingError, it means the user attempted a GET submission\n      // with binary FormData so assign here and skip to handleLoaders.  That\n      // way we handle calling loaders above the boundary etc.  It's not really\n      // different from an actionError in that sense.\n      pendingError = {\n        [findNearestBoundary(matches).route.id]: opts.pendingError,\n      };\n    } else if (opts && opts.submission) {\n      // Call action if we received an action submission\n      let actionOutput = await handleAction(\n        request,\n        location,\n        opts.submission,\n        matches,\n        { replace: opts.replace }\n      );\n\n      if (actionOutput.shortCircuited) {\n        return;\n      }\n\n      pendingActionData = actionOutput.pendingActionData;\n      pendingError = actionOutput.pendingActionError;\n\n      let navigation: NavigationStates[\"Loading\"] = {\n        state: \"loading\",\n        location,\n        ...opts.submission,\n      };\n      loadingNavigation = navigation;\n    }\n\n    // Call loaders\n    let { shortCircuited, loaderData, errors } = await handleLoaders(\n      request,\n      location,\n      matches,\n      loadingNavigation,\n      opts && opts.submission,\n      opts && opts.replace,\n      pendingActionData,\n      pendingError\n    );\n\n    if (shortCircuited) {\n      return;\n    }\n\n    // Clean up now that the action/loaders have completed.  Don't clean up if\n    // we short circuited because pendingNavigationController will have already\n    // been assigned to a new controller for the next navigation\n    pendingNavigationController = null;\n\n    completeNavigation(location, {\n      matches,\n      loaderData,\n      errors,\n    });\n  }\n\n  // Call the action matched by the leaf route for this navigation and handle\n  // redirects/errors\n  async function handleAction(\n    request: Request,\n    location: Location,\n    submission: Submission,\n    matches: AgnosticDataRouteMatch[],\n    opts?: { replace?: boolean }\n  ): Promise<HandleActionResult> {\n    interruptActiveLoads();\n\n    // Put us in a submitting state\n    let navigation: NavigationStates[\"Submitting\"] = {\n      state: \"submitting\",\n      location,\n      ...submission,\n    };\n    updateState({ navigation });\n\n    // Call our action and get the result\n    let result: DataResult;\n    let actionMatch = getTargetMatch(matches, location);\n\n    if (!actionMatch.route.action) {\n      result = getMethodNotAllowedResult(location);\n    } else {\n      result = await callLoaderOrAction(\n        \"action\",\n        request,\n        actionMatch,\n        matches,\n        router.basename\n      );\n\n      if (request.signal.aborted) {\n        return { shortCircuited: true };\n      }\n    }\n\n    if (isRedirectResult(result)) {\n      let redirectNavigation: NavigationStates[\"Loading\"] = {\n        state: \"loading\",\n        location: createLocation(state.location, result.location),\n        ...submission,\n      };\n      await startRedirectNavigation(\n        result,\n        redirectNavigation,\n        opts && opts.replace\n      );\n      return { shortCircuited: true };\n    }\n\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = findNearestBoundary(matches, actionMatch.route.id);\n\n      // By default, all submissions are REPLACE navigations, but if the\n      // action threw an error that'll be rendered in an errorElement, we fall\n      // back to PUSH so that the user can use the back button to get back to\n      // the pre-submission form location to try again\n      if ((opts && opts.replace) !== true) {\n        pendingAction = HistoryAction.Push;\n      }\n\n      return {\n        pendingActionError: { [boundaryMatch.route.id]: result.error },\n      };\n    }\n\n    if (isDeferredResult(result)) {\n      throw new Error(\"defer() is not supported in actions\");\n    }\n\n    return {\n      pendingActionData: { [actionMatch.route.id]: result.data },\n    };\n  }\n\n  // Call all applicable loaders for the given matches, handling redirects,\n  // errors, etc.\n  async function handleLoaders(\n    request: Request,\n    location: Location,\n    matches: AgnosticDataRouteMatch[],\n    overrideNavigation?: Navigation,\n    submission?: Submission,\n    replace?: boolean,\n    pendingActionData?: RouteData,\n    pendingError?: RouteData\n  ): Promise<HandleLoadersResult> {\n    // Figure out the right navigation we want to use for data loading\n    let loadingNavigation = overrideNavigation;\n    if (!loadingNavigation) {\n      let navigation: NavigationStates[\"Loading\"] = {\n        state: \"loading\",\n        location,\n        formMethod: undefined,\n        formAction: undefined,\n        formEncType: undefined,\n        formData: undefined,\n      };\n      loadingNavigation = navigation;\n    }\n\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(\n      state,\n      matches,\n      submission,\n      location,\n      isRevalidationRequired,\n      cancelledDeferredRoutes,\n      cancelledFetcherLoads,\n      pendingActionData,\n      pendingError,\n      fetchLoadMatches\n    );\n\n    // Cancel pending deferreds for no-longer-matched routes or routes we're\n    // about to reload.  Note that if this is an action reload we would have\n    // already cancelled all pending deferreds so this would be a no-op\n    cancelActiveDeferreds(\n      (routeId) =>\n        !(matches && matches.some((m) => m.route.id === routeId)) ||\n        (matchesToLoad && matchesToLoad.some((m) => m.route.id === routeId))\n    );\n\n    // Short circuit if we have no loaders to run\n    if (matchesToLoad.length === 0 && revalidatingFetchers.length === 0) {\n      completeNavigation(location, {\n        matches,\n        loaderData: mergeLoaderData(state.loaderData, {}, matches),\n        // Commit pending error if we're short circuiting\n        errors: pendingError || null,\n        actionData: pendingActionData || null,\n      });\n      return { shortCircuited: true };\n    }\n\n    // If this is an uninterrupted revalidation, we remain in our current idle\n    // state.  If not, we need to switch to our loading state and load data,\n    // preserving any new action data or existing action data (in the case of\n    // a revalidation interrupting an actionReload)\n    if (!isUninterruptedRevalidation) {\n      revalidatingFetchers.forEach(([key]) => {\n        let fetcher = state.fetchers.get(key);\n        let revalidatingFetcher: FetcherStates[\"Loading\"] = {\n          state: \"loading\",\n          data: fetcher && fetcher.data,\n          formMethod: undefined,\n          formAction: undefined,\n          formEncType: undefined,\n          formData: undefined,\n        };\n        state.fetchers.set(key, revalidatingFetcher);\n      });\n      updateState({\n        navigation: loadingNavigation,\n        actionData: pendingActionData || state.actionData || null,\n        ...(revalidatingFetchers.length > 0\n          ? { fetchers: new Map(state.fetchers) }\n          : {}),\n      });\n    }\n\n    pendingNavigationLoadId = ++incrementingLoadId;\n    revalidatingFetchers.forEach(([key]) =>\n      fetchControllers.set(key, pendingNavigationController!)\n    );\n\n    let { results, loaderResults, fetcherResults } =\n      await callLoadersAndMaybeResolveData(\n        state.matches,\n        matches,\n        matchesToLoad,\n        revalidatingFetchers,\n        request\n      );\n\n    if (request.signal.aborted) {\n      return { shortCircuited: true };\n    }\n\n    // Clean up _after_ loaders have completed.  Don't clean up if we short\n    // circuited because fetchControllers would have been aborted and\n    // reassigned to new controllers for the next navigation\n    revalidatingFetchers.forEach(([key]) => fetchControllers.delete(key));\n\n    // If any loaders returned a redirect Response, start a new REPLACE navigation\n    let redirect = findRedirect(results);\n    if (redirect) {\n      let redirectNavigation = getLoaderRedirect(state, redirect);\n      await startRedirectNavigation(redirect, redirectNavigation, replace);\n      return { shortCircuited: true };\n    }\n\n    // Process and commit output from loaders\n    let { loaderData, errors } = processLoaderData(\n      state,\n      matches,\n      matchesToLoad,\n      loaderResults,\n      pendingError,\n      revalidatingFetchers,\n      fetcherResults,\n      activeDeferreds\n    );\n\n    // Wire up subscribers to update loaderData as promises settle\n    activeDeferreds.forEach((deferredData, routeId) => {\n      deferredData.subscribe((aborted) => {\n        // Note: No need to updateState here since the TrackedPromise on\n        // loaderData is stable across resolve/reject\n        // Remove this instance if we were aborted or if promises have settled\n        if (aborted || deferredData.done) {\n          activeDeferreds.delete(routeId);\n        }\n      });\n    });\n\n    markFetchRedirectsDone();\n    let didAbortFetchLoads = abortStaleFetchLoads(pendingNavigationLoadId);\n\n    return {\n      loaderData,\n      errors,\n      ...(didAbortFetchLoads || revalidatingFetchers.length > 0\n        ? { fetchers: new Map(state.fetchers) }\n        : {}),\n    };\n  }\n\n  function getFetcher<TData = any>(key: string): Fetcher<TData> {\n    return state.fetchers.get(key) || IDLE_FETCHER;\n  }\n\n  // Trigger a fetcher load/submit for the given fetcher key\n  function fetch(\n    key: string,\n    routeId: string,\n    href: string,\n    opts?: RouterFetchOptions\n  ) {\n    if (isServer) {\n      throw new Error(\n        \"router.fetch() was called during the server render, but it shouldn't be. \" +\n          \"You are likely calling a useFetcher() method in the body of your component. \" +\n          \"Try moving it to a useEffect or a callback.\"\n      );\n    }\n\n    if (fetchControllers.has(key)) abortFetcher(key);\n\n    let matches = matchRoutes(dataRoutes, href, init.basename);\n    if (!matches) {\n      setFetcherError(key, routeId, new ErrorResponse(404, \"Not Found\", null));\n      return;\n    }\n\n    let { path, submission } = normalizeNavigateOptions(href, opts, true);\n    let match = getTargetMatch(matches, path);\n\n    if (submission) {\n      handleFetcherAction(key, routeId, path, match, matches, submission);\n      return;\n    }\n\n    // Store off the match so we can call it's shouldRevalidate on subsequent\n    // revalidations\n    fetchLoadMatches.set(key, [path, match, matches]);\n    handleFetcherLoader(key, routeId, path, match, matches);\n  }\n\n  // Call the action for the matched fetcher.submit(), and then handle redirects,\n  // errors, and revalidation\n  async function handleFetcherAction(\n    key: string,\n    routeId: string,\n    path: string,\n    match: AgnosticDataRouteMatch,\n    requestMatches: AgnosticDataRouteMatch[],\n    submission: Submission\n  ) {\n    interruptActiveLoads();\n    fetchLoadMatches.delete(key);\n\n    if (!match.route.action) {\n      let { error } = getMethodNotAllowedResult(path);\n      setFetcherError(key, routeId, error);\n      return;\n    }\n\n    // Put this fetcher into it's submitting state\n    let existingFetcher = state.fetchers.get(key);\n    let fetcher: FetcherStates[\"Submitting\"] = {\n      state: \"submitting\",\n      ...submission,\n      data: existingFetcher && existingFetcher.data,\n    };\n    state.fetchers.set(key, fetcher);\n    updateState({ fetchers: new Map(state.fetchers) });\n\n    // Call the action for the fetcher\n    let abortController = new AbortController();\n    let fetchRequest = createRequest(path, abortController.signal, submission);\n    fetchControllers.set(key, abortController);\n\n    let actionResult = await callLoaderOrAction(\n      \"action\",\n      fetchRequest,\n      match,\n      requestMatches,\n      router.basename\n    );\n\n    if (fetchRequest.signal.aborted) {\n      // We can delete this so long as we weren't aborted by ou our own fetcher\n      // re-submit which would have put _new_ controller is in fetchControllers\n      if (fetchControllers.get(key) === abortController) {\n        fetchControllers.delete(key);\n      }\n      return;\n    }\n\n    if (isRedirectResult(actionResult)) {\n      fetchControllers.delete(key);\n      fetchRedirectIds.add(key);\n      let loadingFetcher: FetcherStates[\"Loading\"] = {\n        state: \"loading\",\n        ...submission,\n        data: undefined,\n      };\n      state.fetchers.set(key, loadingFetcher);\n      updateState({ fetchers: new Map(state.fetchers) });\n\n      let redirectNavigation: NavigationStates[\"Loading\"] = {\n        state: \"loading\",\n        location: createLocation(state.location, actionResult.location),\n        ...submission,\n      };\n      await startRedirectNavigation(actionResult, redirectNavigation);\n      return;\n    }\n\n    // Process any non-redirect errors thrown\n    if (isErrorResult(actionResult)) {\n      setFetcherError(key, routeId, actionResult.error);\n      return;\n    }\n\n    if (isDeferredResult(actionResult)) {\n      invariant(false, \"defer() is not supported in actions\");\n    }\n\n    // Start the data load for current matches, or the next location if we're\n    // in the middle of a navigation\n    let nextLocation = state.navigation.location || state.location;\n    let revalidationRequest = createRequest(\n      nextLocation,\n      abortController.signal\n    );\n    let matches =\n      state.navigation.state !== \"idle\"\n        ? matchRoutes(dataRoutes, state.navigation.location, init.basename)\n        : state.matches;\n\n    invariant(matches, \"Didn't find any matches after fetcher action\");\n\n    let loadId = ++incrementingLoadId;\n    fetchReloadIds.set(key, loadId);\n\n    let loadFetcher: FetcherStates[\"Loading\"] = {\n      state: \"loading\",\n      data: actionResult.data,\n      ...submission,\n    };\n    state.fetchers.set(key, loadFetcher);\n\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(\n      state,\n      matches,\n      submission,\n      nextLocation,\n      isRevalidationRequired,\n      cancelledDeferredRoutes,\n      cancelledFetcherLoads,\n      { [match.route.id]: actionResult.data },\n      undefined, // No need to send through errors since we short circuit above\n      fetchLoadMatches\n    );\n\n    // Put all revalidating fetchers into the loading state, except for the\n    // current fetcher which we want to keep in it's current loading state which\n    // contains it's action submission info + action data\n    revalidatingFetchers\n      .filter(([staleKey]) => staleKey !== key)\n      .forEach(([staleKey]) => {\n        let existingFetcher = state.fetchers.get(staleKey);\n        let revalidatingFetcher: FetcherStates[\"Loading\"] = {\n          state: \"loading\",\n          data: existingFetcher && existingFetcher.data,\n          formMethod: undefined,\n          formAction: undefined,\n          formEncType: undefined,\n          formData: undefined,\n        };\n        state.fetchers.set(staleKey, revalidatingFetcher);\n        fetchControllers.set(staleKey, abortController);\n      });\n\n    updateState({ fetchers: new Map(state.fetchers) });\n\n    let { results, loaderResults, fetcherResults } =\n      await callLoadersAndMaybeResolveData(\n        state.matches,\n        matches,\n        matchesToLoad,\n        revalidatingFetchers,\n        revalidationRequest\n      );\n\n    if (abortController.signal.aborted) {\n      return;\n    }\n\n    fetchReloadIds.delete(key);\n    fetchControllers.delete(key);\n    revalidatingFetchers.forEach(([staleKey]) =>\n      fetchControllers.delete(staleKey)\n    );\n\n    let redirect = findRedirect(results);\n    if (redirect) {\n      let redirectNavigation = getLoaderRedirect(state, redirect);\n      await startRedirectNavigation(redirect, redirectNavigation);\n      return;\n    }\n\n    // Process and commit output from loaders\n    let { loaderData, errors } = processLoaderData(\n      state,\n      state.matches,\n      matchesToLoad,\n      loaderResults,\n      undefined,\n      revalidatingFetchers,\n      fetcherResults,\n      activeDeferreds\n    );\n\n    let doneFetcher: FetcherStates[\"Idle\"] = {\n      state: \"idle\",\n      data: actionResult.data,\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n    };\n    state.fetchers.set(key, doneFetcher);\n\n    let didAbortFetchLoads = abortStaleFetchLoads(loadId);\n\n    // If we are currently in a navigation loading state and this fetcher is\n    // more recent than the navigation, we want the newer data so abort the\n    // navigation and complete it with the fetcher data\n    if (\n      state.navigation.state === \"loading\" &&\n      loadId > pendingNavigationLoadId\n    ) {\n      invariant(pendingAction, \"Expected pending action\");\n      pendingNavigationController && pendingNavigationController.abort();\n\n      completeNavigation(state.navigation.location, {\n        matches,\n        loaderData,\n        errors,\n        fetchers: new Map(state.fetchers),\n      });\n    } else {\n      // otherwise just update with the fetcher data, preserving any existing\n      // loaderData for loaders that did not need to reload.  We have to\n      // manually merge here since we aren't going through completeNavigation\n      updateState({\n        errors,\n        loaderData: mergeLoaderData(state.loaderData, loaderData, matches),\n        ...(didAbortFetchLoads ? { fetchers: new Map(state.fetchers) } : {}),\n      });\n      isRevalidationRequired = false;\n    }\n  }\n\n  // Call the matched loader for fetcher.load(), handling redirects, errors, etc.\n  async function handleFetcherLoader(\n    key: string,\n    routeId: string,\n    path: string,\n    match: AgnosticDataRouteMatch,\n    matches: AgnosticDataRouteMatch[]\n  ) {\n    let existingFetcher = state.fetchers.get(key);\n    // Put this fetcher into it's loading state\n    let loadingFetcher: FetcherStates[\"Loading\"] = {\n      state: \"loading\",\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      data: existingFetcher && existingFetcher.data,\n    };\n    state.fetchers.set(key, loadingFetcher);\n    updateState({ fetchers: new Map(state.fetchers) });\n\n    // Call the loader for this fetcher route match\n    let abortController = new AbortController();\n    let fetchRequest = createRequest(path, abortController.signal);\n    fetchControllers.set(key, abortController);\n    let result: DataResult = await callLoaderOrAction(\n      \"loader\",\n      fetchRequest,\n      match,\n      matches,\n      router.basename\n    );\n\n    // Deferred isn't supported or fetcher loads, await everything and treat it\n    // as a normal load.  resolveDeferredData will return undefined if this\n    // fetcher gets aborted, so we just leave result untouched and short circuit\n    // below if that happens\n    if (isDeferredResult(result)) {\n      result =\n        (await resolveDeferredData(result, fetchRequest.signal, true)) ||\n        result;\n    }\n\n    // We can delete this so long as we weren't aborted by ou our own fetcher\n    // re-load which would have put _new_ controller is in fetchControllers\n    if (fetchControllers.get(key) === abortController) {\n      fetchControllers.delete(key);\n    }\n\n    if (fetchRequest.signal.aborted) {\n      return;\n    }\n\n    // If the loader threw a redirect Response, start a new REPLACE navigation\n    if (isRedirectResult(result)) {\n      let redirectNavigation = getLoaderRedirect(state, result);\n      await startRedirectNavigation(result, redirectNavigation);\n      return;\n    }\n\n    // Process any non-redirect errors thrown\n    if (isErrorResult(result)) {\n      let boundaryMatch = findNearestBoundary(state.matches, routeId);\n      state.fetchers.delete(key);\n      // TODO: In remix, this would reset to IDLE_NAVIGATION if it was a catch -\n      // do we need to behave any differently with our non-redirect errors?\n      // What if it was a non-redirect Response?\n      updateState({\n        fetchers: new Map(state.fetchers),\n        errors: {\n          [boundaryMatch.route.id]: result.error,\n        },\n      });\n      return;\n    }\n\n    invariant(!isDeferredResult(result), \"Unhandled fetcher deferred data\");\n\n    // Put the fetcher back into an idle state\n    let doneFetcher: FetcherStates[\"Idle\"] = {\n      state: \"idle\",\n      data: result.data,\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n    };\n    state.fetchers.set(key, doneFetcher);\n    updateState({ fetchers: new Map(state.fetchers) });\n  }\n\n  /**\n   * Utility function to handle redirects returned from an action or loader.\n   * Normally, a redirect \"replaces\" the navigation that triggered it.  So, for\n   * example:\n   *\n   *  - user is on /a\n   *  - user clicks a link to /b\n   *  - loader for /b redirects to /c\n   *\n   * In a non-JS app the browser would track the in-flight navigation to /b and\n   * then replace it with /c when it encountered the redirect response.  In\n   * the end it would only ever update the URL bar with /c.\n   *\n   * In client-side routing using pushState/replaceState, we aim to emulate\n   * this behavior and we also do not update history until the end of the\n   * navigation (including processed redirects).  This means that we never\n   * actually touch history until we've processed redirects, so we just use\n   * the history action from the original navigation (PUSH or REPLACE).\n   */\n  async function startRedirectNavigation(\n    redirect: RedirectResult,\n    navigation: Navigation,\n    replace?: boolean\n  ) {\n    if (redirect.revalidate) {\n      isRevalidationRequired = true;\n    }\n    invariant(\n      navigation.location,\n      \"Expected a location on the redirect navigation\"\n    );\n    // There's no need to abort on redirects, since we don't detect the\n    // redirect until the action/loaders have settled\n    pendingNavigationController = null;\n\n    let redirectHistoryAction =\n      replace === true ? HistoryAction.Replace : HistoryAction.Push;\n\n    await startNavigation(redirectHistoryAction, navigation.location, {\n      overrideNavigation: navigation,\n    });\n  }\n\n  async function callLoadersAndMaybeResolveData(\n    currentMatches: AgnosticDataRouteMatch[],\n    matches: AgnosticDataRouteMatch[],\n    matchesToLoad: AgnosticDataRouteMatch[],\n    fetchersToLoad: RevalidatingFetcher[],\n    request: Request\n  ) {\n    // Call all navigation loaders and revalidating fetcher loaders in parallel,\n    // then slice off the results into separate arrays so we can handle them\n    // accordingly\n    let results = await Promise.all([\n      ...matchesToLoad.map((match) =>\n        callLoaderOrAction(\"loader\", request, match, matches, router.basename)\n      ),\n      ...fetchersToLoad.map(([, href, match, fetchMatches]) =>\n        callLoaderOrAction(\n          \"loader\",\n          createRequest(href, request.signal),\n          match,\n          fetchMatches,\n          router.basename\n        )\n      ),\n    ]);\n    let loaderResults = results.slice(0, matchesToLoad.length);\n    let fetcherResults = results.slice(matchesToLoad.length);\n\n    await Promise.all([\n      resolveDeferredResults(\n        currentMatches,\n        matchesToLoad,\n        loaderResults,\n        request.signal,\n        false,\n        state.loaderData\n      ),\n      resolveDeferredResults(\n        currentMatches,\n        fetchersToLoad.map(([, , match]) => match),\n        fetcherResults,\n        request.signal,\n        true\n      ),\n    ]);\n\n    return { results, loaderResults, fetcherResults };\n  }\n\n  function interruptActiveLoads() {\n    // Every interruption triggers a revalidation\n    isRevalidationRequired = true;\n\n    // Cancel pending route-level deferreds and mark cancelled routes for\n    // revalidation\n    cancelledDeferredRoutes.push(...cancelActiveDeferreds());\n\n    // Abort in-flight fetcher loads\n    fetchLoadMatches.forEach((_, key) => {\n      if (fetchControllers.has(key)) {\n        cancelledFetcherLoads.push(key);\n        abortFetcher(key);\n      }\n    });\n  }\n\n  function setFetcherError(key: string, routeId: string, error: any) {\n    let boundaryMatch = findNearestBoundary(state.matches, routeId);\n    deleteFetcher(key);\n    updateState({\n      errors: {\n        [boundaryMatch.route.id]: error,\n      },\n      fetchers: new Map(state.fetchers),\n    });\n  }\n\n  function deleteFetcher(key: string): void {\n    if (fetchControllers.has(key)) abortFetcher(key);\n    fetchLoadMatches.delete(key);\n    fetchReloadIds.delete(key);\n    fetchRedirectIds.delete(key);\n    state.fetchers.delete(key);\n  }\n\n  function abortFetcher(key: string) {\n    let controller = fetchControllers.get(key);\n    invariant(controller, `Expected fetch controller: ${key}`);\n    controller.abort();\n    fetchControllers.delete(key);\n  }\n\n  function markFetchersDone(keys: string[]) {\n    for (let key of keys) {\n      let fetcher = getFetcher(key);\n      let doneFetcher: FetcherStates[\"Idle\"] = {\n        state: \"idle\",\n        data: fetcher.data,\n        formMethod: undefined,\n        formAction: undefined,\n        formEncType: undefined,\n        formData: undefined,\n      };\n      state.fetchers.set(key, doneFetcher);\n    }\n  }\n\n  function markFetchRedirectsDone(): void {\n    let doneKeys = [];\n    for (let key of fetchRedirectIds) {\n      let fetcher = state.fetchers.get(key);\n      invariant(fetcher, `Expected fetcher: ${key}`);\n      if (fetcher.state === \"loading\") {\n        fetchRedirectIds.delete(key);\n        doneKeys.push(key);\n      }\n    }\n    markFetchersDone(doneKeys);\n  }\n\n  function abortStaleFetchLoads(landedId: number): boolean {\n    let yeetedKeys = [];\n    for (let [key, id] of fetchReloadIds) {\n      if (id < landedId) {\n        let fetcher = state.fetchers.get(key);\n        invariant(fetcher, `Expected fetcher: ${key}`);\n        if (fetcher.state === \"loading\") {\n          abortFetcher(key);\n          fetchReloadIds.delete(key);\n          yeetedKeys.push(key);\n        }\n      }\n    }\n    markFetchersDone(yeetedKeys);\n    return yeetedKeys.length > 0;\n  }\n\n  function cancelActiveDeferreds(\n    predicate?: (routeId: string) => boolean\n  ): string[] {\n    let cancelledRouteIds: string[] = [];\n    activeDeferreds.forEach((dfd, routeId) => {\n      if (!predicate || predicate(routeId)) {\n        // Cancel the deferred - but do not remove from activeDeferreds here -\n        // we rely on the subscribers to do that so our tests can assert proper\n        // cleanup via _internalActiveDeferreds\n        dfd.cancel();\n        cancelledRouteIds.push(routeId);\n        activeDeferreds.delete(routeId);\n      }\n    });\n    return cancelledRouteIds;\n  }\n\n  // Opt in to capturing and reporting scroll positions during navigations,\n  // used by the <ScrollRestoration> component\n  function enableScrollRestoration(\n    positions: Record<string, number>,\n    getPosition: GetScrollPositionFunction,\n    getKey?: GetScrollRestorationKeyFunction\n  ) {\n    savedScrollPositions = positions;\n    getScrollPosition = getPosition;\n    getScrollRestorationKey = getKey || ((location) => location.key);\n\n    // Perform initial hydration scroll restoration, since we miss the boat on\n    // the initial updateState() because we've not yet rendered <ScrollRestoration/>\n    // and therefore have no savedScrollPositions available\n    if (!initialScrollRestored && state.navigation === IDLE_NAVIGATION) {\n      initialScrollRestored = true;\n      let y = getSavedScrollPosition(state.location, state.matches);\n      if (y != null) {\n        updateState({ restoreScrollPosition: y });\n      }\n    }\n\n    return () => {\n      savedScrollPositions = null;\n      getScrollPosition = null;\n      getScrollRestorationKey = null;\n    };\n  }\n\n  function saveScrollPosition(\n    location: Location,\n    matches: AgnosticDataRouteMatch[]\n  ): void {\n    if (savedScrollPositions && getScrollRestorationKey && getScrollPosition) {\n      let userMatches = matches.map((m) =>\n        createUseMatchesMatch(m, state.loaderData)\n      );\n      let key = getScrollRestorationKey(location, userMatches) || location.key;\n      savedScrollPositions[key] = getScrollPosition();\n    }\n  }\n\n  function getSavedScrollPosition(\n    location: Location,\n    matches: AgnosticDataRouteMatch[]\n  ): number | null {\n    if (savedScrollPositions && getScrollRestorationKey && getScrollPosition) {\n      let userMatches = matches.map((m) =>\n        createUseMatchesMatch(m, state.loaderData)\n      );\n      let key = getScrollRestorationKey(location, userMatches) || location.key;\n      let y = savedScrollPositions[key];\n      if (typeof y === \"number\") {\n        return y;\n      }\n    }\n    return null;\n  }\n\n  router = {\n    get basename() {\n      return init.basename;\n    },\n    get state() {\n      return state;\n    },\n    get routes() {\n      return dataRoutes;\n    },\n    initialize,\n    subscribe,\n    enableScrollRestoration,\n    navigate,\n    fetch,\n    revalidate,\n    // Passthrough to history-aware createHref used by useHref so we get proper\n    // hash-aware URLs in DOM paths\n    createHref: (to: To) => init.history.createHref(to),\n    getFetcher,\n    deleteFetcher,\n    dispose,\n    _internalFetchControllers: fetchControllers,\n    _internalActiveDeferreds: activeDeferreds,\n  };\n\n  return router;\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region createStaticHandler\n////////////////////////////////////////////////////////////////////////////////\n\nconst validActionMethods = new Set([\"POST\", \"PUT\", \"PATCH\", \"DELETE\"]);\nconst validRequestMethods = new Set([\"GET\", \"HEAD\", ...validActionMethods]);\n\nexport function unstable_createStaticHandler(\n  routes: AgnosticRouteObject[]\n): StaticHandler {\n  invariant(\n    routes.length > 0,\n    \"You must provide a non-empty routes array to unstable_createStaticHandler\"\n  );\n\n  let dataRoutes = convertRoutesToDataRoutes(routes);\n\n  /**\n   * The query() method is intended for document requests, in which we want to\n   * call an optional action and potentially multiple loaders for all nested\n   * routes.  It returns a StaticHandlerContext object, which is very similar\n   * to the router state (location, loaderData, actionData, errors, etc.) and\n   * also adds SSR-specific information such as the statusCode and headers\n   * from action/loaders Responses.\n   *\n   * It _should_ never throw and should report all errors through the\n   * returned context.errors object, properly associating errors to their error\n   * boundary.  Additionally, it tracks _deepestRenderedBoundaryId which can be\n   * used to emulate React error boundaries during SSr by performing a second\n   * pass only down to the boundaryId.\n   *\n   * The one exception where we do not return a StaticHandlerContext is when a\n   * redirect response is returned or thrown from any action/loader.  We\n   * propagate that out and return the raw Response so the HTTP server can\n   * return it directly.\n   */\n  async function query(\n    request: Request\n  ): Promise<StaticHandlerContext | Response> {\n    let url = new URL(request.url);\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location);\n\n    if (!validRequestMethods.has(request.method)) {\n      let {\n        matches: methodNotAllowedMatches,\n        route,\n        error,\n      } = getMethodNotAllowedMatches(dataRoutes);\n      return {\n        location,\n        matches: methodNotAllowedMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error,\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n      };\n    } else if (!matches) {\n      let {\n        matches: notFoundMatches,\n        route,\n        error,\n      } = getNotFoundMatches(dataRoutes);\n      return {\n        location,\n        matches: notFoundMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error,\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n      };\n    }\n\n    let result = await queryImpl(request, location, matches);\n    if (result instanceof Response) {\n      return result;\n    }\n\n    // When returning StaticHandlerContext, we patch back in the location here\n    // since we need it for React Context.  But this helps keep our submit and\n    // loadRouteData operating on a Request instead of a Location\n    return { location, ...result };\n  }\n\n  /**\n   * The queryRoute() method is intended for targeted route requests, either\n   * for fetch ?_data requests or resource route requests.  In this case, we\n   * are only ever calling a single action or loader, and we are returning the\n   * returned value directly.  In most cases, this will be a Response returned\n   * from the action/loader, but it may be a primitive or other value as well -\n   * and in such cases the calling context should handle that accordingly.\n   *\n   * We do respect the throw/return differentiation, so if an action/loader\n   * throws, then this method will throw the value.  This is important so we\n   * can do proper boundary identification in Remix where a thrown Response\n   * must go to the Catch Boundary but a returned Response is happy-path.\n   *\n   * One thing to note is that any Router-initiated thrown Response (such as a\n   * 404 or 405) will have a custom X-Remix-Router-Error: \"yes\" header on it\n   * in order to differentiate from responses thrown from user actions/loaders.\n   */\n  async function queryRoute(request: Request, routeId?: string): Promise<any> {\n    let url = new URL(request.url);\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location);\n\n    if (!validRequestMethods.has(request.method)) {\n      throw createRouterErrorResponse(null, {\n        status: 405,\n        statusText: \"Method Not Allowed\",\n      });\n    } else if (!matches) {\n      throw createRouterErrorResponse(null, {\n        status: 404,\n        statusText: \"Not Found\",\n      });\n    }\n\n    let match = routeId\n      ? matches.find((m) => m.route.id === routeId)\n      : getTargetMatch(matches, location);\n\n    if (!match) {\n      throw createRouterErrorResponse(null, {\n        status: 404,\n        statusText: \"Not Found\",\n      });\n    }\n\n    let result = await queryImpl(request, location, matches, match);\n    if (result instanceof Response) {\n      return result;\n    }\n\n    let error = result.errors ? Object.values(result.errors)[0] : undefined;\n    if (error !== undefined) {\n      // If we got back result.errors, that means the loader/action threw\n      // _something_ that wasn't a Response, but it's not guaranteed/required\n      // to be an `instanceof Error` either, so we have to use throw here to\n      // preserve the \"error\" state outside of queryImpl.\n      throw error;\n    }\n\n    // Pick off the right state value to return\n    let routeData = [result.actionData, result.loaderData].find((v) => v);\n    return Object.values(routeData || {})[0];\n  }\n\n  async function queryImpl(\n    request: Request,\n    location: Location,\n    matches: AgnosticDataRouteMatch[],\n    routeMatch?: AgnosticDataRouteMatch\n  ): Promise<Omit<StaticHandlerContext, \"location\"> | Response> {\n    invariant(\n      request.signal,\n      \"query()/queryRoute() requests must contain an AbortController signal\"\n    );\n\n    try {\n      if (validActionMethods.has(request.method)) {\n        let result = await submit(\n          request,\n          matches,\n          routeMatch || getTargetMatch(matches, location),\n          routeMatch != null\n        );\n        return result;\n      }\n\n      let result = await loadRouteData(request, matches, routeMatch);\n      return result instanceof Response\n        ? result\n        : {\n            ...result,\n            actionData: null,\n            actionHeaders: {},\n          };\n    } catch (e) {\n      // If the user threw/returned a Response in callLoaderOrAction, we throw\n      // it to bail out and then return or throw here based on whether the user\n      // returned or threw\n      if (isQueryRouteResponse(e)) {\n        if (e.type === ResultType.error && !isRedirectResponse(e.response)) {\n          throw e.response;\n        }\n        return e.response;\n      }\n      // Redirects are always returned since they don't propagate to catch\n      // boundaries\n      if (isRedirectResponse(e)) {\n        return e;\n      }\n      throw e;\n    }\n  }\n\n  async function submit(\n    request: Request,\n    matches: AgnosticDataRouteMatch[],\n    actionMatch: AgnosticDataRouteMatch,\n    isRouteRequest: boolean\n  ): Promise<Omit<StaticHandlerContext, \"location\"> | Response> {\n    let result: DataResult;\n    if (!actionMatch.route.action) {\n      if (isRouteRequest) {\n        throw createRouterErrorResponse(null, {\n          status: 405,\n          statusText: \"Method Not Allowed\",\n        });\n      }\n      result = getMethodNotAllowedResult(request.url);\n    } else {\n      result = await callLoaderOrAction(\n        \"action\",\n        request,\n        actionMatch,\n        matches,\n        undefined, // Basename not currently supported in static handlers\n        true,\n        isRouteRequest\n      );\n\n      if (request.signal.aborted) {\n        let method = isRouteRequest ? \"queryRoute\" : \"query\";\n        throw new Error(`${method}() call aborted`);\n      }\n    }\n\n    if (isRedirectResult(result)) {\n      // Uhhhh - this should never happen, we should always throw these from\n      // callLoaderOrAction, but the type narrowing here keeps TS happy and we\n      // can get back on the \"throw all redirect responses\" train here should\n      // this ever happen :/\n      throw new Response(null, {\n        status: result.status,\n        headers: {\n          Location: result.location,\n        },\n      });\n    }\n\n    if (isDeferredResult(result)) {\n      throw new Error(\"defer() is not supported in actions\");\n    }\n\n    if (isRouteRequest) {\n      // Note: This should only be non-Response values if we get here, since\n      // isRouteRequest should throw any Response received in callLoaderOrAction\n      if (isErrorResult(result)) {\n        let boundaryMatch = findNearestBoundary(matches, actionMatch.route.id);\n        return {\n          matches: [actionMatch],\n          loaderData: {},\n          actionData: null,\n          errors: {\n            [boundaryMatch.route.id]: result.error,\n          },\n          // Note: statusCode + headers are unused here since queryRoute will\n          // return the raw Response or value\n          statusCode: 500,\n          loaderHeaders: {},\n          actionHeaders: {},\n        };\n      }\n\n      return {\n        matches: [actionMatch],\n        loaderData: {},\n        actionData: { [actionMatch.route.id]: result.data },\n        errors: null,\n        // Note: statusCode + headers are unused here since queryRoute will\n        // return the raw Response or value\n        statusCode: 200,\n        loaderHeaders: {},\n        actionHeaders: {},\n      };\n    }\n\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = findNearestBoundary(matches, actionMatch.route.id);\n      let context = await loadRouteData(request, matches, undefined, {\n        [boundaryMatch.route.id]: result.error,\n      });\n\n      // action status codes take precedence over loader status codes\n      return {\n        ...context,\n        statusCode: isRouteErrorResponse(result.error)\n          ? result.error.status\n          : 500,\n        actionData: null,\n        actionHeaders: {\n          ...(result.headers ? { [actionMatch.route.id]: result.headers } : {}),\n        },\n      };\n    }\n\n    let context = await loadRouteData(request, matches);\n\n    return {\n      ...context,\n      // action status codes take precedence over loader status codes\n      ...(result.statusCode ? { statusCode: result.statusCode } : {}),\n      actionData: {\n        [actionMatch.route.id]: result.data,\n      },\n      actionHeaders: {\n        ...(result.headers ? { [actionMatch.route.id]: result.headers } : {}),\n      },\n    };\n  }\n\n  async function loadRouteData(\n    request: Request,\n    matches: AgnosticDataRouteMatch[],\n    routeMatch?: AgnosticDataRouteMatch,\n    pendingActionError?: RouteData\n  ): Promise<\n    | Omit<StaticHandlerContext, \"location\" | \"actionData\" | \"actionHeaders\">\n    | Response\n  > {\n    let isRouteRequest = routeMatch != null;\n    let requestMatches = routeMatch\n      ? [routeMatch]\n      : getLoaderMatchesUntilBoundary(\n          matches,\n          Object.keys(pendingActionError || {})[0]\n        );\n    let matchesToLoad = requestMatches.filter((m) => m.route.loader);\n\n    // Short circuit if we have no loaders to run\n    if (matchesToLoad.length === 0) {\n      return {\n        matches,\n        loaderData: {},\n        errors: pendingActionError || null,\n        statusCode: 200,\n        loaderHeaders: {},\n      };\n    }\n\n    let results = await Promise.all([\n      ...matchesToLoad.map((match) =>\n        callLoaderOrAction(\n          \"loader\",\n          request,\n          match,\n          matches,\n          undefined, // Basename not currently supported in static handlers\n          true,\n          isRouteRequest\n        )\n      ),\n    ]);\n\n    if (request.signal.aborted) {\n      let method = isRouteRequest ? \"queryRoute\" : \"query\";\n      throw new Error(`${method}() call aborted`);\n    }\n\n    // Can't do anything with these without the Remix side of things, so just\n    // cancel them for now\n    results.forEach((result) => {\n      if (isDeferredResult(result)) {\n        result.deferredData.cancel();\n      }\n    });\n\n    // Process and commit output from loaders\n    let context = processRouteLoaderData(\n      matches,\n      matchesToLoad,\n      results,\n      pendingActionError\n    );\n\n    return {\n      ...context,\n      matches,\n    };\n  }\n\n  function createRouterErrorResponse(\n    body: BodyInit | null | undefined,\n    init: ResponseInit\n  ) {\n    return new Response(body, {\n      ...init,\n      headers: {\n        ...init.headers,\n        \"X-Remix-Router-Error\": \"yes\",\n      },\n    });\n  }\n\n  return {\n    dataRoutes,\n    query,\n    queryRoute,\n  };\n}\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Helpers\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Given an existing StaticHandlerContext and an error thrown at render time,\n * provide an updated StaticHandlerContext suitable for a second SSR render\n */\nexport function getStaticContextFromError(\n  routes: AgnosticDataRouteObject[],\n  context: StaticHandlerContext,\n  error: any\n) {\n  let newContext: StaticHandlerContext = {\n    ...context,\n    statusCode: 500,\n    errors: {\n      [context._deepestRenderedBoundaryId || routes[0].id]: error,\n    },\n  };\n  return newContext;\n}\n\n// Normalize navigation options by converting formMethod=GET formData objects to\n// URLSearchParams so they behave identically to links with query params\nfunction normalizeNavigateOptions(\n  to: To,\n  opts?: RouterNavigateOptions,\n  isFetcher = false\n): {\n  path: string;\n  submission?: Submission;\n  error?: ErrorResponse;\n} {\n  let path = typeof to === \"string\" ? to : createPath(to);\n\n  // Return location verbatim on non-submission navigations\n  if (!opts || (!(\"formMethod\" in opts) && !(\"formData\" in opts))) {\n    return { path };\n  }\n\n  // Create a Submission on non-GET navigations\n  if (opts.formMethod != null && opts.formMethod !== \"get\") {\n    return {\n      path,\n      submission: {\n        formMethod: opts.formMethod,\n        formAction: stripHashFromPath(path),\n        formEncType:\n          (opts && opts.formEncType) || \"application/x-www-form-urlencoded\",\n        formData: opts.formData,\n      },\n    };\n  }\n\n  // No formData to flatten for GET submission\n  if (!opts.formData) {\n    return { path };\n  }\n\n  // Flatten submission onto URLSearchParams for GET submissions\n  let parsedPath = parsePath(path);\n  try {\n    let searchParams = convertFormDataToSearchParams(opts.formData);\n    // Since fetcher GET submissions only run a single loader (as opposed to\n    // navigation GET submissions which run all loaders), we need to preserve\n    // any incoming ?index params\n    if (\n      isFetcher &&\n      parsedPath.search &&\n      hasNakedIndexQuery(parsedPath.search)\n    ) {\n      searchParams.append(\"index\", \"\");\n    }\n    parsedPath.search = `?${searchParams}`;\n  } catch (e) {\n    return {\n      path,\n      error: new ErrorResponse(\n        400,\n        \"Bad Request\",\n        \"Cannot submit binary form data using GET\"\n      ),\n    };\n  }\n\n  return { path: createPath(parsedPath) };\n}\n\nfunction getLoaderRedirect(\n  state: RouterState,\n  redirect: RedirectResult\n): Navigation {\n  let { formMethod, formAction, formEncType, formData } = state.navigation;\n  let navigation: NavigationStates[\"Loading\"] = {\n    state: \"loading\",\n    location: createLocation(state.location, redirect.location),\n    formMethod: formMethod || undefined,\n    formAction: formAction || undefined,\n    formEncType: formEncType || undefined,\n    formData: formData || undefined,\n  };\n  return navigation;\n}\n\n// Filter out all routes below any caught error as they aren't going to\n// render so we don't need to load them\nfunction getLoaderMatchesUntilBoundary(\n  matches: AgnosticDataRouteMatch[],\n  boundaryId?: string\n) {\n  let boundaryMatches = matches;\n  if (boundaryId) {\n    let index = matches.findIndex((m) => m.route.id === boundaryId);\n    if (index >= 0) {\n      boundaryMatches = matches.slice(0, index);\n    }\n  }\n  return boundaryMatches;\n}\n\nfunction getMatchesToLoad(\n  state: RouterState,\n  matches: AgnosticDataRouteMatch[],\n  submission: Submission | undefined,\n  location: Location,\n  isRevalidationRequired: boolean,\n  cancelledDeferredRoutes: string[],\n  cancelledFetcherLoads: string[],\n  pendingActionData?: RouteData,\n  pendingError?: RouteData,\n  fetchLoadMatches?: Map<string, FetchLoadMatch>\n): [AgnosticDataRouteMatch[], RevalidatingFetcher[]] {\n  let actionResult = pendingError\n    ? Object.values(pendingError)[0]\n    : pendingActionData\n    ? Object.values(pendingActionData)[0]\n    : null;\n\n  // Pick navigation matches that are net-new or qualify for revalidation\n  let boundaryId = pendingError ? Object.keys(pendingError)[0] : undefined;\n  let boundaryMatches = getLoaderMatchesUntilBoundary(matches, boundaryId);\n  let navigationMatches = boundaryMatches.filter(\n    (match, index) =>\n      match.route.loader != null &&\n      (isNewLoader(state.loaderData, state.matches[index], match) ||\n        // If this route had a pending deferred cancelled it must be revalidated\n        cancelledDeferredRoutes.some((id) => id === match.route.id) ||\n        shouldRevalidateLoader(\n          state.location,\n          state.matches[index],\n          submission,\n          location,\n          match,\n          isRevalidationRequired,\n          actionResult\n        ))\n  );\n\n  // Pick fetcher.loads that need to be revalidated\n  let revalidatingFetchers: RevalidatingFetcher[] = [];\n  fetchLoadMatches &&\n    fetchLoadMatches.forEach(([href, match, fetchMatches], key) => {\n      // This fetcher was cancelled from a prior action submission - force reload\n      if (cancelledFetcherLoads.includes(key)) {\n        revalidatingFetchers.push([key, href, match, fetchMatches]);\n      } else if (isRevalidationRequired) {\n        let shouldRevalidate = shouldRevalidateLoader(\n          href,\n          match,\n          submission,\n          href,\n          match,\n          isRevalidationRequired,\n          actionResult\n        );\n        if (shouldRevalidate) {\n          revalidatingFetchers.push([key, href, match, fetchMatches]);\n        }\n      }\n    });\n\n  return [navigationMatches, revalidatingFetchers];\n}\n\nfunction isNewLoader(\n  currentLoaderData: RouteData,\n  currentMatch: AgnosticDataRouteMatch,\n  match: AgnosticDataRouteMatch\n) {\n  let isNew =\n    // [a] -> [a, b]\n    !currentMatch ||\n    // [a, b] -> [a, c]\n    match.route.id !== currentMatch.route.id;\n\n  // Handle the case that we don't have data for a re-used route, potentially\n  // from a prior error or from a cancelled pending deferred\n  let isMissingData = currentLoaderData[match.route.id] === undefined;\n\n  // Always load if this is a net-new route or we don't yet have data\n  return isNew || isMissingData;\n}\n\nfunction isNewRouteInstance(\n  currentMatch: AgnosticDataRouteMatch,\n  match: AgnosticDataRouteMatch\n) {\n  let currentPath = currentMatch.route.path;\n  return (\n    // param change for this match, /users/123 -> /users/456\n    currentMatch.pathname !== match.pathname ||\n    // splat param changed, which is not present in match.path\n    // e.g. /files/images/avatar.jpg -> files/finances.xls\n    (currentPath &&\n      currentPath.endsWith(\"*\") &&\n      currentMatch.params[\"*\"] !== match.params[\"*\"])\n  );\n}\n\nfunction shouldRevalidateLoader(\n  currentLocation: string | Location,\n  currentMatch: AgnosticDataRouteMatch,\n  submission: Submission | undefined,\n  location: string | Location,\n  match: AgnosticDataRouteMatch,\n  isRevalidationRequired: boolean,\n  actionResult: DataResult | undefined\n) {\n  let currentUrl = createURL(currentLocation);\n  let currentParams = currentMatch.params;\n  let nextUrl = createURL(location);\n  let nextParams = match.params;\n\n  // This is the default implementation as to when we revalidate.  If the route\n  // provides it's own implementation, then we give them full control but\n  // provide this value so they can leverage it if needed after they check\n  // their own specific use cases\n  // Note that fetchers always provide the same current/next locations so the\n  // URL-based checks here don't apply to fetcher shouldRevalidate calls\n  let defaultShouldRevalidate =\n    isNewRouteInstance(currentMatch, match) ||\n    // Clicked the same link, resubmitted a GET form\n    currentUrl.toString() === nextUrl.toString() ||\n    // Search params affect all loaders\n    currentUrl.search !== nextUrl.search ||\n    // Forced revalidation due to submission, useRevalidate, or X-Remix-Revalidate\n    isRevalidationRequired;\n\n  if (match.route.shouldRevalidate) {\n    let routeChoice = match.route.shouldRevalidate({\n      currentUrl,\n      currentParams,\n      nextUrl,\n      nextParams,\n      ...submission,\n      actionResult,\n      defaultShouldRevalidate,\n    });\n    if (typeof routeChoice === \"boolean\") {\n      return routeChoice;\n    }\n  }\n\n  return defaultShouldRevalidate;\n}\n\nasync function callLoaderOrAction(\n  type: \"loader\" | \"action\",\n  request: Request,\n  match: AgnosticDataRouteMatch,\n  matches: AgnosticDataRouteMatch[],\n  basename: string | undefined,\n  isStaticRequest: boolean = false,\n  isRouteRequest: boolean = false\n): Promise<DataResult> {\n  let resultType;\n  let result;\n\n  // Setup a promise we can race against so that abort signals short circuit\n  let reject: () => void;\n  let abortPromise = new Promise((_, r) => (reject = r));\n  let onReject = () => reject();\n  request.signal.addEventListener(\"abort\", onReject);\n\n  try {\n    let handler = match.route[type];\n    invariant<Function>(\n      handler,\n      `Could not find the ${type} to run on the \"${match.route.id}\" route`\n    );\n\n    result = await Promise.race([\n      handler({ request, params: match.params }),\n      abortPromise,\n    ]);\n  } catch (e) {\n    resultType = ResultType.error;\n    result = e;\n  } finally {\n    request.signal.removeEventListener(\"abort\", onReject);\n  }\n\n  if (result instanceof Response) {\n    let status = result.status;\n\n    // Process redirects\n    if (status >= 300 && status <= 399) {\n      let location = result.headers.get(\"Location\");\n      invariant(\n        location,\n        \"Redirects returned/thrown from loaders/actions must have a Location header\"\n      );\n\n      // Support relative routing in redirects\n      let activeMatches = matches.slice(0, matches.indexOf(match) + 1);\n      let routePathnames = getPathContributingMatches(activeMatches).map(\n        (match) => match.pathnameBase\n      );\n      let requestPath = createURL(request.url).pathname;\n      let resolvedLocation = resolveTo(location, routePathnames, requestPath);\n      invariant(\n        createPath(resolvedLocation),\n        `Unable to resolve redirect location: ${result.headers.get(\"Location\")}`\n      );\n\n      // Prepend the basename to the redirect location if we have one\n      if (basename) {\n        let path = resolvedLocation.pathname;\n        resolvedLocation.pathname =\n          path === \"/\" ? basename : joinPaths([basename, path]);\n      }\n\n      location = createPath(resolvedLocation);\n\n      // Don't process redirects in the router during static requests requests.\n      // Instead, throw the Response and let the server handle it with an HTTP\n      // redirect.  We also update the Location header in place in this flow so\n      // basename and relative routing is taken into account\n      if (isStaticRequest) {\n        result.headers.set(\"Location\", location);\n        throw result;\n      }\n\n      return {\n        type: ResultType.redirect,\n        status,\n        location,\n        revalidate: result.headers.get(\"X-Remix-Revalidate\") !== null,\n      };\n    }\n\n    // For SSR single-route requests, we want to hand Responses back directly\n    // without unwrapping.  We do this with the QueryRouteResponse wrapper\n    // interface so we can know whether it was returned or thrown\n    if (isRouteRequest) {\n      // eslint-disable-next-line no-throw-literal\n      throw {\n        type: resultType || ResultType.data,\n        response: result,\n      };\n    }\n\n    let data: any;\n    let contentType = result.headers.get(\"Content-Type\");\n    if (contentType && contentType.startsWith(\"application/json\")) {\n      data = await result.json();\n    } else {\n      data = await result.text();\n    }\n\n    if (resultType === ResultType.error) {\n      return {\n        type: resultType,\n        error: new ErrorResponse(status, result.statusText, data),\n        headers: result.headers,\n      };\n    }\n\n    return {\n      type: ResultType.data,\n      data,\n      statusCode: result.status,\n      headers: result.headers,\n    };\n  }\n\n  if (resultType === ResultType.error) {\n    return { type: resultType, error: result };\n  }\n\n  if (result instanceof DeferredData) {\n    return { type: ResultType.deferred, deferredData: result };\n  }\n\n  return { type: ResultType.data, data: result };\n}\n\nfunction createRequest(\n  location: string | Location,\n  signal: AbortSignal,\n  submission?: Submission\n): Request {\n  let url = createURL(stripHashFromPath(location)).toString();\n  let init: RequestInit = { signal };\n\n  if (submission) {\n    let { formMethod, formEncType, formData } = submission;\n    init.method = formMethod.toUpperCase();\n    init.body =\n      formEncType === \"application/x-www-form-urlencoded\"\n        ? convertFormDataToSearchParams(formData)\n        : formData;\n  }\n\n  // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n  return new Request(url, init);\n}\n\nfunction convertFormDataToSearchParams(formData: FormData): URLSearchParams {\n  let searchParams = new URLSearchParams();\n\n  for (let [key, value] of formData.entries()) {\n    invariant(\n      typeof value === \"string\",\n      'File inputs are not supported with encType \"application/x-www-form-urlencoded\", ' +\n        'please use \"multipart/form-data\" instead.'\n    );\n    searchParams.append(key, value);\n  }\n\n  return searchParams;\n}\n\nfunction processRouteLoaderData(\n  matches: AgnosticDataRouteMatch[],\n  matchesToLoad: AgnosticDataRouteMatch[],\n  results: DataResult[],\n  pendingError: RouteData | undefined,\n  activeDeferreds?: Map<string, DeferredData>\n): {\n  loaderData: RouterState[\"loaderData\"];\n  errors: RouterState[\"errors\"] | null;\n  statusCode: number;\n  loaderHeaders: Record<string, Headers>;\n} {\n  // Fill in loaderData/errors from our loaders\n  let loaderData: RouterState[\"loaderData\"] = {};\n  let errors: RouterState[\"errors\"] | null = null;\n  let statusCode: number | undefined;\n  let foundError = false;\n  let loaderHeaders: Record<string, Headers> = {};\n\n  // Process loader results into state.loaderData/state.errors\n  results.forEach((result, index) => {\n    let id = matchesToLoad[index].route.id;\n    invariant(\n      !isRedirectResult(result),\n      \"Cannot handle redirect results in processLoaderData\"\n    );\n    if (isErrorResult(result)) {\n      // Look upwards from the matched route for the closest ancestor\n      // error boundary, defaulting to the root match\n      let boundaryMatch = findNearestBoundary(matches, id);\n      let error = result.error;\n      // If we have a pending action error, we report it at the highest-route\n      // that throws a loader error, and then clear it out to indicate that\n      // it was consumed\n      if (pendingError) {\n        error = Object.values(pendingError)[0];\n        pendingError = undefined;\n      }\n      errors = Object.assign(errors || {}, {\n        [boundaryMatch.route.id]: error,\n      });\n      // Once we find our first (highest) error, we set the status code and\n      // prevent deeper status codes from overriding\n      if (!foundError) {\n        foundError = true;\n        statusCode = isRouteErrorResponse(result.error)\n          ? result.error.status\n          : 500;\n      }\n      if (result.headers) {\n        loaderHeaders[id] = result.headers;\n      }\n    } else if (isDeferredResult(result)) {\n      activeDeferreds && activeDeferreds.set(id, result.deferredData);\n      loaderData[id] = result.deferredData.data;\n      // TODO: Add statusCode/headers once we wire up streaming in Remix\n    } else {\n      loaderData[id] = result.data;\n      // Error status codes always override success status codes, but if all\n      // loaders are successful we take the deepest status code.\n      if (\n        result.statusCode != null &&\n        result.statusCode !== 200 &&\n        !foundError\n      ) {\n        statusCode = result.statusCode;\n      }\n      if (result.headers) {\n        loaderHeaders[id] = result.headers;\n      }\n    }\n  });\n\n  // If we didn't consume the pending action error (i.e., all loaders\n  // resolved), then consume it here\n  if (pendingError) {\n    errors = pendingError;\n  }\n\n  return {\n    loaderData,\n    errors,\n    statusCode: statusCode || 200,\n    loaderHeaders,\n  };\n}\n\nfunction processLoaderData(\n  state: RouterState,\n  matches: AgnosticDataRouteMatch[],\n  matchesToLoad: AgnosticDataRouteMatch[],\n  results: DataResult[],\n  pendingError: RouteData | undefined,\n  revalidatingFetchers: RevalidatingFetcher[],\n  fetcherResults: DataResult[],\n  activeDeferreds: Map<string, DeferredData>\n): {\n  loaderData: RouterState[\"loaderData\"];\n  errors?: RouterState[\"errors\"];\n} {\n  let { loaderData, errors } = processRouteLoaderData(\n    matches,\n    matchesToLoad,\n    results,\n    pendingError,\n    activeDeferreds\n  );\n\n  // Process results from our revalidating fetchers\n  for (let index = 0; index < revalidatingFetchers.length; index++) {\n    let [key, , match] = revalidatingFetchers[index];\n    invariant(\n      fetcherResults !== undefined && fetcherResults[index] !== undefined,\n      \"Did not find corresponding fetcher result\"\n    );\n    let result = fetcherResults[index];\n\n    // Process fetcher non-redirect errors\n    if (isErrorResult(result)) {\n      let boundaryMatch = findNearestBoundary(state.matches, match.route.id);\n      if (!(errors && errors[boundaryMatch.route.id])) {\n        errors = {\n          ...errors,\n          [boundaryMatch.route.id]: result.error,\n        };\n      }\n      state.fetchers.delete(key);\n    } else if (isRedirectResult(result)) {\n      // Should never get here, redirects should get processed above, but we\n      // keep this to type narrow to a success result in the else\n      throw new Error(\"Unhandled fetcher revalidation redirect\");\n    } else if (isDeferredResult(result)) {\n      // Should never get here, deferred data should be awaited for fetchers\n      // in resolveDeferredResults\n      throw new Error(\"Unhandled fetcher deferred data\");\n    } else {\n      let doneFetcher: FetcherStates[\"Idle\"] = {\n        state: \"idle\",\n        data: result.data,\n        formMethod: undefined,\n        formAction: undefined,\n        formEncType: undefined,\n        formData: undefined,\n      };\n      state.fetchers.set(key, doneFetcher);\n    }\n  }\n\n  return { loaderData, errors };\n}\n\nfunction mergeLoaderData(\n  loaderData: RouteData,\n  newLoaderData: RouteData,\n  matches: AgnosticDataRouteMatch[]\n): RouteData {\n  let mergedLoaderData = { ...newLoaderData };\n  matches.forEach((match) => {\n    let id = match.route.id;\n    if (newLoaderData[id] === undefined && loaderData[id] !== undefined) {\n      mergedLoaderData[id] = loaderData[id];\n    }\n  });\n  return mergedLoaderData;\n}\n\n// Find the nearest error boundary, looking upwards from the leaf route (or the\n// route specified by routeId) for the closest ancestor error boundary,\n// defaulting to the root match\nfunction findNearestBoundary(\n  matches: AgnosticDataRouteMatch[],\n  routeId?: string\n): AgnosticDataRouteMatch {\n  let eligibleMatches = routeId\n    ? matches.slice(0, matches.findIndex((m) => m.route.id === routeId) + 1)\n    : [...matches];\n  return (\n    eligibleMatches.reverse().find((m) => m.route.hasErrorBoundary === true) ||\n    matches[0]\n  );\n}\n\nfunction getShortCircuitMatches(\n  routes: AgnosticDataRouteObject[],\n  status: number,\n  statusText: string\n): {\n  matches: AgnosticDataRouteMatch[];\n  route: AgnosticDataRouteObject;\n  error: ErrorResponse;\n} {\n  // Prefer a root layout route if present, otherwise shim in a route object\n  let route = routes.find((r) => r.index || !r.path || r.path === \"/\") || {\n    id: `__shim-${status}-route__`,\n  };\n\n  return {\n    matches: [\n      {\n        params: {},\n        pathname: \"\",\n        pathnameBase: \"\",\n        route,\n      },\n    ],\n    route,\n    error: new ErrorResponse(status, statusText, null),\n  };\n}\n\nfunction getNotFoundMatches(routes: AgnosticDataRouteObject[]) {\n  return getShortCircuitMatches(routes, 404, \"Not Found\");\n}\n\nfunction getMethodNotAllowedMatches(routes: AgnosticDataRouteObject[]) {\n  return getShortCircuitMatches(routes, 405, \"Method Not Allowed\");\n}\n\nfunction getMethodNotAllowedResult(path: Location | string): ErrorResult {\n  let href = typeof path === \"string\" ? path : createPath(path);\n  console.warn(\n    \"You're trying to submit to a route that does not have an action.  To \" +\n      \"fix this, please add an `action` function to the route for \" +\n      `[${href}]`\n  );\n  return {\n    type: ResultType.error,\n    error: new ErrorResponse(405, \"Method Not Allowed\", \"\"),\n  };\n}\n\n// Find any returned redirect errors, starting from the lowest match\nfunction findRedirect(results: DataResult[]): RedirectResult | undefined {\n  for (let i = results.length - 1; i >= 0; i--) {\n    let result = results[i];\n    if (isRedirectResult(result)) {\n      return result;\n    }\n  }\n}\n\nfunction stripHashFromPath(path: To) {\n  let parsedPath = typeof path === \"string\" ? parsePath(path) : path;\n  return createPath({ ...parsedPath, hash: \"\" });\n}\n\nfunction isHashChangeOnly(a: Location, b: Location): boolean {\n  return (\n    a.pathname === b.pathname && a.search === b.search && a.hash !== b.hash\n  );\n}\n\nfunction isDeferredResult(result: DataResult): result is DeferredResult {\n  return result.type === ResultType.deferred;\n}\n\nfunction isErrorResult(result: DataResult): result is ErrorResult {\n  return result.type === ResultType.error;\n}\n\nfunction isRedirectResult(result?: DataResult): result is RedirectResult {\n  return (result && result.type) === ResultType.redirect;\n}\n\nfunction isRedirectResponse(result: any): result is Response {\n  if (!(result instanceof Response)) {\n    return false;\n  }\n\n  let status = result.status;\n  let location = result.headers.get(\"Location\");\n  return status >= 300 && status <= 399 && location != null;\n}\n\nfunction isQueryRouteResponse(obj: any): obj is QueryRouteResponse {\n  return (\n    obj &&\n    obj.response instanceof Response &&\n    (obj.type === ResultType.data || ResultType.error)\n  );\n}\n\nasync function resolveDeferredResults(\n  currentMatches: AgnosticDataRouteMatch[],\n  matchesToLoad: AgnosticDataRouteMatch[],\n  results: DataResult[],\n  signal: AbortSignal,\n  isFetcher: boolean,\n  currentLoaderData?: RouteData\n) {\n  for (let index = 0; index < results.length; index++) {\n    let result = results[index];\n    let match = matchesToLoad[index];\n    let currentMatch = currentMatches.find(\n      (m) => m.route.id === match.route.id\n    );\n    let isRevalidatingLoader =\n      currentMatch != null &&\n      !isNewRouteInstance(currentMatch, match) &&\n      (currentLoaderData && currentLoaderData[match.route.id]) !== undefined;\n\n    if (isDeferredResult(result) && (isFetcher || isRevalidatingLoader)) {\n      // Note: we do not have to touch activeDeferreds here since we race them\n      // against the signal in resolveDeferredData and they'll get aborted\n      // there if needed\n      await resolveDeferredData(result, signal, isFetcher).then((result) => {\n        if (result) {\n          results[index] = result || results[index];\n        }\n      });\n    }\n  }\n}\n\nasync function resolveDeferredData(\n  result: DeferredResult,\n  signal: AbortSignal,\n  unwrap = false\n): Promise<SuccessResult | ErrorResult | undefined> {\n  let aborted = await result.deferredData.resolveData(signal);\n  if (aborted) {\n    return;\n  }\n\n  if (unwrap) {\n    try {\n      return {\n        type: ResultType.data,\n        data: result.deferredData.unwrappedData,\n      };\n    } catch (e) {\n      // Handle any TrackedPromise._error values encountered while unwrapping\n      return {\n        type: ResultType.error,\n        error: e,\n      };\n    }\n  }\n\n  return {\n    type: ResultType.data,\n    data: result.deferredData.data,\n  };\n}\n\nfunction hasNakedIndexQuery(search: string): boolean {\n  return new URLSearchParams(search).getAll(\"index\").some((v) => v === \"\");\n}\n\n// Note: This should match the format exported by useMatches, so if you change\n// this please also change that :)  Eventually we'll DRY this up\nfunction createUseMatchesMatch(\n  match: AgnosticDataRouteMatch,\n  loaderData: RouteData\n): UseMatchesMatch {\n  let { route, pathname, params } = match;\n  return {\n    id: route.id,\n    pathname,\n    params,\n    data: loaderData[route.id] as unknown,\n    handle: route.handle as unknown,\n  };\n}\n\nfunction getTargetMatch(\n  matches: AgnosticDataRouteMatch[],\n  location: Location | string\n) {\n  let search =\n    typeof location === \"string\" ? parsePath(location).search : location.search;\n  if (\n    matches[matches.length - 1].route.index &&\n    hasNakedIndexQuery(search || \"\")\n  ) {\n    // Return the leaf index route when index is present\n    return matches[matches.length - 1];\n  }\n  // Otherwise grab the deepest \"path contributing\" match (ignoring index and\n  // pathless layout routes)\n  let pathMatches = getPathContributingMatches(matches);\n  return pathMatches[pathMatches.length - 1];\n}\n//#endregion\n"]}, "metadata": {}, "sourceType": "module"}