{"ast": null, "code": "import { guid } from 'utils/guid';\nexport const ZoneType = 'zone';\nexport function createZone(baseZone) {\n  const zone = {\n    _id: guid(),\n    type: ZoneType,\n    name: '',\n    tables: 0,\n    isOffsite: false\n  };\n  if (baseZone) {\n    Object.assign(zone, baseZone);\n  }\n  return zone;\n}", "map": {"version": 3, "names": ["guid", "ZoneType", "createZone", "baseZone", "zone", "_id", "type", "name", "tables", "isOffsite", "Object", "assign"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/api/models/zones.ts"], "sourcesContent": ["import { guid } from 'utils/guid';\r\nimport { ModelBase } from './model-base';\r\n\r\nexport const ZoneType = 'zone';\r\n\r\nexport interface Zone extends ModelBase {\r\n  type: string;\r\n  name: string;\r\n  tables: number;\r\n  isOffsite?: boolean;\r\n}\r\n\r\nexport function createZone(baseZone?: Zone) {\r\n  const zone: Zone = {\r\n    _id: guid(),\r\n    type: ZoneType,\r\n    name: '',\r\n    tables: 0,\r\n    isOffsite: false,\r\n  };\r\n\r\n  if (baseZone) {\r\n    Object.assign(zone, baseZone);\r\n  }\r\n\r\n  return zone;\r\n}\r\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,YAAY;AAGjC,OAAO,MAAMC,QAAQ,GAAG,MAAM;AAS9B,OAAO,SAASC,UAAU,CAACC,QAAe,EAAE;EAC1C,MAAMC,IAAU,GAAG;IACjBC,GAAG,EAAEL,IAAI,EAAE;IACXM,IAAI,EAAEL,QAAQ;IACdM,IAAI,EAAE,EAAE;IACRC,MAAM,EAAE,CAAC;IACTC,SAAS,EAAE;EACb,CAAC;EAED,IAAIN,QAAQ,EAAE;IACZO,MAAM,CAACC,MAAM,CAACP,IAAI,EAAED,QAAQ,CAAC;EAC/B;EAEA,OAAOC,IAAI;AACb"}, "metadata": {}, "sourceType": "module"}