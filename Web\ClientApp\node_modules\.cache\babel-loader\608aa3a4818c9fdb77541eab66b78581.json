{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\GreenRP\\\\Web\\\\ClientApp\\\\src\\\\features\\\\driver-tasks\\\\New.tsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { useNavigate, Navigate } from 'react-router';\nimport { Button, FormGroup, Input, Label } from 'reactstrap';\nimport moment from 'moment';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { driverTasksApi } from 'api/driver-tasks-service';\nimport * as models from 'api/models/driver-tasks';\nimport { routes } from 'app/routes';\nimport { useAuth } from 'features/auth/use-auth';\nimport { Error } from 'features/error/Error';\nimport { getDrivers, selectDrivers } from './driver-task-slice';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport function New() {\n  _s();\n  const {\n      isInRole,\n      user\n    } = useAuth(),\n    dispatch = useDispatch(),\n    navigate = useNavigate(),\n    drivers = useSelector(selectDrivers),\n    [dueDate, setDueDate] = useState(moment().format('YYYY-MM-DD')),\n    [assignedTo, setAssignedTo] = useState(null),\n    [priority, setPriority] = useState('Normal'),\n    [notes, setNotes] = useState(''),\n    [status, setStatus] = useState('Not Started'),\n    [fromLocation, setFromLocation] = useState(''),\n    [toLocation, setToLocation] = useState(''),\n    [sendText, setSendText] = useState(false),\n    [error, setError] = useState(null),\n    canCreateDriverTasks = isInRole('create:driver-tasks');\n  useEffect(() => {\n    if (user) {\n      dispatch(getDrivers(user));\n    }\n  }, [dispatch, user]);\n  const handleGoBackClick = () => goBack();\n  const handleSaveClick = async () => {\n    if (user) {\n      try {\n        await driverTasksApi.createDriverTask(user, dueDate, assignedTo, priority, notes, status, fromLocation, toLocation, sendText);\n        goBack();\n      } catch (e) {\n        setError(e);\n      }\n    }\n  };\n  const handleDueDateChange = e => {\n    setDueDate(e.target.value);\n  };\n  const handleAssignedToChange = e => {\n    const driver = drivers.find(d => d.name === e.target.value) || null;\n    setAssignedTo(driver);\n    if (!(driver !== null && driver !== void 0 && driver.phone)) {\n      setSendText(false);\n    }\n  };\n  const handleSendTextChange = e => {\n    setSendText(e.target.checked);\n  };\n  const handleNotesChange = e => {\n    setNotes(e.target.value);\n  };\n  const handlePriorityChange = e => {\n    setPriority(e.target.value);\n  };\n  const handleStatusChange = e => {\n    setStatus(e.target.value);\n  };\n  const handleFromLocationChange = e => {\n    setFromLocation(e.target.value);\n  };\n  const handleToLocationChange = e => {\n    setToLocation(e.target.value);\n  };\n  const handleClearErrorClick = () => {\n    setError(null);\n  };\n  const goBack = () => {\n    if (window.history.length > 1) {\n      navigate(-1);\n    } else {\n      navigate(routes.driverTasks.list.path);\n    }\n  };\n  if (!user || !canCreateDriverTasks) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: routes.driverTasks.list.path,\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container-fluid h-100-vh d-flex flex-column\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white sticky-top-navbar\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto row mt-2 py-2 border-bottom shadow\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 row my-2\",\n          children: /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"col\",\n            children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: ['fat', 'truck-fast']\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this), \"\\xA0 New Driver Task\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 row\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container flex-grow-1\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row p-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"due-date\",\n            children: [\"Due Date\\xA0\", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-danger\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"due-date\",\n            type: \"date\",\n            value: dueDate,\n            onChange: handleDueDateChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"assigned-to\",\n            children: \"Assigned To\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"assigned-to\",\n            type: \"select\",\n            value: (assignedTo === null || assignedTo === void 0 ? void 0 : assignedTo.name) || '',\n            onChange: handleAssignedToChange,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Unassigned\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this), drivers.map(driver => /*#__PURE__*/_jsxDEV(\"option\", {\n              children: driver.name\n            }, driver.name, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), !!assignedTo && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [!(assignedTo !== null && assignedTo !== void 0 && assignedTo.phone) && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-muted fst-italic\",\n              children: \"Driver doesn't have a phone number.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 19\n            }, this), !!(assignedTo !== null && assignedTo !== void 0 && assignedTo.phone) && /*#__PURE__*/_jsxDEV(FormGroup, {\n              switch: true,\n              className: \"mt-2\",\n              children: [/*#__PURE__*/_jsxDEV(Input, {\n                type: \"switch\",\n                id: \"driver-list-show-complete-filter\",\n                checked: sendText,\n                onChange: handleSendTextChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Label, {\n                htmlFor: \"driver-list-show-complete-filter\",\n                check: true,\n                children: \"Send Text Notification\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"notes\",\n            children: [\"Task\\xA0\", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-danger\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"notes\",\n            type: \"textarea\",\n            rows: 3,\n            value: notes,\n            onChange: handleNotesChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"from-location\",\n            children: [\"From Location\\xA0\", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-danger\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 34\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"from-location\",\n            value: fromLocation,\n            onChange: handleFromLocationChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"to-location\",\n            children: [\"To Location\\xA0\", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-danger\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 32\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"to-location\",\n            value: toLocation,\n            onChange: handleToLocationChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"priority\",\n            children: \"Priority\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"priority\",\n            type: \"select\",\n            value: priority,\n            onChange: handlePriorityChange,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: models.HighPriority,\n              children: models.HighPriority\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: models.NormalPriority,\n              children: models.NormalPriority\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: models.LowPriority,\n              children: models.LowPriority\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-12 col-md-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"status\",\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"status\",\n            type: \"select\",\n            value: status,\n            onChange: handleStatusChange,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: models.NotStartedStatus,\n              children: models.NotStartedStatus\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: models.InProgressStatus,\n              children: models.InProgressStatus\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: models.CompleteStatus,\n              children: models.CompleteStatus\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row container mx-auto mt-5 bg-white sticky-bottom border-top py-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-0 col-md\",\n        children: \"\\xA0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-6 col-md-auto text-end\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          outline: true,\n          size: \"lg\",\n          onClick: handleGoBackClick,\n          className: \"d-block d-md-inline-block w-100\",\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-6 col-md-auto\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSaveClick,\n          color: \"success\",\n          size: \"lg\",\n          className: \"d-block d-md-inline-block w-100\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: ['fat', 'save']\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this), \"\\xA0 Save\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Error, {\n        error: error,\n        clearError: handleClearErrorClick\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 115,\n    columnNumber: 5\n  }, this);\n}\n_s(New, \"EtBGjQGY146wRixk5uhkLmXM/io=\", false, function () {\n  return [useAuth, useDispatch, useNavigate, useSelector];\n});\n_c = New;\nexport default New;\nvar _c;\n$RefreshReg$(_c, \"New\");", "map": {"version": 3, "names": ["useEffect", "useState", "useDispatch", "useSelector", "useNavigate", "Navigate", "<PERSON><PERSON>", "FormGroup", "Input", "Label", "moment", "FontAwesomeIcon", "driverTasksApi", "models", "routes", "useAuth", "Error", "getDrivers", "selectDrivers", "New", "isInRole", "user", "dispatch", "navigate", "drivers", "dueDate", "setDueDate", "format", "assignedTo", "setAssignedTo", "priority", "setPriority", "notes", "setNotes", "status", "setStatus", "fromLocation", "setFromLocation", "toLocation", "setToLocation", "sendText", "setSendText", "error", "setError", "canCreateDriverTasks", "handleGoBackClick", "goBack", "handleSaveClick", "createDriverTask", "e", "handleDueDateChange", "target", "value", "handleAssignedToChange", "driver", "find", "d", "name", "phone", "handleSendTextChange", "checked", "handleNotesChange", "handlePriorityChange", "handleStatusChange", "handleFromLocationChange", "handleToLocationChange", "handleClearErrorClick", "window", "history", "length", "driverTasks", "list", "path", "map", "HighPriority", "NormalPriority", "LowPriority", "NotStartedStatus", "InProgressStatus", "CompleteStatus"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/driver-tasks/New.tsx"], "sourcesContent": ["import { useEffect, useState } from 'react';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { useNavigate, Navigate } from 'react-router';\r\nimport { Button, FormGroup, Input, Label } from 'reactstrap';\r\nimport moment from 'moment';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { driverTasksApi } from 'api/driver-tasks-service';\r\nimport * as models from 'api/models/driver-tasks';\r\nimport { routes } from 'app/routes';\r\nimport { useAuth } from 'features/auth/use-auth';\r\nimport { Error } from 'features/error/Error';\r\nimport { ProblemDetails } from 'utils/problem-details';\r\nimport { getDrivers, selectDrivers } from './driver-task-slice';\r\n\r\nexport function New() {\r\n  const { isInRole, user } = useAuth(),\r\n    dispatch = useDispatch(),\r\n    navigate = useNavigate(),\r\n    drivers = useSelector(selectDrivers),\r\n    [dueDate, setDueDate] = useState(moment().format('YYYY-MM-DD')),\r\n    [assignedTo, setAssignedTo] = useState<models.Driver | null>(null),\r\n    [priority, setPriority] = useState<models.Priority>('Normal'),\r\n    [notes, setNotes] = useState(''),\r\n    [status, setStatus] = useState<models.Status>('Not Started'),\r\n    [fromLocation, setFromLocation] = useState(''),\r\n    [toLocation, setToLocation] = useState(''),\r\n    [sendText, setSendText] = useState(false),\r\n    [error, setError] = useState<ProblemDetails | null>(null),\r\n    canCreateDriverTasks = isInRole('create:driver-tasks');\r\n\r\n  useEffect(() => {\r\n    if (user) {\r\n      dispatch(getDrivers(user));\r\n    }\r\n  }, [dispatch, user]);\r\n\r\n  const handleGoBackClick = () => goBack();\r\n\r\n  const handleSaveClick = async () => {\r\n    if (user) {\r\n      try {\r\n        await driverTasksApi.createDriverTask(\r\n          user,\r\n          dueDate,\r\n          assignedTo,\r\n          priority,\r\n          notes,\r\n          status,\r\n          fromLocation,\r\n          toLocation,\r\n          sendText\r\n        );\r\n\r\n        goBack();\r\n      } catch (e) {\r\n        setError(e as ProblemDetails);\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleDueDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    setDueDate(e.target.value);\r\n  };\r\n\r\n  const handleAssignedToChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const driver = drivers.find((d) => d.name === e.target.value) || null;\r\n    setAssignedTo(driver);\r\n\r\n    if (!driver?.phone) {\r\n      setSendText(false);\r\n    }\r\n  };\r\n\r\n  const handleSendTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    setSendText(e.target.checked);\r\n  };\r\n\r\n  const handleNotesChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    setNotes(e.target.value);\r\n  };\r\n\r\n  const handlePriorityChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    setPriority(e.target.value as models.Priority);\r\n  };\r\n\r\n  const handleStatusChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    setStatus(e.target.value as models.Status);\r\n  };\r\n\r\n  const handleFromLocationChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    setFromLocation(e.target.value);\r\n  };\r\n\r\n  const handleToLocationChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    setToLocation(e.target.value);\r\n  };\r\n\r\n  const handleClearErrorClick = () => {\r\n    setError(null);\r\n  };\r\n\r\n  const goBack = () => {\r\n    if (window.history.length > 1) {\r\n      navigate(-1);\r\n    } else {\r\n      navigate(routes.driverTasks.list.path);\r\n    }\r\n  };\r\n\r\n  if (!user || !canCreateDriverTasks) {\r\n    return <Navigate to={routes.driverTasks.list.path} replace />;\r\n  }\r\n\r\n  return (\r\n    <div className=\"container-fluid h-100-vh d-flex flex-column\">\r\n      <div className=\"bg-white sticky-top-navbar\">\r\n        <div className=\"container mx-auto row mt-2 py-2 border-bottom shadow\">\r\n          <div className=\"col-12 row my-2\">\r\n            <h1 className=\"col\">\r\n              <FontAwesomeIcon icon={['fat', 'truck-fast']} />\r\n              &nbsp; New Driver Task\r\n            </h1>\r\n          </div>\r\n          <div className=\"col-12 row\"></div>\r\n        </div>\r\n      </div>\r\n      <div className=\"container flex-grow-1\">\r\n        <div className=\"row p-2\">\r\n          <div className=\"col-12 col-md-3\">\r\n            <label htmlFor=\"due-date\">\r\n              Due Date&nbsp;<span className=\"text-danger\">*</span>\r\n            </label>\r\n            <Input\r\n              id=\"due-date\"\r\n              type=\"date\"\r\n              value={dueDate}\r\n              onChange={handleDueDateChange}\r\n            />\r\n          </div>\r\n          <div className=\"col-12 col-md-3\">\r\n            <label htmlFor=\"assigned-to\">Assigned To</label>\r\n            <Input\r\n              id=\"assigned-to\"\r\n              type=\"select\"\r\n              value={assignedTo?.name || ''}\r\n              onChange={handleAssignedToChange}>\r\n              <option value=\"\">Unassigned</option>\r\n              {drivers.map((driver) => (\r\n                <option key={driver.name}>{driver.name}</option>\r\n              ))}\r\n            </Input>\r\n            {!!assignedTo && (\r\n              <>\r\n                {!assignedTo?.phone && (\r\n                  <div className=\"text-muted fst-italic\">\r\n                    Driver doesn't have a phone number.\r\n                  </div>\r\n                )}\r\n                {!!assignedTo?.phone && (\r\n                  <FormGroup switch className=\"mt-2\">\r\n                    <Input\r\n                      type=\"switch\"\r\n                      id=\"driver-list-show-complete-filter\"\r\n                      checked={sendText}\r\n                      onChange={handleSendTextChange}\r\n                    />\r\n                    <Label htmlFor=\"driver-list-show-complete-filter\" check>\r\n                      Send Text Notification\r\n                    </Label>\r\n                  </FormGroup>\r\n                )}\r\n              </>\r\n            )}\r\n          </div>\r\n          <div className=\"col-12 col-md-6\">\r\n            <label htmlFor=\"notes\">\r\n              Task&nbsp;<span className=\"text-danger\">*</span>\r\n            </label>\r\n            <Input\r\n              id=\"notes\"\r\n              type=\"textarea\"\r\n              rows={3}\r\n              value={notes}\r\n              onChange={handleNotesChange}\r\n            />\r\n          </div>\r\n          <div className=\"col-12 col-md-3\">\r\n            <label htmlFor=\"from-location\">\r\n              From Location&nbsp;<span className=\"text-danger\">*</span>\r\n            </label>\r\n            <Input\r\n              id=\"from-location\"\r\n              value={fromLocation}\r\n              onChange={handleFromLocationChange}\r\n            />\r\n          </div>\r\n          <div className=\"col-12 col-md-3\">\r\n            <label htmlFor=\"to-location\">\r\n              To Location&nbsp;<span className=\"text-danger\">*</span>\r\n            </label>\r\n            <Input\r\n              id=\"to-location\"\r\n              value={toLocation}\r\n              onChange={handleToLocationChange}\r\n            />\r\n          </div>\r\n          <div className=\"col-12 col-md-3\">\r\n            <label htmlFor=\"priority\">Priority</label>\r\n            <Input\r\n              id=\"priority\"\r\n              type=\"select\"\r\n              value={priority}\r\n              onChange={handlePriorityChange}>\r\n              <option value={models.HighPriority}>{models.HighPriority}</option>\r\n              <option value={models.NormalPriority}>\r\n                {models.NormalPriority}\r\n              </option>\r\n              <option value={models.LowPriority}>{models.LowPriority}</option>\r\n            </Input>\r\n          </div>\r\n          <div className=\"col-12 col-md-3\">\r\n            <label htmlFor=\"status\">Status</label>\r\n            <Input\r\n              id=\"status\"\r\n              type=\"select\"\r\n              value={status}\r\n              onChange={handleStatusChange}>\r\n              <option value={models.NotStartedStatus}>\r\n                {models.NotStartedStatus}\r\n              </option>\r\n              <option value={models.InProgressStatus}>\r\n                {models.InProgressStatus}\r\n              </option>\r\n              <option value={models.CompleteStatus}>\r\n                {models.CompleteStatus}\r\n              </option>\r\n            </Input>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div className=\"row container mx-auto mt-5 bg-white sticky-bottom border-top py-2\">\r\n        <div className=\"col-0 col-md\">&nbsp;</div>\r\n        <div className=\"col-6 col-md-auto text-end\">\r\n          <Button\r\n            outline\r\n            size=\"lg\"\r\n            onClick={handleGoBackClick}\r\n            className=\"d-block d-md-inline-block w-100\">\r\n            Cancel\r\n          </Button>\r\n        </div>\r\n        <div className=\"col-6 col-md-auto\">\r\n          <Button\r\n            onClick={handleSaveClick}\r\n            color=\"success\"\r\n            size=\"lg\"\r\n            className=\"d-block d-md-inline-block w-100\">\r\n            <FontAwesomeIcon icon={['fat', 'save']} />\r\n            &nbsp; Save\r\n          </Button>\r\n        </div>\r\n        <Error error={error} clearError={handleClearErrorClick} />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default New;\r\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,QAAQ,QAAQ,cAAc;AACpD,SAASC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,KAAK,QAAQ,YAAY;AAC5D,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,cAAc,QAAQ,0BAA0B;AACzD,OAAO,KAAKC,MAAM,MAAM,yBAAyB;AACjD,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,KAAK,QAAQ,sBAAsB;AAE5C,SAASC,UAAU,EAAEC,aAAa,QAAQ,qBAAqB;AAAC;AAAA;AAEhE,OAAO,SAASC,GAAG,GAAG;EAAA;EACpB,MAAM;MAAEC,QAAQ;MAAEC;IAAK,CAAC,GAAGN,OAAO,EAAE;IAClCO,QAAQ,GAAGpB,WAAW,EAAE;IACxBqB,QAAQ,GAAGnB,WAAW,EAAE;IACxBoB,OAAO,GAAGrB,WAAW,CAACe,aAAa,CAAC;IACpC,CAACO,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAACS,MAAM,EAAE,CAACiB,MAAM,CAAC,YAAY,CAAC,CAAC;IAC/D,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAuB,IAAI,CAAC;IAClE,CAAC6B,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAkB,QAAQ,CAAC;IAC7D,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;IAChC,CAACiC,MAAM,EAAEC,SAAS,CAAC,GAAGlC,QAAQ,CAAgB,aAAa,CAAC;IAC5D,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;IAC9C,CAACqC,UAAU,EAAEC,aAAa,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;IAC1C,CAACuC,QAAQ,EAAEC,WAAW,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;IACzC,CAACyC,KAAK,EAAEC,QAAQ,CAAC,GAAG1C,QAAQ,CAAwB,IAAI,CAAC;IACzD2C,oBAAoB,GAAGxB,QAAQ,CAAC,qBAAqB,CAAC;EAExDpB,SAAS,CAAC,MAAM;IACd,IAAIqB,IAAI,EAAE;MACRC,QAAQ,CAACL,UAAU,CAACI,IAAI,CAAC,CAAC;IAC5B;EACF,CAAC,EAAE,CAACC,QAAQ,EAAED,IAAI,CAAC,CAAC;EAEpB,MAAMwB,iBAAiB,GAAG,MAAMC,MAAM,EAAE;EAExC,MAAMC,eAAe,GAAG,YAAY;IAClC,IAAI1B,IAAI,EAAE;MACR,IAAI;QACF,MAAMT,cAAc,CAACoC,gBAAgB,CACnC3B,IAAI,EACJI,OAAO,EACPG,UAAU,EACVE,QAAQ,EACRE,KAAK,EACLE,MAAM,EACNE,YAAY,EACZE,UAAU,EACVE,QAAQ,CACT;QAEDM,MAAM,EAAE;MACV,CAAC,CAAC,OAAOG,CAAC,EAAE;QACVN,QAAQ,CAACM,CAAC,CAAmB;MAC/B;IACF;EACF,CAAC;EAED,MAAMC,mBAAmB,GAAID,CAAsC,IAAK;IACtEvB,UAAU,CAACuB,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC;EAC5B,CAAC;EAED,MAAMC,sBAAsB,GAAIJ,CAAsC,IAAK;IACzE,MAAMK,MAAM,GAAG9B,OAAO,CAAC+B,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,IAAI,KAAKR,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,IAAI,IAAI;IACrEvB,aAAa,CAACyB,MAAM,CAAC;IAErB,IAAI,EAACA,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEI,KAAK,GAAE;MAClBjB,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAMkB,oBAAoB,GAAIV,CAAsC,IAAK;IACvER,WAAW,CAACQ,CAAC,CAACE,MAAM,CAACS,OAAO,CAAC;EAC/B,CAAC;EAED,MAAMC,iBAAiB,GAAIZ,CAAsC,IAAK;IACpEhB,QAAQ,CAACgB,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC;EAC1B,CAAC;EAED,MAAMU,oBAAoB,GAAIb,CAAsC,IAAK;IACvElB,WAAW,CAACkB,CAAC,CAACE,MAAM,CAACC,KAAK,CAAoB;EAChD,CAAC;EAED,MAAMW,kBAAkB,GAAId,CAAsC,IAAK;IACrEd,SAAS,CAACc,CAAC,CAACE,MAAM,CAACC,KAAK,CAAkB;EAC5C,CAAC;EAED,MAAMY,wBAAwB,GAAIf,CAAsC,IAAK;IAC3EZ,eAAe,CAACY,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC;EACjC,CAAC;EAED,MAAMa,sBAAsB,GAAIhB,CAAsC,IAAK;IACzEV,aAAa,CAACU,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC;EAC/B,CAAC;EAED,MAAMc,qBAAqB,GAAG,MAAM;IAClCvB,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;EAED,MAAMG,MAAM,GAAG,MAAM;IACnB,IAAIqB,MAAM,CAACC,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;MAC7B9C,QAAQ,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,MAAM;MACLA,QAAQ,CAACT,MAAM,CAACwD,WAAW,CAACC,IAAI,CAACC,IAAI,CAAC;IACxC;EACF,CAAC;EAED,IAAI,CAACnD,IAAI,IAAI,CAACuB,oBAAoB,EAAE;IAClC,oBAAO,QAAC,QAAQ;MAAC,EAAE,EAAE9B,MAAM,CAACwD,WAAW,CAACC,IAAI,CAACC,IAAK;MAAC,OAAO;IAAA;MAAA;MAAA;MAAA;IAAA,QAAG;EAC/D;EAEA,oBACE;IAAK,SAAS,EAAC,6CAA6C;IAAA,wBAC1D;MAAK,SAAS,EAAC,4BAA4B;MAAA,uBACzC;QAAK,SAAS,EAAC,sDAAsD;QAAA,wBACnE;UAAK,SAAS,EAAC,iBAAiB;UAAA,uBAC9B;YAAI,SAAS,EAAC,KAAK;YAAA,wBACjB,QAAC,eAAe;cAAC,IAAI,EAAE,CAAC,KAAK,EAAE,YAAY;YAAE;cAAA;cAAA;cAAA;YAAA,QAAG;UAAA;YAAA;YAAA;YAAA;UAAA;QAE7C;UAAA;UAAA;UAAA;QAAA,QACD,eACN;UAAK,SAAS,EAAC;QAAY;UAAA;UAAA;UAAA;QAAA,QAAO;MAAA;QAAA;QAAA;QAAA;MAAA;IAC9B;MAAA;MAAA;MAAA;IAAA,QACF,eACN;MAAK,SAAS,EAAC,uBAAuB;MAAA,uBACpC;QAAK,SAAS,EAAC,SAAS;QAAA,wBACtB;UAAK,SAAS,EAAC,iBAAiB;UAAA,wBAC9B;YAAO,OAAO,EAAC,UAAU;YAAA,wCACT;cAAM,SAAS,EAAC,aAAa;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAAS;UAAA;YAAA;YAAA;YAAA;UAAA,QAC9C,eACR,QAAC,KAAK;YACJ,EAAE,EAAC,UAAU;YACb,IAAI,EAAC,MAAM;YACX,KAAK,EAAE/C,OAAQ;YACf,QAAQ,EAAEyB;UAAoB;YAAA;YAAA;YAAA;UAAA,QAC9B;QAAA;UAAA;UAAA;UAAA;QAAA,QACE,eACN;UAAK,SAAS,EAAC,iBAAiB;UAAA,wBAC9B;YAAO,OAAO,EAAC,aAAa;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAoB,eAChD,QAAC,KAAK;YACJ,EAAE,EAAC,aAAa;YAChB,IAAI,EAAC,QAAQ;YACb,KAAK,EAAE,CAAAtB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE6B,IAAI,KAAI,EAAG;YAC9B,QAAQ,EAAEJ,sBAAuB;YAAA,wBACjC;cAAQ,KAAK,EAAC,EAAE;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAAoB,EACnC7B,OAAO,CAACiD,GAAG,CAAEnB,MAAM,iBAClB;cAAA,UAA2BA,MAAM,CAACG;YAAI,GAAzBH,MAAM,CAACG,IAAI;cAAA;cAAA;cAAA;YAAA,QACzB,CAAC;UAAA;YAAA;YAAA;YAAA;UAAA,QACI,EACP,CAAC,CAAC7B,UAAU,iBACX;YAAA,WACG,EAACA,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAE8B,KAAK,kBACjB;cAAK,SAAS,EAAC,uBAAuB;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAGvC,EACA,CAAC,EAAC9B,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAE8B,KAAK,kBAClB,QAAC,SAAS;cAAC,MAAM;cAAC,SAAS,EAAC,MAAM;cAAA,wBAChC,QAAC,KAAK;gBACJ,IAAI,EAAC,QAAQ;gBACb,EAAE,EAAC,kCAAkC;gBACrC,OAAO,EAAElB,QAAS;gBAClB,QAAQ,EAAEmB;cAAqB;gBAAA;gBAAA;gBAAA;cAAA,QAC/B,eACF,QAAC,KAAK;gBAAC,OAAO,EAAC,kCAAkC;gBAAC,KAAK;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA,QAE/C;YAAA;cAAA;cAAA;cAAA;YAAA,QAEX;UAAA,gBAEJ;QAAA;UAAA;UAAA;UAAA;QAAA,QACG,eACN;UAAK,SAAS,EAAC,iBAAiB;UAAA,wBAC9B;YAAO,OAAO,EAAC,OAAO;YAAA,oCACV;cAAM,SAAS,EAAC,aAAa;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAAS;UAAA;YAAA;YAAA;YAAA;UAAA,QAC1C,eACR,QAAC,KAAK;YACJ,EAAE,EAAC,OAAO;YACV,IAAI,EAAC,UAAU;YACf,IAAI,EAAE,CAAE;YACR,KAAK,EAAE3B,KAAM;YACb,QAAQ,EAAE6B;UAAkB;YAAA;YAAA;YAAA;UAAA,QAC5B;QAAA;UAAA;UAAA;UAAA;QAAA,QACE,eACN;UAAK,SAAS,EAAC,iBAAiB;UAAA,wBAC9B;YAAO,OAAO,EAAC,eAAe;YAAA,6CACT;cAAM,SAAS,EAAC,aAAa;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAAS;UAAA;YAAA;YAAA;YAAA;UAAA,QACnD,eACR,QAAC,KAAK;YACJ,EAAE,EAAC,eAAe;YAClB,KAAK,EAAEzB,YAAa;YACpB,QAAQ,EAAE4B;UAAyB;YAAA;YAAA;YAAA;UAAA,QACnC;QAAA;UAAA;UAAA;UAAA;QAAA,QACE,eACN;UAAK,SAAS,EAAC,iBAAiB;UAAA,wBAC9B;YAAO,OAAO,EAAC,aAAa;YAAA,2CACT;cAAM,SAAS,EAAC,aAAa;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAAS;UAAA;YAAA;YAAA;YAAA;UAAA,QACjD,eACR,QAAC,KAAK;YACJ,EAAE,EAAC,aAAa;YAChB,KAAK,EAAE1B,UAAW;YAClB,QAAQ,EAAE2B;UAAuB;YAAA;YAAA;YAAA;UAAA,QACjC;QAAA;UAAA;UAAA;UAAA;QAAA,QACE,eACN;UAAK,SAAS,EAAC,iBAAiB;UAAA,wBAC9B;YAAO,OAAO,EAAC,UAAU;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAiB,eAC1C,QAAC,KAAK;YACJ,EAAE,EAAC,UAAU;YACb,IAAI,EAAC,QAAQ;YACb,KAAK,EAAEnC,QAAS;YAChB,QAAQ,EAAEgC,oBAAqB;YAAA,wBAC/B;cAAQ,KAAK,EAAEjD,MAAM,CAAC6D,YAAa;cAAA,UAAE7D,MAAM,CAAC6D;YAAY;cAAA;cAAA;cAAA;YAAA,QAAU,eAClE;cAAQ,KAAK,EAAE7D,MAAM,CAAC8D,cAAe;cAAA,UAClC9D,MAAM,CAAC8D;YAAc;cAAA;cAAA;cAAA;YAAA,QACf,eACT;cAAQ,KAAK,EAAE9D,MAAM,CAAC+D,WAAY;cAAA,UAAE/D,MAAM,CAAC+D;YAAW;cAAA;cAAA;cAAA;YAAA,QAAU;UAAA;YAAA;YAAA;YAAA;UAAA,QAC1D;QAAA;UAAA;UAAA;UAAA;QAAA,QACJ,eACN;UAAK,SAAS,EAAC,iBAAiB;UAAA,wBAC9B;YAAO,OAAO,EAAC,QAAQ;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAe,eACtC,QAAC,KAAK;YACJ,EAAE,EAAC,QAAQ;YACX,IAAI,EAAC,QAAQ;YACb,KAAK,EAAE1C,MAAO;YACd,QAAQ,EAAE6B,kBAAmB;YAAA,wBAC7B;cAAQ,KAAK,EAAElD,MAAM,CAACgE,gBAAiB;cAAA,UACpChE,MAAM,CAACgE;YAAgB;cAAA;cAAA;cAAA;YAAA,QACjB,eACT;cAAQ,KAAK,EAAEhE,MAAM,CAACiE,gBAAiB;cAAA,UACpCjE,MAAM,CAACiE;YAAgB;cAAA;cAAA;cAAA;YAAA,QACjB,eACT;cAAQ,KAAK,EAAEjE,MAAM,CAACkE,cAAe;cAAA,UAClClE,MAAM,CAACkE;YAAc;cAAA;cAAA;cAAA;YAAA,QACf;UAAA;YAAA;YAAA;YAAA;UAAA,QACH;QAAA;UAAA;UAAA;UAAA;QAAA,QACJ;MAAA;QAAA;QAAA;QAAA;MAAA;IACF;MAAA;MAAA;MAAA;IAAA,QACF,eACN;MAAK,SAAS,EAAC,mEAAmE;MAAA,wBAChF;QAAK,SAAS,EAAC,cAAc;QAAA;MAAA;QAAA;QAAA;QAAA;MAAA,QAAa,eAC1C;QAAK,SAAS,EAAC,4BAA4B;QAAA,uBACzC,QAAC,MAAM;UACL,OAAO;UACP,IAAI,EAAC,IAAI;UACT,OAAO,EAAElC,iBAAkB;UAC3B,SAAS,EAAC,iCAAiC;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA;MAEpC;QAAA;QAAA;QAAA;MAAA,QACL,eACN;QAAK,SAAS,EAAC,mBAAmB;QAAA,uBAChC,QAAC,MAAM;UACL,OAAO,EAAEE,eAAgB;UACzB,KAAK,EAAC,SAAS;UACf,IAAI,EAAC,IAAI;UACT,SAAS,EAAC,iCAAiC;UAAA,wBAC3C,QAAC,eAAe;YAAC,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM;UAAE;YAAA;YAAA;YAAA;UAAA,QAAG;QAAA;UAAA;UAAA;UAAA;QAAA;MAEnC;QAAA;QAAA;QAAA;MAAA,QACL,eACN,QAAC,KAAK;QAAC,KAAK,EAAEL,KAAM;QAAC,UAAU,EAAEwB;MAAsB;QAAA;QAAA;QAAA;MAAA,QAAG;IAAA;MAAA;MAAA;MAAA;IAAA,QACtD;EAAA;IAAA;IAAA;IAAA;EAAA,QACF;AAEV;AAAC,GA3Pe/C,GAAG;EAAA,QACUJ,OAAO,EACrBb,WAAW,EACXE,WAAW,EACZD,WAAW;AAAA;AAAA,KAJTgB,GAAG;AA6PnB,eAAeA,GAAG;AAAC;AAAA"}, "metadata": {}, "sourceType": "module"}