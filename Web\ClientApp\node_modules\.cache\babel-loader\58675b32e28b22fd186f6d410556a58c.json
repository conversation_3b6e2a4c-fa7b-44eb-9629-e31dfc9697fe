{"ast": null, "code": "import { ServiceBase } from './service-base';\nclass ZoneService extends ServiceBase {\n  getAll() {\n    return this.query('filters/zones');\n  }\n  save(zone) {\n    return this.saveDocument(zone);\n  }\n}\nexport const zoneApi = new ZoneService();", "map": {"version": 3, "names": ["ServiceBase", "ZoneService", "getAll", "query", "save", "zone", "saveDocument", "zoneApi"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/api/zone-service.ts"], "sourcesContent": ["import * as models from './models/zones';\r\nimport { ServiceBase } from './service-base';\r\n\r\nclass ZoneService extends ServiceBase {\r\n  getAll() {\r\n    return this.query<models.Zone>('filters/zones');\r\n  }\r\n\r\n  save(zone: models.Zone) {\r\n    return this.saveDocument<models.Zone>(zone);\r\n  }\r\n}\r\n\r\nexport const zoneApi = new ZoneService();\r\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,gBAAgB;AAE5C,MAAMC,WAAW,SAASD,WAAW,CAAC;EACpCE,MAAM,GAAG;IACP,OAAO,IAAI,CAACC,KAAK,CAAc,eAAe,CAAC;EACjD;EAEAC,IAAI,CAACC,IAAiB,EAAE;IACtB,OAAO,IAAI,CAACC,YAAY,CAAcD,IAAI,CAAC;EAC7C;AACF;AAEA,OAAO,MAAME,OAAO,GAAG,IAAIN,WAAW,EAAE"}, "metadata": {}, "sourceType": "module"}