{"ast": null, "code": "import { createAction, createAsyncThunk, createSlice } from '@reduxjs/toolkit';\nimport { authApi } from 'api/auth-service';\nconst initialState = {\n  users: [],\n  isLoading: false,\n  error: null\n};\nexport const getAllUsers = createAsyncThunk('users/getAllUsers', async (user, _ref) => {\n  let {\n    rejectWithValue\n  } = _ref;\n  try {\n    return await authApi.getAllUsers(user);\n  } catch (e) {\n    return rejectWithValue(e);\n  }\n});\nconst getAllUsersPending = createAction(getAllUsers.pending.type),\n  getAllUsersFulfilled = createAction(getAllUsers.fulfilled.type),\n  getAllUsersRejected = createAction(getAllUsers.rejected.type);\nexport const usersSlice = createSlice({\n  name: 'users',\n  initialState,\n  reducers: {},\n  extraReducers: builder => builder.addCase(getAllUsersPending, state => {\n    state.error = null;\n    state.isLoading = true;\n  }).addCase(getAllUsersFulfilled, (state, _ref2) => {\n    let {\n      payload\n    } = _ref2;\n    state.isLoading = false;\n    state.users = payload;\n  }).addCase(getAllUsersRejected, (state, _ref3) => {\n    let {\n      payload\n    } = _ref3;\n    state.isLoading = false;\n    state.error = payload;\n  })\n});\nexport const selectUsers = state => state.users.users;\nexport const selectIsLoading = state => state.users.isLoading;\nexport const selectError = state => state.users.error;\nexport default usersSlice.reducer;", "map": {"version": 3, "names": ["createAction", "createAsyncThunk", "createSlice", "authApi", "initialState", "users", "isLoading", "error", "getAllUsers", "user", "rejectWithValue", "e", "getAllUsersPending", "pending", "type", "getAllUsersFulfilled", "fulfilled", "getAllUsersRejected", "rejected", "usersSlice", "name", "reducers", "extraReducers", "builder", "addCase", "state", "payload", "selectUsers", "selectIsLoading", "selectError", "reducer"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/users/users-slice.ts"], "sourcesContent": ["import { AsyncThunk, createAction, createAsyncThunk, createSlice } from '@reduxjs/toolkit';\r\nimport { authApi } from 'api/auth-service';\r\nimport * as models from 'api/models/auth';\r\nimport { RootState } from 'app/store';\r\nimport { ProblemDetails } from 'utils/problem-details';\r\n\r\nexport interface UsersState {\r\n  users: models.UserDoc[];\r\n  isLoading: boolean;\r\n  error: ProblemDetails | null;\r\n}\r\n\r\nconst initialState: UsersState = {\r\n  users: [],\r\n  isLoading: false,\r\n  error: null\r\n};\r\n\r\nexport const getAllUsers: AsyncThunk<models.UserDoc[], models.UserInfo, {state: RootState}> = createAsyncThunk(\r\n  'users/getAllUsers',\r\n  async (user, {rejectWithValue}) => {\r\n    try {\r\n\r\n       return await authApi.getAllUsers(user);\r\n\r\n    } catch(e) {\r\n      return rejectWithValue(e as ProblemDetails);\r\n    }\r\n  }\r\n);\r\n\r\nconst getAllUsersPending = createAction(getAllUsers.pending.type),\r\n  getAllUsersFulfilled = createAction<models.UserDoc[]>(getAllUsers.fulfilled.type),\r\n  getAllUsersRejected = createAction<ProblemDetails>(getAllUsers.rejected.type);\r\n\r\nexport const usersSlice = createSlice({\r\n  name: 'users',\r\n  initialState,\r\n  reducers: {\r\n\r\n  },\r\n  extraReducers: builder =>\r\n    builder\r\n      .addCase(getAllUsersPending, state => {\r\n        state.error = null;\r\n        state.isLoading = true;\r\n      })\r\n      .addCase(getAllUsersFulfilled, (state, {payload}) => {\r\n        state.isLoading = false;\r\n        state.users = payload;\r\n      })\r\n      .addCase(getAllUsersRejected, (state, {payload}) => {\r\n        state.isLoading = false;\r\n        state.error = payload;\r\n      })\r\n});\r\n\r\nexport const selectUsers = (state: RootState) => state.users.users;\r\nexport const selectIsLoading = (state: RootState) => state.users.isLoading;\r\nexport const selectError = (state: RootState) => state.users.error;\r\n\r\nexport default usersSlice.reducer;\r\n"], "mappings": "AAAA,SAAqBA,YAAY,EAAEC,gBAAgB,EAAEC,WAAW,QAAQ,kBAAkB;AAC1F,SAASC,OAAO,QAAQ,kBAAkB;AAW1C,MAAMC,YAAwB,GAAG;EAC/BC,KAAK,EAAE,EAAE;EACTC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,WAA8E,GAAGP,gBAAgB,CAC5G,mBAAmB,EACnB,OAAOQ,IAAI,WAAwB;EAAA,IAAtB;IAACC;EAAe,CAAC;EAC5B,IAAI;IAED,OAAO,MAAMP,OAAO,CAACK,WAAW,CAACC,IAAI,CAAC;EAEzC,CAAC,CAAC,OAAME,CAAC,EAAE;IACT,OAAOD,eAAe,CAACC,CAAC,CAAmB;EAC7C;AACF,CAAC,CACF;AAED,MAAMC,kBAAkB,GAAGZ,YAAY,CAACQ,WAAW,CAACK,OAAO,CAACC,IAAI,CAAC;EAC/DC,oBAAoB,GAAGf,YAAY,CAAmBQ,WAAW,CAACQ,SAAS,CAACF,IAAI,CAAC;EACjFG,mBAAmB,GAAGjB,YAAY,CAAiBQ,WAAW,CAACU,QAAQ,CAACJ,IAAI,CAAC;AAE/E,OAAO,MAAMK,UAAU,GAAGjB,WAAW,CAAC;EACpCkB,IAAI,EAAE,OAAO;EACbhB,YAAY;EACZiB,QAAQ,EAAE,CAEV,CAAC;EACDC,aAAa,EAAEC,OAAO,IACpBA,OAAO,CACJC,OAAO,CAACZ,kBAAkB,EAAEa,KAAK,IAAI;IACpCA,KAAK,CAAClB,KAAK,GAAG,IAAI;IAClBkB,KAAK,CAACnB,SAAS,GAAG,IAAI;EACxB,CAAC,CAAC,CACDkB,OAAO,CAACT,oBAAoB,EAAE,CAACU,KAAK,YAAgB;IAAA,IAAd;MAACC;IAAO,CAAC;IAC9CD,KAAK,CAACnB,SAAS,GAAG,KAAK;IACvBmB,KAAK,CAACpB,KAAK,GAAGqB,OAAO;EACvB,CAAC,CAAC,CACDF,OAAO,CAACP,mBAAmB,EAAE,CAACQ,KAAK,YAAgB;IAAA,IAAd;MAACC;IAAO,CAAC;IAC7CD,KAAK,CAACnB,SAAS,GAAG,KAAK;IACvBmB,KAAK,CAAClB,KAAK,GAAGmB,OAAO;EACvB,CAAC;AACP,CAAC,CAAC;AAEF,OAAO,MAAMC,WAAW,GAAIF,KAAgB,IAAKA,KAAK,CAACpB,KAAK,CAACA,KAAK;AAClE,OAAO,MAAMuB,eAAe,GAAIH,KAAgB,IAAKA,KAAK,CAACpB,KAAK,CAACC,SAAS;AAC1E,OAAO,MAAMuB,WAAW,GAAIJ,KAAgB,IAAKA,KAAK,CAACpB,KAAK,CAACE,KAAK;AAElE,eAAeY,UAAU,CAACW,OAAO"}, "metadata": {}, "sourceType": "module"}