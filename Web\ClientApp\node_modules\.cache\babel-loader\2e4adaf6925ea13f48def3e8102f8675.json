{"ast": null, "code": "/*! @preserve\n * numeral.js\n * version : 2.0.6\n * author : <PERSON>\n * license : MIT\n * http://adamwdraper.github.com/Numeral-js/\n */\n\n(function (global, factory) {\n  if (typeof define === 'function' && define.amd) {\n    define(factory);\n  } else if (typeof module === 'object' && module.exports) {\n    module.exports = factory();\n  } else {\n    global.numeral = factory();\n  }\n})(this, function () {\n  /************************************\n      Variables\n  ************************************/\n\n  var numeral,\n    _,\n    VERSION = '2.0.6',\n    formats = {},\n    locales = {},\n    defaults = {\n      currentLocale: 'en',\n      zeroFormat: null,\n      nullFormat: null,\n      defaultFormat: '0,0',\n      scalePercentBy100: true\n    },\n    options = {\n      currentLocale: defaults.currentLocale,\n      zeroFormat: defaults.zeroFormat,\n      nullFormat: defaults.nullFormat,\n      defaultFormat: defaults.defaultFormat,\n      scalePercentBy100: defaults.scalePercentBy100\n    };\n\n  /************************************\n      Constructors\n  ************************************/\n\n  // Numeral prototype object\n  function Numeral(input, number) {\n    this._input = input;\n    this._value = number;\n  }\n  numeral = function (input) {\n    var value, kind, unformatFunction, regexp;\n    if (numeral.isNumeral(input)) {\n      value = input.value();\n    } else if (input === 0 || typeof input === 'undefined') {\n      value = 0;\n    } else if (input === null || _.isNaN(input)) {\n      value = null;\n    } else if (typeof input === 'string') {\n      if (options.zeroFormat && input === options.zeroFormat) {\n        value = 0;\n      } else if (options.nullFormat && input === options.nullFormat || !input.replace(/[^0-9]+/g, '').length) {\n        value = null;\n      } else {\n        for (kind in formats) {\n          regexp = typeof formats[kind].regexps.unformat === 'function' ? formats[kind].regexps.unformat() : formats[kind].regexps.unformat;\n          if (regexp && input.match(regexp)) {\n            unformatFunction = formats[kind].unformat;\n            break;\n          }\n        }\n        unformatFunction = unformatFunction || numeral._.stringToNumber;\n        value = unformatFunction(input);\n      }\n    } else {\n      value = Number(input) || null;\n    }\n    return new Numeral(input, value);\n  };\n\n  // version number\n  numeral.version = VERSION;\n\n  // compare numeral object\n  numeral.isNumeral = function (obj) {\n    return obj instanceof Numeral;\n  };\n\n  // helper functions\n  numeral._ = _ = {\n    // formats numbers separators, decimals places, signs, abbreviations\n    numberToFormat: function (value, format, roundingFunction) {\n      var locale = locales[numeral.options.currentLocale],\n        negP = false,\n        optDec = false,\n        leadingCount = 0,\n        abbr = '',\n        trillion = 1000000000000,\n        billion = 1000000000,\n        million = 1000000,\n        thousand = 1000,\n        decimal = '',\n        neg = false,\n        abbrForce,\n        // force abbreviation\n        abs,\n        min,\n        max,\n        power,\n        int,\n        precision,\n        signed,\n        thousands,\n        output;\n\n      // make sure we never format a null value\n      value = value || 0;\n      abs = Math.abs(value);\n\n      // see if we should use parentheses for negative number or if we should prefix with a sign\n      // if both are present we default to parentheses\n      if (numeral._.includes(format, '(')) {\n        negP = true;\n        format = format.replace(/[\\(|\\)]/g, '');\n      } else if (numeral._.includes(format, '+') || numeral._.includes(format, '-')) {\n        signed = numeral._.includes(format, '+') ? format.indexOf('+') : value < 0 ? format.indexOf('-') : -1;\n        format = format.replace(/[\\+|\\-]/g, '');\n      }\n\n      // see if abbreviation is wanted\n      if (numeral._.includes(format, 'a')) {\n        abbrForce = format.match(/a(k|m|b|t)?/);\n        abbrForce = abbrForce ? abbrForce[1] : false;\n\n        // check for space before abbreviation\n        if (numeral._.includes(format, ' a')) {\n          abbr = ' ';\n        }\n        format = format.replace(new RegExp(abbr + 'a[kmbt]?'), '');\n        if (abs >= trillion && !abbrForce || abbrForce === 't') {\n          // trillion\n          abbr += locale.abbreviations.trillion;\n          value = value / trillion;\n        } else if (abs < trillion && abs >= billion && !abbrForce || abbrForce === 'b') {\n          // billion\n          abbr += locale.abbreviations.billion;\n          value = value / billion;\n        } else if (abs < billion && abs >= million && !abbrForce || abbrForce === 'm') {\n          // million\n          abbr += locale.abbreviations.million;\n          value = value / million;\n        } else if (abs < million && abs >= thousand && !abbrForce || abbrForce === 'k') {\n          // thousand\n          abbr += locale.abbreviations.thousand;\n          value = value / thousand;\n        }\n      }\n\n      // check for optional decimals\n      if (numeral._.includes(format, '[.]')) {\n        optDec = true;\n        format = format.replace('[.]', '.');\n      }\n\n      // break number and format\n      int = value.toString().split('.')[0];\n      precision = format.split('.')[1];\n      thousands = format.indexOf(',');\n      leadingCount = (format.split('.')[0].split(',')[0].match(/0/g) || []).length;\n      if (precision) {\n        if (numeral._.includes(precision, '[')) {\n          precision = precision.replace(']', '');\n          precision = precision.split('[');\n          decimal = numeral._.toFixed(value, precision[0].length + precision[1].length, roundingFunction, precision[1].length);\n        } else {\n          decimal = numeral._.toFixed(value, precision.length, roundingFunction);\n        }\n        int = decimal.split('.')[0];\n        if (numeral._.includes(decimal, '.')) {\n          decimal = locale.delimiters.decimal + decimal.split('.')[1];\n        } else {\n          decimal = '';\n        }\n        if (optDec && Number(decimal.slice(1)) === 0) {\n          decimal = '';\n        }\n      } else {\n        int = numeral._.toFixed(value, 0, roundingFunction);\n      }\n\n      // check abbreviation again after rounding\n      if (abbr && !abbrForce && Number(int) >= 1000 && abbr !== locale.abbreviations.trillion) {\n        int = String(Number(int) / 1000);\n        switch (abbr) {\n          case locale.abbreviations.thousand:\n            abbr = locale.abbreviations.million;\n            break;\n          case locale.abbreviations.million:\n            abbr = locale.abbreviations.billion;\n            break;\n          case locale.abbreviations.billion:\n            abbr = locale.abbreviations.trillion;\n            break;\n        }\n      }\n\n      // format number\n      if (numeral._.includes(int, '-')) {\n        int = int.slice(1);\n        neg = true;\n      }\n      if (int.length < leadingCount) {\n        for (var i = leadingCount - int.length; i > 0; i--) {\n          int = '0' + int;\n        }\n      }\n      if (thousands > -1) {\n        int = int.toString().replace(/(\\d)(?=(\\d{3})+(?!\\d))/g, '$1' + locale.delimiters.thousands);\n      }\n      if (format.indexOf('.') === 0) {\n        int = '';\n      }\n      output = int + decimal + (abbr ? abbr : '');\n      if (negP) {\n        output = (negP && neg ? '(' : '') + output + (negP && neg ? ')' : '');\n      } else {\n        if (signed >= 0) {\n          output = signed === 0 ? (neg ? '-' : '+') + output : output + (neg ? '-' : '+');\n        } else if (neg) {\n          output = '-' + output;\n        }\n      }\n      return output;\n    },\n    // unformats numbers separators, decimals places, signs, abbreviations\n    stringToNumber: function (string) {\n      var locale = locales[options.currentLocale],\n        stringOriginal = string,\n        abbreviations = {\n          thousand: 3,\n          million: 6,\n          billion: 9,\n          trillion: 12\n        },\n        abbreviation,\n        value,\n        i,\n        regexp;\n      if (options.zeroFormat && string === options.zeroFormat) {\n        value = 0;\n      } else if (options.nullFormat && string === options.nullFormat || !string.replace(/[^0-9]+/g, '').length) {\n        value = null;\n      } else {\n        value = 1;\n        if (locale.delimiters.decimal !== '.') {\n          string = string.replace(/\\./g, '').replace(locale.delimiters.decimal, '.');\n        }\n        for (abbreviation in abbreviations) {\n          regexp = new RegExp('[^a-zA-Z]' + locale.abbreviations[abbreviation] + '(?:\\\\)|(\\\\' + locale.currency.symbol + ')?(?:\\\\))?)?$');\n          if (stringOriginal.match(regexp)) {\n            value *= Math.pow(10, abbreviations[abbreviation]);\n            break;\n          }\n        }\n\n        // check for negative number\n        value *= (string.split('-').length + Math.min(string.split('(').length - 1, string.split(')').length - 1)) % 2 ? 1 : -1;\n\n        // remove non numbers\n        string = string.replace(/[^0-9\\.]+/g, '');\n        value *= Number(string);\n      }\n      return value;\n    },\n    isNaN: function (value) {\n      return typeof value === 'number' && isNaN(value);\n    },\n    includes: function (string, search) {\n      return string.indexOf(search) !== -1;\n    },\n    insert: function (string, subString, start) {\n      return string.slice(0, start) + subString + string.slice(start);\n    },\n    reduce: function (array, callback /*, initialValue*/) {\n      if (this === null) {\n        throw new TypeError('Array.prototype.reduce called on null or undefined');\n      }\n      if (typeof callback !== 'function') {\n        throw new TypeError(callback + ' is not a function');\n      }\n      var t = Object(array),\n        len = t.length >>> 0,\n        k = 0,\n        value;\n      if (arguments.length === 3) {\n        value = arguments[2];\n      } else {\n        while (k < len && !(k in t)) {\n          k++;\n        }\n        if (k >= len) {\n          throw new TypeError('Reduce of empty array with no initial value');\n        }\n        value = t[k++];\n      }\n      for (; k < len; k++) {\n        if (k in t) {\n          value = callback(value, t[k], k, t);\n        }\n      }\n      return value;\n    },\n    /**\n     * Computes the multiplier necessary to make x >= 1,\n     * effectively eliminating miscalculations caused by\n     * finite precision.\n     */\n    multiplier: function (x) {\n      var parts = x.toString().split('.');\n      return parts.length < 2 ? 1 : Math.pow(10, parts[1].length);\n    },\n    /**\n     * Given a variable number of arguments, returns the maximum\n     * multiplier that must be used to normalize an operation involving\n     * all of them.\n     */\n    correctionFactor: function () {\n      var args = Array.prototype.slice.call(arguments);\n      return args.reduce(function (accum, next) {\n        var mn = _.multiplier(next);\n        return accum > mn ? accum : mn;\n      }, 1);\n    },\n    /**\n     * Implementation of toFixed() that treats floats more like decimals\n     *\n     * Fixes binary rounding issues (eg. (0.615).toFixed(2) === '0.61') that present\n     * problems for accounting- and finance-related software.\n     */\n    toFixed: function (value, maxDecimals, roundingFunction, optionals) {\n      var splitValue = value.toString().split('.'),\n        minDecimals = maxDecimals - (optionals || 0),\n        boundedPrecision,\n        optionalsRegExp,\n        power,\n        output;\n\n      // Use the smallest precision value possible to avoid errors from floating point representation\n      if (splitValue.length === 2) {\n        boundedPrecision = Math.min(Math.max(splitValue[1].length, minDecimals), maxDecimals);\n      } else {\n        boundedPrecision = minDecimals;\n      }\n      power = Math.pow(10, boundedPrecision);\n\n      // Multiply up by precision, round accurately, then divide and use native toFixed():\n      output = (roundingFunction(value + 'e+' + boundedPrecision) / power).toFixed(boundedPrecision);\n      if (optionals > maxDecimals - boundedPrecision) {\n        optionalsRegExp = new RegExp('\\\\.?0{1,' + (optionals - (maxDecimals - boundedPrecision)) + '}$');\n        output = output.replace(optionalsRegExp, '');\n      }\n      return output;\n    }\n  };\n\n  // avaliable options\n  numeral.options = options;\n\n  // avaliable formats\n  numeral.formats = formats;\n\n  // avaliable formats\n  numeral.locales = locales;\n\n  // This function sets the current locale.  If\n  // no arguments are passed in, it will simply return the current global\n  // locale key.\n  numeral.locale = function (key) {\n    if (key) {\n      options.currentLocale = key.toLowerCase();\n    }\n    return options.currentLocale;\n  };\n\n  // This function provides access to the loaded locale data.  If\n  // no arguments are passed in, it will simply return the current\n  // global locale object.\n  numeral.localeData = function (key) {\n    if (!key) {\n      return locales[options.currentLocale];\n    }\n    key = key.toLowerCase();\n    if (!locales[key]) {\n      throw new Error('Unknown locale : ' + key);\n    }\n    return locales[key];\n  };\n  numeral.reset = function () {\n    for (var property in defaults) {\n      options[property] = defaults[property];\n    }\n  };\n  numeral.zeroFormat = function (format) {\n    options.zeroFormat = typeof format === 'string' ? format : null;\n  };\n  numeral.nullFormat = function (format) {\n    options.nullFormat = typeof format === 'string' ? format : null;\n  };\n  numeral.defaultFormat = function (format) {\n    options.defaultFormat = typeof format === 'string' ? format : '0.0';\n  };\n  numeral.register = function (type, name, format) {\n    name = name.toLowerCase();\n    if (this[type + 's'][name]) {\n      throw new TypeError(name + ' ' + type + ' already registered.');\n    }\n    this[type + 's'][name] = format;\n    return format;\n  };\n  numeral.validate = function (val, culture) {\n    var _decimalSep, _thousandSep, _currSymbol, _valArray, _abbrObj, _thousandRegEx, localeData, temp;\n\n    //coerce val to string\n    if (typeof val !== 'string') {\n      val += '';\n      if (console.warn) {\n        console.warn('Numeral.js: Value is not string. It has been co-erced to: ', val);\n      }\n    }\n\n    //trim whitespaces from either sides\n    val = val.trim();\n\n    //if val is just digits return true\n    if (!!val.match(/^\\d+$/)) {\n      return true;\n    }\n\n    //if val is empty return false\n    if (val === '') {\n      return false;\n    }\n\n    //get the decimal and thousands separator from numeral.localeData\n    try {\n      //check if the culture is understood by numeral. if not, default it to current locale\n      localeData = numeral.localeData(culture);\n    } catch (e) {\n      localeData = numeral.localeData(numeral.locale());\n    }\n\n    //setup the delimiters and currency symbol based on culture/locale\n    _currSymbol = localeData.currency.symbol;\n    _abbrObj = localeData.abbreviations;\n    _decimalSep = localeData.delimiters.decimal;\n    if (localeData.delimiters.thousands === '.') {\n      _thousandSep = '\\\\.';\n    } else {\n      _thousandSep = localeData.delimiters.thousands;\n    }\n\n    // validating currency symbol\n    temp = val.match(/^[^\\d]+/);\n    if (temp !== null) {\n      val = val.substr(1);\n      if (temp[0] !== _currSymbol) {\n        return false;\n      }\n    }\n\n    //validating abbreviation symbol\n    temp = val.match(/[^\\d]+$/);\n    if (temp !== null) {\n      val = val.slice(0, -1);\n      if (temp[0] !== _abbrObj.thousand && temp[0] !== _abbrObj.million && temp[0] !== _abbrObj.billion && temp[0] !== _abbrObj.trillion) {\n        return false;\n      }\n    }\n    _thousandRegEx = new RegExp(_thousandSep + '{2}');\n    if (!val.match(/[^\\d.,]/g)) {\n      _valArray = val.split(_decimalSep);\n      if (_valArray.length > 2) {\n        return false;\n      } else {\n        if (_valArray.length < 2) {\n          return !!_valArray[0].match(/^\\d+.*\\d$/) && !_valArray[0].match(_thousandRegEx);\n        } else {\n          if (_valArray[0].length === 1) {\n            return !!_valArray[0].match(/^\\d+$/) && !_valArray[0].match(_thousandRegEx) && !!_valArray[1].match(/^\\d+$/);\n          } else {\n            return !!_valArray[0].match(/^\\d+.*\\d$/) && !_valArray[0].match(_thousandRegEx) && !!_valArray[1].match(/^\\d+$/);\n          }\n        }\n      }\n    }\n    return false;\n  };\n\n  /************************************\n      Numeral Prototype\n  ************************************/\n\n  numeral.fn = Numeral.prototype = {\n    clone: function () {\n      return numeral(this);\n    },\n    format: function (inputString, roundingFunction) {\n      var value = this._value,\n        format = inputString || options.defaultFormat,\n        kind,\n        output,\n        formatFunction;\n\n      // make sure we have a roundingFunction\n      roundingFunction = roundingFunction || Math.round;\n\n      // format based on value\n      if (value === 0 && options.zeroFormat !== null) {\n        output = options.zeroFormat;\n      } else if (value === null && options.nullFormat !== null) {\n        output = options.nullFormat;\n      } else {\n        for (kind in formats) {\n          if (format.match(formats[kind].regexps.format)) {\n            formatFunction = formats[kind].format;\n            break;\n          }\n        }\n        formatFunction = formatFunction || numeral._.numberToFormat;\n        output = formatFunction(value, format, roundingFunction);\n      }\n      return output;\n    },\n    value: function () {\n      return this._value;\n    },\n    input: function () {\n      return this._input;\n    },\n    set: function (value) {\n      this._value = Number(value);\n      return this;\n    },\n    add: function (value) {\n      var corrFactor = _.correctionFactor.call(null, this._value, value);\n      function cback(accum, curr, currI, O) {\n        return accum + Math.round(corrFactor * curr);\n      }\n      this._value = _.reduce([this._value, value], cback, 0) / corrFactor;\n      return this;\n    },\n    subtract: function (value) {\n      var corrFactor = _.correctionFactor.call(null, this._value, value);\n      function cback(accum, curr, currI, O) {\n        return accum - Math.round(corrFactor * curr);\n      }\n      this._value = _.reduce([value], cback, Math.round(this._value * corrFactor)) / corrFactor;\n      return this;\n    },\n    multiply: function (value) {\n      function cback(accum, curr, currI, O) {\n        var corrFactor = _.correctionFactor(accum, curr);\n        return Math.round(accum * corrFactor) * Math.round(curr * corrFactor) / Math.round(corrFactor * corrFactor);\n      }\n      this._value = _.reduce([this._value, value], cback, 1);\n      return this;\n    },\n    divide: function (value) {\n      function cback(accum, curr, currI, O) {\n        var corrFactor = _.correctionFactor(accum, curr);\n        return Math.round(accum * corrFactor) / Math.round(curr * corrFactor);\n      }\n      this._value = _.reduce([this._value, value], cback);\n      return this;\n    },\n    difference: function (value) {\n      return Math.abs(numeral(this._value).subtract(value).value());\n    }\n  };\n\n  /************************************\n      Default Locale && Format\n  ************************************/\n\n  numeral.register('locale', 'en', {\n    delimiters: {\n      thousands: ',',\n      decimal: '.'\n    },\n    abbreviations: {\n      thousand: 'k',\n      million: 'm',\n      billion: 'b',\n      trillion: 't'\n    },\n    ordinal: function (number) {\n      var b = number % 10;\n      return ~~(number % 100 / 10) === 1 ? 'th' : b === 1 ? 'st' : b === 2 ? 'nd' : b === 3 ? 'rd' : 'th';\n    },\n    currency: {\n      symbol: '$'\n    }\n  });\n  (function () {\n    numeral.register('format', 'bps', {\n      regexps: {\n        format: /(BPS)/,\n        unformat: /(BPS)/\n      },\n      format: function (value, format, roundingFunction) {\n        var space = numeral._.includes(format, ' BPS') ? ' ' : '',\n          output;\n        value = value * 10000;\n\n        // check for space before BPS\n        format = format.replace(/\\s?BPS/, '');\n        output = numeral._.numberToFormat(value, format, roundingFunction);\n        if (numeral._.includes(output, ')')) {\n          output = output.split('');\n          output.splice(-1, 0, space + 'BPS');\n          output = output.join('');\n        } else {\n          output = output + space + 'BPS';\n        }\n        return output;\n      },\n      unformat: function (string) {\n        return +(numeral._.stringToNumber(string) * 0.0001).toFixed(15);\n      }\n    });\n  })();\n  (function () {\n    var decimal = {\n        base: 1000,\n        suffixes: ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']\n      },\n      binary = {\n        base: 1024,\n        suffixes: ['B', 'KiB', 'MiB', 'GiB', 'TiB', 'PiB', 'EiB', 'ZiB', 'YiB']\n      };\n    var allSuffixes = decimal.suffixes.concat(binary.suffixes.filter(function (item) {\n      return decimal.suffixes.indexOf(item) < 0;\n    }));\n    var unformatRegex = allSuffixes.join('|');\n    // Allow support for BPS (http://www.investopedia.com/terms/b/basispoint.asp)\n    unformatRegex = '(' + unformatRegex.replace('B', 'B(?!PS)') + ')';\n    numeral.register('format', 'bytes', {\n      regexps: {\n        format: /([0\\s]i?b)/,\n        unformat: new RegExp(unformatRegex)\n      },\n      format: function (value, format, roundingFunction) {\n        var output,\n          bytes = numeral._.includes(format, 'ib') ? binary : decimal,\n          suffix = numeral._.includes(format, ' b') || numeral._.includes(format, ' ib') ? ' ' : '',\n          power,\n          min,\n          max;\n\n        // check for space before\n        format = format.replace(/\\s?i?b/, '');\n        for (power = 0; power <= bytes.suffixes.length; power++) {\n          min = Math.pow(bytes.base, power);\n          max = Math.pow(bytes.base, power + 1);\n          if (value === null || value === 0 || value >= min && value < max) {\n            suffix += bytes.suffixes[power];\n            if (min > 0) {\n              value = value / min;\n            }\n            break;\n          }\n        }\n        output = numeral._.numberToFormat(value, format, roundingFunction);\n        return output + suffix;\n      },\n      unformat: function (string) {\n        var value = numeral._.stringToNumber(string),\n          power,\n          bytesMultiplier;\n        if (value) {\n          for (power = decimal.suffixes.length - 1; power >= 0; power--) {\n            if (numeral._.includes(string, decimal.suffixes[power])) {\n              bytesMultiplier = Math.pow(decimal.base, power);\n              break;\n            }\n            if (numeral._.includes(string, binary.suffixes[power])) {\n              bytesMultiplier = Math.pow(binary.base, power);\n              break;\n            }\n          }\n          value *= bytesMultiplier || 1;\n        }\n        return value;\n      }\n    });\n  })();\n  (function () {\n    numeral.register('format', 'currency', {\n      regexps: {\n        format: /(\\$)/\n      },\n      format: function (value, format, roundingFunction) {\n        var locale = numeral.locales[numeral.options.currentLocale],\n          symbols = {\n            before: format.match(/^([\\+|\\-|\\(|\\s|\\$]*)/)[0],\n            after: format.match(/([\\+|\\-|\\)|\\s|\\$]*)$/)[0]\n          },\n          output,\n          symbol,\n          i;\n\n        // strip format of spaces and $\n        format = format.replace(/\\s?\\$\\s?/, '');\n\n        // format the number\n        output = numeral._.numberToFormat(value, format, roundingFunction);\n\n        // update the before and after based on value\n        if (value >= 0) {\n          symbols.before = symbols.before.replace(/[\\-\\(]/, '');\n          symbols.after = symbols.after.replace(/[\\-\\)]/, '');\n        } else if (value < 0 && !numeral._.includes(symbols.before, '-') && !numeral._.includes(symbols.before, '(')) {\n          symbols.before = '-' + symbols.before;\n        }\n\n        // loop through each before symbol\n        for (i = 0; i < symbols.before.length; i++) {\n          symbol = symbols.before[i];\n          switch (symbol) {\n            case '$':\n              output = numeral._.insert(output, locale.currency.symbol, i);\n              break;\n            case ' ':\n              output = numeral._.insert(output, ' ', i + locale.currency.symbol.length - 1);\n              break;\n          }\n        }\n\n        // loop through each after symbol\n        for (i = symbols.after.length - 1; i >= 0; i--) {\n          symbol = symbols.after[i];\n          switch (symbol) {\n            case '$':\n              output = i === symbols.after.length - 1 ? output + locale.currency.symbol : numeral._.insert(output, locale.currency.symbol, -(symbols.after.length - (1 + i)));\n              break;\n            case ' ':\n              output = i === symbols.after.length - 1 ? output + ' ' : numeral._.insert(output, ' ', -(symbols.after.length - (1 + i) + locale.currency.symbol.length - 1));\n              break;\n          }\n        }\n        return output;\n      }\n    });\n  })();\n  (function () {\n    numeral.register('format', 'exponential', {\n      regexps: {\n        format: /(e\\+|e-)/,\n        unformat: /(e\\+|e-)/\n      },\n      format: function (value, format, roundingFunction) {\n        var output,\n          exponential = typeof value === 'number' && !numeral._.isNaN(value) ? value.toExponential() : '0e+0',\n          parts = exponential.split('e');\n        format = format.replace(/e[\\+|\\-]{1}0/, '');\n        output = numeral._.numberToFormat(Number(parts[0]), format, roundingFunction);\n        return output + 'e' + parts[1];\n      },\n      unformat: function (string) {\n        var parts = numeral._.includes(string, 'e+') ? string.split('e+') : string.split('e-'),\n          value = Number(parts[0]),\n          power = Number(parts[1]);\n        power = numeral._.includes(string, 'e-') ? power *= -1 : power;\n        function cback(accum, curr, currI, O) {\n          var corrFactor = numeral._.correctionFactor(accum, curr),\n            num = accum * corrFactor * (curr * corrFactor) / (corrFactor * corrFactor);\n          return num;\n        }\n        return numeral._.reduce([value, Math.pow(10, power)], cback, 1);\n      }\n    });\n  })();\n  (function () {\n    numeral.register('format', 'ordinal', {\n      regexps: {\n        format: /(o)/\n      },\n      format: function (value, format, roundingFunction) {\n        var locale = numeral.locales[numeral.options.currentLocale],\n          output,\n          ordinal = numeral._.includes(format, ' o') ? ' ' : '';\n\n        // check for space before\n        format = format.replace(/\\s?o/, '');\n        ordinal += locale.ordinal(value);\n        output = numeral._.numberToFormat(value, format, roundingFunction);\n        return output + ordinal;\n      }\n    });\n  })();\n  (function () {\n    numeral.register('format', 'percentage', {\n      regexps: {\n        format: /(%)/,\n        unformat: /(%)/\n      },\n      format: function (value, format, roundingFunction) {\n        var space = numeral._.includes(format, ' %') ? ' ' : '',\n          output;\n        if (numeral.options.scalePercentBy100) {\n          value = value * 100;\n        }\n\n        // check for space before %\n        format = format.replace(/\\s?\\%/, '');\n        output = numeral._.numberToFormat(value, format, roundingFunction);\n        if (numeral._.includes(output, ')')) {\n          output = output.split('');\n          output.splice(-1, 0, space + '%');\n          output = output.join('');\n        } else {\n          output = output + space + '%';\n        }\n        return output;\n      },\n      unformat: function (string) {\n        var number = numeral._.stringToNumber(string);\n        if (numeral.options.scalePercentBy100) {\n          return number * 0.01;\n        }\n        return number;\n      }\n    });\n  })();\n  (function () {\n    numeral.register('format', 'time', {\n      regexps: {\n        format: /(:)/,\n        unformat: /(:)/\n      },\n      format: function (value, format, roundingFunction) {\n        var hours = Math.floor(value / 60 / 60),\n          minutes = Math.floor((value - hours * 60 * 60) / 60),\n          seconds = Math.round(value - hours * 60 * 60 - minutes * 60);\n        return hours + ':' + (minutes < 10 ? '0' + minutes : minutes) + ':' + (seconds < 10 ? '0' + seconds : seconds);\n      },\n      unformat: function (string) {\n        var timeArray = string.split(':'),\n          seconds = 0;\n\n        // turn hours and minutes into seconds and add them all up\n        if (timeArray.length === 3) {\n          // hours\n          seconds = seconds + Number(timeArray[0]) * 60 * 60;\n          // minutes\n          seconds = seconds + Number(timeArray[1]) * 60;\n          // seconds\n          seconds = seconds + Number(timeArray[2]);\n        } else if (timeArray.length === 2) {\n          // minutes\n          seconds = seconds + Number(timeArray[0]) * 60;\n          // seconds\n          seconds = seconds + Number(timeArray[1]);\n        }\n        return Number(seconds);\n      }\n    });\n  })();\n  return numeral;\n});", "map": {"version": 3, "names": ["global", "factory", "define", "amd", "module", "exports", "numeral", "_", "VERSION", "formats", "locales", "defaults", "currentLocale", "zeroFormat", "nullFormat", "defaultFormat", "scalePercentBy100", "options", "Numeral", "input", "number", "_input", "_value", "value", "kind", "unformatFunction", "regexp", "isNumeral", "isNaN", "replace", "length", "regexps", "unformat", "match", "stringToNumber", "Number", "version", "obj", "numberToFormat", "format", "roundingFunction", "locale", "negP", "optDec", "leadingCount", "abbr", "trillion", "billion", "million", "thousand", "decimal", "neg", "abbr<PERSON><PERSON>ce", "abs", "min", "max", "power", "int", "precision", "signed", "thousands", "output", "Math", "includes", "indexOf", "RegExp", "abbreviations", "toString", "split", "toFixed", "delimiters", "slice", "String", "i", "string", "stringOriginal", "abbreviation", "currency", "symbol", "pow", "search", "insert", "subString", "start", "reduce", "array", "callback", "TypeError", "t", "Object", "len", "k", "arguments", "multiplier", "x", "parts", "correctionFactor", "args", "Array", "prototype", "call", "accum", "next", "mn", "maxDecimals", "optionals", "splitValue", "minDecimals", "boundedPrecision", "optionalsRegExp", "key", "toLowerCase", "localeData", "Error", "reset", "property", "register", "type", "name", "validate", "val", "culture", "_decimalSep", "_thousandSep", "_currSymbol", "_valArray", "_abbrObj", "_thousandRegEx", "temp", "console", "warn", "trim", "e", "substr", "fn", "clone", "inputString", "formatFunction", "round", "set", "add", "corrFactor", "cback", "curr", "currI", "O", "subtract", "multiply", "divide", "difference", "ordinal", "b", "space", "splice", "join", "base", "suffixes", "binary", "allSuffixes", "concat", "filter", "item", "unformatRegex", "bytes", "suffix", "bytesMultiplier", "symbols", "before", "after", "exponential", "toExponential", "num", "hours", "floor", "minutes", "seconds", "timeArray"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/node_modules/numeral/numeral.js"], "sourcesContent": ["/*! @preserve\n * numeral.js\n * version : 2.0.6\n * author : <PERSON>\n * license : MIT\n * http://adamwdraper.github.com/Numeral-js/\n */\n\n(function (global, factory) {\n    if (typeof define === 'function' && define.amd) {\n        define(factory);\n    } else if (typeof module === 'object' && module.exports) {\n        module.exports = factory();\n    } else {\n        global.numeral = factory();\n    }\n}(this, function () {\n    /************************************\n        Variables\n    ************************************/\n\n    var numeral,\n        _,\n        VERSION = '2.0.6',\n        formats = {},\n        locales = {},\n        defaults = {\n            currentLocale: 'en',\n            zeroFormat: null,\n            nullFormat: null,\n            defaultFormat: '0,0',\n            scalePercentBy100: true\n        },\n        options = {\n            currentLocale: defaults.currentLocale,\n            zeroFormat: defaults.zeroFormat,\n            nullFormat: defaults.nullFormat,\n            defaultFormat: defaults.defaultFormat,\n            scalePercentBy100: defaults.scalePercentBy100\n        };\n\n\n    /************************************\n        Constructors\n    ************************************/\n\n    // Numeral prototype object\n    function Numeral(input, number) {\n        this._input = input;\n\n        this._value = number;\n    }\n\n    numeral = function(input) {\n        var value,\n            kind,\n            unformatFunction,\n            regexp;\n\n        if (numeral.isNumeral(input)) {\n            value = input.value();\n        } else if (input === 0 || typeof input === 'undefined') {\n            value = 0;\n        } else if (input === null || _.isNaN(input)) {\n            value = null;\n        } else if (typeof input === 'string') {\n            if (options.zeroFormat && input === options.zeroFormat) {\n                value = 0;\n            } else if (options.nullFormat && input === options.nullFormat || !input.replace(/[^0-9]+/g, '').length) {\n                value = null;\n            } else {\n                for (kind in formats) {\n                    regexp = typeof formats[kind].regexps.unformat === 'function' ? formats[kind].regexps.unformat() : formats[kind].regexps.unformat;\n\n                    if (regexp && input.match(regexp)) {\n                        unformatFunction = formats[kind].unformat;\n\n                        break;\n                    }\n                }\n\n                unformatFunction = unformatFunction || numeral._.stringToNumber;\n\n                value = unformatFunction(input);\n            }\n        } else {\n            value = Number(input)|| null;\n        }\n\n        return new Numeral(input, value);\n    };\n\n    // version number\n    numeral.version = VERSION;\n\n    // compare numeral object\n    numeral.isNumeral = function(obj) {\n        return obj instanceof Numeral;\n    };\n\n    // helper functions\n    numeral._ = _ = {\n        // formats numbers separators, decimals places, signs, abbreviations\n        numberToFormat: function(value, format, roundingFunction) {\n            var locale = locales[numeral.options.currentLocale],\n                negP = false,\n                optDec = false,\n                leadingCount = 0,\n                abbr = '',\n                trillion = 1000000000000,\n                billion = 1000000000,\n                million = 1000000,\n                thousand = 1000,\n                decimal = '',\n                neg = false,\n                abbrForce, // force abbreviation\n                abs,\n                min,\n                max,\n                power,\n                int,\n                precision,\n                signed,\n                thousands,\n                output;\n\n            // make sure we never format a null value\n            value = value || 0;\n\n            abs = Math.abs(value);\n\n            // see if we should use parentheses for negative number or if we should prefix with a sign\n            // if both are present we default to parentheses\n            if (numeral._.includes(format, '(')) {\n                negP = true;\n                format = format.replace(/[\\(|\\)]/g, '');\n            } else if (numeral._.includes(format, '+') || numeral._.includes(format, '-')) {\n                signed = numeral._.includes(format, '+') ? format.indexOf('+') : value < 0 ? format.indexOf('-') : -1;\n                format = format.replace(/[\\+|\\-]/g, '');\n            }\n\n            // see if abbreviation is wanted\n            if (numeral._.includes(format, 'a')) {\n                abbrForce = format.match(/a(k|m|b|t)?/);\n\n                abbrForce = abbrForce ? abbrForce[1] : false;\n\n                // check for space before abbreviation\n                if (numeral._.includes(format, ' a')) {\n                    abbr = ' ';\n                }\n\n                format = format.replace(new RegExp(abbr + 'a[kmbt]?'), '');\n\n                if (abs >= trillion && !abbrForce || abbrForce === 't') {\n                    // trillion\n                    abbr += locale.abbreviations.trillion;\n                    value = value / trillion;\n                } else if (abs < trillion && abs >= billion && !abbrForce || abbrForce === 'b') {\n                    // billion\n                    abbr += locale.abbreviations.billion;\n                    value = value / billion;\n                } else if (abs < billion && abs >= million && !abbrForce || abbrForce === 'm') {\n                    // million\n                    abbr += locale.abbreviations.million;\n                    value = value / million;\n                } else if (abs < million && abs >= thousand && !abbrForce || abbrForce === 'k') {\n                    // thousand\n                    abbr += locale.abbreviations.thousand;\n                    value = value / thousand;\n                }\n            }\n\n            // check for optional decimals\n            if (numeral._.includes(format, '[.]')) {\n                optDec = true;\n                format = format.replace('[.]', '.');\n            }\n\n            // break number and format\n            int = value.toString().split('.')[0];\n            precision = format.split('.')[1];\n            thousands = format.indexOf(',');\n            leadingCount = (format.split('.')[0].split(',')[0].match(/0/g) || []).length;\n\n            if (precision) {\n                if (numeral._.includes(precision, '[')) {\n                    precision = precision.replace(']', '');\n                    precision = precision.split('[');\n                    decimal = numeral._.toFixed(value, (precision[0].length + precision[1].length), roundingFunction, precision[1].length);\n                } else {\n                    decimal = numeral._.toFixed(value, precision.length, roundingFunction);\n                }\n\n                int = decimal.split('.')[0];\n\n                if (numeral._.includes(decimal, '.')) {\n                    decimal = locale.delimiters.decimal + decimal.split('.')[1];\n                } else {\n                    decimal = '';\n                }\n\n                if (optDec && Number(decimal.slice(1)) === 0) {\n                    decimal = '';\n                }\n            } else {\n                int = numeral._.toFixed(value, 0, roundingFunction);\n            }\n\n            // check abbreviation again after rounding\n            if (abbr && !abbrForce && Number(int) >= 1000 && abbr !== locale.abbreviations.trillion) {\n                int = String(Number(int) / 1000);\n\n                switch (abbr) {\n                    case locale.abbreviations.thousand:\n                        abbr = locale.abbreviations.million;\n                        break;\n                    case locale.abbreviations.million:\n                        abbr = locale.abbreviations.billion;\n                        break;\n                    case locale.abbreviations.billion:\n                        abbr = locale.abbreviations.trillion;\n                        break;\n                }\n            }\n\n\n            // format number\n            if (numeral._.includes(int, '-')) {\n                int = int.slice(1);\n                neg = true;\n            }\n\n            if (int.length < leadingCount) {\n                for (var i = leadingCount - int.length; i > 0; i--) {\n                    int = '0' + int;\n                }\n            }\n\n            if (thousands > -1) {\n                int = int.toString().replace(/(\\d)(?=(\\d{3})+(?!\\d))/g, '$1' + locale.delimiters.thousands);\n            }\n\n            if (format.indexOf('.') === 0) {\n                int = '';\n            }\n\n            output = int + decimal + (abbr ? abbr : '');\n\n            if (negP) {\n                output = (negP && neg ? '(' : '') + output + (negP && neg ? ')' : '');\n            } else {\n                if (signed >= 0) {\n                    output = signed === 0 ? (neg ? '-' : '+') + output : output + (neg ? '-' : '+');\n                } else if (neg) {\n                    output = '-' + output;\n                }\n            }\n\n            return output;\n        },\n        // unformats numbers separators, decimals places, signs, abbreviations\n        stringToNumber: function(string) {\n            var locale = locales[options.currentLocale],\n                stringOriginal = string,\n                abbreviations = {\n                    thousand: 3,\n                    million: 6,\n                    billion: 9,\n                    trillion: 12\n                },\n                abbreviation,\n                value,\n                i,\n                regexp;\n\n            if (options.zeroFormat && string === options.zeroFormat) {\n                value = 0;\n            } else if (options.nullFormat && string === options.nullFormat || !string.replace(/[^0-9]+/g, '').length) {\n                value = null;\n            } else {\n                value = 1;\n\n                if (locale.delimiters.decimal !== '.') {\n                    string = string.replace(/\\./g, '').replace(locale.delimiters.decimal, '.');\n                }\n\n                for (abbreviation in abbreviations) {\n                    regexp = new RegExp('[^a-zA-Z]' + locale.abbreviations[abbreviation] + '(?:\\\\)|(\\\\' + locale.currency.symbol + ')?(?:\\\\))?)?$');\n\n                    if (stringOriginal.match(regexp)) {\n                        value *= Math.pow(10, abbreviations[abbreviation]);\n                        break;\n                    }\n                }\n\n                // check for negative number\n                value *= (string.split('-').length + Math.min(string.split('(').length - 1, string.split(')').length - 1)) % 2 ? 1 : -1;\n\n                // remove non numbers\n                string = string.replace(/[^0-9\\.]+/g, '');\n\n                value *= Number(string);\n            }\n\n            return value;\n        },\n        isNaN: function(value) {\n            return typeof value === 'number' && isNaN(value);\n        },\n        includes: function(string, search) {\n            return string.indexOf(search) !== -1;\n        },\n        insert: function(string, subString, start) {\n            return string.slice(0, start) + subString + string.slice(start);\n        },\n        reduce: function(array, callback /*, initialValue*/) {\n            if (this === null) {\n                throw new TypeError('Array.prototype.reduce called on null or undefined');\n            }\n\n            if (typeof callback !== 'function') {\n                throw new TypeError(callback + ' is not a function');\n            }\n\n            var t = Object(array),\n                len = t.length >>> 0,\n                k = 0,\n                value;\n\n            if (arguments.length === 3) {\n                value = arguments[2];\n            } else {\n                while (k < len && !(k in t)) {\n                    k++;\n                }\n\n                if (k >= len) {\n                    throw new TypeError('Reduce of empty array with no initial value');\n                }\n\n                value = t[k++];\n            }\n            for (; k < len; k++) {\n                if (k in t) {\n                    value = callback(value, t[k], k, t);\n                }\n            }\n            return value;\n        },\n        /**\n         * Computes the multiplier necessary to make x >= 1,\n         * effectively eliminating miscalculations caused by\n         * finite precision.\n         */\n        multiplier: function (x) {\n            var parts = x.toString().split('.');\n\n            return parts.length < 2 ? 1 : Math.pow(10, parts[1].length);\n        },\n        /**\n         * Given a variable number of arguments, returns the maximum\n         * multiplier that must be used to normalize an operation involving\n         * all of them.\n         */\n        correctionFactor: function () {\n            var args = Array.prototype.slice.call(arguments);\n\n            return args.reduce(function(accum, next) {\n                var mn = _.multiplier(next);\n                return accum > mn ? accum : mn;\n            }, 1);\n        },\n        /**\n         * Implementation of toFixed() that treats floats more like decimals\n         *\n         * Fixes binary rounding issues (eg. (0.615).toFixed(2) === '0.61') that present\n         * problems for accounting- and finance-related software.\n         */\n        toFixed: function(value, maxDecimals, roundingFunction, optionals) {\n            var splitValue = value.toString().split('.'),\n                minDecimals = maxDecimals - (optionals || 0),\n                boundedPrecision,\n                optionalsRegExp,\n                power,\n                output;\n\n            // Use the smallest precision value possible to avoid errors from floating point representation\n            if (splitValue.length === 2) {\n              boundedPrecision = Math.min(Math.max(splitValue[1].length, minDecimals), maxDecimals);\n            } else {\n              boundedPrecision = minDecimals;\n            }\n\n            power = Math.pow(10, boundedPrecision);\n\n            // Multiply up by precision, round accurately, then divide and use native toFixed():\n            output = (roundingFunction(value + 'e+' + boundedPrecision) / power).toFixed(boundedPrecision);\n\n            if (optionals > maxDecimals - boundedPrecision) {\n                optionalsRegExp = new RegExp('\\\\.?0{1,' + (optionals - (maxDecimals - boundedPrecision)) + '}$');\n                output = output.replace(optionalsRegExp, '');\n            }\n\n            return output;\n        }\n    };\n\n    // avaliable options\n    numeral.options = options;\n\n    // avaliable formats\n    numeral.formats = formats;\n\n    // avaliable formats\n    numeral.locales = locales;\n\n    // This function sets the current locale.  If\n    // no arguments are passed in, it will simply return the current global\n    // locale key.\n    numeral.locale = function(key) {\n        if (key) {\n            options.currentLocale = key.toLowerCase();\n        }\n\n        return options.currentLocale;\n    };\n\n    // This function provides access to the loaded locale data.  If\n    // no arguments are passed in, it will simply return the current\n    // global locale object.\n    numeral.localeData = function(key) {\n        if (!key) {\n            return locales[options.currentLocale];\n        }\n\n        key = key.toLowerCase();\n\n        if (!locales[key]) {\n            throw new Error('Unknown locale : ' + key);\n        }\n\n        return locales[key];\n    };\n\n    numeral.reset = function() {\n        for (var property in defaults) {\n            options[property] = defaults[property];\n        }\n    };\n\n    numeral.zeroFormat = function(format) {\n        options.zeroFormat = typeof(format) === 'string' ? format : null;\n    };\n\n    numeral.nullFormat = function (format) {\n        options.nullFormat = typeof(format) === 'string' ? format : null;\n    };\n\n    numeral.defaultFormat = function(format) {\n        options.defaultFormat = typeof(format) === 'string' ? format : '0.0';\n    };\n\n    numeral.register = function(type, name, format) {\n        name = name.toLowerCase();\n\n        if (this[type + 's'][name]) {\n            throw new TypeError(name + ' ' + type + ' already registered.');\n        }\n\n        this[type + 's'][name] = format;\n\n        return format;\n    };\n\n\n    numeral.validate = function(val, culture) {\n        var _decimalSep,\n            _thousandSep,\n            _currSymbol,\n            _valArray,\n            _abbrObj,\n            _thousandRegEx,\n            localeData,\n            temp;\n\n        //coerce val to string\n        if (typeof val !== 'string') {\n            val += '';\n\n            if (console.warn) {\n                console.warn('Numeral.js: Value is not string. It has been co-erced to: ', val);\n            }\n        }\n\n        //trim whitespaces from either sides\n        val = val.trim();\n\n        //if val is just digits return true\n        if (!!val.match(/^\\d+$/)) {\n            return true;\n        }\n\n        //if val is empty return false\n        if (val === '') {\n            return false;\n        }\n\n        //get the decimal and thousands separator from numeral.localeData\n        try {\n            //check if the culture is understood by numeral. if not, default it to current locale\n            localeData = numeral.localeData(culture);\n        } catch (e) {\n            localeData = numeral.localeData(numeral.locale());\n        }\n\n        //setup the delimiters and currency symbol based on culture/locale\n        _currSymbol = localeData.currency.symbol;\n        _abbrObj = localeData.abbreviations;\n        _decimalSep = localeData.delimiters.decimal;\n        if (localeData.delimiters.thousands === '.') {\n            _thousandSep = '\\\\.';\n        } else {\n            _thousandSep = localeData.delimiters.thousands;\n        }\n\n        // validating currency symbol\n        temp = val.match(/^[^\\d]+/);\n        if (temp !== null) {\n            val = val.substr(1);\n            if (temp[0] !== _currSymbol) {\n                return false;\n            }\n        }\n\n        //validating abbreviation symbol\n        temp = val.match(/[^\\d]+$/);\n        if (temp !== null) {\n            val = val.slice(0, -1);\n            if (temp[0] !== _abbrObj.thousand && temp[0] !== _abbrObj.million && temp[0] !== _abbrObj.billion && temp[0] !== _abbrObj.trillion) {\n                return false;\n            }\n        }\n\n        _thousandRegEx = new RegExp(_thousandSep + '{2}');\n\n        if (!val.match(/[^\\d.,]/g)) {\n            _valArray = val.split(_decimalSep);\n            if (_valArray.length > 2) {\n                return false;\n            } else {\n                if (_valArray.length < 2) {\n                    return ( !! _valArray[0].match(/^\\d+.*\\d$/) && !_valArray[0].match(_thousandRegEx));\n                } else {\n                    if (_valArray[0].length === 1) {\n                        return ( !! _valArray[0].match(/^\\d+$/) && !_valArray[0].match(_thousandRegEx) && !! _valArray[1].match(/^\\d+$/));\n                    } else {\n                        return ( !! _valArray[0].match(/^\\d+.*\\d$/) && !_valArray[0].match(_thousandRegEx) && !! _valArray[1].match(/^\\d+$/));\n                    }\n                }\n            }\n        }\n\n        return false;\n    };\n\n\n    /************************************\n        Numeral Prototype\n    ************************************/\n\n    numeral.fn = Numeral.prototype = {\n        clone: function() {\n            return numeral(this);\n        },\n        format: function(inputString, roundingFunction) {\n            var value = this._value,\n                format = inputString || options.defaultFormat,\n                kind,\n                output,\n                formatFunction;\n\n            // make sure we have a roundingFunction\n            roundingFunction = roundingFunction || Math.round;\n\n            // format based on value\n            if (value === 0 && options.zeroFormat !== null) {\n                output = options.zeroFormat;\n            } else if (value === null && options.nullFormat !== null) {\n                output = options.nullFormat;\n            } else {\n                for (kind in formats) {\n                    if (format.match(formats[kind].regexps.format)) {\n                        formatFunction = formats[kind].format;\n\n                        break;\n                    }\n                }\n\n                formatFunction = formatFunction || numeral._.numberToFormat;\n\n                output = formatFunction(value, format, roundingFunction);\n            }\n\n            return output;\n        },\n        value: function() {\n            return this._value;\n        },\n        input: function() {\n            return this._input;\n        },\n        set: function(value) {\n            this._value = Number(value);\n\n            return this;\n        },\n        add: function(value) {\n            var corrFactor = _.correctionFactor.call(null, this._value, value);\n\n            function cback(accum, curr, currI, O) {\n                return accum + Math.round(corrFactor * curr);\n            }\n\n            this._value = _.reduce([this._value, value], cback, 0) / corrFactor;\n\n            return this;\n        },\n        subtract: function(value) {\n            var corrFactor = _.correctionFactor.call(null, this._value, value);\n\n            function cback(accum, curr, currI, O) {\n                return accum - Math.round(corrFactor * curr);\n            }\n\n            this._value = _.reduce([value], cback, Math.round(this._value * corrFactor)) / corrFactor;\n\n            return this;\n        },\n        multiply: function(value) {\n            function cback(accum, curr, currI, O) {\n                var corrFactor = _.correctionFactor(accum, curr);\n                return Math.round(accum * corrFactor) * Math.round(curr * corrFactor) / Math.round(corrFactor * corrFactor);\n            }\n\n            this._value = _.reduce([this._value, value], cback, 1);\n\n            return this;\n        },\n        divide: function(value) {\n            function cback(accum, curr, currI, O) {\n                var corrFactor = _.correctionFactor(accum, curr);\n                return Math.round(accum * corrFactor) / Math.round(curr * corrFactor);\n            }\n\n            this._value = _.reduce([this._value, value], cback);\n\n            return this;\n        },\n        difference: function(value) {\n            return Math.abs(numeral(this._value).subtract(value).value());\n        }\n    };\n\n    /************************************\n        Default Locale && Format\n    ************************************/\n\n    numeral.register('locale', 'en', {\n        delimiters: {\n            thousands: ',',\n            decimal: '.'\n        },\n        abbreviations: {\n            thousand: 'k',\n            million: 'm',\n            billion: 'b',\n            trillion: 't'\n        },\n        ordinal: function(number) {\n            var b = number % 10;\n            return (~~(number % 100 / 10) === 1) ? 'th' :\n                (b === 1) ? 'st' :\n                (b === 2) ? 'nd' :\n                (b === 3) ? 'rd' : 'th';\n        },\n        currency: {\n            symbol: '$'\n        }\n    });\n\n    \n\n(function() {\n        numeral.register('format', 'bps', {\n            regexps: {\n                format: /(BPS)/,\n                unformat: /(BPS)/\n            },\n            format: function(value, format, roundingFunction) {\n                var space = numeral._.includes(format, ' BPS') ? ' ' : '',\n                    output;\n\n                value = value * 10000;\n\n                // check for space before BPS\n                format = format.replace(/\\s?BPS/, '');\n\n                output = numeral._.numberToFormat(value, format, roundingFunction);\n\n                if (numeral._.includes(output, ')')) {\n                    output = output.split('');\n\n                    output.splice(-1, 0, space + 'BPS');\n\n                    output = output.join('');\n                } else {\n                    output = output + space + 'BPS';\n                }\n\n                return output;\n            },\n            unformat: function(string) {\n                return +(numeral._.stringToNumber(string) * 0.0001).toFixed(15);\n            }\n        });\n})();\n\n\n(function() {\n        var decimal = {\n            base: 1000,\n            suffixes: ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']\n        },\n        binary = {\n            base: 1024,\n            suffixes: ['B', 'KiB', 'MiB', 'GiB', 'TiB', 'PiB', 'EiB', 'ZiB', 'YiB']\n        };\n\n    var allSuffixes =  decimal.suffixes.concat(binary.suffixes.filter(function (item) {\n            return decimal.suffixes.indexOf(item) < 0;\n        }));\n        var unformatRegex = allSuffixes.join('|');\n        // Allow support for BPS (http://www.investopedia.com/terms/b/basispoint.asp)\n        unformatRegex = '(' + unformatRegex.replace('B', 'B(?!PS)') + ')';\n\n    numeral.register('format', 'bytes', {\n        regexps: {\n            format: /([0\\s]i?b)/,\n            unformat: new RegExp(unformatRegex)\n        },\n        format: function(value, format, roundingFunction) {\n            var output,\n                bytes = numeral._.includes(format, 'ib') ? binary : decimal,\n                suffix = numeral._.includes(format, ' b') || numeral._.includes(format, ' ib') ? ' ' : '',\n                power,\n                min,\n                max;\n\n            // check for space before\n            format = format.replace(/\\s?i?b/, '');\n\n            for (power = 0; power <= bytes.suffixes.length; power++) {\n                min = Math.pow(bytes.base, power);\n                max = Math.pow(bytes.base, power + 1);\n\n                if (value === null || value === 0 || value >= min && value < max) {\n                    suffix += bytes.suffixes[power];\n\n                    if (min > 0) {\n                        value = value / min;\n                    }\n\n                    break;\n                }\n            }\n\n            output = numeral._.numberToFormat(value, format, roundingFunction);\n\n            return output + suffix;\n        },\n        unformat: function(string) {\n            var value = numeral._.stringToNumber(string),\n                power,\n                bytesMultiplier;\n\n            if (value) {\n                for (power = decimal.suffixes.length - 1; power >= 0; power--) {\n                    if (numeral._.includes(string, decimal.suffixes[power])) {\n                        bytesMultiplier = Math.pow(decimal.base, power);\n\n                        break;\n                    }\n\n                    if (numeral._.includes(string, binary.suffixes[power])) {\n                        bytesMultiplier = Math.pow(binary.base, power);\n\n                        break;\n                    }\n                }\n\n                value *= (bytesMultiplier || 1);\n            }\n\n            return value;\n        }\n    });\n})();\n\n\n(function() {\n        numeral.register('format', 'currency', {\n        regexps: {\n            format: /(\\$)/\n        },\n        format: function(value, format, roundingFunction) {\n            var locale = numeral.locales[numeral.options.currentLocale],\n                symbols = {\n                    before: format.match(/^([\\+|\\-|\\(|\\s|\\$]*)/)[0],\n                    after: format.match(/([\\+|\\-|\\)|\\s|\\$]*)$/)[0]\n                },\n                output,\n                symbol,\n                i;\n\n            // strip format of spaces and $\n            format = format.replace(/\\s?\\$\\s?/, '');\n\n            // format the number\n            output = numeral._.numberToFormat(value, format, roundingFunction);\n\n            // update the before and after based on value\n            if (value >= 0) {\n                symbols.before = symbols.before.replace(/[\\-\\(]/, '');\n                symbols.after = symbols.after.replace(/[\\-\\)]/, '');\n            } else if (value < 0 && (!numeral._.includes(symbols.before, '-') && !numeral._.includes(symbols.before, '('))) {\n                symbols.before = '-' + symbols.before;\n            }\n\n            // loop through each before symbol\n            for (i = 0; i < symbols.before.length; i++) {\n                symbol = symbols.before[i];\n\n                switch (symbol) {\n                    case '$':\n                        output = numeral._.insert(output, locale.currency.symbol, i);\n                        break;\n                    case ' ':\n                        output = numeral._.insert(output, ' ', i + locale.currency.symbol.length - 1);\n                        break;\n                }\n            }\n\n            // loop through each after symbol\n            for (i = symbols.after.length - 1; i >= 0; i--) {\n                symbol = symbols.after[i];\n\n                switch (symbol) {\n                    case '$':\n                        output = i === symbols.after.length - 1 ? output + locale.currency.symbol : numeral._.insert(output, locale.currency.symbol, -(symbols.after.length - (1 + i)));\n                        break;\n                    case ' ':\n                        output = i === symbols.after.length - 1 ? output + ' ' : numeral._.insert(output, ' ', -(symbols.after.length - (1 + i) + locale.currency.symbol.length - 1));\n                        break;\n                }\n            }\n\n\n            return output;\n        }\n    });\n})();\n\n\n(function() {\n        numeral.register('format', 'exponential', {\n        regexps: {\n            format: /(e\\+|e-)/,\n            unformat: /(e\\+|e-)/\n        },\n        format: function(value, format, roundingFunction) {\n            var output,\n                exponential = typeof value === 'number' && !numeral._.isNaN(value) ? value.toExponential() : '0e+0',\n                parts = exponential.split('e');\n\n            format = format.replace(/e[\\+|\\-]{1}0/, '');\n\n            output = numeral._.numberToFormat(Number(parts[0]), format, roundingFunction);\n\n            return output + 'e' + parts[1];\n        },\n        unformat: function(string) {\n            var parts = numeral._.includes(string, 'e+') ? string.split('e+') : string.split('e-'),\n                value = Number(parts[0]),\n                power = Number(parts[1]);\n\n            power = numeral._.includes(string, 'e-') ? power *= -1 : power;\n\n            function cback(accum, curr, currI, O) {\n                var corrFactor = numeral._.correctionFactor(accum, curr),\n                    num = (accum * corrFactor) * (curr * corrFactor) / (corrFactor * corrFactor);\n                return num;\n            }\n\n            return numeral._.reduce([value, Math.pow(10, power)], cback, 1);\n        }\n    });\n})();\n\n\n(function() {\n        numeral.register('format', 'ordinal', {\n        regexps: {\n            format: /(o)/\n        },\n        format: function(value, format, roundingFunction) {\n            var locale = numeral.locales[numeral.options.currentLocale],\n                output,\n                ordinal = numeral._.includes(format, ' o') ? ' ' : '';\n\n            // check for space before\n            format = format.replace(/\\s?o/, '');\n\n            ordinal += locale.ordinal(value);\n\n            output = numeral._.numberToFormat(value, format, roundingFunction);\n\n            return output + ordinal;\n        }\n    });\n})();\n\n\n(function() {\n        numeral.register('format', 'percentage', {\n        regexps: {\n            format: /(%)/,\n            unformat: /(%)/\n        },\n        format: function(value, format, roundingFunction) {\n            var space = numeral._.includes(format, ' %') ? ' ' : '',\n                output;\n\n            if (numeral.options.scalePercentBy100) {\n                value = value * 100;\n            }\n\n            // check for space before %\n            format = format.replace(/\\s?\\%/, '');\n\n            output = numeral._.numberToFormat(value, format, roundingFunction);\n\n            if (numeral._.includes(output, ')')) {\n                output = output.split('');\n\n                output.splice(-1, 0, space + '%');\n\n                output = output.join('');\n            } else {\n                output = output + space + '%';\n            }\n\n            return output;\n        },\n        unformat: function(string) {\n            var number = numeral._.stringToNumber(string);\n            if (numeral.options.scalePercentBy100) {\n                return number * 0.01;\n            }\n            return number;\n        }\n    });\n})();\n\n\n(function() {\n        numeral.register('format', 'time', {\n        regexps: {\n            format: /(:)/,\n            unformat: /(:)/\n        },\n        format: function(value, format, roundingFunction) {\n            var hours = Math.floor(value / 60 / 60),\n                minutes = Math.floor((value - (hours * 60 * 60)) / 60),\n                seconds = Math.round(value - (hours * 60 * 60) - (minutes * 60));\n\n            return hours + ':' + (minutes < 10 ? '0' + minutes : minutes) + ':' + (seconds < 10 ? '0' + seconds : seconds);\n        },\n        unformat: function(string) {\n            var timeArray = string.split(':'),\n                seconds = 0;\n\n            // turn hours and minutes into seconds and add them all up\n            if (timeArray.length === 3) {\n                // hours\n                seconds = seconds + (Number(timeArray[0]) * 60 * 60);\n                // minutes\n                seconds = seconds + (Number(timeArray[1]) * 60);\n                // seconds\n                seconds = seconds + Number(timeArray[2]);\n            } else if (timeArray.length === 2) {\n                // minutes\n                seconds = seconds + (Number(timeArray[0]) * 60);\n                // seconds\n                seconds = seconds + Number(timeArray[1]);\n            }\n            return Number(seconds);\n        }\n    });\n})();\n\nreturn numeral;\n}));\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEC,WAAUA,MAAM,EAAEC,OAAO,EAAE;EACxB,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IAC5CD,MAAM,CAACD,OAAO,CAAC;EACnB,CAAC,MAAM,IAAI,OAAOG,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACC,OAAO,EAAE;IACrDD,MAAM,CAACC,OAAO,GAAGJ,OAAO,EAAE;EAC9B,CAAC,MAAM;IACHD,MAAM,CAACM,OAAO,GAAGL,OAAO,EAAE;EAC9B;AACJ,CAAC,EAAC,IAAI,EAAE,YAAY;EAChB;AACJ;AACA;;EAEI,IAAIK,OAAO;IACPC,CAAC;IACDC,OAAO,GAAG,OAAO;IACjBC,OAAO,GAAG,CAAC,CAAC;IACZC,OAAO,GAAG,CAAC,CAAC;IACZC,QAAQ,GAAG;MACPC,aAAa,EAAE,IAAI;MACnBC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,IAAI;MAChBC,aAAa,EAAE,KAAK;MACpBC,iBAAiB,EAAE;IACvB,CAAC;IACDC,OAAO,GAAG;MACNL,aAAa,EAAED,QAAQ,CAACC,aAAa;MACrCC,UAAU,EAAEF,QAAQ,CAACE,UAAU;MAC/BC,UAAU,EAAEH,QAAQ,CAACG,UAAU;MAC/BC,aAAa,EAAEJ,QAAQ,CAACI,aAAa;MACrCC,iBAAiB,EAAEL,QAAQ,CAACK;IAChC,CAAC;;EAGL;AACJ;AACA;;EAEI;EACA,SAASE,OAAO,CAACC,KAAK,EAAEC,MAAM,EAAE;IAC5B,IAAI,CAACC,MAAM,GAAGF,KAAK;IAEnB,IAAI,CAACG,MAAM,GAAGF,MAAM;EACxB;EAEAd,OAAO,GAAG,UAASa,KAAK,EAAE;IACtB,IAAII,KAAK,EACLC,IAAI,EACJC,gBAAgB,EAChBC,MAAM;IAEV,IAAIpB,OAAO,CAACqB,SAAS,CAACR,KAAK,CAAC,EAAE;MAC1BI,KAAK,GAAGJ,KAAK,CAACI,KAAK,EAAE;IACzB,CAAC,MAAM,IAAIJ,KAAK,KAAK,CAAC,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;MACpDI,KAAK,GAAG,CAAC;IACb,CAAC,MAAM,IAAIJ,KAAK,KAAK,IAAI,IAAIZ,CAAC,CAACqB,KAAK,CAACT,KAAK,CAAC,EAAE;MACzCI,KAAK,GAAG,IAAI;IAChB,CAAC,MAAM,IAAI,OAAOJ,KAAK,KAAK,QAAQ,EAAE;MAClC,IAAIF,OAAO,CAACJ,UAAU,IAAIM,KAAK,KAAKF,OAAO,CAACJ,UAAU,EAAE;QACpDU,KAAK,GAAG,CAAC;MACb,CAAC,MAAM,IAAIN,OAAO,CAACH,UAAU,IAAIK,KAAK,KAAKF,OAAO,CAACH,UAAU,IAAI,CAACK,KAAK,CAACU,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAACC,MAAM,EAAE;QACpGP,KAAK,GAAG,IAAI;MAChB,CAAC,MAAM;QACH,KAAKC,IAAI,IAAIf,OAAO,EAAE;UAClBiB,MAAM,GAAG,OAAOjB,OAAO,CAACe,IAAI,CAAC,CAACO,OAAO,CAACC,QAAQ,KAAK,UAAU,GAAGvB,OAAO,CAACe,IAAI,CAAC,CAACO,OAAO,CAACC,QAAQ,EAAE,GAAGvB,OAAO,CAACe,IAAI,CAAC,CAACO,OAAO,CAACC,QAAQ;UAEjI,IAAIN,MAAM,IAAIP,KAAK,CAACc,KAAK,CAACP,MAAM,CAAC,EAAE;YAC/BD,gBAAgB,GAAGhB,OAAO,CAACe,IAAI,CAAC,CAACQ,QAAQ;YAEzC;UACJ;QACJ;QAEAP,gBAAgB,GAAGA,gBAAgB,IAAInB,OAAO,CAACC,CAAC,CAAC2B,cAAc;QAE/DX,KAAK,GAAGE,gBAAgB,CAACN,KAAK,CAAC;MACnC;IACJ,CAAC,MAAM;MACHI,KAAK,GAAGY,MAAM,CAAChB,KAAK,CAAC,IAAG,IAAI;IAChC;IAEA,OAAO,IAAID,OAAO,CAACC,KAAK,EAAEI,KAAK,CAAC;EACpC,CAAC;;EAED;EACAjB,OAAO,CAAC8B,OAAO,GAAG5B,OAAO;;EAEzB;EACAF,OAAO,CAACqB,SAAS,GAAG,UAASU,GAAG,EAAE;IAC9B,OAAOA,GAAG,YAAYnB,OAAO;EACjC,CAAC;;EAED;EACAZ,OAAO,CAACC,CAAC,GAAGA,CAAC,GAAG;IACZ;IACA+B,cAAc,EAAE,UAASf,KAAK,EAAEgB,MAAM,EAAEC,gBAAgB,EAAE;MACtD,IAAIC,MAAM,GAAG/B,OAAO,CAACJ,OAAO,CAACW,OAAO,CAACL,aAAa,CAAC;QAC/C8B,IAAI,GAAG,KAAK;QACZC,MAAM,GAAG,KAAK;QACdC,YAAY,GAAG,CAAC;QAChBC,IAAI,GAAG,EAAE;QACTC,QAAQ,GAAG,aAAa;QACxBC,OAAO,GAAG,UAAU;QACpBC,OAAO,GAAG,OAAO;QACjBC,QAAQ,GAAG,IAAI;QACfC,OAAO,GAAG,EAAE;QACZC,GAAG,GAAG,KAAK;QACXC,SAAS;QAAE;QACXC,GAAG;QACHC,GAAG;QACHC,GAAG;QACHC,KAAK;QACLC,GAAG;QACHC,SAAS;QACTC,MAAM;QACNC,SAAS;QACTC,MAAM;;MAEV;MACAtC,KAAK,GAAGA,KAAK,IAAI,CAAC;MAElB8B,GAAG,GAAGS,IAAI,CAACT,GAAG,CAAC9B,KAAK,CAAC;;MAErB;MACA;MACA,IAAIjB,OAAO,CAACC,CAAC,CAACwD,QAAQ,CAACxB,MAAM,EAAE,GAAG,CAAC,EAAE;QACjCG,IAAI,GAAG,IAAI;QACXH,MAAM,GAAGA,MAAM,CAACV,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;MAC3C,CAAC,MAAM,IAAIvB,OAAO,CAACC,CAAC,CAACwD,QAAQ,CAACxB,MAAM,EAAE,GAAG,CAAC,IAAIjC,OAAO,CAACC,CAAC,CAACwD,QAAQ,CAACxB,MAAM,EAAE,GAAG,CAAC,EAAE;QAC3EoB,MAAM,GAAGrD,OAAO,CAACC,CAAC,CAACwD,QAAQ,CAACxB,MAAM,EAAE,GAAG,CAAC,GAAGA,MAAM,CAACyB,OAAO,CAAC,GAAG,CAAC,GAAGzC,KAAK,GAAG,CAAC,GAAGgB,MAAM,CAACyB,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACrGzB,MAAM,GAAGA,MAAM,CAACV,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;MAC3C;;MAEA;MACA,IAAIvB,OAAO,CAACC,CAAC,CAACwD,QAAQ,CAACxB,MAAM,EAAE,GAAG,CAAC,EAAE;QACjCa,SAAS,GAAGb,MAAM,CAACN,KAAK,CAAC,aAAa,CAAC;QAEvCmB,SAAS,GAAGA,SAAS,GAAGA,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;;QAE5C;QACA,IAAI9C,OAAO,CAACC,CAAC,CAACwD,QAAQ,CAACxB,MAAM,EAAE,IAAI,CAAC,EAAE;UAClCM,IAAI,GAAG,GAAG;QACd;QAEAN,MAAM,GAAGA,MAAM,CAACV,OAAO,CAAC,IAAIoC,MAAM,CAACpB,IAAI,GAAG,UAAU,CAAC,EAAE,EAAE,CAAC;QAE1D,IAAIQ,GAAG,IAAIP,QAAQ,IAAI,CAACM,SAAS,IAAIA,SAAS,KAAK,GAAG,EAAE;UACpD;UACAP,IAAI,IAAIJ,MAAM,CAACyB,aAAa,CAACpB,QAAQ;UACrCvB,KAAK,GAAGA,KAAK,GAAGuB,QAAQ;QAC5B,CAAC,MAAM,IAAIO,GAAG,GAAGP,QAAQ,IAAIO,GAAG,IAAIN,OAAO,IAAI,CAACK,SAAS,IAAIA,SAAS,KAAK,GAAG,EAAE;UAC5E;UACAP,IAAI,IAAIJ,MAAM,CAACyB,aAAa,CAACnB,OAAO;UACpCxB,KAAK,GAAGA,KAAK,GAAGwB,OAAO;QAC3B,CAAC,MAAM,IAAIM,GAAG,GAAGN,OAAO,IAAIM,GAAG,IAAIL,OAAO,IAAI,CAACI,SAAS,IAAIA,SAAS,KAAK,GAAG,EAAE;UAC3E;UACAP,IAAI,IAAIJ,MAAM,CAACyB,aAAa,CAAClB,OAAO;UACpCzB,KAAK,GAAGA,KAAK,GAAGyB,OAAO;QAC3B,CAAC,MAAM,IAAIK,GAAG,GAAGL,OAAO,IAAIK,GAAG,IAAIJ,QAAQ,IAAI,CAACG,SAAS,IAAIA,SAAS,KAAK,GAAG,EAAE;UAC5E;UACAP,IAAI,IAAIJ,MAAM,CAACyB,aAAa,CAACjB,QAAQ;UACrC1B,KAAK,GAAGA,KAAK,GAAG0B,QAAQ;QAC5B;MACJ;;MAEA;MACA,IAAI3C,OAAO,CAACC,CAAC,CAACwD,QAAQ,CAACxB,MAAM,EAAE,KAAK,CAAC,EAAE;QACnCI,MAAM,GAAG,IAAI;QACbJ,MAAM,GAAGA,MAAM,CAACV,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;MACvC;;MAEA;MACA4B,GAAG,GAAGlC,KAAK,CAAC4C,QAAQ,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpCV,SAAS,GAAGnB,MAAM,CAAC6B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAChCR,SAAS,GAAGrB,MAAM,CAACyB,OAAO,CAAC,GAAG,CAAC;MAC/BpB,YAAY,GAAG,CAACL,MAAM,CAAC6B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACnC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,EAAEH,MAAM;MAE5E,IAAI4B,SAAS,EAAE;QACX,IAAIpD,OAAO,CAACC,CAAC,CAACwD,QAAQ,CAACL,SAAS,EAAE,GAAG,CAAC,EAAE;UACpCA,SAAS,GAAGA,SAAS,CAAC7B,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;UACtC6B,SAAS,GAAGA,SAAS,CAACU,KAAK,CAAC,GAAG,CAAC;UAChClB,OAAO,GAAG5C,OAAO,CAACC,CAAC,CAAC8D,OAAO,CAAC9C,KAAK,EAAGmC,SAAS,CAAC,CAAC,CAAC,CAAC5B,MAAM,GAAG4B,SAAS,CAAC,CAAC,CAAC,CAAC5B,MAAM,EAAGU,gBAAgB,EAAEkB,SAAS,CAAC,CAAC,CAAC,CAAC5B,MAAM,CAAC;QAC1H,CAAC,MAAM;UACHoB,OAAO,GAAG5C,OAAO,CAACC,CAAC,CAAC8D,OAAO,CAAC9C,KAAK,EAAEmC,SAAS,CAAC5B,MAAM,EAAEU,gBAAgB,CAAC;QAC1E;QAEAiB,GAAG,GAAGP,OAAO,CAACkB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAE3B,IAAI9D,OAAO,CAACC,CAAC,CAACwD,QAAQ,CAACb,OAAO,EAAE,GAAG,CAAC,EAAE;UAClCA,OAAO,GAAGT,MAAM,CAAC6B,UAAU,CAACpB,OAAO,GAAGA,OAAO,CAACkB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/D,CAAC,MAAM;UACHlB,OAAO,GAAG,EAAE;QAChB;QAEA,IAAIP,MAAM,IAAIR,MAAM,CAACe,OAAO,CAACqB,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;UAC1CrB,OAAO,GAAG,EAAE;QAChB;MACJ,CAAC,MAAM;QACHO,GAAG,GAAGnD,OAAO,CAACC,CAAC,CAAC8D,OAAO,CAAC9C,KAAK,EAAE,CAAC,EAAEiB,gBAAgB,CAAC;MACvD;;MAEA;MACA,IAAIK,IAAI,IAAI,CAACO,SAAS,IAAIjB,MAAM,CAACsB,GAAG,CAAC,IAAI,IAAI,IAAIZ,IAAI,KAAKJ,MAAM,CAACyB,aAAa,CAACpB,QAAQ,EAAE;QACrFW,GAAG,GAAGe,MAAM,CAACrC,MAAM,CAACsB,GAAG,CAAC,GAAG,IAAI,CAAC;QAEhC,QAAQZ,IAAI;UACR,KAAKJ,MAAM,CAACyB,aAAa,CAACjB,QAAQ;YAC9BJ,IAAI,GAAGJ,MAAM,CAACyB,aAAa,CAAClB,OAAO;YACnC;UACJ,KAAKP,MAAM,CAACyB,aAAa,CAAClB,OAAO;YAC7BH,IAAI,GAAGJ,MAAM,CAACyB,aAAa,CAACnB,OAAO;YACnC;UACJ,KAAKN,MAAM,CAACyB,aAAa,CAACnB,OAAO;YAC7BF,IAAI,GAAGJ,MAAM,CAACyB,aAAa,CAACpB,QAAQ;YACpC;QAAM;MAElB;;MAGA;MACA,IAAIxC,OAAO,CAACC,CAAC,CAACwD,QAAQ,CAACN,GAAG,EAAE,GAAG,CAAC,EAAE;QAC9BA,GAAG,GAAGA,GAAG,CAACc,KAAK,CAAC,CAAC,CAAC;QAClBpB,GAAG,GAAG,IAAI;MACd;MAEA,IAAIM,GAAG,CAAC3B,MAAM,GAAGc,YAAY,EAAE;QAC3B,KAAK,IAAI6B,CAAC,GAAG7B,YAAY,GAAGa,GAAG,CAAC3B,MAAM,EAAE2C,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;UAChDhB,GAAG,GAAG,GAAG,GAAGA,GAAG;QACnB;MACJ;MAEA,IAAIG,SAAS,GAAG,CAAC,CAAC,EAAE;QAChBH,GAAG,GAAGA,GAAG,CAACU,QAAQ,EAAE,CAACtC,OAAO,CAAC,yBAAyB,EAAE,IAAI,GAAGY,MAAM,CAAC6B,UAAU,CAACV,SAAS,CAAC;MAC/F;MAEA,IAAIrB,MAAM,CAACyB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;QAC3BP,GAAG,GAAG,EAAE;MACZ;MAEAI,MAAM,GAAGJ,GAAG,GAAGP,OAAO,IAAIL,IAAI,GAAGA,IAAI,GAAG,EAAE,CAAC;MAE3C,IAAIH,IAAI,EAAE;QACNmB,MAAM,GAAG,CAACnB,IAAI,IAAIS,GAAG,GAAG,GAAG,GAAG,EAAE,IAAIU,MAAM,IAAInB,IAAI,IAAIS,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC;MACzE,CAAC,MAAM;QACH,IAAIQ,MAAM,IAAI,CAAC,EAAE;UACbE,MAAM,GAAGF,MAAM,KAAK,CAAC,GAAG,CAACR,GAAG,GAAG,GAAG,GAAG,GAAG,IAAIU,MAAM,GAAGA,MAAM,IAAIV,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;QACnF,CAAC,MAAM,IAAIA,GAAG,EAAE;UACZU,MAAM,GAAG,GAAG,GAAGA,MAAM;QACzB;MACJ;MAEA,OAAOA,MAAM;IACjB,CAAC;IACD;IACA3B,cAAc,EAAE,UAASwC,MAAM,EAAE;MAC7B,IAAIjC,MAAM,GAAG/B,OAAO,CAACO,OAAO,CAACL,aAAa,CAAC;QACvC+D,cAAc,GAAGD,MAAM;QACvBR,aAAa,GAAG;UACZjB,QAAQ,EAAE,CAAC;UACXD,OAAO,EAAE,CAAC;UACVD,OAAO,EAAE,CAAC;UACVD,QAAQ,EAAE;QACd,CAAC;QACD8B,YAAY;QACZrD,KAAK;QACLkD,CAAC;QACD/C,MAAM;MAEV,IAAIT,OAAO,CAACJ,UAAU,IAAI6D,MAAM,KAAKzD,OAAO,CAACJ,UAAU,EAAE;QACrDU,KAAK,GAAG,CAAC;MACb,CAAC,MAAM,IAAIN,OAAO,CAACH,UAAU,IAAI4D,MAAM,KAAKzD,OAAO,CAACH,UAAU,IAAI,CAAC4D,MAAM,CAAC7C,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAACC,MAAM,EAAE;QACtGP,KAAK,GAAG,IAAI;MAChB,CAAC,MAAM;QACHA,KAAK,GAAG,CAAC;QAET,IAAIkB,MAAM,CAAC6B,UAAU,CAACpB,OAAO,KAAK,GAAG,EAAE;UACnCwB,MAAM,GAAGA,MAAM,CAAC7C,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACA,OAAO,CAACY,MAAM,CAAC6B,UAAU,CAACpB,OAAO,EAAE,GAAG,CAAC;QAC9E;QAEA,KAAK0B,YAAY,IAAIV,aAAa,EAAE;UAChCxC,MAAM,GAAG,IAAIuC,MAAM,CAAC,WAAW,GAAGxB,MAAM,CAACyB,aAAa,CAACU,YAAY,CAAC,GAAG,YAAY,GAAGnC,MAAM,CAACoC,QAAQ,CAACC,MAAM,GAAG,eAAe,CAAC;UAE/H,IAAIH,cAAc,CAAC1C,KAAK,CAACP,MAAM,CAAC,EAAE;YAC9BH,KAAK,IAAIuC,IAAI,CAACiB,GAAG,CAAC,EAAE,EAAEb,aAAa,CAACU,YAAY,CAAC,CAAC;YAClD;UACJ;QACJ;;QAEA;QACArD,KAAK,IAAI,CAACmD,MAAM,CAACN,KAAK,CAAC,GAAG,CAAC,CAACtC,MAAM,GAAGgC,IAAI,CAACR,GAAG,CAACoB,MAAM,CAACN,KAAK,CAAC,GAAG,CAAC,CAACtC,MAAM,GAAG,CAAC,EAAE4C,MAAM,CAACN,KAAK,CAAC,GAAG,CAAC,CAACtC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;;QAEvH;QACA4C,MAAM,GAAGA,MAAM,CAAC7C,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;QAEzCN,KAAK,IAAIY,MAAM,CAACuC,MAAM,CAAC;MAC3B;MAEA,OAAOnD,KAAK;IAChB,CAAC;IACDK,KAAK,EAAE,UAASL,KAAK,EAAE;MACnB,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIK,KAAK,CAACL,KAAK,CAAC;IACpD,CAAC;IACDwC,QAAQ,EAAE,UAASW,MAAM,EAAEM,MAAM,EAAE;MAC/B,OAAON,MAAM,CAACV,OAAO,CAACgB,MAAM,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;IACDC,MAAM,EAAE,UAASP,MAAM,EAAEQ,SAAS,EAAEC,KAAK,EAAE;MACvC,OAAOT,MAAM,CAACH,KAAK,CAAC,CAAC,EAAEY,KAAK,CAAC,GAAGD,SAAS,GAAGR,MAAM,CAACH,KAAK,CAACY,KAAK,CAAC;IACnE,CAAC;IACDC,MAAM,EAAE,UAASC,KAAK,EAAEC,QAAQ,CAAC,oBAAoB;MACjD,IAAI,IAAI,KAAK,IAAI,EAAE;QACf,MAAM,IAAIC,SAAS,CAAC,oDAAoD,CAAC;MAC7E;MAEA,IAAI,OAAOD,QAAQ,KAAK,UAAU,EAAE;QAChC,MAAM,IAAIC,SAAS,CAACD,QAAQ,GAAG,oBAAoB,CAAC;MACxD;MAEA,IAAIE,CAAC,GAAGC,MAAM,CAACJ,KAAK,CAAC;QACjBK,GAAG,GAAGF,CAAC,CAAC1D,MAAM,KAAK,CAAC;QACpB6D,CAAC,GAAG,CAAC;QACLpE,KAAK;MAET,IAAIqE,SAAS,CAAC9D,MAAM,KAAK,CAAC,EAAE;QACxBP,KAAK,GAAGqE,SAAS,CAAC,CAAC,CAAC;MACxB,CAAC,MAAM;QACH,OAAOD,CAAC,GAAGD,GAAG,IAAI,EAAEC,CAAC,IAAIH,CAAC,CAAC,EAAE;UACzBG,CAAC,EAAE;QACP;QAEA,IAAIA,CAAC,IAAID,GAAG,EAAE;UACV,MAAM,IAAIH,SAAS,CAAC,6CAA6C,CAAC;QACtE;QAEAhE,KAAK,GAAGiE,CAAC,CAACG,CAAC,EAAE,CAAC;MAClB;MACA,OAAOA,CAAC,GAAGD,GAAG,EAAEC,CAAC,EAAE,EAAE;QACjB,IAAIA,CAAC,IAAIH,CAAC,EAAE;UACRjE,KAAK,GAAG+D,QAAQ,CAAC/D,KAAK,EAAEiE,CAAC,CAACG,CAAC,CAAC,EAAEA,CAAC,EAAEH,CAAC,CAAC;QACvC;MACJ;MACA,OAAOjE,KAAK;IAChB,CAAC;IACD;AACR;AACA;AACA;AACA;IACQsE,UAAU,EAAE,UAAUC,CAAC,EAAE;MACrB,IAAIC,KAAK,GAAGD,CAAC,CAAC3B,QAAQ,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;MAEnC,OAAO2B,KAAK,CAACjE,MAAM,GAAG,CAAC,GAAG,CAAC,GAAGgC,IAAI,CAACiB,GAAG,CAAC,EAAE,EAAEgB,KAAK,CAAC,CAAC,CAAC,CAACjE,MAAM,CAAC;IAC/D,CAAC;IACD;AACR;AACA;AACA;AACA;IACQkE,gBAAgB,EAAE,YAAY;MAC1B,IAAIC,IAAI,GAAGC,KAAK,CAACC,SAAS,CAAC5B,KAAK,CAAC6B,IAAI,CAACR,SAAS,CAAC;MAEhD,OAAOK,IAAI,CAACb,MAAM,CAAC,UAASiB,KAAK,EAAEC,IAAI,EAAE;QACrC,IAAIC,EAAE,GAAGhG,CAAC,CAACsF,UAAU,CAACS,IAAI,CAAC;QAC3B,OAAOD,KAAK,GAAGE,EAAE,GAAGF,KAAK,GAAGE,EAAE;MAClC,CAAC,EAAE,CAAC,CAAC;IACT,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;IACQlC,OAAO,EAAE,UAAS9C,KAAK,EAAEiF,WAAW,EAAEhE,gBAAgB,EAAEiE,SAAS,EAAE;MAC/D,IAAIC,UAAU,GAAGnF,KAAK,CAAC4C,QAAQ,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;QACxCuC,WAAW,GAAGH,WAAW,IAAIC,SAAS,IAAI,CAAC,CAAC;QAC5CG,gBAAgB;QAChBC,eAAe;QACfrD,KAAK;QACLK,MAAM;;MAEV;MACA,IAAI6C,UAAU,CAAC5E,MAAM,KAAK,CAAC,EAAE;QAC3B8E,gBAAgB,GAAG9C,IAAI,CAACR,GAAG,CAACQ,IAAI,CAACP,GAAG,CAACmD,UAAU,CAAC,CAAC,CAAC,CAAC5E,MAAM,EAAE6E,WAAW,CAAC,EAAEH,WAAW,CAAC;MACvF,CAAC,MAAM;QACLI,gBAAgB,GAAGD,WAAW;MAChC;MAEAnD,KAAK,GAAGM,IAAI,CAACiB,GAAG,CAAC,EAAE,EAAE6B,gBAAgB,CAAC;;MAEtC;MACA/C,MAAM,GAAG,CAACrB,gBAAgB,CAACjB,KAAK,GAAG,IAAI,GAAGqF,gBAAgB,CAAC,GAAGpD,KAAK,EAAEa,OAAO,CAACuC,gBAAgB,CAAC;MAE9F,IAAIH,SAAS,GAAGD,WAAW,GAAGI,gBAAgB,EAAE;QAC5CC,eAAe,GAAG,IAAI5C,MAAM,CAAC,UAAU,IAAIwC,SAAS,IAAID,WAAW,GAAGI,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAAC;QAChG/C,MAAM,GAAGA,MAAM,CAAChC,OAAO,CAACgF,eAAe,EAAE,EAAE,CAAC;MAChD;MAEA,OAAOhD,MAAM;IACjB;EACJ,CAAC;;EAED;EACAvD,OAAO,CAACW,OAAO,GAAGA,OAAO;;EAEzB;EACAX,OAAO,CAACG,OAAO,GAAGA,OAAO;;EAEzB;EACAH,OAAO,CAACI,OAAO,GAAGA,OAAO;;EAEzB;EACA;EACA;EACAJ,OAAO,CAACmC,MAAM,GAAG,UAASqE,GAAG,EAAE;IAC3B,IAAIA,GAAG,EAAE;MACL7F,OAAO,CAACL,aAAa,GAAGkG,GAAG,CAACC,WAAW,EAAE;IAC7C;IAEA,OAAO9F,OAAO,CAACL,aAAa;EAChC,CAAC;;EAED;EACA;EACA;EACAN,OAAO,CAAC0G,UAAU,GAAG,UAASF,GAAG,EAAE;IAC/B,IAAI,CAACA,GAAG,EAAE;MACN,OAAOpG,OAAO,CAACO,OAAO,CAACL,aAAa,CAAC;IACzC;IAEAkG,GAAG,GAAGA,GAAG,CAACC,WAAW,EAAE;IAEvB,IAAI,CAACrG,OAAO,CAACoG,GAAG,CAAC,EAAE;MACf,MAAM,IAAIG,KAAK,CAAC,mBAAmB,GAAGH,GAAG,CAAC;IAC9C;IAEA,OAAOpG,OAAO,CAACoG,GAAG,CAAC;EACvB,CAAC;EAEDxG,OAAO,CAAC4G,KAAK,GAAG,YAAW;IACvB,KAAK,IAAIC,QAAQ,IAAIxG,QAAQ,EAAE;MAC3BM,OAAO,CAACkG,QAAQ,CAAC,GAAGxG,QAAQ,CAACwG,QAAQ,CAAC;IAC1C;EACJ,CAAC;EAED7G,OAAO,CAACO,UAAU,GAAG,UAAS0B,MAAM,EAAE;IAClCtB,OAAO,CAACJ,UAAU,GAAG,OAAO0B,MAAO,KAAK,QAAQ,GAAGA,MAAM,GAAG,IAAI;EACpE,CAAC;EAEDjC,OAAO,CAACQ,UAAU,GAAG,UAAUyB,MAAM,EAAE;IACnCtB,OAAO,CAACH,UAAU,GAAG,OAAOyB,MAAO,KAAK,QAAQ,GAAGA,MAAM,GAAG,IAAI;EACpE,CAAC;EAEDjC,OAAO,CAACS,aAAa,GAAG,UAASwB,MAAM,EAAE;IACrCtB,OAAO,CAACF,aAAa,GAAG,OAAOwB,MAAO,KAAK,QAAQ,GAAGA,MAAM,GAAG,KAAK;EACxE,CAAC;EAEDjC,OAAO,CAAC8G,QAAQ,GAAG,UAASC,IAAI,EAAEC,IAAI,EAAE/E,MAAM,EAAE;IAC5C+E,IAAI,GAAGA,IAAI,CAACP,WAAW,EAAE;IAEzB,IAAI,IAAI,CAACM,IAAI,GAAG,GAAG,CAAC,CAACC,IAAI,CAAC,EAAE;MACxB,MAAM,IAAI/B,SAAS,CAAC+B,IAAI,GAAG,GAAG,GAAGD,IAAI,GAAG,sBAAsB,CAAC;IACnE;IAEA,IAAI,CAACA,IAAI,GAAG,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG/E,MAAM;IAE/B,OAAOA,MAAM;EACjB,CAAC;EAGDjC,OAAO,CAACiH,QAAQ,GAAG,UAASC,GAAG,EAAEC,OAAO,EAAE;IACtC,IAAIC,WAAW,EACXC,YAAY,EACZC,WAAW,EACXC,SAAS,EACTC,QAAQ,EACRC,cAAc,EACdf,UAAU,EACVgB,IAAI;;IAER;IACA,IAAI,OAAOR,GAAG,KAAK,QAAQ,EAAE;MACzBA,GAAG,IAAI,EAAE;MAET,IAAIS,OAAO,CAACC,IAAI,EAAE;QACdD,OAAO,CAACC,IAAI,CAAC,4DAA4D,EAAEV,GAAG,CAAC;MACnF;IACJ;;IAEA;IACAA,GAAG,GAAGA,GAAG,CAACW,IAAI,EAAE;;IAEhB;IACA,IAAI,CAAC,CAACX,GAAG,CAACvF,KAAK,CAAC,OAAO,CAAC,EAAE;MACtB,OAAO,IAAI;IACf;;IAEA;IACA,IAAIuF,GAAG,KAAK,EAAE,EAAE;MACZ,OAAO,KAAK;IAChB;;IAEA;IACA,IAAI;MACA;MACAR,UAAU,GAAG1G,OAAO,CAAC0G,UAAU,CAACS,OAAO,CAAC;IAC5C,CAAC,CAAC,OAAOW,CAAC,EAAE;MACRpB,UAAU,GAAG1G,OAAO,CAAC0G,UAAU,CAAC1G,OAAO,CAACmC,MAAM,EAAE,CAAC;IACrD;;IAEA;IACAmF,WAAW,GAAGZ,UAAU,CAACnC,QAAQ,CAACC,MAAM;IACxCgD,QAAQ,GAAGd,UAAU,CAAC9C,aAAa;IACnCwD,WAAW,GAAGV,UAAU,CAAC1C,UAAU,CAACpB,OAAO;IAC3C,IAAI8D,UAAU,CAAC1C,UAAU,CAACV,SAAS,KAAK,GAAG,EAAE;MACzC+D,YAAY,GAAG,KAAK;IACxB,CAAC,MAAM;MACHA,YAAY,GAAGX,UAAU,CAAC1C,UAAU,CAACV,SAAS;IAClD;;IAEA;IACAoE,IAAI,GAAGR,GAAG,CAACvF,KAAK,CAAC,SAAS,CAAC;IAC3B,IAAI+F,IAAI,KAAK,IAAI,EAAE;MACfR,GAAG,GAAGA,GAAG,CAACa,MAAM,CAAC,CAAC,CAAC;MACnB,IAAIL,IAAI,CAAC,CAAC,CAAC,KAAKJ,WAAW,EAAE;QACzB,OAAO,KAAK;MAChB;IACJ;;IAEA;IACAI,IAAI,GAAGR,GAAG,CAACvF,KAAK,CAAC,SAAS,CAAC;IAC3B,IAAI+F,IAAI,KAAK,IAAI,EAAE;MACfR,GAAG,GAAGA,GAAG,CAACjD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACtB,IAAIyD,IAAI,CAAC,CAAC,CAAC,KAAKF,QAAQ,CAAC7E,QAAQ,IAAI+E,IAAI,CAAC,CAAC,CAAC,KAAKF,QAAQ,CAAC9E,OAAO,IAAIgF,IAAI,CAAC,CAAC,CAAC,KAAKF,QAAQ,CAAC/E,OAAO,IAAIiF,IAAI,CAAC,CAAC,CAAC,KAAKF,QAAQ,CAAChF,QAAQ,EAAE;QAChI,OAAO,KAAK;MAChB;IACJ;IAEAiF,cAAc,GAAG,IAAI9D,MAAM,CAAC0D,YAAY,GAAG,KAAK,CAAC;IAEjD,IAAI,CAACH,GAAG,CAACvF,KAAK,CAAC,UAAU,CAAC,EAAE;MACxB4F,SAAS,GAAGL,GAAG,CAACpD,KAAK,CAACsD,WAAW,CAAC;MAClC,IAAIG,SAAS,CAAC/F,MAAM,GAAG,CAAC,EAAE;QACtB,OAAO,KAAK;MAChB,CAAC,MAAM;QACH,IAAI+F,SAAS,CAAC/F,MAAM,GAAG,CAAC,EAAE;UACtB,OAAS,CAAC,CAAE+F,SAAS,CAAC,CAAC,CAAC,CAAC5F,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC4F,SAAS,CAAC,CAAC,CAAC,CAAC5F,KAAK,CAAC8F,cAAc,CAAC;QACtF,CAAC,MAAM;UACH,IAAIF,SAAS,CAAC,CAAC,CAAC,CAAC/F,MAAM,KAAK,CAAC,EAAE;YAC3B,OAAS,CAAC,CAAE+F,SAAS,CAAC,CAAC,CAAC,CAAC5F,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC4F,SAAS,CAAC,CAAC,CAAC,CAAC5F,KAAK,CAAC8F,cAAc,CAAC,IAAI,CAAC,CAAEF,SAAS,CAAC,CAAC,CAAC,CAAC5F,KAAK,CAAC,OAAO,CAAC;UACpH,CAAC,MAAM;YACH,OAAS,CAAC,CAAE4F,SAAS,CAAC,CAAC,CAAC,CAAC5F,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC4F,SAAS,CAAC,CAAC,CAAC,CAAC5F,KAAK,CAAC8F,cAAc,CAAC,IAAI,CAAC,CAAEF,SAAS,CAAC,CAAC,CAAC,CAAC5F,KAAK,CAAC,OAAO,CAAC;UACxH;QACJ;MACJ;IACJ;IAEA,OAAO,KAAK;EAChB,CAAC;;EAGD;AACJ;AACA;;EAEI3B,OAAO,CAACgI,EAAE,GAAGpH,OAAO,CAACiF,SAAS,GAAG;IAC7BoC,KAAK,EAAE,YAAW;MACd,OAAOjI,OAAO,CAAC,IAAI,CAAC;IACxB,CAAC;IACDiC,MAAM,EAAE,UAASiG,WAAW,EAAEhG,gBAAgB,EAAE;MAC5C,IAAIjB,KAAK,GAAG,IAAI,CAACD,MAAM;QACnBiB,MAAM,GAAGiG,WAAW,IAAIvH,OAAO,CAACF,aAAa;QAC7CS,IAAI;QACJqC,MAAM;QACN4E,cAAc;;MAElB;MACAjG,gBAAgB,GAAGA,gBAAgB,IAAIsB,IAAI,CAAC4E,KAAK;;MAEjD;MACA,IAAInH,KAAK,KAAK,CAAC,IAAIN,OAAO,CAACJ,UAAU,KAAK,IAAI,EAAE;QAC5CgD,MAAM,GAAG5C,OAAO,CAACJ,UAAU;MAC/B,CAAC,MAAM,IAAIU,KAAK,KAAK,IAAI,IAAIN,OAAO,CAACH,UAAU,KAAK,IAAI,EAAE;QACtD+C,MAAM,GAAG5C,OAAO,CAACH,UAAU;MAC/B,CAAC,MAAM;QACH,KAAKU,IAAI,IAAIf,OAAO,EAAE;UAClB,IAAI8B,MAAM,CAACN,KAAK,CAACxB,OAAO,CAACe,IAAI,CAAC,CAACO,OAAO,CAACQ,MAAM,CAAC,EAAE;YAC5CkG,cAAc,GAAGhI,OAAO,CAACe,IAAI,CAAC,CAACe,MAAM;YAErC;UACJ;QACJ;QAEAkG,cAAc,GAAGA,cAAc,IAAInI,OAAO,CAACC,CAAC,CAAC+B,cAAc;QAE3DuB,MAAM,GAAG4E,cAAc,CAAClH,KAAK,EAAEgB,MAAM,EAAEC,gBAAgB,CAAC;MAC5D;MAEA,OAAOqB,MAAM;IACjB,CAAC;IACDtC,KAAK,EAAE,YAAW;MACd,OAAO,IAAI,CAACD,MAAM;IACtB,CAAC;IACDH,KAAK,EAAE,YAAW;MACd,OAAO,IAAI,CAACE,MAAM;IACtB,CAAC;IACDsH,GAAG,EAAE,UAASpH,KAAK,EAAE;MACjB,IAAI,CAACD,MAAM,GAAGa,MAAM,CAACZ,KAAK,CAAC;MAE3B,OAAO,IAAI;IACf,CAAC;IACDqH,GAAG,EAAE,UAASrH,KAAK,EAAE;MACjB,IAAIsH,UAAU,GAAGtI,CAAC,CAACyF,gBAAgB,CAACI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC9E,MAAM,EAAEC,KAAK,CAAC;MAElE,SAASuH,KAAK,CAACzC,KAAK,EAAE0C,IAAI,EAAEC,KAAK,EAAEC,CAAC,EAAE;QAClC,OAAO5C,KAAK,GAAGvC,IAAI,CAAC4E,KAAK,CAACG,UAAU,GAAGE,IAAI,CAAC;MAChD;MAEA,IAAI,CAACzH,MAAM,GAAGf,CAAC,CAAC6E,MAAM,CAAC,CAAC,IAAI,CAAC9D,MAAM,EAAEC,KAAK,CAAC,EAAEuH,KAAK,EAAE,CAAC,CAAC,GAAGD,UAAU;MAEnE,OAAO,IAAI;IACf,CAAC;IACDK,QAAQ,EAAE,UAAS3H,KAAK,EAAE;MACtB,IAAIsH,UAAU,GAAGtI,CAAC,CAACyF,gBAAgB,CAACI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC9E,MAAM,EAAEC,KAAK,CAAC;MAElE,SAASuH,KAAK,CAACzC,KAAK,EAAE0C,IAAI,EAAEC,KAAK,EAAEC,CAAC,EAAE;QAClC,OAAO5C,KAAK,GAAGvC,IAAI,CAAC4E,KAAK,CAACG,UAAU,GAAGE,IAAI,CAAC;MAChD;MAEA,IAAI,CAACzH,MAAM,GAAGf,CAAC,CAAC6E,MAAM,CAAC,CAAC7D,KAAK,CAAC,EAAEuH,KAAK,EAAEhF,IAAI,CAAC4E,KAAK,CAAC,IAAI,CAACpH,MAAM,GAAGuH,UAAU,CAAC,CAAC,GAAGA,UAAU;MAEzF,OAAO,IAAI;IACf,CAAC;IACDM,QAAQ,EAAE,UAAS5H,KAAK,EAAE;MACtB,SAASuH,KAAK,CAACzC,KAAK,EAAE0C,IAAI,EAAEC,KAAK,EAAEC,CAAC,EAAE;QAClC,IAAIJ,UAAU,GAAGtI,CAAC,CAACyF,gBAAgB,CAACK,KAAK,EAAE0C,IAAI,CAAC;QAChD,OAAOjF,IAAI,CAAC4E,KAAK,CAACrC,KAAK,GAAGwC,UAAU,CAAC,GAAG/E,IAAI,CAAC4E,KAAK,CAACK,IAAI,GAAGF,UAAU,CAAC,GAAG/E,IAAI,CAAC4E,KAAK,CAACG,UAAU,GAAGA,UAAU,CAAC;MAC/G;MAEA,IAAI,CAACvH,MAAM,GAAGf,CAAC,CAAC6E,MAAM,CAAC,CAAC,IAAI,CAAC9D,MAAM,EAAEC,KAAK,CAAC,EAAEuH,KAAK,EAAE,CAAC,CAAC;MAEtD,OAAO,IAAI;IACf,CAAC;IACDM,MAAM,EAAE,UAAS7H,KAAK,EAAE;MACpB,SAASuH,KAAK,CAACzC,KAAK,EAAE0C,IAAI,EAAEC,KAAK,EAAEC,CAAC,EAAE;QAClC,IAAIJ,UAAU,GAAGtI,CAAC,CAACyF,gBAAgB,CAACK,KAAK,EAAE0C,IAAI,CAAC;QAChD,OAAOjF,IAAI,CAAC4E,KAAK,CAACrC,KAAK,GAAGwC,UAAU,CAAC,GAAG/E,IAAI,CAAC4E,KAAK,CAACK,IAAI,GAAGF,UAAU,CAAC;MACzE;MAEA,IAAI,CAACvH,MAAM,GAAGf,CAAC,CAAC6E,MAAM,CAAC,CAAC,IAAI,CAAC9D,MAAM,EAAEC,KAAK,CAAC,EAAEuH,KAAK,CAAC;MAEnD,OAAO,IAAI;IACf,CAAC;IACDO,UAAU,EAAE,UAAS9H,KAAK,EAAE;MACxB,OAAOuC,IAAI,CAACT,GAAG,CAAC/C,OAAO,CAAC,IAAI,CAACgB,MAAM,CAAC,CAAC4H,QAAQ,CAAC3H,KAAK,CAAC,CAACA,KAAK,EAAE,CAAC;IACjE;EACJ,CAAC;;EAED;AACJ;AACA;;EAEIjB,OAAO,CAAC8G,QAAQ,CAAC,QAAQ,EAAE,IAAI,EAAE;IAC7B9C,UAAU,EAAE;MACRV,SAAS,EAAE,GAAG;MACdV,OAAO,EAAE;IACb,CAAC;IACDgB,aAAa,EAAE;MACXjB,QAAQ,EAAE,GAAG;MACbD,OAAO,EAAE,GAAG;MACZD,OAAO,EAAE,GAAG;MACZD,QAAQ,EAAE;IACd,CAAC;IACDwG,OAAO,EAAE,UAASlI,MAAM,EAAE;MACtB,IAAImI,CAAC,GAAGnI,MAAM,GAAG,EAAE;MACnB,OAAQ,CAAC,EAAEA,MAAM,GAAG,GAAG,GAAG,EAAE,CAAC,KAAK,CAAC,GAAI,IAAI,GACtCmI,CAAC,KAAK,CAAC,GAAI,IAAI,GACfA,CAAC,KAAK,CAAC,GAAI,IAAI,GACfA,CAAC,KAAK,CAAC,GAAI,IAAI,GAAG,IAAI;IAC/B,CAAC;IACD1E,QAAQ,EAAE;MACNC,MAAM,EAAE;IACZ;EACJ,CAAC,CAAC;EAIN,CAAC,YAAW;IACJxE,OAAO,CAAC8G,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAE;MAC9BrF,OAAO,EAAE;QACLQ,MAAM,EAAE,OAAO;QACfP,QAAQ,EAAE;MACd,CAAC;MACDO,MAAM,EAAE,UAAShB,KAAK,EAAEgB,MAAM,EAAEC,gBAAgB,EAAE;QAC9C,IAAIgH,KAAK,GAAGlJ,OAAO,CAACC,CAAC,CAACwD,QAAQ,CAACxB,MAAM,EAAE,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;UACrDsB,MAAM;QAEVtC,KAAK,GAAGA,KAAK,GAAG,KAAK;;QAErB;QACAgB,MAAM,GAAGA,MAAM,CAACV,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;QAErCgC,MAAM,GAAGvD,OAAO,CAACC,CAAC,CAAC+B,cAAc,CAACf,KAAK,EAAEgB,MAAM,EAAEC,gBAAgB,CAAC;QAElE,IAAIlC,OAAO,CAACC,CAAC,CAACwD,QAAQ,CAACF,MAAM,EAAE,GAAG,CAAC,EAAE;UACjCA,MAAM,GAAGA,MAAM,CAACO,KAAK,CAAC,EAAE,CAAC;UAEzBP,MAAM,CAAC4F,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAED,KAAK,GAAG,KAAK,CAAC;UAEnC3F,MAAM,GAAGA,MAAM,CAAC6F,IAAI,CAAC,EAAE,CAAC;QAC5B,CAAC,MAAM;UACH7F,MAAM,GAAGA,MAAM,GAAG2F,KAAK,GAAG,KAAK;QACnC;QAEA,OAAO3F,MAAM;MACjB,CAAC;MACD7B,QAAQ,EAAE,UAAS0C,MAAM,EAAE;QACvB,OAAO,CAAC,CAACpE,OAAO,CAACC,CAAC,CAAC2B,cAAc,CAACwC,MAAM,CAAC,GAAG,MAAM,EAAEL,OAAO,CAAC,EAAE,CAAC;MACnE;IACJ,CAAC,CAAC;EACV,CAAC,GAAG;EAGJ,CAAC,YAAW;IACJ,IAAInB,OAAO,GAAG;QACVyG,IAAI,EAAE,IAAI;QACVC,QAAQ,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;MAClE,CAAC;MACDC,MAAM,GAAG;QACLF,IAAI,EAAE,IAAI;QACVC,QAAQ,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK;MAC1E,CAAC;IAEL,IAAIE,WAAW,GAAI5G,OAAO,CAAC0G,QAAQ,CAACG,MAAM,CAACF,MAAM,CAACD,QAAQ,CAACI,MAAM,CAAC,UAAUC,IAAI,EAAE;MAC1E,OAAO/G,OAAO,CAAC0G,QAAQ,CAAC5F,OAAO,CAACiG,IAAI,CAAC,GAAG,CAAC;IAC7C,CAAC,CAAC,CAAC;IACH,IAAIC,aAAa,GAAGJ,WAAW,CAACJ,IAAI,CAAC,GAAG,CAAC;IACzC;IACAQ,aAAa,GAAG,GAAG,GAAGA,aAAa,CAACrI,OAAO,CAAC,GAAG,EAAE,SAAS,CAAC,GAAG,GAAG;IAErEvB,OAAO,CAAC8G,QAAQ,CAAC,QAAQ,EAAE,OAAO,EAAE;MAChCrF,OAAO,EAAE;QACLQ,MAAM,EAAE,YAAY;QACpBP,QAAQ,EAAE,IAAIiC,MAAM,CAACiG,aAAa;MACtC,CAAC;MACD3H,MAAM,EAAE,UAAShB,KAAK,EAAEgB,MAAM,EAAEC,gBAAgB,EAAE;QAC9C,IAAIqB,MAAM;UACNsG,KAAK,GAAG7J,OAAO,CAACC,CAAC,CAACwD,QAAQ,CAACxB,MAAM,EAAE,IAAI,CAAC,GAAGsH,MAAM,GAAG3G,OAAO;UAC3DkH,MAAM,GAAG9J,OAAO,CAACC,CAAC,CAACwD,QAAQ,CAACxB,MAAM,EAAE,IAAI,CAAC,IAAIjC,OAAO,CAACC,CAAC,CAACwD,QAAQ,CAACxB,MAAM,EAAE,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;UACzFiB,KAAK;UACLF,GAAG;UACHC,GAAG;;QAEP;QACAhB,MAAM,GAAGA,MAAM,CAACV,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;QAErC,KAAK2B,KAAK,GAAG,CAAC,EAAEA,KAAK,IAAI2G,KAAK,CAACP,QAAQ,CAAC9H,MAAM,EAAE0B,KAAK,EAAE,EAAE;UACrDF,GAAG,GAAGQ,IAAI,CAACiB,GAAG,CAACoF,KAAK,CAACR,IAAI,EAAEnG,KAAK,CAAC;UACjCD,GAAG,GAAGO,IAAI,CAACiB,GAAG,CAACoF,KAAK,CAACR,IAAI,EAAEnG,KAAK,GAAG,CAAC,CAAC;UAErC,IAAIjC,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,CAAC,IAAIA,KAAK,IAAI+B,GAAG,IAAI/B,KAAK,GAAGgC,GAAG,EAAE;YAC9D6G,MAAM,IAAID,KAAK,CAACP,QAAQ,CAACpG,KAAK,CAAC;YAE/B,IAAIF,GAAG,GAAG,CAAC,EAAE;cACT/B,KAAK,GAAGA,KAAK,GAAG+B,GAAG;YACvB;YAEA;UACJ;QACJ;QAEAO,MAAM,GAAGvD,OAAO,CAACC,CAAC,CAAC+B,cAAc,CAACf,KAAK,EAAEgB,MAAM,EAAEC,gBAAgB,CAAC;QAElE,OAAOqB,MAAM,GAAGuG,MAAM;MAC1B,CAAC;MACDpI,QAAQ,EAAE,UAAS0C,MAAM,EAAE;QACvB,IAAInD,KAAK,GAAGjB,OAAO,CAACC,CAAC,CAAC2B,cAAc,CAACwC,MAAM,CAAC;UACxClB,KAAK;UACL6G,eAAe;QAEnB,IAAI9I,KAAK,EAAE;UACP,KAAKiC,KAAK,GAAGN,OAAO,CAAC0G,QAAQ,CAAC9H,MAAM,GAAG,CAAC,EAAE0B,KAAK,IAAI,CAAC,EAAEA,KAAK,EAAE,EAAE;YAC3D,IAAIlD,OAAO,CAACC,CAAC,CAACwD,QAAQ,CAACW,MAAM,EAAExB,OAAO,CAAC0G,QAAQ,CAACpG,KAAK,CAAC,CAAC,EAAE;cACrD6G,eAAe,GAAGvG,IAAI,CAACiB,GAAG,CAAC7B,OAAO,CAACyG,IAAI,EAAEnG,KAAK,CAAC;cAE/C;YACJ;YAEA,IAAIlD,OAAO,CAACC,CAAC,CAACwD,QAAQ,CAACW,MAAM,EAAEmF,MAAM,CAACD,QAAQ,CAACpG,KAAK,CAAC,CAAC,EAAE;cACpD6G,eAAe,GAAGvG,IAAI,CAACiB,GAAG,CAAC8E,MAAM,CAACF,IAAI,EAAEnG,KAAK,CAAC;cAE9C;YACJ;UACJ;UAEAjC,KAAK,IAAK8I,eAAe,IAAI,CAAE;QACnC;QAEA,OAAO9I,KAAK;MAChB;IACJ,CAAC,CAAC;EACN,CAAC,GAAG;EAGJ,CAAC,YAAW;IACJjB,OAAO,CAAC8G,QAAQ,CAAC,QAAQ,EAAE,UAAU,EAAE;MACvCrF,OAAO,EAAE;QACLQ,MAAM,EAAE;MACZ,CAAC;MACDA,MAAM,EAAE,UAAShB,KAAK,EAAEgB,MAAM,EAAEC,gBAAgB,EAAE;QAC9C,IAAIC,MAAM,GAAGnC,OAAO,CAACI,OAAO,CAACJ,OAAO,CAACW,OAAO,CAACL,aAAa,CAAC;UACvD0J,OAAO,GAAG;YACNC,MAAM,EAAEhI,MAAM,CAACN,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAC/CuI,KAAK,EAAEjI,MAAM,CAACN,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC;UACjD,CAAC;UACD4B,MAAM;UACNiB,MAAM;UACNL,CAAC;;QAEL;QACAlC,MAAM,GAAGA,MAAM,CAACV,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;;QAEvC;QACAgC,MAAM,GAAGvD,OAAO,CAACC,CAAC,CAAC+B,cAAc,CAACf,KAAK,EAAEgB,MAAM,EAAEC,gBAAgB,CAAC;;QAElE;QACA,IAAIjB,KAAK,IAAI,CAAC,EAAE;UACZ+I,OAAO,CAACC,MAAM,GAAGD,OAAO,CAACC,MAAM,CAAC1I,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;UACrDyI,OAAO,CAACE,KAAK,GAAGF,OAAO,CAACE,KAAK,CAAC3I,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;QACvD,CAAC,MAAM,IAAIN,KAAK,GAAG,CAAC,IAAK,CAACjB,OAAO,CAACC,CAAC,CAACwD,QAAQ,CAACuG,OAAO,CAACC,MAAM,EAAE,GAAG,CAAC,IAAI,CAACjK,OAAO,CAACC,CAAC,CAACwD,QAAQ,CAACuG,OAAO,CAACC,MAAM,EAAE,GAAG,CAAE,EAAE;UAC5GD,OAAO,CAACC,MAAM,GAAG,GAAG,GAAGD,OAAO,CAACC,MAAM;QACzC;;QAEA;QACA,KAAK9F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6F,OAAO,CAACC,MAAM,CAACzI,MAAM,EAAE2C,CAAC,EAAE,EAAE;UACxCK,MAAM,GAAGwF,OAAO,CAACC,MAAM,CAAC9F,CAAC,CAAC;UAE1B,QAAQK,MAAM;YACV,KAAK,GAAG;cACJjB,MAAM,GAAGvD,OAAO,CAACC,CAAC,CAAC0E,MAAM,CAACpB,MAAM,EAAEpB,MAAM,CAACoC,QAAQ,CAACC,MAAM,EAAEL,CAAC,CAAC;cAC5D;YACJ,KAAK,GAAG;cACJZ,MAAM,GAAGvD,OAAO,CAACC,CAAC,CAAC0E,MAAM,CAACpB,MAAM,EAAE,GAAG,EAAEY,CAAC,GAAGhC,MAAM,CAACoC,QAAQ,CAACC,MAAM,CAAChD,MAAM,GAAG,CAAC,CAAC;cAC7E;UAAM;QAElB;;QAEA;QACA,KAAK2C,CAAC,GAAG6F,OAAO,CAACE,KAAK,CAAC1I,MAAM,GAAG,CAAC,EAAE2C,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;UAC5CK,MAAM,GAAGwF,OAAO,CAACE,KAAK,CAAC/F,CAAC,CAAC;UAEzB,QAAQK,MAAM;YACV,KAAK,GAAG;cACJjB,MAAM,GAAGY,CAAC,KAAK6F,OAAO,CAACE,KAAK,CAAC1I,MAAM,GAAG,CAAC,GAAG+B,MAAM,GAAGpB,MAAM,CAACoC,QAAQ,CAACC,MAAM,GAAGxE,OAAO,CAACC,CAAC,CAAC0E,MAAM,CAACpB,MAAM,EAAEpB,MAAM,CAACoC,QAAQ,CAACC,MAAM,EAAE,EAAEwF,OAAO,CAACE,KAAK,CAAC1I,MAAM,IAAI,CAAC,GAAG2C,CAAC,CAAC,CAAC,CAAC;cAC/J;YACJ,KAAK,GAAG;cACJZ,MAAM,GAAGY,CAAC,KAAK6F,OAAO,CAACE,KAAK,CAAC1I,MAAM,GAAG,CAAC,GAAG+B,MAAM,GAAG,GAAG,GAAGvD,OAAO,CAACC,CAAC,CAAC0E,MAAM,CAACpB,MAAM,EAAE,GAAG,EAAE,EAAEyG,OAAO,CAACE,KAAK,CAAC1I,MAAM,IAAI,CAAC,GAAG2C,CAAC,CAAC,GAAGhC,MAAM,CAACoC,QAAQ,CAACC,MAAM,CAAChD,MAAM,GAAG,CAAC,CAAC,CAAC;cAC7J;UAAM;QAElB;QAGA,OAAO+B,MAAM;MACjB;IACJ,CAAC,CAAC;EACN,CAAC,GAAG;EAGJ,CAAC,YAAW;IACJvD,OAAO,CAAC8G,QAAQ,CAAC,QAAQ,EAAE,aAAa,EAAE;MAC1CrF,OAAO,EAAE;QACLQ,MAAM,EAAE,UAAU;QAClBP,QAAQ,EAAE;MACd,CAAC;MACDO,MAAM,EAAE,UAAShB,KAAK,EAAEgB,MAAM,EAAEC,gBAAgB,EAAE;QAC9C,IAAIqB,MAAM;UACN4G,WAAW,GAAG,OAAOlJ,KAAK,KAAK,QAAQ,IAAI,CAACjB,OAAO,CAACC,CAAC,CAACqB,KAAK,CAACL,KAAK,CAAC,GAAGA,KAAK,CAACmJ,aAAa,EAAE,GAAG,MAAM;UACnG3E,KAAK,GAAG0E,WAAW,CAACrG,KAAK,CAAC,GAAG,CAAC;QAElC7B,MAAM,GAAGA,MAAM,CAACV,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;QAE3CgC,MAAM,GAAGvD,OAAO,CAACC,CAAC,CAAC+B,cAAc,CAACH,MAAM,CAAC4D,KAAK,CAAC,CAAC,CAAC,CAAC,EAAExD,MAAM,EAAEC,gBAAgB,CAAC;QAE7E,OAAOqB,MAAM,GAAG,GAAG,GAAGkC,KAAK,CAAC,CAAC,CAAC;MAClC,CAAC;MACD/D,QAAQ,EAAE,UAAS0C,MAAM,EAAE;QACvB,IAAIqB,KAAK,GAAGzF,OAAO,CAACC,CAAC,CAACwD,QAAQ,CAACW,MAAM,EAAE,IAAI,CAAC,GAAGA,MAAM,CAACN,KAAK,CAAC,IAAI,CAAC,GAAGM,MAAM,CAACN,KAAK,CAAC,IAAI,CAAC;UAClF7C,KAAK,GAAGY,MAAM,CAAC4D,KAAK,CAAC,CAAC,CAAC,CAAC;UACxBvC,KAAK,GAAGrB,MAAM,CAAC4D,KAAK,CAAC,CAAC,CAAC,CAAC;QAE5BvC,KAAK,GAAGlD,OAAO,CAACC,CAAC,CAACwD,QAAQ,CAACW,MAAM,EAAE,IAAI,CAAC,GAAGlB,KAAK,IAAI,CAAC,CAAC,GAAGA,KAAK;QAE9D,SAASsF,KAAK,CAACzC,KAAK,EAAE0C,IAAI,EAAEC,KAAK,EAAEC,CAAC,EAAE;UAClC,IAAIJ,UAAU,GAAGvI,OAAO,CAACC,CAAC,CAACyF,gBAAgB,CAACK,KAAK,EAAE0C,IAAI,CAAC;YACpD4B,GAAG,GAAItE,KAAK,GAAGwC,UAAU,IAAKE,IAAI,GAAGF,UAAU,CAAC,IAAIA,UAAU,GAAGA,UAAU,CAAC;UAChF,OAAO8B,GAAG;QACd;QAEA,OAAOrK,OAAO,CAACC,CAAC,CAAC6E,MAAM,CAAC,CAAC7D,KAAK,EAAEuC,IAAI,CAACiB,GAAG,CAAC,EAAE,EAAEvB,KAAK,CAAC,CAAC,EAAEsF,KAAK,EAAE,CAAC,CAAC;MACnE;IACJ,CAAC,CAAC;EACN,CAAC,GAAG;EAGJ,CAAC,YAAW;IACJxI,OAAO,CAAC8G,QAAQ,CAAC,QAAQ,EAAE,SAAS,EAAE;MACtCrF,OAAO,EAAE;QACLQ,MAAM,EAAE;MACZ,CAAC;MACDA,MAAM,EAAE,UAAShB,KAAK,EAAEgB,MAAM,EAAEC,gBAAgB,EAAE;QAC9C,IAAIC,MAAM,GAAGnC,OAAO,CAACI,OAAO,CAACJ,OAAO,CAACW,OAAO,CAACL,aAAa,CAAC;UACvDiD,MAAM;UACNyF,OAAO,GAAGhJ,OAAO,CAACC,CAAC,CAACwD,QAAQ,CAACxB,MAAM,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE;;QAEzD;QACAA,MAAM,GAAGA,MAAM,CAACV,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;QAEnCyH,OAAO,IAAI7G,MAAM,CAAC6G,OAAO,CAAC/H,KAAK,CAAC;QAEhCsC,MAAM,GAAGvD,OAAO,CAACC,CAAC,CAAC+B,cAAc,CAACf,KAAK,EAAEgB,MAAM,EAAEC,gBAAgB,CAAC;QAElE,OAAOqB,MAAM,GAAGyF,OAAO;MAC3B;IACJ,CAAC,CAAC;EACN,CAAC,GAAG;EAGJ,CAAC,YAAW;IACJhJ,OAAO,CAAC8G,QAAQ,CAAC,QAAQ,EAAE,YAAY,EAAE;MACzCrF,OAAO,EAAE;QACLQ,MAAM,EAAE,KAAK;QACbP,QAAQ,EAAE;MACd,CAAC;MACDO,MAAM,EAAE,UAAShB,KAAK,EAAEgB,MAAM,EAAEC,gBAAgB,EAAE;QAC9C,IAAIgH,KAAK,GAAGlJ,OAAO,CAACC,CAAC,CAACwD,QAAQ,CAACxB,MAAM,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE;UACnDsB,MAAM;QAEV,IAAIvD,OAAO,CAACW,OAAO,CAACD,iBAAiB,EAAE;UACnCO,KAAK,GAAGA,KAAK,GAAG,GAAG;QACvB;;QAEA;QACAgB,MAAM,GAAGA,MAAM,CAACV,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;QAEpCgC,MAAM,GAAGvD,OAAO,CAACC,CAAC,CAAC+B,cAAc,CAACf,KAAK,EAAEgB,MAAM,EAAEC,gBAAgB,CAAC;QAElE,IAAIlC,OAAO,CAACC,CAAC,CAACwD,QAAQ,CAACF,MAAM,EAAE,GAAG,CAAC,EAAE;UACjCA,MAAM,GAAGA,MAAM,CAACO,KAAK,CAAC,EAAE,CAAC;UAEzBP,MAAM,CAAC4F,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAED,KAAK,GAAG,GAAG,CAAC;UAEjC3F,MAAM,GAAGA,MAAM,CAAC6F,IAAI,CAAC,EAAE,CAAC;QAC5B,CAAC,MAAM;UACH7F,MAAM,GAAGA,MAAM,GAAG2F,KAAK,GAAG,GAAG;QACjC;QAEA,OAAO3F,MAAM;MACjB,CAAC;MACD7B,QAAQ,EAAE,UAAS0C,MAAM,EAAE;QACvB,IAAItD,MAAM,GAAGd,OAAO,CAACC,CAAC,CAAC2B,cAAc,CAACwC,MAAM,CAAC;QAC7C,IAAIpE,OAAO,CAACW,OAAO,CAACD,iBAAiB,EAAE;UACnC,OAAOI,MAAM,GAAG,IAAI;QACxB;QACA,OAAOA,MAAM;MACjB;IACJ,CAAC,CAAC;EACN,CAAC,GAAG;EAGJ,CAAC,YAAW;IACJd,OAAO,CAAC8G,QAAQ,CAAC,QAAQ,EAAE,MAAM,EAAE;MACnCrF,OAAO,EAAE;QACLQ,MAAM,EAAE,KAAK;QACbP,QAAQ,EAAE;MACd,CAAC;MACDO,MAAM,EAAE,UAAShB,KAAK,EAAEgB,MAAM,EAAEC,gBAAgB,EAAE;QAC9C,IAAIoI,KAAK,GAAG9G,IAAI,CAAC+G,KAAK,CAACtJ,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;UACnCuJ,OAAO,GAAGhH,IAAI,CAAC+G,KAAK,CAAC,CAACtJ,KAAK,GAAIqJ,KAAK,GAAG,EAAE,GAAG,EAAG,IAAI,EAAE,CAAC;UACtDG,OAAO,GAAGjH,IAAI,CAAC4E,KAAK,CAACnH,KAAK,GAAIqJ,KAAK,GAAG,EAAE,GAAG,EAAG,GAAIE,OAAO,GAAG,EAAG,CAAC;QAEpE,OAAOF,KAAK,GAAG,GAAG,IAAIE,OAAO,GAAG,EAAE,GAAG,GAAG,GAAGA,OAAO,GAAGA,OAAO,CAAC,GAAG,GAAG,IAAIC,OAAO,GAAG,EAAE,GAAG,GAAG,GAAGA,OAAO,GAAGA,OAAO,CAAC;MAClH,CAAC;MACD/I,QAAQ,EAAE,UAAS0C,MAAM,EAAE;QACvB,IAAIsG,SAAS,GAAGtG,MAAM,CAACN,KAAK,CAAC,GAAG,CAAC;UAC7B2G,OAAO,GAAG,CAAC;;QAEf;QACA,IAAIC,SAAS,CAAClJ,MAAM,KAAK,CAAC,EAAE;UACxB;UACAiJ,OAAO,GAAGA,OAAO,GAAI5I,MAAM,CAAC6I,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAG;UACpD;UACAD,OAAO,GAAGA,OAAO,GAAI5I,MAAM,CAAC6I,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,EAAG;UAC/C;UACAD,OAAO,GAAGA,OAAO,GAAG5I,MAAM,CAAC6I,SAAS,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,MAAM,IAAIA,SAAS,CAAClJ,MAAM,KAAK,CAAC,EAAE;UAC/B;UACAiJ,OAAO,GAAGA,OAAO,GAAI5I,MAAM,CAAC6I,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,EAAG;UAC/C;UACAD,OAAO,GAAGA,OAAO,GAAG5I,MAAM,CAAC6I,SAAS,CAAC,CAAC,CAAC,CAAC;QAC5C;QACA,OAAO7I,MAAM,CAAC4I,OAAO,CAAC;MAC1B;IACJ,CAAC,CAAC;EACN,CAAC,GAAG;EAEJ,OAAOzK,OAAO;AACd,CAAC,CAAC"}, "metadata": {}, "sourceType": "script"}