{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\GreenRP\\\\Web\\\\ClientApp\\\\src\\\\features\\\\auth\\\\auth-provider.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { AuthContext } from './auth-context';\nimport { authApi } from 'api/auth-service';\nimport { events, EventTypes } from 'app/events';\nimport { equals } from 'utils/equals';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst storage_key = 'auth_token';\nexport function AuthProvider(_ref) {\n  _s();\n  let {\n    children\n  } = _ref;\n  const [user, setUser] = useState(getUserInfo());\n  const signin = async (name, password) => {\n    try {\n      const doc = await authApi.login(name, password),\n        user = doc ? {\n          name,\n          password,\n          roles: doc.roles,\n          email: doc.email || null,\n          phone: doc.phone || null\n        } : null;\n      saveUserInfo(user);\n      setUser(user);\n      if (user) {\n        events.emit(EventTypes.authenticated);\n      }\n    } catch (e) {\n      throw e;\n    }\n  };\n  const signout = callback => {\n    setUser(null);\n    saveUserInfo();\n    callback();\n  };\n  const isInRole = role => {\n    if (!user || !Array.isArray(user === null || user === void 0 ? void 0 : user.roles)) {\n      return false;\n    }\n    return user.roles.some(r => equals(r, role));\n  };\n  const value = {\n    user,\n    signin,\n    signout,\n    isInRole\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 10\n  }, this);\n}\n_s(AuthProvider, \"IxFKRcRQ+zQbTcoST/jgJOlJSWg=\");\n_c = AuthProvider;\nfunction saveUserInfo(user) {\n  if (user) {\n    localStorage[storage_key] = JSON.stringify(user);\n  } else {\n    localStorage.removeItem(storage_key);\n  }\n}\nexport function getUserInfo() {\n  return JSON.parse(localStorage[storage_key] || null);\n}\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "useState", "AuthContext", "authApi", "events", "EventTypes", "equals", "storage_key", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "user", "setUser", "getUserInfo", "signin", "name", "password", "doc", "login", "roles", "email", "phone", "saveUserInfo", "emit", "authenticated", "e", "signout", "callback", "isInRole", "role", "Array", "isArray", "some", "r", "value", "localStorage", "JSON", "stringify", "removeItem", "parse"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/auth/auth-provider.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { AuthContext } from './auth-context';\r\nimport { authApi } from 'api/auth-service';\r\nimport { Roles, UserInfo } from 'api/models/auth';\r\nimport { events, EventTypes } from 'app/events';\r\nimport { equals } from 'utils/equals';\r\n\r\nconst storage_key = 'auth_token';\r\n\r\ninterface AuthProviderProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nexport function AuthProvider({children}: AuthProviderProps) {\r\n  const [user, setUser] = useState<UserInfo | null>(getUserInfo());\r\n\r\n  const signin = async (name: string, password: string) => {\r\n    try {\r\n      const doc = await authApi.login(name, password),\r\n        user = doc ? {name, password, roles: doc.roles, email: (doc.email || null), phone: (doc.phone || null)} : null;\r\n\r\n      saveUserInfo(user);\r\n      setUser(user);\r\n      if(user) {\r\n        events.emit(EventTypes.authenticated);\r\n      }\r\n\r\n    } catch(e) {\r\n      throw e;\r\n    }\r\n  }\r\n\r\n  const signout = (callback: VoidFunction) => {\r\n    setUser(null);\r\n    saveUserInfo();\r\n    callback();\r\n  };\r\n\r\n  const isInRole = (role: Roles) => {\r\n    if(!user || !Array.isArray(user?.roles)) {\r\n      return false;\r\n    }\r\n    \r\n    return user.roles.some(r => equals(r, role));\r\n  };\r\n\r\n  const value = {user, signin, signout, isInRole};\r\n\r\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\r\n}\r\n\r\nfunction saveUserInfo(user?: UserInfo | null) {\r\n  if(user) {\r\n    localStorage[storage_key] = JSON.stringify(user);\r\n  } else {\r\n    localStorage.removeItem(storage_key);\r\n  }\r\n}\r\n\r\nexport function getUserInfo(): UserInfo | null {\r\n  return JSON.parse(localStorage[storage_key] || null);\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,OAAO,QAAQ,kBAAkB;AAE1C,SAASC,MAAM,EAAEC,UAAU,QAAQ,YAAY;AAC/C,SAASC,MAAM,QAAQ,cAAc;AAAC;AAEtC,MAAMC,WAAW,GAAG,YAAY;AAMhC,OAAO,SAASC,YAAY,OAAgC;EAAA;EAAA,IAA/B;IAACC;EAA2B,CAAC;EACxD,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGV,QAAQ,CAAkBW,WAAW,EAAE,CAAC;EAEhE,MAAMC,MAAM,GAAG,OAAOC,IAAY,EAAEC,QAAgB,KAAK;IACvD,IAAI;MACF,MAAMC,GAAG,GAAG,MAAMb,OAAO,CAACc,KAAK,CAACH,IAAI,EAAEC,QAAQ,CAAC;QAC7CL,IAAI,GAAGM,GAAG,GAAG;UAACF,IAAI;UAAEC,QAAQ;UAAEG,KAAK,EAAEF,GAAG,CAACE,KAAK;UAAEC,KAAK,EAAGH,GAAG,CAACG,KAAK,IAAI,IAAK;UAAEC,KAAK,EAAGJ,GAAG,CAACI,KAAK,IAAI;QAAK,CAAC,GAAG,IAAI;MAEhHC,YAAY,CAACX,IAAI,CAAC;MAClBC,OAAO,CAACD,IAAI,CAAC;MACb,IAAGA,IAAI,EAAE;QACPN,MAAM,CAACkB,IAAI,CAACjB,UAAU,CAACkB,aAAa,CAAC;MACvC;IAEF,CAAC,CAAC,OAAMC,CAAC,EAAE;MACT,MAAMA,CAAC;IACT;EACF,CAAC;EAED,MAAMC,OAAO,GAAIC,QAAsB,IAAK;IAC1Cf,OAAO,CAAC,IAAI,CAAC;IACbU,YAAY,EAAE;IACdK,QAAQ,EAAE;EACZ,CAAC;EAED,MAAMC,QAAQ,GAAIC,IAAW,IAAK;IAChC,IAAG,CAAClB,IAAI,IAAI,CAACmB,KAAK,CAACC,OAAO,CAACpB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,KAAK,CAAC,EAAE;MACvC,OAAO,KAAK;IACd;IAEA,OAAOR,IAAI,CAACQ,KAAK,CAACa,IAAI,CAACC,CAAC,IAAI1B,MAAM,CAAC0B,CAAC,EAAEJ,IAAI,CAAC,CAAC;EAC9C,CAAC;EAED,MAAMK,KAAK,GAAG;IAACvB,IAAI;IAAEG,MAAM;IAAEY,OAAO;IAAEE;EAAQ,CAAC;EAE/C,oBAAO,QAAC,WAAW,CAAC,QAAQ;IAAC,KAAK,EAAEM,KAAM;IAAA,UAAExB;EAAQ;IAAA;IAAA;IAAA;EAAA,QAAwB;AAC9E;AAAC,GApCeD,YAAY;AAAA,KAAZA,YAAY;AAsC5B,SAASa,YAAY,CAACX,IAAsB,EAAE;EAC5C,IAAGA,IAAI,EAAE;IACPwB,YAAY,CAAC3B,WAAW,CAAC,GAAG4B,IAAI,CAACC,SAAS,CAAC1B,IAAI,CAAC;EAClD,CAAC,MAAM;IACLwB,YAAY,CAACG,UAAU,CAAC9B,WAAW,CAAC;EACtC;AACF;AAEA,OAAO,SAASK,WAAW,GAAoB;EAC7C,OAAOuB,IAAI,CAACG,KAAK,CAACJ,YAAY,CAAC3B,WAAW,CAAC,IAAI,IAAI,CAAC;AACtD;AAAC;AAAA"}, "metadata": {}, "sourceType": "module"}