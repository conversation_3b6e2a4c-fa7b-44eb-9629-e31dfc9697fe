{"ast": null, "code": "import Axios from 'axios';\nexport const axios = Axios.create();", "map": {"version": 3, "names": ["A<PERSON>os", "axios", "create"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/boot/axios.ts"], "sourcesContent": ["import Axios from 'axios';\r\n\r\nexport const axios = Axios.create();\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,OAAO,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,EAAE"}, "metadata": {}, "sourceType": "module"}