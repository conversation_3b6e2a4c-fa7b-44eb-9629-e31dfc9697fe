{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\GreenRP\\\\Web\\\\ClientApp\\\\src\\\\features\\\\driver-tasks\\\\List-Date.tsx\",\n  _s = $RefreshSig$();\nimport { useSelector } from 'react-redux';\nimport moment from 'moment';\nimport { classNames } from 'utils/class-names';\nimport { formatDate } from 'utils/format';\nimport { selectTasks } from './driver-task-slice';\nimport { ListItem } from './List-Item';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport function ListDate(_ref) {\n  _s();\n  let {\n    date\n  } = _ref;\n  const m = moment(date),\n    today = moment(),\n    isPast = m.isBefore(today, 'day'),\n    allTasks = useSelector(selectTasks),\n    filteredTasks = allTasks.filter(t => moment(t.dueDate).isSame(m, 'day')),\n    formatted = formatDate(date, 'dddd, MMM D');\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n      className: classNames('col-12 p-2 mb-0 border border-2 rounded text-center', isPast && 'text-danger'),\n      children: formatted\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row row-cols-1 row-cols-md-2 row-cols-lg-3 row-cols-xl-4 g-4 mt-0 mb-4\",\n      children: filteredTasks.map(task => /*#__PURE__*/_jsxDEV(ListItem, {\n        task: task\n      }, task._id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}\n_s(ListDate, \"Zmy5aeTREYmn9uV+KkLlrQZk39Q=\", false, function () {\n  return [useSelector];\n});\n_c = ListDate;\nvar _c;\n$RefreshReg$(_c, \"ListDate\");", "map": {"version": 3, "names": ["useSelector", "moment", "classNames", "formatDate", "selectTasks", "ListItem", "ListDate", "date", "m", "today", "isPast", "isBefore", "allTasks", "filteredTasks", "filter", "t", "dueDate", "isSame", "formatted", "map", "task", "_id"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/driver-tasks/List-Date.tsx"], "sourcesContent": ["import { useSelector } from 'react-redux';\r\nimport moment from 'moment';\r\nimport { classNames } from 'utils/class-names';\r\nimport { formatDate } from 'utils/format';\r\nimport { selectTasks } from './driver-task-slice';\r\nimport { ListItem } from './List-Item';\r\n\r\ninterface ListDateProps {\r\n  date: string;\r\n}\r\n\r\nexport function ListDate({ date }: ListDateProps) {\r\n  const m = moment(date),\r\n    today = moment(),\r\n    isPast = m.isBefore(today, 'day'),\r\n    allTasks = useSelector(selectTasks),\r\n    filteredTasks = allTasks.filter((t) => moment(t.dueDate).isSame(m, 'day')),\r\n    formatted = formatDate(date, 'dddd, MMM D');\r\n  return (\r\n    <>\r\n      <h5\r\n        className={classNames(\r\n          'col-12 p-2 mb-0 border border-2 rounded text-center',\r\n          isPast && 'text-danger'\r\n        )}>\r\n        {formatted}\r\n      </h5>\r\n      <div className=\"row row-cols-1 row-cols-md-2 row-cols-lg-3 row-cols-xl-4 g-4 mt-0 mb-4\">\r\n        {filteredTasks.map((task) => (\r\n          <ListItem key={task._id} task={task} />\r\n        ))}\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,SAASA,WAAW,QAAQ,aAAa;AACzC,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,QAAQ,QAAQ,aAAa;AAAC;AAAA;AAMvC,OAAO,SAASC,QAAQ,OAA0B;EAAA;EAAA,IAAzB;IAAEC;EAAoB,CAAC;EAC9C,MAAMC,CAAC,GAAGP,MAAM,CAACM,IAAI,CAAC;IACpBE,KAAK,GAAGR,MAAM,EAAE;IAChBS,MAAM,GAAGF,CAAC,CAACG,QAAQ,CAACF,KAAK,EAAE,KAAK,CAAC;IACjCG,QAAQ,GAAGZ,WAAW,CAACI,WAAW,CAAC;IACnCS,aAAa,GAAGD,QAAQ,CAACE,MAAM,CAAEC,CAAC,IAAKd,MAAM,CAACc,CAAC,CAACC,OAAO,CAAC,CAACC,MAAM,CAACT,CAAC,EAAE,KAAK,CAAC,CAAC;IAC1EU,SAAS,GAAGf,UAAU,CAACI,IAAI,EAAE,aAAa,CAAC;EAC7C,oBACE;IAAA,wBACE;MACE,SAAS,EAAEL,UAAU,CACnB,qDAAqD,EACrDQ,MAAM,IAAI,aAAa,CACvB;MAAA,UACDQ;IAAS;MAAA;MAAA;MAAA;IAAA,QACP,eACL;MAAK,SAAS,EAAC,wEAAwE;MAAA,UACpFL,aAAa,CAACM,GAAG,CAAEC,IAAI,iBACtB,QAAC,QAAQ;QAAgB,IAAI,EAAEA;MAAK,GAArBA,IAAI,CAACC,GAAG;QAAA;QAAA;QAAA;MAAA,QACxB;IAAC;MAAA;MAAA;MAAA;IAAA,QACE;EAAA,gBACL;AAEP;AAAC,GAvBef,QAAQ;EAAA,QAITN,WAAW;AAAA;AAAA,KAJVM,QAAQ;AAAA;AAAA"}, "metadata": {}, "sourceType": "module"}