{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\GreenRP\\\\Web\\\\ClientApp\\\\src\\\\features\\\\auth\\\\Login.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Button, FormGroup, Input, Label } from 'reactstrap';\nimport { useLocation, useNavigate } from 'react-router';\nimport { useAuth } from './use-auth';\nimport { routes } from 'app/routes';\nimport { Error } from 'features/error/Error';\nimport { createProblemDetails } from 'utils/problem-details';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport function Login() {\n  _s();\n  var _state$from;\n  const auth = useAuth(),\n    navigate = useNavigate(),\n    location = useLocation(),\n    [name, setName] = useState(''),\n    [password, setPassword] = useState(''),\n    [error, setError] = useState(null),\n    state = location.state,\n    from = state === null || state === void 0 ? void 0 : (_state$from = state.from) === null || _state$from === void 0 ? void 0 : _state$from.pathname;\n  const handleLoginClick = async e => {\n    e.preventDefault();\n    setError(null);\n    try {\n      await auth.signin(name, password);\n      const path = !from || from === routes.login.path ? routes.home.path : from;\n      navigate(path, {\n        replace: true\n      });\n    } catch (e) {\n      setError(createProblemDetails(e));\n    }\n  };\n  const clearError = () => {\n    setError(null);\n  };\n  return /*#__PURE__*/_jsxDEV(\"form\", {\n    id: \"login\",\n    className: \"mt-5\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      id: \"login-wrapper\",\n      className: \"col-12 col-md-6 col-lg-4 mx-auto border p-3 rounded shadow\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"/images/logo.png\",\n          alt: \"Boekestyn Greenhouses\",\n          style: {\n            maxWidth: '200px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"col-12 mb-3 text-center\",\n        children: \"Boekestyn Greenhouses Resource Planning App\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n        floating: true,\n        className: \"mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(Input, {\n          id: \"name\",\n          value: name,\n          onChange: e => setName(e.target.value),\n          placeholder: \"Username\",\n          autoFocus: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Label, {\n          htmlFor: \"name\",\n          children: \"Username\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n        floating: true,\n        className: \"mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(Input, {\n          id: \"password\",\n          type: \"password\",\n          value: password,\n          onChange: e => setPassword(e.target.value),\n          placeholder: \"Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Label, {\n          htmlFor: \"password\",\n          children: \"Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Error, {\n        error: error,\n        clearError: clearError\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          color: \"primary\",\n          size: \"lg\",\n          onClick: handleLoginClick,\n          children: \"Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 5\n  }, this);\n}\n_s(Login, \"wgIV3TMACt01mCzbNWXSrsHwOpk=\", false, function () {\n  return [useAuth, useNavigate, useLocation];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "<PERSON><PERSON>", "FormGroup", "Input", "Label", "useLocation", "useNavigate", "useAuth", "routes", "Error", "createProblemDetails", "<PERSON><PERSON>", "auth", "navigate", "location", "name", "setName", "password", "setPassword", "error", "setError", "state", "from", "pathname", "handleLoginClick", "e", "preventDefault", "signin", "path", "login", "home", "replace", "clearError", "max<PERSON><PERSON><PERSON>", "target", "value"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/auth/Login.tsx"], "sourcesContent": ["import React, { useState } from 'react'\r\nimport { Button, FormGroup, Input, Label } from 'reactstrap';\r\nimport { useLocation, useNavigate } from 'react-router';\r\nimport { useAuth } from './use-auth';\r\nimport { routes } from 'app/routes';\r\nimport { Error } from 'features/error/Error';\r\nimport { createProblemDetails, ProblemDetails } from 'utils/problem-details';\r\n\r\nexport function Login() {\r\n  const auth = useAuth(),\r\n    navigate = useNavigate(),\r\n    location = useLocation(),\r\n    [name, setName] = useState(''),\r\n    [password, setPassword] = useState(''),\r\n    [error, setError] = useState<ProblemDetails | null>(null),\r\n    state = location.state as any,\r\n    from = state?.from?.pathname;\r\n\r\n  const handleLoginClick = async (e: React.MouseEvent) => {\r\n    e.preventDefault();\r\n\r\n    setError(null);\r\n\r\n    try {\r\n\r\n      await auth.signin(name, password);\r\n\r\n      const path = (!from || from === routes.login.path) ? routes.home.path : from;\r\n\r\n      navigate(path, { replace: true})\r\n\r\n    } catch(e) {\r\n      setError(createProblemDetails(e));\r\n    }\r\n  };\r\n\r\n  const clearError = () => {\r\n    setError(null);\r\n  };\r\n\r\n  return (\r\n    <form id=\"login\" className=\"mt-5\">\r\n      <div id=\"login-wrapper\" className=\"col-12 col-md-6 col-lg-4 mx-auto border p-3 rounded shadow\">\r\n        <div className=\"text-center\">\r\n          <img src=\"/images/logo.png\" alt=\"Boekestyn Greenhouses\" style={{maxWidth: '200px'}} />\r\n        </div>\r\n        <h1 className=\"col-12 mb-3 text-center\">Boekestyn Greenhouses Resource Planning App</h1>\r\n        <FormGroup floating className=\"mb-3\">\r\n          <Input id=\"name\" value={name} onChange={e => setName(e.target.value)} placeholder=\"Username\" autoFocus />\r\n          <Label htmlFor=\"name\">Username</Label>\r\n        </FormGroup>\r\n        <FormGroup floating className=\"mb-3\">\r\n          <Input id=\"password\" type=\"password\" value={password} onChange={e => setPassword(e.target.value)} placeholder=\"Password\" />\r\n          <Label htmlFor=\"password\">Password</Label>\r\n        </FormGroup>\r\n        <Error error={error} clearError={clearError} />\r\n        <div className=\"text-center\">\r\n          <Button type=\"submit\" color=\"primary\" size=\"lg\" onClick={handleLoginClick}>Login</Button>\r\n        </div>\r\n      </div>      \r\n    </form>\r\n  );\r\n}\r\n\r\nexport default Login;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,KAAK,QAAQ,YAAY;AAC5D,SAASC,WAAW,EAAEC,WAAW,QAAQ,cAAc;AACvD,SAASC,OAAO,QAAQ,YAAY;AACpC,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,KAAK,QAAQ,sBAAsB;AAC5C,SAASC,oBAAoB,QAAwB,uBAAuB;AAAC;AAE7E,OAAO,SAASC,KAAK,GAAG;EAAA;EAAA;EACtB,MAAMC,IAAI,GAAGL,OAAO,EAAE;IACpBM,QAAQ,GAAGP,WAAW,EAAE;IACxBQ,QAAQ,GAAGT,WAAW,EAAE;IACxB,CAACU,IAAI,EAAEC,OAAO,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;IAC9B,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;IACtC,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAwB,IAAI,CAAC;IACzDqB,KAAK,GAAGP,QAAQ,CAACO,KAAY;IAC7BC,IAAI,GAAGD,KAAK,aAALA,KAAK,sCAALA,KAAK,CAAEC,IAAI,gDAAX,YAAaC,QAAQ;EAE9B,MAAMC,gBAAgB,GAAG,MAAOC,CAAmB,IAAK;IACtDA,CAAC,CAACC,cAAc,EAAE;IAElBN,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MAEF,MAAMR,IAAI,CAACe,MAAM,CAACZ,IAAI,EAAEE,QAAQ,CAAC;MAEjC,MAAMW,IAAI,GAAI,CAACN,IAAI,IAAIA,IAAI,KAAKd,MAAM,CAACqB,KAAK,CAACD,IAAI,GAAIpB,MAAM,CAACsB,IAAI,CAACF,IAAI,GAAGN,IAAI;MAE5ET,QAAQ,CAACe,IAAI,EAAE;QAAEG,OAAO,EAAE;MAAI,CAAC,CAAC;IAElC,CAAC,CAAC,OAAMN,CAAC,EAAE;MACTL,QAAQ,CAACV,oBAAoB,CAACe,CAAC,CAAC,CAAC;IACnC;EACF,CAAC;EAED,MAAMO,UAAU,GAAG,MAAM;IACvBZ,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;EAED,oBACE;IAAM,EAAE,EAAC,OAAO;IAAC,SAAS,EAAC,MAAM;IAAA,uBAC/B;MAAK,EAAE,EAAC,eAAe;MAAC,SAAS,EAAC,4DAA4D;MAAA,wBAC5F;QAAK,SAAS,EAAC,aAAa;QAAA,uBAC1B;UAAK,GAAG,EAAC,kBAAkB;UAAC,GAAG,EAAC,uBAAuB;UAAC,KAAK,EAAE;YAACa,QAAQ,EAAE;UAAO;QAAE;UAAA;UAAA;UAAA;QAAA;MAAG;QAAA;QAAA;QAAA;MAAA,QAClF,eACN;QAAI,SAAS,EAAC,yBAAyB;QAAA;MAAA;QAAA;QAAA;QAAA;MAAA,QAAiD,eACxF,QAAC,SAAS;QAAC,QAAQ;QAAC,SAAS,EAAC,MAAM;QAAA,wBAClC,QAAC,KAAK;UAAC,EAAE,EAAC,MAAM;UAAC,KAAK,EAAElB,IAAK;UAAC,QAAQ,EAAEU,CAAC,IAAIT,OAAO,CAACS,CAAC,CAACS,MAAM,CAACC,KAAK,CAAE;UAAC,WAAW,EAAC,UAAU;UAAC,SAAS;QAAA;UAAA;UAAA;UAAA;QAAA,QAAG,eACzG,QAAC,KAAK;UAAC,OAAO,EAAC,MAAM;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAAiB;MAAA;QAAA;QAAA;QAAA;MAAA,QAC5B,eACZ,QAAC,SAAS;QAAC,QAAQ;QAAC,SAAS,EAAC,MAAM;QAAA,wBAClC,QAAC,KAAK;UAAC,EAAE,EAAC,UAAU;UAAC,IAAI,EAAC,UAAU;UAAC,KAAK,EAAElB,QAAS;UAAC,QAAQ,EAAEQ,CAAC,IAAIP,WAAW,CAACO,CAAC,CAACS,MAAM,CAACC,KAAK,CAAE;UAAC,WAAW,EAAC;QAAU;UAAA;UAAA;UAAA;QAAA,QAAG,eAC3H,QAAC,KAAK;UAAC,OAAO,EAAC,UAAU;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAAiB;MAAA;QAAA;QAAA;QAAA;MAAA,QAChC,eACZ,QAAC,KAAK;QAAC,KAAK,EAAEhB,KAAM;QAAC,UAAU,EAAEa;MAAW;QAAA;QAAA;QAAA;MAAA,QAAG,eAC/C;QAAK,SAAS,EAAC,aAAa;QAAA,uBAC1B,QAAC,MAAM;UAAC,IAAI,EAAC,QAAQ;UAAC,KAAK,EAAC,SAAS;UAAC,IAAI,EAAC,IAAI;UAAC,OAAO,EAAER,gBAAiB;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA;MAAe;QAAA;QAAA;QAAA;MAAA,QACrF;IAAA;MAAA;MAAA;MAAA;IAAA;EACF;IAAA;IAAA;IAAA;EAAA,QACD;AAEX;AAAC,GAtDeb,KAAK;EAAA,QACNJ,OAAO,EACPD,WAAW,EACXD,WAAW;AAAA;AAAA,KAHVM,KAAK;AAwDrB,eAAeA,KAAK;AAAC;AAAA"}, "metadata": {}, "sourceType": "module"}