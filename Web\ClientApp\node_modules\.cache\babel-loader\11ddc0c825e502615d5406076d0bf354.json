{"ast": null, "code": "import { createAction, createAsyncThunk, createSlice } from '@reduxjs/toolkit';\nimport { orderApi } from 'api/order-service';\nimport { createOrder } from 'api/models/orders';\nconst initialState = {\n  isLoading: false,\n  order: null,\n  error: null\n};\nexport const saveOrder = createAsyncThunk('order-detail/save-order', async (_, _ref) => {\n  let {\n    rejectWithValue,\n    getState\n  } = _ref;\n  const order = getState().orderDetail.order;\n  if (order) {\n    try {\n      const doc = {\n          ...order\n        },\n        updated = await orderApi.save(doc);\n      return updated;\n    } catch (e) {\n      return rejectWithValue(e);\n    }\n  }\n});\nexport const deleteOrder = createAsyncThunk('order-detail/delete-order', async (_, _ref2) => {\n  let {\n    rejectWithValue,\n    getState\n  } = _ref2;\n  const order = getState().orderDetail.order;\n  if (order) {\n    try {\n      const doc = {\n          ...order\n        },\n        updated = await orderApi.delete(doc);\n      return updated;\n    } catch (e) {\n      return rejectWithValue(e);\n    }\n  }\n});\nconst savePending = createAction(saveOrder.pending.type),\n  saveFulfilled = createAction(saveOrder.fulfilled.type),\n  saveRejected = createAction(saveOrder.rejected.type),\n  deletePending = createAction(deleteOrder.pending.type),\n  deleteFulfilled = createAction(deleteOrder.fulfilled.type),\n  deleteRejected = createAction(deleteOrder.rejected.type);\nexport const orderDetailSlice = createSlice({\n  name: 'order-detail',\n  initialState,\n  reducers: {\n    setOrder(state, action) {\n      state.order = action.payload;\n    }\n  },\n  extraReducers: builder => builder.addCase(savePending, state => {\n    state.isLoading = true;\n    state.error = null;\n  }).addCase(saveFulfilled, (state, action) => {\n    state.isLoading = false;\n    if (action.payload) {\n      state.order = action.payload;\n    }\n  }).addCase(saveRejected, (state, action) => {\n    state.isLoading = false;\n    state.error = action.payload;\n  }).addCase(deletePending, state => {\n    state.isLoading = true;\n    state.error = null;\n  }).addCase(deleteFulfilled, state => {\n    state.isLoading = false;\n    state.order = createOrder();\n  }).addCase(deleteRejected, (state, action) => {\n    state.isLoading = false;\n    state.error = action.payload;\n  })\n});\nexport const {\n  setOrder\n} = orderDetailSlice.actions;\nexport const selectOrder = state => state.orderDetail.order;\nexport const selectIsLoading = state => state.orderDetail.isLoading;\nexport const selectError = state => state.orderDetail.error;\nexport default orderDetailSlice.reducer;", "map": {"version": 3, "names": ["createAction", "createAsyncThunk", "createSlice", "orderApi", "createOrder", "initialState", "isLoading", "order", "error", "saveOrder", "_", "rejectWithValue", "getState", "orderDetail", "doc", "updated", "save", "e", "deleteOrder", "delete", "savePending", "pending", "type", "saveFulfilled", "fulfilled", "saveRejected", "rejected", "deletePending", "deleteFulfilled", "deleteRejected", "orderDetailSlice", "name", "reducers", "setOrder", "state", "action", "payload", "extraReducers", "builder", "addCase", "actions", "selectOrder", "selectIsLoading", "selectError", "reducer"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/orders/detail-slice.ts"], "sourcesContent": ["import {\r\n  AsyncThunk,\r\n  createAction,\r\n  createAsyncThunk,\r\n  createSlice,\r\n  PayloadAction,\r\n} from '@reduxjs/toolkit';\r\nimport { orderApi } from 'api/order-service';\r\nimport { createOrder, Order } from 'api/models/orders';\r\nimport { RootState } from 'app/store';\r\nimport { ProblemDetails } from 'utils/problem-details';\r\n\r\ninterface OrderDetailState {\r\n  isLoading: boolean;\r\n  order: Order | null;\r\n  error: ProblemDetails | null;\r\n}\r\n\r\nconst initialState: OrderDetailState = {\r\n  isLoading: false,\r\n  order: null,\r\n  error: null,\r\n};\r\n\r\nexport const saveOrder: AsyncThunk<\r\n  Order | undefined,\r\n  void,\r\n  { state: RootState }\r\n> = createAsyncThunk(\r\n  'order-detail/save-order',\r\n  async (_, { rejectWithValue, getState }) => {\r\n    const order = (getState() as RootState).orderDetail.order;\r\n\r\n    if (order) {\r\n      try {\r\n        const doc = { ...order },\r\n          updated = await orderApi.save(doc);\r\n\r\n        return updated;\r\n      } catch (e) {\r\n        return rejectWithValue(e as ProblemDetails);\r\n      }\r\n    }\r\n  }\r\n);\r\n\r\nexport const deleteOrder: AsyncThunk<void, void, { state: RootState }> =\r\n  createAsyncThunk(\r\n    'order-detail/delete-order',\r\n    async (_, { rejectWithValue, getState }) => {\r\n      const order = (getState() as RootState).orderDetail.order;\r\n\r\n      if (order) {\r\n        try {\r\n          const doc = { ...order },\r\n            updated = await orderApi.delete(doc);\r\n\r\n          return updated;\r\n        } catch (e) {\r\n          return rejectWithValue(e as ProblemDetails);\r\n        }\r\n      }\r\n    }\r\n  );\r\n\r\nconst savePending = createAction(saveOrder.pending.type),\r\n  saveFulfilled = createAction<Order | undefined>(saveOrder.fulfilled.type),\r\n  saveRejected = createAction<ProblemDetails>(saveOrder.rejected.type),\r\n  deletePending = createAction(deleteOrder.pending.type),\r\n  deleteFulfilled = createAction(deleteOrder.fulfilled.type),\r\n  deleteRejected = createAction<ProblemDetails>(deleteOrder.rejected.type);\r\n\r\nexport const orderDetailSlice = createSlice({\r\n  name: 'order-detail',\r\n  initialState,\r\n  reducers: {\r\n    setOrder(state, action: PayloadAction<Order | null>) {\r\n      state.order = action.payload;\r\n    },\r\n  },\r\n  extraReducers: (builder) =>\r\n    builder\r\n      .addCase(savePending, (state) => {\r\n        state.isLoading = true;\r\n        state.error = null;\r\n      })\r\n      .addCase(saveFulfilled, (state, action) => {\r\n        state.isLoading = false;\r\n        if (action.payload) {\r\n          state.order = action.payload;\r\n        }\r\n      })\r\n      .addCase(saveRejected, (state, action) => {\r\n        state.isLoading = false;\r\n        state.error = action.payload;\r\n      })\r\n      .addCase(deletePending, (state) => {\r\n        state.isLoading = true;\r\n        state.error = null;\r\n      })\r\n      .addCase(deleteFulfilled, (state) => {\r\n        state.isLoading = false;\r\n        state.order = createOrder();\r\n      })\r\n      .addCase(deleteRejected, (state, action) => {\r\n        state.isLoading = false;\r\n        state.error = action.payload;\r\n      }),\r\n});\r\n\r\nexport const { setOrder } = orderDetailSlice.actions;\r\n\r\nexport const selectOrder = (state: RootState) => state.orderDetail.order;\r\nexport const selectIsLoading = (state: RootState) =>\r\n  state.orderDetail.isLoading;\r\nexport const selectError = (state: RootState) => state.orderDetail.error;\r\n\r\nexport default orderDetailSlice.reducer;\r\n"], "mappings": "AAAA,SAEEA,YAAY,EACZC,gBAAgB,EAChBC,WAAW,QAEN,kBAAkB;AACzB,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,SAASC,WAAW,QAAe,mBAAmB;AAUtD,MAAMC,YAA8B,GAAG;EACrCC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAE,IAAI;EACXC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,SAIZ,GAAGR,gBAAgB,CAClB,yBAAyB,EACzB,OAAOS,CAAC,WAAoC;EAAA,IAAlC;IAAEC,eAAe;IAAEC;EAAS,CAAC;EACrC,MAAML,KAAK,GAAIK,QAAQ,EAAE,CAAeC,WAAW,CAACN,KAAK;EAEzD,IAAIA,KAAK,EAAE;IACT,IAAI;MACF,MAAMO,GAAG,GAAG;UAAE,GAAGP;QAAM,CAAC;QACtBQ,OAAO,GAAG,MAAMZ,QAAQ,CAACa,IAAI,CAACF,GAAG,CAAC;MAEpC,OAAOC,OAAO;IAChB,CAAC,CAAC,OAAOE,CAAC,EAAE;MACV,OAAON,eAAe,CAACM,CAAC,CAAmB;IAC7C;EACF;AACF,CAAC,CACF;AAED,OAAO,MAAMC,WAAyD,GACpEjB,gBAAgB,CACd,2BAA2B,EAC3B,OAAOS,CAAC,YAAoC;EAAA,IAAlC;IAAEC,eAAe;IAAEC;EAAS,CAAC;EACrC,MAAML,KAAK,GAAIK,QAAQ,EAAE,CAAeC,WAAW,CAACN,KAAK;EAEzD,IAAIA,KAAK,EAAE;IACT,IAAI;MACF,MAAMO,GAAG,GAAG;UAAE,GAAGP;QAAM,CAAC;QACtBQ,OAAO,GAAG,MAAMZ,QAAQ,CAACgB,MAAM,CAACL,GAAG,CAAC;MAEtC,OAAOC,OAAO;IAChB,CAAC,CAAC,OAAOE,CAAC,EAAE;MACV,OAAON,eAAe,CAACM,CAAC,CAAmB;IAC7C;EACF;AACF,CAAC,CACF;AAEH,MAAMG,WAAW,GAAGpB,YAAY,CAACS,SAAS,CAACY,OAAO,CAACC,IAAI,CAAC;EACtDC,aAAa,GAAGvB,YAAY,CAAoBS,SAAS,CAACe,SAAS,CAACF,IAAI,CAAC;EACzEG,YAAY,GAAGzB,YAAY,CAAiBS,SAAS,CAACiB,QAAQ,CAACJ,IAAI,CAAC;EACpEK,aAAa,GAAG3B,YAAY,CAACkB,WAAW,CAACG,OAAO,CAACC,IAAI,CAAC;EACtDM,eAAe,GAAG5B,YAAY,CAACkB,WAAW,CAACM,SAAS,CAACF,IAAI,CAAC;EAC1DO,cAAc,GAAG7B,YAAY,CAAiBkB,WAAW,CAACQ,QAAQ,CAACJ,IAAI,CAAC;AAE1E,OAAO,MAAMQ,gBAAgB,GAAG5B,WAAW,CAAC;EAC1C6B,IAAI,EAAE,cAAc;EACpB1B,YAAY;EACZ2B,QAAQ,EAAE;IACRC,QAAQ,CAACC,KAAK,EAAEC,MAAmC,EAAE;MACnDD,KAAK,CAAC3B,KAAK,GAAG4B,MAAM,CAACC,OAAO;IAC9B;EACF,CAAC;EACDC,aAAa,EAAGC,OAAO,IACrBA,OAAO,CACJC,OAAO,CAACnB,WAAW,EAAGc,KAAK,IAAK;IAC/BA,KAAK,CAAC5B,SAAS,GAAG,IAAI;IACtB4B,KAAK,CAAC1B,KAAK,GAAG,IAAI;EACpB,CAAC,CAAC,CACD+B,OAAO,CAAChB,aAAa,EAAE,CAACW,KAAK,EAAEC,MAAM,KAAK;IACzCD,KAAK,CAAC5B,SAAS,GAAG,KAAK;IACvB,IAAI6B,MAAM,CAACC,OAAO,EAAE;MAClBF,KAAK,CAAC3B,KAAK,GAAG4B,MAAM,CAACC,OAAO;IAC9B;EACF,CAAC,CAAC,CACDG,OAAO,CAACd,YAAY,EAAE,CAACS,KAAK,EAAEC,MAAM,KAAK;IACxCD,KAAK,CAAC5B,SAAS,GAAG,KAAK;IACvB4B,KAAK,CAAC1B,KAAK,GAAG2B,MAAM,CAACC,OAAO;EAC9B,CAAC,CAAC,CACDG,OAAO,CAACZ,aAAa,EAAGO,KAAK,IAAK;IACjCA,KAAK,CAAC5B,SAAS,GAAG,IAAI;IACtB4B,KAAK,CAAC1B,KAAK,GAAG,IAAI;EACpB,CAAC,CAAC,CACD+B,OAAO,CAACX,eAAe,EAAGM,KAAK,IAAK;IACnCA,KAAK,CAAC5B,SAAS,GAAG,KAAK;IACvB4B,KAAK,CAAC3B,KAAK,GAAGH,WAAW,EAAE;EAC7B,CAAC,CAAC,CACDmC,OAAO,CAACV,cAAc,EAAE,CAACK,KAAK,EAAEC,MAAM,KAAK;IAC1CD,KAAK,CAAC5B,SAAS,GAAG,KAAK;IACvB4B,KAAK,CAAC1B,KAAK,GAAG2B,MAAM,CAACC,OAAO;EAC9B,CAAC;AACP,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEH;AAAS,CAAC,GAAGH,gBAAgB,CAACU,OAAO;AAEpD,OAAO,MAAMC,WAAW,GAAIP,KAAgB,IAAKA,KAAK,CAACrB,WAAW,CAACN,KAAK;AACxE,OAAO,MAAMmC,eAAe,GAAIR,KAAgB,IAC9CA,KAAK,CAACrB,WAAW,CAACP,SAAS;AAC7B,OAAO,MAAMqC,WAAW,GAAIT,KAAgB,IAAKA,KAAK,CAACrB,WAAW,CAACL,KAAK;AAExE,eAAesB,gBAAgB,CAACc,OAAO"}, "metadata": {}, "sourceType": "module"}