{"ast": null, "code": "const list = [\n// Native ES errors https://262.ecma-international.org/12.0/#sec-well-known-intrinsic-objects\n<PERSON><PERSON><PERSON><PERSON><PERSON>, RangeError, ReferenceError, SyntaxError, TypeError, URIError,\n// Built-in errors\nglobalThis.DOMException,\n// Node-specific errors\n// https://nodejs.org/api/errors.html\nglobalThis.AssertionError, globalThis.SystemError]\n// Non-native Errors are used with `globalThis` because they might be missing. This filter drops them when undefined.\n.filter(Boolean).map(constructor => [constructor.name, constructor]);\nconst errorConstructors = new Map(list);\nexport default errorConstructors;", "map": {"version": 3, "names": ["list", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RangeError", "ReferenceError", "SyntaxError", "TypeError", "URIError", "globalThis", "DOMException", "AssertionError", "SystemError", "filter", "Boolean", "map", "constructor", "name", "errorConstructors", "Map"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/node_modules/serialize-error/error-constructors.js"], "sourcesContent": ["const list = [\n\t// Native ES errors https://262.ecma-international.org/12.0/#sec-well-known-intrinsic-objects\n\t<PERSON><PERSON><PERSON><PERSON><PERSON>,\n\tRangeError,\n\tReferenceError,\n\tSyntaxError,\n\tTypeError,\n\tURIError,\n\n\t// Built-in errors\n\tglobalThis.DOMException,\n\n\t// Node-specific errors\n\t// https://nodejs.org/api/errors.html\n\tglobalThis.AssertionError,\n\tglobalThis.SystemError,\n]\n\t// Non-native Errors are used with `globalThis` because they might be missing. This filter drops them when undefined.\n\t.filter(Boolean)\n\t.map(\n\t\tconstructor => [constructor.name, constructor],\n\t);\n\nconst errorConstructors = new Map(list);\n\nexport default errorConstructors;\n"], "mappings": "AAAA,MAAMA,IAAI,GAAG;AACZ;AACAC,SAAS,EACTC,UAAU,EACVC,cAAc,EACdC,WAAW,EACXC,SAAS,EACTC,QAAQ;AAER;AACAC,UAAU,CAACC,YAAY;AAEvB;AACA;AACAD,UAAU,CAACE,cAAc,EACzBF,UAAU,CAACG,WAAW;AAEtB;AAAA,CACCC,MAAM,CAACC,OAAO,CAAC,CACfC,GAAG,CACHC,WAAW,IAAI,CAACA,WAAW,CAACC,IAAI,EAAED,WAAW,CAAC,CAC9C;AAEF,MAAME,iBAAiB,GAAG,IAAIC,GAAG,CAACjB,IAAI,CAAC;AAEvC,eAAegB,iBAAiB"}, "metadata": {}, "sourceType": "module"}