{"ast": null, "code": "import stringify from './stringify.js';\nimport parse from './parse.js';\nfunction stringToBytes(str) {\n  str = unescape(encodeURIComponent(str)); // UTF8 escape\n\n  var bytes = [];\n  for (var i = 0; i < str.length; ++i) {\n    bytes.push(str.charCodeAt(i));\n  }\n  return bytes;\n}\nexport var DNS = '6ba7b810-9dad-11d1-80b4-00c04fd430c8';\nexport var URL = '6ba7b811-9dad-11d1-80b4-00c04fd430c8';\nexport default function (name, version, hashfunc) {\n  function generateUUID(value, namespace, buf, offset) {\n    if (typeof value === 'string') {\n      value = stringToBytes(value);\n    }\n    if (typeof namespace === 'string') {\n      namespace = parse(namespace);\n    }\n    if (namespace.length !== 16) {\n      throw TypeError('Namespace must be array-like (16 iterable integer values, 0-255)');\n    } // Compute hash of namespace and value, Per 4.3\n    // Future: Use spread syntax when supported on all platforms, e.g. `bytes =\n    // hashfunc([...namespace, ... value])`\n\n    var bytes = new Uint8Array(16 + value.length);\n    bytes.set(namespace);\n    bytes.set(value, namespace.length);\n    bytes = hashfunc(bytes);\n    bytes[6] = bytes[6] & 0x0f | version;\n    bytes[8] = bytes[8] & 0x3f | 0x80;\n    if (buf) {\n      offset = offset || 0;\n      for (var i = 0; i < 16; ++i) {\n        buf[offset + i] = bytes[i];\n      }\n      return buf;\n    }\n    return stringify(bytes);\n  } // Function#name is not settable on some platforms (#270)\n\n  try {\n    generateUUID.name = name; // eslint-disable-next-line no-empty\n  } catch (err) {} // For CommonJS default export support\n\n  generateUUID.DNS = DNS;\n  generateUUID.URL = URL;\n  return generateUUID;\n}", "map": {"version": 3, "names": ["stringify", "parse", "stringToBytes", "str", "unescape", "encodeURIComponent", "bytes", "i", "length", "push", "charCodeAt", "DNS", "URL", "name", "version", "hashfunc", "generateUUID", "value", "namespace", "buf", "offset", "TypeError", "Uint8Array", "set", "err"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/node_modules/uuid/dist/esm-browser/v35.js"], "sourcesContent": ["import stringify from './stringify.js';\nimport parse from './parse.js';\n\nfunction stringToBytes(str) {\n  str = unescape(encodeURIComponent(str)); // UTF8 escape\n\n  var bytes = [];\n\n  for (var i = 0; i < str.length; ++i) {\n    bytes.push(str.charCodeAt(i));\n  }\n\n  return bytes;\n}\n\nexport var DNS = '6ba7b810-9dad-11d1-80b4-00c04fd430c8';\nexport var URL = '6ba7b811-9dad-11d1-80b4-00c04fd430c8';\nexport default function (name, version, hashfunc) {\n  function generateUUID(value, namespace, buf, offset) {\n    if (typeof value === 'string') {\n      value = stringToBytes(value);\n    }\n\n    if (typeof namespace === 'string') {\n      namespace = parse(namespace);\n    }\n\n    if (namespace.length !== 16) {\n      throw TypeError('Namespace must be array-like (16 iterable integer values, 0-255)');\n    } // Compute hash of namespace and value, Per 4.3\n    // Future: Use spread syntax when supported on all platforms, e.g. `bytes =\n    // hashfunc([...namespace, ... value])`\n\n\n    var bytes = new Uint8Array(16 + value.length);\n    bytes.set(namespace);\n    bytes.set(value, namespace.length);\n    bytes = hashfunc(bytes);\n    bytes[6] = bytes[6] & 0x0f | version;\n    bytes[8] = bytes[8] & 0x3f | 0x80;\n\n    if (buf) {\n      offset = offset || 0;\n\n      for (var i = 0; i < 16; ++i) {\n        buf[offset + i] = bytes[i];\n      }\n\n      return buf;\n    }\n\n    return stringify(bytes);\n  } // Function#name is not settable on some platforms (#270)\n\n\n  try {\n    generateUUID.name = name; // eslint-disable-next-line no-empty\n  } catch (err) {} // For CommonJS default export support\n\n\n  generateUUID.DNS = DNS;\n  generateUUID.URL = URL;\n  return generateUUID;\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;AACtC,OAAOC,KAAK,MAAM,YAAY;AAE9B,SAASC,aAAa,CAACC,GAAG,EAAE;EAC1BA,GAAG,GAAGC,QAAQ,CAACC,kBAAkB,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC;;EAEzC,IAAIG,KAAK,GAAG,EAAE;EAEd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,GAAG,CAACK,MAAM,EAAE,EAAED,CAAC,EAAE;IACnCD,KAAK,CAACG,IAAI,CAACN,GAAG,CAACO,UAAU,CAACH,CAAC,CAAC,CAAC;EAC/B;EAEA,OAAOD,KAAK;AACd;AAEA,OAAO,IAAIK,GAAG,GAAG,sCAAsC;AACvD,OAAO,IAAIC,GAAG,GAAG,sCAAsC;AACvD,eAAe,UAAUC,IAAI,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EAChD,SAASC,YAAY,CAACC,KAAK,EAAEC,SAAS,EAAEC,GAAG,EAAEC,MAAM,EAAE;IACnD,IAAI,OAAOH,KAAK,KAAK,QAAQ,EAAE;MAC7BA,KAAK,GAAGf,aAAa,CAACe,KAAK,CAAC;IAC9B;IAEA,IAAI,OAAOC,SAAS,KAAK,QAAQ,EAAE;MACjCA,SAAS,GAAGjB,KAAK,CAACiB,SAAS,CAAC;IAC9B;IAEA,IAAIA,SAAS,CAACV,MAAM,KAAK,EAAE,EAAE;MAC3B,MAAMa,SAAS,CAAC,kEAAkE,CAAC;IACrF,CAAC,CAAC;IACF;IACA;;IAGA,IAAIf,KAAK,GAAG,IAAIgB,UAAU,CAAC,EAAE,GAAGL,KAAK,CAACT,MAAM,CAAC;IAC7CF,KAAK,CAACiB,GAAG,CAACL,SAAS,CAAC;IACpBZ,KAAK,CAACiB,GAAG,CAACN,KAAK,EAAEC,SAAS,CAACV,MAAM,CAAC;IAClCF,KAAK,GAAGS,QAAQ,CAACT,KAAK,CAAC;IACvBA,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAGQ,OAAO;IACpCR,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI;IAEjC,IAAIa,GAAG,EAAE;MACPC,MAAM,GAAGA,MAAM,IAAI,CAAC;MAEpB,KAAK,IAAIb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EAAE;QAC3BY,GAAG,CAACC,MAAM,GAAGb,CAAC,CAAC,GAAGD,KAAK,CAACC,CAAC,CAAC;MAC5B;MAEA,OAAOY,GAAG;IACZ;IAEA,OAAOnB,SAAS,CAACM,KAAK,CAAC;EACzB,CAAC,CAAC;;EAGF,IAAI;IACFU,YAAY,CAACH,IAAI,GAAGA,IAAI,CAAC,CAAC;EAC5B,CAAC,CAAC,OAAOW,GAAG,EAAE,CAAC,CAAC,CAAC;;EAGjBR,YAAY,CAACL,GAAG,GAAGA,GAAG;EACtBK,YAAY,CAACJ,GAAG,GAAGA,GAAG;EACtB,OAAOI,YAAY;AACrB"}, "metadata": {}, "sourceType": "module"}