{"ast": null, "code": "function ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nfunction _wrapRegExp() {\n  _wrapRegExp = function (re, groups) {\n    return new BabelRegExp(re, void 0, groups);\n  };\n  var _super = RegExp.prototype,\n    _groups = new WeakMap();\n  function BabelRegExp(re, flags, groups) {\n    var _this = new RegExp(re, flags);\n    return _groups.set(_this, groups || _groups.get(re)), _setPrototypeOf(_this, BabelRegExp.prototype);\n  }\n  function buildGroups(result, re) {\n    var g = _groups.get(re);\n    return Object.keys(g).reduce(function (groups, name) {\n      return groups[name] = result[g[name]], groups;\n    }, Object.create(null));\n  }\n  return _inherits(BabelRegExp, RegExp), BabelRegExp.prototype.exec = function (str) {\n    var result = _super.exec.call(this, str);\n    return result && (result.groups = buildGroups(result, this)), result;\n  }, BabelRegExp.prototype[Symbol.replace] = function (str, substitution) {\n    if (\"string\" == typeof substitution) {\n      var groups = _groups.get(this);\n      return _super[Symbol.replace].call(this, str, substitution.replace(/\\$<([^>]+)>/g, function (_, name) {\n        return \"$\" + groups[name];\n      }));\n    }\n    if (\"function\" == typeof substitution) {\n      var _this = this;\n      return _super[Symbol.replace].call(this, str, function () {\n        var args = arguments;\n        return \"object\" != typeof args[args.length - 1] && (args = [].slice.call(args)).push(buildGroups(args, _this)), substitution.apply(this, args);\n      });\n    }\n    return _super[Symbol.replace].call(this, str, substitution);\n  }, _wrapRegExp.apply(this, arguments);\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _s, _e;\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n  return _arr;\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nvar noop = function noop() {};\nvar _WINDOW = {};\nvar _DOCUMENT = {};\nvar _MUTATION_OBSERVER = null;\nvar _PERFORMANCE = {\n  mark: noop,\n  measure: noop\n};\ntry {\n  if (typeof window !== 'undefined') _WINDOW = window;\n  if (typeof document !== 'undefined') _DOCUMENT = document;\n  if (typeof MutationObserver !== 'undefined') _MUTATION_OBSERVER = MutationObserver;\n  if (typeof performance !== 'undefined') _PERFORMANCE = performance;\n} catch (e) {}\nvar _ref = _WINDOW.navigator || {},\n  _ref$userAgent = _ref.userAgent,\n  userAgent = _ref$userAgent === void 0 ? '' : _ref$userAgent;\nvar WINDOW = _WINDOW;\nvar DOCUMENT = _DOCUMENT;\nvar MUTATION_OBSERVER = _MUTATION_OBSERVER;\nvar PERFORMANCE = _PERFORMANCE;\nvar IS_BROWSER = !!WINDOW.document;\nvar IS_DOM = !!DOCUMENT.documentElement && !!DOCUMENT.head && typeof DOCUMENT.addEventListener === 'function' && typeof DOCUMENT.createElement === 'function';\nvar IS_IE = ~userAgent.indexOf('MSIE') || ~userAgent.indexOf('Trident/');\nvar _familyProxy, _familyProxy2, _familyProxy3, _familyProxy4, _familyProxy5;\nvar NAMESPACE_IDENTIFIER = '___FONT_AWESOME___';\nvar UNITS_IN_GRID = 16;\nvar DEFAULT_CSS_PREFIX = 'fa';\nvar DEFAULT_REPLACEMENT_CLASS = 'svg-inline--fa';\nvar DATA_FA_I2SVG = 'data-fa-i2svg';\nvar DATA_FA_PSEUDO_ELEMENT = 'data-fa-pseudo-element';\nvar DATA_FA_PSEUDO_ELEMENT_PENDING = 'data-fa-pseudo-element-pending';\nvar DATA_PREFIX = 'data-prefix';\nvar DATA_ICON = 'data-icon';\nvar HTML_CLASS_I2SVG_BASE_CLASS = 'fontawesome-i2svg';\nvar MUTATION_APPROACH_ASYNC = 'async';\nvar TAGNAMES_TO_SKIP_FOR_PSEUDOELEMENTS = ['HTML', 'HEAD', 'STYLE', 'SCRIPT'];\nvar PRODUCTION = function () {\n  try {\n    return process.env.NODE_ENV === 'production';\n  } catch (e) {\n    return false;\n  }\n}();\nvar FAMILY_CLASSIC = 'classic';\nvar FAMILY_SHARP = 'sharp';\nvar FAMILIES = [FAMILY_CLASSIC, FAMILY_SHARP];\nfunction familyProxy(obj) {\n  // Defaults to the classic family if family is not available\n  return new Proxy(obj, {\n    get: function get(target, prop) {\n      return prop in target ? target[prop] : target[FAMILY_CLASSIC];\n    }\n  });\n}\nvar PREFIX_TO_STYLE = familyProxy((_familyProxy = {}, _defineProperty(_familyProxy, FAMILY_CLASSIC, {\n  'fa': 'solid',\n  'fas': 'solid',\n  'fa-solid': 'solid',\n  'far': 'regular',\n  'fa-regular': 'regular',\n  'fal': 'light',\n  'fa-light': 'light',\n  'fat': 'thin',\n  'fa-thin': 'thin',\n  'fad': 'duotone',\n  'fa-duotone': 'duotone',\n  'fab': 'brands',\n  'fa-brands': 'brands',\n  'fak': 'kit',\n  'fakd': 'kit',\n  'fa-kit': 'kit',\n  'fa-kit-duotone': 'kit'\n}), _defineProperty(_familyProxy, FAMILY_SHARP, {\n  'fa': 'solid',\n  'fass': 'solid',\n  'fa-solid': 'solid',\n  'fasr': 'regular',\n  'fa-regular': 'regular',\n  'fasl': 'light',\n  'fa-light': 'light',\n  'fast': 'thin',\n  'fa-thin': 'thin'\n}), _familyProxy));\nvar STYLE_TO_PREFIX = familyProxy((_familyProxy2 = {}, _defineProperty(_familyProxy2, FAMILY_CLASSIC, {\n  solid: 'fas',\n  regular: 'far',\n  light: 'fal',\n  thin: 'fat',\n  duotone: 'fad',\n  brands: 'fab',\n  kit: 'fak'\n}), _defineProperty(_familyProxy2, FAMILY_SHARP, {\n  solid: 'fass',\n  regular: 'fasr',\n  light: 'fasl',\n  thin: 'fast'\n}), _familyProxy2));\nvar PREFIX_TO_LONG_STYLE = familyProxy((_familyProxy3 = {}, _defineProperty(_familyProxy3, FAMILY_CLASSIC, {\n  fab: 'fa-brands',\n  fad: 'fa-duotone',\n  fak: 'fa-kit',\n  fal: 'fa-light',\n  far: 'fa-regular',\n  fas: 'fa-solid',\n  fat: 'fa-thin'\n}), _defineProperty(_familyProxy3, FAMILY_SHARP, {\n  fass: 'fa-solid',\n  fasr: 'fa-regular',\n  fasl: 'fa-light',\n  fast: 'fa-thin'\n}), _familyProxy3));\nvar LONG_STYLE_TO_PREFIX = familyProxy((_familyProxy4 = {}, _defineProperty(_familyProxy4, FAMILY_CLASSIC, {\n  'fa-brands': 'fab',\n  'fa-duotone': 'fad',\n  'fa-kit': 'fak',\n  'fa-light': 'fal',\n  'fa-regular': 'far',\n  'fa-solid': 'fas',\n  'fa-thin': 'fat'\n}), _defineProperty(_familyProxy4, FAMILY_SHARP, {\n  'fa-solid': 'fass',\n  'fa-regular': 'fasr',\n  'fa-light': 'fasl',\n  'fa-thin': 'fast'\n}), _familyProxy4));\nvar ICON_SELECTION_SYNTAX_PATTERN = /fa(s|r|l|t|d|b|k|ss|sr|sl|st)?[\\-\\ ]/; // eslint-disable-line no-useless-escape\n\nvar LAYERS_TEXT_CLASSNAME = 'fa-layers-text';\nvar FONT_FAMILY_PATTERN = /Font ?Awesome ?([56 ]*)(Solid|Regular|Light|Thin|Duotone|Brands|Free|Pro|Sharp|Kit)?.*/i;\nvar FONT_WEIGHT_TO_PREFIX = familyProxy((_familyProxy5 = {}, _defineProperty(_familyProxy5, FAMILY_CLASSIC, {\n  900: 'fas',\n  400: 'far',\n  normal: 'far',\n  300: 'fal',\n  100: 'fat'\n}), _defineProperty(_familyProxy5, FAMILY_SHARP, {\n  900: 'fass',\n  400: 'fasr',\n  300: 'fasl',\n  100: 'fast'\n}), _familyProxy5));\nvar oneToTen = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];\nvar oneToTwenty = oneToTen.concat([11, 12, 13, 14, 15, 16, 17, 18, 19, 20]);\nvar ATTRIBUTES_WATCHED_FOR_MUTATION = ['class', 'data-prefix', 'data-icon', 'data-fa-transform', 'data-fa-mask'];\nvar DUOTONE_CLASSES = {\n  GROUP: 'duotone-group',\n  SWAP_OPACITY: 'swap-opacity',\n  PRIMARY: 'primary',\n  SECONDARY: 'secondary'\n};\nvar prefixes = new Set();\nObject.keys(STYLE_TO_PREFIX[FAMILY_CLASSIC]).map(prefixes.add.bind(prefixes));\nObject.keys(STYLE_TO_PREFIX[FAMILY_SHARP]).map(prefixes.add.bind(prefixes));\nvar RESERVED_CLASSES = [].concat(FAMILIES, _toConsumableArray(prefixes), ['2xs', 'xs', 'sm', 'lg', 'xl', '2xl', 'beat', 'border', 'fade', 'beat-fade', 'bounce', 'flip-both', 'flip-horizontal', 'flip-vertical', 'flip', 'fw', 'inverse', 'layers-counter', 'layers-text', 'layers', 'li', 'pull-left', 'pull-right', 'pulse', 'rotate-180', 'rotate-270', 'rotate-90', 'rotate-by', 'shake', 'spin-pulse', 'spin-reverse', 'spin', 'stack-1x', 'stack-2x', 'stack', 'ul', DUOTONE_CLASSES.GROUP, DUOTONE_CLASSES.SWAP_OPACITY, DUOTONE_CLASSES.PRIMARY, DUOTONE_CLASSES.SECONDARY]).concat(oneToTen.map(function (n) {\n  return \"\".concat(n, \"x\");\n})).concat(oneToTwenty.map(function (n) {\n  return \"w-\".concat(n);\n}));\nvar initial = WINDOW.FontAwesomeConfig || {};\nfunction getAttrConfig(attr) {\n  var element = DOCUMENT.querySelector('script[' + attr + ']');\n  if (element) {\n    return element.getAttribute(attr);\n  }\n}\nfunction coerce(val) {\n  // Getting an empty string will occur if the attribute is set on the HTML tag but without a value\n  // We'll assume that this is an indication that it should be toggled to true\n  if (val === '') return true;\n  if (val === 'false') return false;\n  if (val === 'true') return true;\n  return val;\n}\nif (DOCUMENT && typeof DOCUMENT.querySelector === 'function') {\n  var attrs = [['data-family-prefix', 'familyPrefix'], ['data-css-prefix', 'cssPrefix'], ['data-family-default', 'familyDefault'], ['data-style-default', 'styleDefault'], ['data-replacement-class', 'replacementClass'], ['data-auto-replace-svg', 'autoReplaceSvg'], ['data-auto-add-css', 'autoAddCss'], ['data-auto-a11y', 'autoA11y'], ['data-search-pseudo-elements', 'searchPseudoElements'], ['data-observe-mutations', 'observeMutations'], ['data-mutate-approach', 'mutateApproach'], ['data-keep-original-source', 'keepOriginalSource'], ['data-measure-performance', 'measurePerformance'], ['data-show-missing-icons', 'showMissingIcons']];\n  attrs.forEach(function (_ref) {\n    var _ref2 = _slicedToArray(_ref, 2),\n      attr = _ref2[0],\n      key = _ref2[1];\n    var val = coerce(getAttrConfig(attr));\n    if (val !== undefined && val !== null) {\n      initial[key] = val;\n    }\n  });\n}\nvar _default = {\n  styleDefault: 'solid',\n  familyDefault: 'classic',\n  cssPrefix: DEFAULT_CSS_PREFIX,\n  replacementClass: DEFAULT_REPLACEMENT_CLASS,\n  autoReplaceSvg: true,\n  autoAddCss: true,\n  autoA11y: true,\n  searchPseudoElements: false,\n  observeMutations: true,\n  mutateApproach: 'async',\n  keepOriginalSource: true,\n  measurePerformance: false,\n  showMissingIcons: true\n}; // familyPrefix is deprecated but we must still support it if present\n\nif (initial.familyPrefix) {\n  initial.cssPrefix = initial.familyPrefix;\n}\nvar _config = _objectSpread2(_objectSpread2({}, _default), initial);\nif (!_config.autoReplaceSvg) _config.observeMutations = false;\nvar config = {};\nObject.keys(_default).forEach(function (key) {\n  Object.defineProperty(config, key, {\n    enumerable: true,\n    set: function set(val) {\n      _config[key] = val;\n      _onChangeCb.forEach(function (cb) {\n        return cb(config);\n      });\n    },\n    get: function get() {\n      return _config[key];\n    }\n  });\n}); // familyPrefix is deprecated as of 6.2.0 and should be removed in 7.0.0\n\nObject.defineProperty(config, 'familyPrefix', {\n  enumerable: true,\n  set: function set(val) {\n    _config.cssPrefix = val;\n    _onChangeCb.forEach(function (cb) {\n      return cb(config);\n    });\n  },\n  get: function get() {\n    return _config.cssPrefix;\n  }\n});\nWINDOW.FontAwesomeConfig = config;\nvar _onChangeCb = [];\nfunction onChange(cb) {\n  _onChangeCb.push(cb);\n  return function () {\n    _onChangeCb.splice(_onChangeCb.indexOf(cb), 1);\n  };\n}\nvar d = UNITS_IN_GRID;\nvar meaninglessTransform = {\n  size: 16,\n  x: 0,\n  y: 0,\n  rotate: 0,\n  flipX: false,\n  flipY: false\n};\nfunction insertCss(css) {\n  if (!css || !IS_DOM) {\n    return;\n  }\n  var style = DOCUMENT.createElement('style');\n  style.setAttribute('type', 'text/css');\n  style.innerHTML = css;\n  var headChildren = DOCUMENT.head.childNodes;\n  var beforeChild = null;\n  for (var i = headChildren.length - 1; i > -1; i--) {\n    var child = headChildren[i];\n    var tagName = (child.tagName || '').toUpperCase();\n    if (['STYLE', 'LINK'].indexOf(tagName) > -1) {\n      beforeChild = child;\n    }\n  }\n  DOCUMENT.head.insertBefore(style, beforeChild);\n  return css;\n}\nvar idPool = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';\nfunction nextUniqueId() {\n  var size = 12;\n  var id = '';\n  while (size-- > 0) {\n    id += idPool[Math.random() * 62 | 0];\n  }\n  return id;\n}\nfunction toArray(obj) {\n  var array = [];\n  for (var i = (obj || []).length >>> 0; i--;) {\n    array[i] = obj[i];\n  }\n  return array;\n}\nfunction classArray(node) {\n  if (node.classList) {\n    return toArray(node.classList);\n  } else {\n    return (node.getAttribute('class') || '').split(' ').filter(function (i) {\n      return i;\n    });\n  }\n}\nfunction htmlEscape(str) {\n  return \"\".concat(str).replace(/&/g, '&amp;').replace(/\"/g, '&quot;').replace(/'/g, '&#39;').replace(/</g, '&lt;').replace(/>/g, '&gt;');\n}\nfunction joinAttributes(attributes) {\n  return Object.keys(attributes || {}).reduce(function (acc, attributeName) {\n    return acc + \"\".concat(attributeName, \"=\\\"\").concat(htmlEscape(attributes[attributeName]), \"\\\" \");\n  }, '').trim();\n}\nfunction joinStyles(styles) {\n  return Object.keys(styles || {}).reduce(function (acc, styleName) {\n    return acc + \"\".concat(styleName, \": \").concat(styles[styleName].trim(), \";\");\n  }, '');\n}\nfunction transformIsMeaningful(transform) {\n  return transform.size !== meaninglessTransform.size || transform.x !== meaninglessTransform.x || transform.y !== meaninglessTransform.y || transform.rotate !== meaninglessTransform.rotate || transform.flipX || transform.flipY;\n}\nfunction transformForSvg(_ref) {\n  var transform = _ref.transform,\n    containerWidth = _ref.containerWidth,\n    iconWidth = _ref.iconWidth;\n  var outer = {\n    transform: \"translate(\".concat(containerWidth / 2, \" 256)\")\n  };\n  var innerTranslate = \"translate(\".concat(transform.x * 32, \", \").concat(transform.y * 32, \") \");\n  var innerScale = \"scale(\".concat(transform.size / 16 * (transform.flipX ? -1 : 1), \", \").concat(transform.size / 16 * (transform.flipY ? -1 : 1), \") \");\n  var innerRotate = \"rotate(\".concat(transform.rotate, \" 0 0)\");\n  var inner = {\n    transform: \"\".concat(innerTranslate, \" \").concat(innerScale, \" \").concat(innerRotate)\n  };\n  var path = {\n    transform: \"translate(\".concat(iconWidth / 2 * -1, \" -256)\")\n  };\n  return {\n    outer: outer,\n    inner: inner,\n    path: path\n  };\n}\nfunction transformForCss(_ref2) {\n  var transform = _ref2.transform,\n    _ref2$width = _ref2.width,\n    width = _ref2$width === void 0 ? UNITS_IN_GRID : _ref2$width,\n    _ref2$height = _ref2.height,\n    height = _ref2$height === void 0 ? UNITS_IN_GRID : _ref2$height,\n    _ref2$startCentered = _ref2.startCentered,\n    startCentered = _ref2$startCentered === void 0 ? false : _ref2$startCentered;\n  var val = '';\n  if (startCentered && IS_IE) {\n    val += \"translate(\".concat(transform.x / d - width / 2, \"em, \").concat(transform.y / d - height / 2, \"em) \");\n  } else if (startCentered) {\n    val += \"translate(calc(-50% + \".concat(transform.x / d, \"em), calc(-50% + \").concat(transform.y / d, \"em)) \");\n  } else {\n    val += \"translate(\".concat(transform.x / d, \"em, \").concat(transform.y / d, \"em) \");\n  }\n  val += \"scale(\".concat(transform.size / d * (transform.flipX ? -1 : 1), \", \").concat(transform.size / d * (transform.flipY ? -1 : 1), \") \");\n  val += \"rotate(\".concat(transform.rotate, \"deg) \");\n  return val;\n}\nvar baseStyles = \":root, :host {\\n  --fa-font-solid: normal 900 1em/1 \\\"Font Awesome 6 Solid\\\";\\n  --fa-font-regular: normal 400 1em/1 \\\"Font Awesome 6 Regular\\\";\\n  --fa-font-light: normal 300 1em/1 \\\"Font Awesome 6 Light\\\";\\n  --fa-font-thin: normal 100 1em/1 \\\"Font Awesome 6 Thin\\\";\\n  --fa-font-duotone: normal 900 1em/1 \\\"Font Awesome 6 Duotone\\\";\\n  --fa-font-sharp-solid: normal 900 1em/1 \\\"Font Awesome 6 Sharp\\\";\\n  --fa-font-sharp-regular: normal 400 1em/1 \\\"Font Awesome 6 Sharp\\\";\\n  --fa-font-sharp-light: normal 300 1em/1 \\\"Font Awesome 6 Sharp\\\";\\n  --fa-font-sharp-thin: normal 100 1em/1 \\\"Font Awesome 6 Sharp\\\";\\n  --fa-font-brands: normal 400 1em/1 \\\"Font Awesome 6 Brands\\\";\\n}\\n\\nsvg:not(:root).svg-inline--fa, svg:not(:host).svg-inline--fa {\\n  overflow: visible;\\n  box-sizing: content-box;\\n}\\n\\n.svg-inline--fa {\\n  display: var(--fa-display, inline-block);\\n  height: 1em;\\n  overflow: visible;\\n  vertical-align: -0.125em;\\n}\\n.svg-inline--fa.fa-2xs {\\n  vertical-align: 0.1em;\\n}\\n.svg-inline--fa.fa-xs {\\n  vertical-align: 0em;\\n}\\n.svg-inline--fa.fa-sm {\\n  vertical-align: -0.0714285705em;\\n}\\n.svg-inline--fa.fa-lg {\\n  vertical-align: -0.2em;\\n}\\n.svg-inline--fa.fa-xl {\\n  vertical-align: -0.25em;\\n}\\n.svg-inline--fa.fa-2xl {\\n  vertical-align: -0.3125em;\\n}\\n.svg-inline--fa.fa-pull-left {\\n  margin-right: var(--fa-pull-margin, 0.3em);\\n  width: auto;\\n}\\n.svg-inline--fa.fa-pull-right {\\n  margin-left: var(--fa-pull-margin, 0.3em);\\n  width: auto;\\n}\\n.svg-inline--fa.fa-li {\\n  width: var(--fa-li-width, 2em);\\n  top: 0.25em;\\n}\\n.svg-inline--fa.fa-fw {\\n  width: var(--fa-fw-width, 1.25em);\\n}\\n\\n.fa-layers svg.svg-inline--fa {\\n  bottom: 0;\\n  left: 0;\\n  margin: auto;\\n  position: absolute;\\n  right: 0;\\n  top: 0;\\n}\\n\\n.fa-layers-counter, .fa-layers-text {\\n  display: inline-block;\\n  position: absolute;\\n  text-align: center;\\n}\\n\\n.fa-layers {\\n  display: inline-block;\\n  height: 1em;\\n  position: relative;\\n  text-align: center;\\n  vertical-align: -0.125em;\\n  width: 1em;\\n}\\n.fa-layers svg.svg-inline--fa {\\n  -webkit-transform-origin: center center;\\n          transform-origin: center center;\\n}\\n\\n.fa-layers-text {\\n  left: 50%;\\n  top: 50%;\\n  -webkit-transform: translate(-50%, -50%);\\n          transform: translate(-50%, -50%);\\n  -webkit-transform-origin: center center;\\n          transform-origin: center center;\\n}\\n\\n.fa-layers-counter {\\n  background-color: var(--fa-counter-background-color, #ff253a);\\n  border-radius: var(--fa-counter-border-radius, 1em);\\n  box-sizing: border-box;\\n  color: var(--fa-inverse, #fff);\\n  line-height: var(--fa-counter-line-height, 1);\\n  max-width: var(--fa-counter-max-width, 5em);\\n  min-width: var(--fa-counter-min-width, 1.5em);\\n  overflow: hidden;\\n  padding: var(--fa-counter-padding, 0.25em 0.5em);\\n  right: var(--fa-right, 0);\\n  text-overflow: ellipsis;\\n  top: var(--fa-top, 0);\\n  -webkit-transform: scale(var(--fa-counter-scale, 0.25));\\n          transform: scale(var(--fa-counter-scale, 0.25));\\n  -webkit-transform-origin: top right;\\n          transform-origin: top right;\\n}\\n\\n.fa-layers-bottom-right {\\n  bottom: var(--fa-bottom, 0);\\n  right: var(--fa-right, 0);\\n  top: auto;\\n  -webkit-transform: scale(var(--fa-layers-scale, 0.25));\\n          transform: scale(var(--fa-layers-scale, 0.25));\\n  -webkit-transform-origin: bottom right;\\n          transform-origin: bottom right;\\n}\\n\\n.fa-layers-bottom-left {\\n  bottom: var(--fa-bottom, 0);\\n  left: var(--fa-left, 0);\\n  right: auto;\\n  top: auto;\\n  -webkit-transform: scale(var(--fa-layers-scale, 0.25));\\n          transform: scale(var(--fa-layers-scale, 0.25));\\n  -webkit-transform-origin: bottom left;\\n          transform-origin: bottom left;\\n}\\n\\n.fa-layers-top-right {\\n  top: var(--fa-top, 0);\\n  right: var(--fa-right, 0);\\n  -webkit-transform: scale(var(--fa-layers-scale, 0.25));\\n          transform: scale(var(--fa-layers-scale, 0.25));\\n  -webkit-transform-origin: top right;\\n          transform-origin: top right;\\n}\\n\\n.fa-layers-top-left {\\n  left: var(--fa-left, 0);\\n  right: auto;\\n  top: var(--fa-top, 0);\\n  -webkit-transform: scale(var(--fa-layers-scale, 0.25));\\n          transform: scale(var(--fa-layers-scale, 0.25));\\n  -webkit-transform-origin: top left;\\n          transform-origin: top left;\\n}\\n\\n.fa-1x {\\n  font-size: 1em;\\n}\\n\\n.fa-2x {\\n  font-size: 2em;\\n}\\n\\n.fa-3x {\\n  font-size: 3em;\\n}\\n\\n.fa-4x {\\n  font-size: 4em;\\n}\\n\\n.fa-5x {\\n  font-size: 5em;\\n}\\n\\n.fa-6x {\\n  font-size: 6em;\\n}\\n\\n.fa-7x {\\n  font-size: 7em;\\n}\\n\\n.fa-8x {\\n  font-size: 8em;\\n}\\n\\n.fa-9x {\\n  font-size: 9em;\\n}\\n\\n.fa-10x {\\n  font-size: 10em;\\n}\\n\\n.fa-2xs {\\n  font-size: 0.625em;\\n  line-height: 0.1em;\\n  vertical-align: 0.225em;\\n}\\n\\n.fa-xs {\\n  font-size: 0.75em;\\n  line-height: 0.0833333337em;\\n  vertical-align: 0.125em;\\n}\\n\\n.fa-sm {\\n  font-size: 0.875em;\\n  line-height: 0.0714285718em;\\n  vertical-align: 0.0535714295em;\\n}\\n\\n.fa-lg {\\n  font-size: 1.25em;\\n  line-height: 0.05em;\\n  vertical-align: -0.075em;\\n}\\n\\n.fa-xl {\\n  font-size: 1.5em;\\n  line-height: 0.0416666682em;\\n  vertical-align: -0.125em;\\n}\\n\\n.fa-2xl {\\n  font-size: 2em;\\n  line-height: 0.03125em;\\n  vertical-align: -0.1875em;\\n}\\n\\n.fa-fw {\\n  text-align: center;\\n  width: 1.25em;\\n}\\n\\n.fa-ul {\\n  list-style-type: none;\\n  margin-left: var(--fa-li-margin, 2.5em);\\n  padding-left: 0;\\n}\\n.fa-ul > li {\\n  position: relative;\\n}\\n\\n.fa-li {\\n  left: calc(var(--fa-li-width, 2em) * -1);\\n  position: absolute;\\n  text-align: center;\\n  width: var(--fa-li-width, 2em);\\n  line-height: inherit;\\n}\\n\\n.fa-border {\\n  border-color: var(--fa-border-color, #eee);\\n  border-radius: var(--fa-border-radius, 0.1em);\\n  border-style: var(--fa-border-style, solid);\\n  border-width: var(--fa-border-width, 0.08em);\\n  padding: var(--fa-border-padding, 0.2em 0.25em 0.15em);\\n}\\n\\n.fa-pull-left {\\n  float: left;\\n  margin-right: var(--fa-pull-margin, 0.3em);\\n}\\n\\n.fa-pull-right {\\n  float: right;\\n  margin-left: var(--fa-pull-margin, 0.3em);\\n}\\n\\n.fa-beat {\\n  -webkit-animation-name: fa-beat;\\n          animation-name: fa-beat;\\n  -webkit-animation-delay: var(--fa-animation-delay, 0s);\\n          animation-delay: var(--fa-animation-delay, 0s);\\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\\n          animation-direction: var(--fa-animation-direction, normal);\\n  -webkit-animation-duration: var(--fa-animation-duration, 1s);\\n          animation-duration: var(--fa-animation-duration, 1s);\\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  -webkit-animation-timing-function: var(--fa-animation-timing, ease-in-out);\\n          animation-timing-function: var(--fa-animation-timing, ease-in-out);\\n}\\n\\n.fa-bounce {\\n  -webkit-animation-name: fa-bounce;\\n          animation-name: fa-bounce;\\n  -webkit-animation-delay: var(--fa-animation-delay, 0s);\\n          animation-delay: var(--fa-animation-delay, 0s);\\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\\n          animation-direction: var(--fa-animation-direction, normal);\\n  -webkit-animation-duration: var(--fa-animation-duration, 1s);\\n          animation-duration: var(--fa-animation-duration, 1s);\\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  -webkit-animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1));\\n          animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1));\\n}\\n\\n.fa-fade {\\n  -webkit-animation-name: fa-fade;\\n          animation-name: fa-fade;\\n  -webkit-animation-delay: var(--fa-animation-delay, 0s);\\n          animation-delay: var(--fa-animation-delay, 0s);\\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\\n          animation-direction: var(--fa-animation-direction, normal);\\n  -webkit-animation-duration: var(--fa-animation-duration, 1s);\\n          animation-duration: var(--fa-animation-duration, 1s);\\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  -webkit-animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\\n          animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\\n}\\n\\n.fa-beat-fade {\\n  -webkit-animation-name: fa-beat-fade;\\n          animation-name: fa-beat-fade;\\n  -webkit-animation-delay: var(--fa-animation-delay, 0s);\\n          animation-delay: var(--fa-animation-delay, 0s);\\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\\n          animation-direction: var(--fa-animation-direction, normal);\\n  -webkit-animation-duration: var(--fa-animation-duration, 1s);\\n          animation-duration: var(--fa-animation-duration, 1s);\\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  -webkit-animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\\n          animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\\n}\\n\\n.fa-flip {\\n  -webkit-animation-name: fa-flip;\\n          animation-name: fa-flip;\\n  -webkit-animation-delay: var(--fa-animation-delay, 0s);\\n          animation-delay: var(--fa-animation-delay, 0s);\\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\\n          animation-direction: var(--fa-animation-direction, normal);\\n  -webkit-animation-duration: var(--fa-animation-duration, 1s);\\n          animation-duration: var(--fa-animation-duration, 1s);\\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  -webkit-animation-timing-function: var(--fa-animation-timing, ease-in-out);\\n          animation-timing-function: var(--fa-animation-timing, ease-in-out);\\n}\\n\\n.fa-shake {\\n  -webkit-animation-name: fa-shake;\\n          animation-name: fa-shake;\\n  -webkit-animation-delay: var(--fa-animation-delay, 0s);\\n          animation-delay: var(--fa-animation-delay, 0s);\\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\\n          animation-direction: var(--fa-animation-direction, normal);\\n  -webkit-animation-duration: var(--fa-animation-duration, 1s);\\n          animation-duration: var(--fa-animation-duration, 1s);\\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  -webkit-animation-timing-function: var(--fa-animation-timing, linear);\\n          animation-timing-function: var(--fa-animation-timing, linear);\\n}\\n\\n.fa-spin {\\n  -webkit-animation-name: fa-spin;\\n          animation-name: fa-spin;\\n  -webkit-animation-delay: var(--fa-animation-delay, 0s);\\n          animation-delay: var(--fa-animation-delay, 0s);\\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\\n          animation-direction: var(--fa-animation-direction, normal);\\n  -webkit-animation-duration: var(--fa-animation-duration, 2s);\\n          animation-duration: var(--fa-animation-duration, 2s);\\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  -webkit-animation-timing-function: var(--fa-animation-timing, linear);\\n          animation-timing-function: var(--fa-animation-timing, linear);\\n}\\n\\n.fa-spin-reverse {\\n  --fa-animation-direction: reverse;\\n}\\n\\n.fa-pulse,\\n.fa-spin-pulse {\\n  -webkit-animation-name: fa-spin;\\n          animation-name: fa-spin;\\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\\n          animation-direction: var(--fa-animation-direction, normal);\\n  -webkit-animation-duration: var(--fa-animation-duration, 1s);\\n          animation-duration: var(--fa-animation-duration, 1s);\\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  -webkit-animation-timing-function: var(--fa-animation-timing, steps(8));\\n          animation-timing-function: var(--fa-animation-timing, steps(8));\\n}\\n\\n@media (prefers-reduced-motion: reduce) {\\n  .fa-beat,\\n.fa-bounce,\\n.fa-fade,\\n.fa-beat-fade,\\n.fa-flip,\\n.fa-pulse,\\n.fa-shake,\\n.fa-spin,\\n.fa-spin-pulse {\\n    -webkit-animation-delay: -1ms;\\n            animation-delay: -1ms;\\n    -webkit-animation-duration: 1ms;\\n            animation-duration: 1ms;\\n    -webkit-animation-iteration-count: 1;\\n            animation-iteration-count: 1;\\n    -webkit-transition-delay: 0s;\\n            transition-delay: 0s;\\n    -webkit-transition-duration: 0s;\\n            transition-duration: 0s;\\n  }\\n}\\n@-webkit-keyframes fa-beat {\\n  0%, 90% {\\n    -webkit-transform: scale(1);\\n            transform: scale(1);\\n  }\\n  45% {\\n    -webkit-transform: scale(var(--fa-beat-scale, 1.25));\\n            transform: scale(var(--fa-beat-scale, 1.25));\\n  }\\n}\\n@keyframes fa-beat {\\n  0%, 90% {\\n    -webkit-transform: scale(1);\\n            transform: scale(1);\\n  }\\n  45% {\\n    -webkit-transform: scale(var(--fa-beat-scale, 1.25));\\n            transform: scale(var(--fa-beat-scale, 1.25));\\n  }\\n}\\n@-webkit-keyframes fa-bounce {\\n  0% {\\n    -webkit-transform: scale(1, 1) translateY(0);\\n            transform: scale(1, 1) translateY(0);\\n  }\\n  10% {\\n    -webkit-transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);\\n            transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);\\n  }\\n  30% {\\n    -webkit-transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));\\n            transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));\\n  }\\n  50% {\\n    -webkit-transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);\\n            transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);\\n  }\\n  57% {\\n    -webkit-transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));\\n            transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));\\n  }\\n  64% {\\n    -webkit-transform: scale(1, 1) translateY(0);\\n            transform: scale(1, 1) translateY(0);\\n  }\\n  100% {\\n    -webkit-transform: scale(1, 1) translateY(0);\\n            transform: scale(1, 1) translateY(0);\\n  }\\n}\\n@keyframes fa-bounce {\\n  0% {\\n    -webkit-transform: scale(1, 1) translateY(0);\\n            transform: scale(1, 1) translateY(0);\\n  }\\n  10% {\\n    -webkit-transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);\\n            transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);\\n  }\\n  30% {\\n    -webkit-transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));\\n            transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));\\n  }\\n  50% {\\n    -webkit-transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);\\n            transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);\\n  }\\n  57% {\\n    -webkit-transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));\\n            transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));\\n  }\\n  64% {\\n    -webkit-transform: scale(1, 1) translateY(0);\\n            transform: scale(1, 1) translateY(0);\\n  }\\n  100% {\\n    -webkit-transform: scale(1, 1) translateY(0);\\n            transform: scale(1, 1) translateY(0);\\n  }\\n}\\n@-webkit-keyframes fa-fade {\\n  50% {\\n    opacity: var(--fa-fade-opacity, 0.4);\\n  }\\n}\\n@keyframes fa-fade {\\n  50% {\\n    opacity: var(--fa-fade-opacity, 0.4);\\n  }\\n}\\n@-webkit-keyframes fa-beat-fade {\\n  0%, 100% {\\n    opacity: var(--fa-beat-fade-opacity, 0.4);\\n    -webkit-transform: scale(1);\\n            transform: scale(1);\\n  }\\n  50% {\\n    opacity: 1;\\n    -webkit-transform: scale(var(--fa-beat-fade-scale, 1.125));\\n            transform: scale(var(--fa-beat-fade-scale, 1.125));\\n  }\\n}\\n@keyframes fa-beat-fade {\\n  0%, 100% {\\n    opacity: var(--fa-beat-fade-opacity, 0.4);\\n    -webkit-transform: scale(1);\\n            transform: scale(1);\\n  }\\n  50% {\\n    opacity: 1;\\n    -webkit-transform: scale(var(--fa-beat-fade-scale, 1.125));\\n            transform: scale(var(--fa-beat-fade-scale, 1.125));\\n  }\\n}\\n@-webkit-keyframes fa-flip {\\n  50% {\\n    -webkit-transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));\\n            transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));\\n  }\\n}\\n@keyframes fa-flip {\\n  50% {\\n    -webkit-transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));\\n            transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));\\n  }\\n}\\n@-webkit-keyframes fa-shake {\\n  0% {\\n    -webkit-transform: rotate(-15deg);\\n            transform: rotate(-15deg);\\n  }\\n  4% {\\n    -webkit-transform: rotate(15deg);\\n            transform: rotate(15deg);\\n  }\\n  8%, 24% {\\n    -webkit-transform: rotate(-18deg);\\n            transform: rotate(-18deg);\\n  }\\n  12%, 28% {\\n    -webkit-transform: rotate(18deg);\\n            transform: rotate(18deg);\\n  }\\n  16% {\\n    -webkit-transform: rotate(-22deg);\\n            transform: rotate(-22deg);\\n  }\\n  20% {\\n    -webkit-transform: rotate(22deg);\\n            transform: rotate(22deg);\\n  }\\n  32% {\\n    -webkit-transform: rotate(-12deg);\\n            transform: rotate(-12deg);\\n  }\\n  36% {\\n    -webkit-transform: rotate(12deg);\\n            transform: rotate(12deg);\\n  }\\n  40%, 100% {\\n    -webkit-transform: rotate(0deg);\\n            transform: rotate(0deg);\\n  }\\n}\\n@keyframes fa-shake {\\n  0% {\\n    -webkit-transform: rotate(-15deg);\\n            transform: rotate(-15deg);\\n  }\\n  4% {\\n    -webkit-transform: rotate(15deg);\\n            transform: rotate(15deg);\\n  }\\n  8%, 24% {\\n    -webkit-transform: rotate(-18deg);\\n            transform: rotate(-18deg);\\n  }\\n  12%, 28% {\\n    -webkit-transform: rotate(18deg);\\n            transform: rotate(18deg);\\n  }\\n  16% {\\n    -webkit-transform: rotate(-22deg);\\n            transform: rotate(-22deg);\\n  }\\n  20% {\\n    -webkit-transform: rotate(22deg);\\n            transform: rotate(22deg);\\n  }\\n  32% {\\n    -webkit-transform: rotate(-12deg);\\n            transform: rotate(-12deg);\\n  }\\n  36% {\\n    -webkit-transform: rotate(12deg);\\n            transform: rotate(12deg);\\n  }\\n  40%, 100% {\\n    -webkit-transform: rotate(0deg);\\n            transform: rotate(0deg);\\n  }\\n}\\n@-webkit-keyframes fa-spin {\\n  0% {\\n    -webkit-transform: rotate(0deg);\\n            transform: rotate(0deg);\\n  }\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n            transform: rotate(360deg);\\n  }\\n}\\n@keyframes fa-spin {\\n  0% {\\n    -webkit-transform: rotate(0deg);\\n            transform: rotate(0deg);\\n  }\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n            transform: rotate(360deg);\\n  }\\n}\\n.fa-rotate-90 {\\n  -webkit-transform: rotate(90deg);\\n          transform: rotate(90deg);\\n}\\n\\n.fa-rotate-180 {\\n  -webkit-transform: rotate(180deg);\\n          transform: rotate(180deg);\\n}\\n\\n.fa-rotate-270 {\\n  -webkit-transform: rotate(270deg);\\n          transform: rotate(270deg);\\n}\\n\\n.fa-flip-horizontal {\\n  -webkit-transform: scale(-1, 1);\\n          transform: scale(-1, 1);\\n}\\n\\n.fa-flip-vertical {\\n  -webkit-transform: scale(1, -1);\\n          transform: scale(1, -1);\\n}\\n\\n.fa-flip-both,\\n.fa-flip-horizontal.fa-flip-vertical {\\n  -webkit-transform: scale(-1, -1);\\n          transform: scale(-1, -1);\\n}\\n\\n.fa-rotate-by {\\n  -webkit-transform: rotate(var(--fa-rotate-angle, 0));\\n          transform: rotate(var(--fa-rotate-angle, 0));\\n}\\n\\n.fa-stack {\\n  display: inline-block;\\n  vertical-align: middle;\\n  height: 2em;\\n  position: relative;\\n  width: 2.5em;\\n}\\n\\n.fa-stack-1x,\\n.fa-stack-2x {\\n  bottom: 0;\\n  left: 0;\\n  margin: auto;\\n  position: absolute;\\n  right: 0;\\n  top: 0;\\n  z-index: var(--fa-stack-z-index, auto);\\n}\\n\\n.svg-inline--fa.fa-stack-1x {\\n  height: 1em;\\n  width: 1.25em;\\n}\\n.svg-inline--fa.fa-stack-2x {\\n  height: 2em;\\n  width: 2.5em;\\n}\\n\\n.fa-inverse {\\n  color: var(--fa-inverse, #fff);\\n}\\n\\n.sr-only,\\n.fa-sr-only {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\n\\n.sr-only-focusable:not(:focus),\\n.fa-sr-only-focusable:not(:focus) {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\n\\n.svg-inline--fa .fa-primary {\\n  fill: var(--fa-primary-color, currentColor);\\n  opacity: var(--fa-primary-opacity, 1);\\n}\\n\\n.svg-inline--fa .fa-secondary {\\n  fill: var(--fa-secondary-color, currentColor);\\n  opacity: var(--fa-secondary-opacity, 0.4);\\n}\\n\\n.svg-inline--fa.fa-swap-opacity .fa-primary {\\n  opacity: var(--fa-secondary-opacity, 0.4);\\n}\\n\\n.svg-inline--fa.fa-swap-opacity .fa-secondary {\\n  opacity: var(--fa-primary-opacity, 1);\\n}\\n\\n.svg-inline--fa mask .fa-primary,\\n.svg-inline--fa mask .fa-secondary {\\n  fill: black;\\n}\\n\\n.fad.fa-inverse,\\n.fa-duotone.fa-inverse {\\n  color: var(--fa-inverse, #fff);\\n}\";\nfunction css() {\n  var dcp = DEFAULT_CSS_PREFIX;\n  var drc = DEFAULT_REPLACEMENT_CLASS;\n  var fp = config.cssPrefix;\n  var rc = config.replacementClass;\n  var s = baseStyles;\n  if (fp !== dcp || rc !== drc) {\n    var dPatt = new RegExp(\"\\\\.\".concat(dcp, \"\\\\-\"), 'g');\n    var customPropPatt = new RegExp(\"\\\\--\".concat(dcp, \"\\\\-\"), 'g');\n    var rPatt = new RegExp(\"\\\\.\".concat(drc), 'g');\n    s = s.replace(dPatt, \".\".concat(fp, \"-\")).replace(customPropPatt, \"--\".concat(fp, \"-\")).replace(rPatt, \".\".concat(rc));\n  }\n  return s;\n}\nvar _cssInserted = false;\nfunction ensureCss() {\n  if (config.autoAddCss && !_cssInserted) {\n    insertCss(css());\n    _cssInserted = true;\n  }\n}\nvar InjectCSS = {\n  mixout: function mixout() {\n    return {\n      dom: {\n        css: css,\n        insertCss: ensureCss\n      }\n    };\n  },\n  hooks: function hooks() {\n    return {\n      beforeDOMElementCreation: function beforeDOMElementCreation() {\n        ensureCss();\n      },\n      beforeI2svg: function beforeI2svg() {\n        ensureCss();\n      }\n    };\n  }\n};\nvar w = WINDOW || {};\nif (!w[NAMESPACE_IDENTIFIER]) w[NAMESPACE_IDENTIFIER] = {};\nif (!w[NAMESPACE_IDENTIFIER].styles) w[NAMESPACE_IDENTIFIER].styles = {};\nif (!w[NAMESPACE_IDENTIFIER].hooks) w[NAMESPACE_IDENTIFIER].hooks = {};\nif (!w[NAMESPACE_IDENTIFIER].shims) w[NAMESPACE_IDENTIFIER].shims = [];\nvar namespace = w[NAMESPACE_IDENTIFIER];\nvar functions = [];\nvar listener = function listener() {\n  DOCUMENT.removeEventListener('DOMContentLoaded', listener);\n  loaded = 1;\n  functions.map(function (fn) {\n    return fn();\n  });\n};\nvar loaded = false;\nif (IS_DOM) {\n  loaded = (DOCUMENT.documentElement.doScroll ? /^loaded|^c/ : /^loaded|^i|^c/).test(DOCUMENT.readyState);\n  if (!loaded) DOCUMENT.addEventListener('DOMContentLoaded', listener);\n}\nfunction domready(fn) {\n  if (!IS_DOM) return;\n  loaded ? setTimeout(fn, 0) : functions.push(fn);\n}\nfunction toHtml(abstractNodes) {\n  var tag = abstractNodes.tag,\n    _abstractNodes$attrib = abstractNodes.attributes,\n    attributes = _abstractNodes$attrib === void 0 ? {} : _abstractNodes$attrib,\n    _abstractNodes$childr = abstractNodes.children,\n    children = _abstractNodes$childr === void 0 ? [] : _abstractNodes$childr;\n  if (typeof abstractNodes === 'string') {\n    return htmlEscape(abstractNodes);\n  } else {\n    return \"<\".concat(tag, \" \").concat(joinAttributes(attributes), \">\").concat(children.map(toHtml).join(''), \"</\").concat(tag, \">\");\n  }\n}\nfunction iconFromMapping(mapping, prefix, iconName) {\n  if (mapping && mapping[prefix] && mapping[prefix][iconName]) {\n    return {\n      prefix: prefix,\n      iconName: iconName,\n      icon: mapping[prefix][iconName]\n    };\n  }\n}\n\n/**\n * Internal helper to bind a function known to have 4 arguments\n * to a given context.\n */\n\nvar bindInternal4 = function bindInternal4(func, thisContext) {\n  return function (a, b, c, d) {\n    return func.call(thisContext, a, b, c, d);\n  };\n};\n\n/**\n * # Reduce\n *\n * A fast object `.reduce()` implementation.\n *\n * @param  {Object}   subject      The object to reduce over.\n * @param  {Function} fn           The reducer function.\n * @param  {mixed}    initialValue The initial value for the reducer, defaults to subject[0].\n * @param  {Object}   thisContext  The context for the reducer.\n * @return {mixed}                 The final result.\n */\n\nvar reduce = function fastReduceObject(subject, fn, initialValue, thisContext) {\n  var keys = Object.keys(subject),\n    length = keys.length,\n    iterator = thisContext !== undefined ? bindInternal4(fn, thisContext) : fn,\n    i,\n    key,\n    result;\n  if (initialValue === undefined) {\n    i = 1;\n    result = subject[keys[0]];\n  } else {\n    i = 0;\n    result = initialValue;\n  }\n  for (; i < length; i++) {\n    key = keys[i];\n    result = iterator(result, subject[key], key, subject);\n  }\n  return result;\n};\n\n/**\n * ucs2decode() and codePointAt() are both works of Mathias Bynens and licensed under MIT\n *\n * Copyright Mathias Bynens <https://mathiasbynens.be/>\n\n * Permission is hereby granted, free of charge, to any person obtaining\n * a copy of this software and associated documentation files (the\n * \"Software\"), to deal in the Software without restriction, including\n * without limitation the rights to use, copy, modify, merge, publish,\n * distribute, sublicense, and/or sell copies of the Software, and to\n * permit persons to whom the Software is furnished to do so, subject to\n * the following conditions:\n\n * The above copyright notice and this permission notice shall be\n * included in all copies or substantial portions of the Software.\n\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\n * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\n * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\n * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n */\nfunction ucs2decode(string) {\n  var output = [];\n  var counter = 0;\n  var length = string.length;\n  while (counter < length) {\n    var value = string.charCodeAt(counter++);\n    if (value >= 0xD800 && value <= 0xDBFF && counter < length) {\n      var extra = string.charCodeAt(counter++);\n      if ((extra & 0xFC00) == 0xDC00) {\n        // eslint-disable-line eqeqeq\n        output.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\n      } else {\n        output.push(value);\n        counter--;\n      }\n    } else {\n      output.push(value);\n    }\n  }\n  return output;\n}\nfunction toHex(unicode) {\n  var decoded = ucs2decode(unicode);\n  return decoded.length === 1 ? decoded[0].toString(16) : null;\n}\nfunction codePointAt(string, index) {\n  var size = string.length;\n  var first = string.charCodeAt(index);\n  var second;\n  if (first >= 0xD800 && first <= 0xDBFF && size > index + 1) {\n    second = string.charCodeAt(index + 1);\n    if (second >= 0xDC00 && second <= 0xDFFF) {\n      return (first - 0xD800) * 0x400 + second - 0xDC00 + 0x10000;\n    }\n  }\n  return first;\n}\nfunction normalizeIcons(icons) {\n  return Object.keys(icons).reduce(function (acc, iconName) {\n    var icon = icons[iconName];\n    var expanded = !!icon.icon;\n    if (expanded) {\n      acc[icon.iconName] = icon.icon;\n    } else {\n      acc[iconName] = icon;\n    }\n    return acc;\n  }, {});\n}\nfunction defineIcons(prefix, icons) {\n  var params = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  var _params$skipHooks = params.skipHooks,\n    skipHooks = _params$skipHooks === void 0 ? false : _params$skipHooks;\n  var normalized = normalizeIcons(icons);\n  if (typeof namespace.hooks.addPack === 'function' && !skipHooks) {\n    namespace.hooks.addPack(prefix, normalizeIcons(icons));\n  } else {\n    namespace.styles[prefix] = _objectSpread2(_objectSpread2({}, namespace.styles[prefix] || {}), normalized);\n  }\n  /**\n   * Font Awesome 4 used the prefix of `fa` for all icons. With the introduction\n   * of new styles we needed to differentiate between them. Prefix `fa` is now an alias\n   * for `fas` so we'll ease the upgrade process for our users by automatically defining\n   * this as well.\n   */\n\n  if (prefix === 'fas') {\n    defineIcons('fa', icons);\n  }\n}\nvar duotonePathRe = [/*#__PURE__*/_wrapRegExp(/path d=\"((?:(?!\")[\\s\\S])+)\".*path d=\"((?:(?!\")[\\s\\S])+)\"/, {\n  d1: 1,\n  d2: 2\n}), /*#__PURE__*/_wrapRegExp(/path class=\"((?:(?!\")[\\s\\S])+)\".*d=\"((?:(?!\")[\\s\\S])+)\".*path class=\"((?:(?!\")[\\s\\S])+)\".*d=\"((?:(?!\")[\\s\\S])+)\"/, {\n  cls1: 1,\n  d1: 2,\n  cls2: 3,\n  d2: 4\n}), /*#__PURE__*/_wrapRegExp(/path class=\"((?:(?!\")[\\s\\S])+)\".*d=\"((?:(?!\")[\\s\\S])+)\"/, {\n  cls1: 1,\n  d1: 2\n})];\nvar _LONG_STYLE, _PREFIXES, _PREFIXES_FOR_FAMILY;\nvar styles = namespace.styles,\n  shims = namespace.shims;\nvar LONG_STYLE = (_LONG_STYLE = {}, _defineProperty(_LONG_STYLE, FAMILY_CLASSIC, Object.values(PREFIX_TO_LONG_STYLE[FAMILY_CLASSIC])), _defineProperty(_LONG_STYLE, FAMILY_SHARP, Object.values(PREFIX_TO_LONG_STYLE[FAMILY_SHARP])), _LONG_STYLE);\nvar _defaultUsablePrefix = null;\nvar _byUnicode = {};\nvar _byLigature = {};\nvar _byOldName = {};\nvar _byOldUnicode = {};\nvar _byAlias = {};\nvar PREFIXES = (_PREFIXES = {}, _defineProperty(_PREFIXES, FAMILY_CLASSIC, Object.keys(PREFIX_TO_STYLE[FAMILY_CLASSIC])), _defineProperty(_PREFIXES, FAMILY_SHARP, Object.keys(PREFIX_TO_STYLE[FAMILY_SHARP])), _PREFIXES);\nfunction isReserved(name) {\n  return ~RESERVED_CLASSES.indexOf(name);\n}\nfunction getIconName(cssPrefix, cls) {\n  var parts = cls.split('-');\n  var prefix = parts[0];\n  var iconName = parts.slice(1).join('-');\n  if (prefix === cssPrefix && iconName !== '' && !isReserved(iconName)) {\n    return iconName;\n  } else {\n    return null;\n  }\n}\nvar build = function build() {\n  var lookup = function lookup(reducer) {\n    return reduce(styles, function (o, style, prefix) {\n      o[prefix] = reduce(style, reducer, {});\n      return o;\n    }, {});\n  };\n  _byUnicode = lookup(function (acc, icon, iconName) {\n    if (icon[3]) {\n      acc[icon[3]] = iconName;\n    }\n    if (icon[2]) {\n      var aliases = icon[2].filter(function (a) {\n        return typeof a === 'number';\n      });\n      aliases.forEach(function (alias) {\n        acc[alias.toString(16)] = iconName;\n      });\n    }\n    return acc;\n  });\n  _byLigature = lookup(function (acc, icon, iconName) {\n    acc[iconName] = iconName;\n    if (icon[2]) {\n      var aliases = icon[2].filter(function (a) {\n        return typeof a === 'string';\n      });\n      aliases.forEach(function (alias) {\n        acc[alias] = iconName;\n      });\n    }\n    return acc;\n  });\n  _byAlias = lookup(function (acc, icon, iconName) {\n    var aliases = icon[2];\n    acc[iconName] = iconName;\n    aliases.forEach(function (alias) {\n      acc[alias] = iconName;\n    });\n    return acc;\n  }); // If we have a Kit, we can't determine if regular is available since we\n  // could be auto-fetching it. We'll have to assume that it is available.\n\n  var hasRegular = 'far' in styles || config.autoFetchSvg;\n  var shimLookups = reduce(shims, function (acc, shim) {\n    var maybeNameMaybeUnicode = shim[0];\n    var prefix = shim[1];\n    var iconName = shim[2];\n    if (prefix === 'far' && !hasRegular) {\n      prefix = 'fas';\n    }\n    if (typeof maybeNameMaybeUnicode === 'string') {\n      acc.names[maybeNameMaybeUnicode] = {\n        prefix: prefix,\n        iconName: iconName\n      };\n    }\n    if (typeof maybeNameMaybeUnicode === 'number') {\n      acc.unicodes[maybeNameMaybeUnicode.toString(16)] = {\n        prefix: prefix,\n        iconName: iconName\n      };\n    }\n    return acc;\n  }, {\n    names: {},\n    unicodes: {}\n  });\n  _byOldName = shimLookups.names;\n  _byOldUnicode = shimLookups.unicodes;\n  _defaultUsablePrefix = getCanonicalPrefix(config.styleDefault, {\n    family: config.familyDefault\n  });\n};\nonChange(function (c) {\n  _defaultUsablePrefix = getCanonicalPrefix(c.styleDefault, {\n    family: config.familyDefault\n  });\n});\nbuild();\nfunction byUnicode(prefix, unicode) {\n  return (_byUnicode[prefix] || {})[unicode];\n}\nfunction byLigature(prefix, ligature) {\n  return (_byLigature[prefix] || {})[ligature];\n}\nfunction byAlias(prefix, alias) {\n  return (_byAlias[prefix] || {})[alias];\n}\nfunction byOldName(name) {\n  return _byOldName[name] || {\n    prefix: null,\n    iconName: null\n  };\n}\nfunction byOldUnicode(unicode) {\n  var oldUnicode = _byOldUnicode[unicode];\n  var newUnicode = byUnicode('fas', unicode);\n  return oldUnicode || (newUnicode ? {\n    prefix: 'fas',\n    iconName: newUnicode\n  } : null) || {\n    prefix: null,\n    iconName: null\n  };\n}\nfunction getDefaultUsablePrefix() {\n  return _defaultUsablePrefix;\n}\nvar emptyCanonicalIcon = function emptyCanonicalIcon() {\n  return {\n    prefix: null,\n    iconName: null,\n    rest: []\n  };\n};\nfunction getCanonicalPrefix(styleOrPrefix) {\n  var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var _params$family = params.family,\n    family = _params$family === void 0 ? FAMILY_CLASSIC : _params$family;\n  var style = PREFIX_TO_STYLE[family][styleOrPrefix];\n  var prefix = STYLE_TO_PREFIX[family][styleOrPrefix] || STYLE_TO_PREFIX[family][style];\n  var defined = styleOrPrefix in namespace.styles ? styleOrPrefix : null;\n  return prefix || defined || null;\n}\nvar PREFIXES_FOR_FAMILY = (_PREFIXES_FOR_FAMILY = {}, _defineProperty(_PREFIXES_FOR_FAMILY, FAMILY_CLASSIC, Object.keys(PREFIX_TO_LONG_STYLE[FAMILY_CLASSIC])), _defineProperty(_PREFIXES_FOR_FAMILY, FAMILY_SHARP, Object.keys(PREFIX_TO_LONG_STYLE[FAMILY_SHARP])), _PREFIXES_FOR_FAMILY);\nfunction getCanonicalIcon(values) {\n  var _famProps;\n  var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var _params$skipLookups = params.skipLookups,\n    skipLookups = _params$skipLookups === void 0 ? false : _params$skipLookups;\n  var famProps = (_famProps = {}, _defineProperty(_famProps, FAMILY_CLASSIC, \"\".concat(config.cssPrefix, \"-\").concat(FAMILY_CLASSIC)), _defineProperty(_famProps, FAMILY_SHARP, \"\".concat(config.cssPrefix, \"-\").concat(FAMILY_SHARP)), _famProps);\n  var givenPrefix = null;\n  var family = FAMILY_CLASSIC;\n  if (values.includes(famProps[FAMILY_CLASSIC]) || values.some(function (v) {\n    return PREFIXES_FOR_FAMILY[FAMILY_CLASSIC].includes(v);\n  })) {\n    family = FAMILY_CLASSIC;\n  }\n  if (values.includes(famProps[FAMILY_SHARP]) || values.some(function (v) {\n    return PREFIXES_FOR_FAMILY[FAMILY_SHARP].includes(v);\n  })) {\n    family = FAMILY_SHARP;\n  }\n  var canonical = values.reduce(function (acc, cls) {\n    var iconName = getIconName(config.cssPrefix, cls);\n    if (styles[cls]) {\n      cls = LONG_STYLE[family].includes(cls) ? LONG_STYLE_TO_PREFIX[family][cls] : cls;\n      givenPrefix = cls;\n      acc.prefix = cls;\n    } else if (PREFIXES[family].indexOf(cls) > -1) {\n      givenPrefix = cls;\n      acc.prefix = getCanonicalPrefix(cls, {\n        family: family\n      });\n    } else if (iconName) {\n      acc.iconName = iconName;\n    } else if (cls !== config.replacementClass && cls !== famProps[FAMILY_CLASSIC] && cls !== famProps[FAMILY_SHARP]) {\n      acc.rest.push(cls);\n    }\n    if (!skipLookups && acc.prefix && acc.iconName) {\n      var shim = givenPrefix === 'fa' ? byOldName(acc.iconName) : {};\n      var aliasIconName = byAlias(acc.prefix, acc.iconName);\n      if (shim.prefix) {\n        givenPrefix = null;\n      }\n      acc.iconName = shim.iconName || aliasIconName || acc.iconName;\n      acc.prefix = shim.prefix || acc.prefix;\n      if (acc.prefix === 'far' && !styles['far'] && styles['fas'] && !config.autoFetchSvg) {\n        // Allow a fallback from the regular style to solid if regular is not available\n        // but only if we aren't auto-fetching SVGs\n        acc.prefix = 'fas';\n      }\n    }\n    return acc;\n  }, emptyCanonicalIcon());\n  if (values.includes('fa-brands') || values.includes('fab')) {\n    canonical.prefix = 'fab';\n  }\n  if (values.includes('fa-duotone') || values.includes('fad')) {\n    canonical.prefix = 'fad';\n  }\n  if (!canonical.prefix && family === FAMILY_SHARP && (styles['fass'] || config.autoFetchSvg)) {\n    canonical.prefix = 'fass';\n    canonical.iconName = byAlias(canonical.prefix, canonical.iconName) || canonical.iconName;\n  }\n  if (canonical.prefix === 'fa' || givenPrefix === 'fa') {\n    // The fa prefix is not canonical. So if it has made it through until this point\n    // we will shift it to the correct prefix.\n    canonical.prefix = getDefaultUsablePrefix() || 'fas';\n  }\n  return canonical;\n}\nvar Library = /*#__PURE__*/function () {\n  function Library() {\n    _classCallCheck(this, Library);\n    this.definitions = {};\n  }\n  _createClass(Library, [{\n    key: \"add\",\n    value: function add() {\n      var _this = this;\n      for (var _len = arguments.length, definitions = new Array(_len), _key = 0; _key < _len; _key++) {\n        definitions[_key] = arguments[_key];\n      }\n      var additions = definitions.reduce(this._pullDefinitions, {});\n      Object.keys(additions).forEach(function (key) {\n        _this.definitions[key] = _objectSpread2(_objectSpread2({}, _this.definitions[key] || {}), additions[key]);\n        defineIcons(key, additions[key]); // TODO can we stop doing this? We can't get the icons by 'fa-solid' any longer so this probably needs to change\n\n        var longPrefix = PREFIX_TO_LONG_STYLE[FAMILY_CLASSIC][key];\n        if (longPrefix) defineIcons(longPrefix, additions[key]);\n        build();\n      });\n    }\n  }, {\n    key: \"reset\",\n    value: function reset() {\n      this.definitions = {};\n    }\n  }, {\n    key: \"_pullDefinitions\",\n    value: function _pullDefinitions(additions, definition) {\n      var normalized = definition.prefix && definition.iconName && definition.icon ? {\n        0: definition\n      } : definition;\n      Object.keys(normalized).map(function (key) {\n        var _normalized$key = normalized[key],\n          prefix = _normalized$key.prefix,\n          iconName = _normalized$key.iconName,\n          icon = _normalized$key.icon;\n        var aliases = icon[2];\n        if (!additions[prefix]) additions[prefix] = {};\n        if (aliases.length > 0) {\n          aliases.forEach(function (alias) {\n            if (typeof alias === 'string') {\n              additions[prefix][alias] = icon;\n            }\n          });\n        }\n        additions[prefix][iconName] = icon;\n      });\n      return additions;\n    }\n  }]);\n  return Library;\n}();\nvar _plugins = [];\nvar _hooks = {};\nvar providers = {};\nvar defaultProviderKeys = Object.keys(providers);\nfunction registerPlugins(nextPlugins, _ref) {\n  var obj = _ref.mixoutsTo;\n  _plugins = nextPlugins;\n  _hooks = {};\n  Object.keys(providers).forEach(function (k) {\n    if (defaultProviderKeys.indexOf(k) === -1) {\n      delete providers[k];\n    }\n  });\n  _plugins.forEach(function (plugin) {\n    var mixout = plugin.mixout ? plugin.mixout() : {};\n    Object.keys(mixout).forEach(function (tk) {\n      if (typeof mixout[tk] === 'function') {\n        obj[tk] = mixout[tk];\n      }\n      if (_typeof(mixout[tk]) === 'object') {\n        Object.keys(mixout[tk]).forEach(function (sk) {\n          if (!obj[tk]) {\n            obj[tk] = {};\n          }\n          obj[tk][sk] = mixout[tk][sk];\n        });\n      }\n    });\n    if (plugin.hooks) {\n      var hooks = plugin.hooks();\n      Object.keys(hooks).forEach(function (hook) {\n        if (!_hooks[hook]) {\n          _hooks[hook] = [];\n        }\n        _hooks[hook].push(hooks[hook]);\n      });\n    }\n    if (plugin.provides) {\n      plugin.provides(providers);\n    }\n  });\n  return obj;\n}\nfunction chainHooks(hook, accumulator) {\n  for (var _len = arguments.length, args = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n    args[_key - 2] = arguments[_key];\n  }\n  var hookFns = _hooks[hook] || [];\n  hookFns.forEach(function (hookFn) {\n    accumulator = hookFn.apply(null, [accumulator].concat(args)); // eslint-disable-line no-useless-call\n  });\n\n  return accumulator;\n}\nfunction callHooks(hook) {\n  for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n    args[_key2 - 1] = arguments[_key2];\n  }\n  var hookFns = _hooks[hook] || [];\n  hookFns.forEach(function (hookFn) {\n    hookFn.apply(null, args);\n  });\n  return undefined;\n}\nfunction callProvided() {\n  var hook = arguments[0];\n  var args = Array.prototype.slice.call(arguments, 1);\n  return providers[hook] ? providers[hook].apply(null, args) : undefined;\n}\nfunction findIconDefinition(iconLookup) {\n  if (iconLookup.prefix === 'fa') {\n    iconLookup.prefix = 'fas';\n  }\n  var iconName = iconLookup.iconName;\n  var prefix = iconLookup.prefix || getDefaultUsablePrefix();\n  if (!iconName) return;\n  iconName = byAlias(prefix, iconName) || iconName;\n  return iconFromMapping(library.definitions, prefix, iconName) || iconFromMapping(namespace.styles, prefix, iconName);\n}\nvar library = new Library();\nvar noAuto = function noAuto() {\n  config.autoReplaceSvg = false;\n  config.observeMutations = false;\n  callHooks('noAuto');\n};\nvar dom = {\n  i2svg: function i2svg() {\n    var params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    if (IS_DOM) {\n      callHooks('beforeI2svg', params);\n      callProvided('pseudoElements2svg', params);\n      return callProvided('i2svg', params);\n    } else {\n      return Promise.reject('Operation requires a DOM of some kind.');\n    }\n  },\n  watch: function watch() {\n    var params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var autoReplaceSvgRoot = params.autoReplaceSvgRoot;\n    if (config.autoReplaceSvg === false) {\n      config.autoReplaceSvg = true;\n    }\n    config.observeMutations = true;\n    domready(function () {\n      autoReplace({\n        autoReplaceSvgRoot: autoReplaceSvgRoot\n      });\n      callHooks('watch', params);\n    });\n  }\n};\nvar parse = {\n  icon: function icon(_icon) {\n    if (_icon === null) {\n      return null;\n    }\n    if (_typeof(_icon) === 'object' && _icon.prefix && _icon.iconName) {\n      return {\n        prefix: _icon.prefix,\n        iconName: byAlias(_icon.prefix, _icon.iconName) || _icon.iconName\n      };\n    }\n    if (Array.isArray(_icon) && _icon.length === 2) {\n      var iconName = _icon[1].indexOf('fa-') === 0 ? _icon[1].slice(3) : _icon[1];\n      var prefix = getCanonicalPrefix(_icon[0]);\n      return {\n        prefix: prefix,\n        iconName: byAlias(prefix, iconName) || iconName\n      };\n    }\n    if (typeof _icon === 'string' && (_icon.indexOf(\"\".concat(config.cssPrefix, \"-\")) > -1 || _icon.match(ICON_SELECTION_SYNTAX_PATTERN))) {\n      var canonicalIcon = getCanonicalIcon(_icon.split(' '), {\n        skipLookups: true\n      });\n      return {\n        prefix: canonicalIcon.prefix || getDefaultUsablePrefix(),\n        iconName: byAlias(canonicalIcon.prefix, canonicalIcon.iconName) || canonicalIcon.iconName\n      };\n    }\n    if (typeof _icon === 'string') {\n      var _prefix = getDefaultUsablePrefix();\n      return {\n        prefix: _prefix,\n        iconName: byAlias(_prefix, _icon) || _icon\n      };\n    }\n  }\n};\nvar api = {\n  noAuto: noAuto,\n  config: config,\n  dom: dom,\n  parse: parse,\n  library: library,\n  findIconDefinition: findIconDefinition,\n  toHtml: toHtml\n};\nvar autoReplace = function autoReplace() {\n  var params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var _params$autoReplaceSv = params.autoReplaceSvgRoot,\n    autoReplaceSvgRoot = _params$autoReplaceSv === void 0 ? DOCUMENT : _params$autoReplaceSv;\n  if ((Object.keys(namespace.styles).length > 0 || config.autoFetchSvg) && IS_DOM && config.autoReplaceSvg) api.dom.i2svg({\n    node: autoReplaceSvgRoot\n  });\n};\nfunction domVariants(val, abstractCreator) {\n  Object.defineProperty(val, 'abstract', {\n    get: abstractCreator\n  });\n  Object.defineProperty(val, 'html', {\n    get: function get() {\n      return val.abstract.map(function (a) {\n        return toHtml(a);\n      });\n    }\n  });\n  Object.defineProperty(val, 'node', {\n    get: function get() {\n      if (!IS_DOM) return;\n      var container = DOCUMENT.createElement('div');\n      container.innerHTML = val.html;\n      return container.children;\n    }\n  });\n  return val;\n}\nfunction asIcon(_ref) {\n  var children = _ref.children,\n    main = _ref.main,\n    mask = _ref.mask,\n    attributes = _ref.attributes,\n    styles = _ref.styles,\n    transform = _ref.transform;\n  if (transformIsMeaningful(transform) && main.found && !mask.found) {\n    var width = main.width,\n      height = main.height;\n    var offset = {\n      x: width / height / 2,\n      y: 0.5\n    };\n    attributes['style'] = joinStyles(_objectSpread2(_objectSpread2({}, styles), {}, {\n      'transform-origin': \"\".concat(offset.x + transform.x / 16, \"em \").concat(offset.y + transform.y / 16, \"em\")\n    }));\n  }\n  return [{\n    tag: 'svg',\n    attributes: attributes,\n    children: children\n  }];\n}\nfunction asSymbol(_ref) {\n  var prefix = _ref.prefix,\n    iconName = _ref.iconName,\n    children = _ref.children,\n    attributes = _ref.attributes,\n    symbol = _ref.symbol;\n  var id = symbol === true ? \"\".concat(prefix, \"-\").concat(config.cssPrefix, \"-\").concat(iconName) : symbol;\n  return [{\n    tag: 'svg',\n    attributes: {\n      style: 'display: none;'\n    },\n    children: [{\n      tag: 'symbol',\n      attributes: _objectSpread2(_objectSpread2({}, attributes), {}, {\n        id: id\n      }),\n      children: children\n    }]\n  }];\n}\nfunction makeInlineSvgAbstract(params) {\n  var _params$icons = params.icons,\n    main = _params$icons.main,\n    mask = _params$icons.mask,\n    prefix = params.prefix,\n    iconName = params.iconName,\n    transform = params.transform,\n    symbol = params.symbol,\n    title = params.title,\n    maskId = params.maskId,\n    titleId = params.titleId,\n    extra = params.extra,\n    _params$watchable = params.watchable,\n    watchable = _params$watchable === void 0 ? false : _params$watchable;\n  var _ref = mask.found ? mask : main,\n    width = _ref.width,\n    height = _ref.height;\n  var isUploadedIcon = prefix === 'fak';\n  var attrClass = [config.replacementClass, iconName ? \"\".concat(config.cssPrefix, \"-\").concat(iconName) : ''].filter(function (c) {\n    return extra.classes.indexOf(c) === -1;\n  }).filter(function (c) {\n    return c !== '' || !!c;\n  }).concat(extra.classes).join(' ');\n  var content = {\n    children: [],\n    attributes: _objectSpread2(_objectSpread2({}, extra.attributes), {}, {\n      'data-prefix': prefix,\n      'data-icon': iconName,\n      'class': attrClass,\n      'role': extra.attributes.role || 'img',\n      'xmlns': 'http://www.w3.org/2000/svg',\n      'viewBox': \"0 0 \".concat(width, \" \").concat(height)\n    })\n  };\n  var uploadedIconWidthStyle = isUploadedIcon && !~extra.classes.indexOf('fa-fw') ? {\n    width: \"\".concat(width / height * 16 * 0.0625, \"em\")\n  } : {};\n  if (watchable) {\n    content.attributes[DATA_FA_I2SVG] = '';\n  }\n  if (title) {\n    content.children.push({\n      tag: 'title',\n      attributes: {\n        id: content.attributes['aria-labelledby'] || \"title-\".concat(titleId || nextUniqueId())\n      },\n      children: [title]\n    });\n    delete content.attributes.title;\n  }\n  var args = _objectSpread2(_objectSpread2({}, content), {}, {\n    prefix: prefix,\n    iconName: iconName,\n    main: main,\n    mask: mask,\n    maskId: maskId,\n    transform: transform,\n    symbol: symbol,\n    styles: _objectSpread2(_objectSpread2({}, uploadedIconWidthStyle), extra.styles)\n  });\n  var _ref2 = mask.found && main.found ? callProvided('generateAbstractMask', args) || {\n      children: [],\n      attributes: {}\n    } : callProvided('generateAbstractIcon', args) || {\n      children: [],\n      attributes: {}\n    },\n    children = _ref2.children,\n    attributes = _ref2.attributes;\n  args.children = children;\n  args.attributes = attributes;\n  if (symbol) {\n    return asSymbol(args);\n  } else {\n    return asIcon(args);\n  }\n}\nfunction makeLayersTextAbstract(params) {\n  var content = params.content,\n    width = params.width,\n    height = params.height,\n    transform = params.transform,\n    title = params.title,\n    extra = params.extra,\n    _params$watchable2 = params.watchable,\n    watchable = _params$watchable2 === void 0 ? false : _params$watchable2;\n  var attributes = _objectSpread2(_objectSpread2(_objectSpread2({}, extra.attributes), title ? {\n    'title': title\n  } : {}), {}, {\n    'class': extra.classes.join(' ')\n  });\n  if (watchable) {\n    attributes[DATA_FA_I2SVG] = '';\n  }\n  var styles = _objectSpread2({}, extra.styles);\n  if (transformIsMeaningful(transform)) {\n    styles['transform'] = transformForCss({\n      transform: transform,\n      startCentered: true,\n      width: width,\n      height: height\n    });\n    styles['-webkit-transform'] = styles['transform'];\n  }\n  var styleString = joinStyles(styles);\n  if (styleString.length > 0) {\n    attributes['style'] = styleString;\n  }\n  var val = [];\n  val.push({\n    tag: 'span',\n    attributes: attributes,\n    children: [content]\n  });\n  if (title) {\n    val.push({\n      tag: 'span',\n      attributes: {\n        class: 'sr-only'\n      },\n      children: [title]\n    });\n  }\n  return val;\n}\nfunction makeLayersCounterAbstract(params) {\n  var content = params.content,\n    title = params.title,\n    extra = params.extra;\n  var attributes = _objectSpread2(_objectSpread2(_objectSpread2({}, extra.attributes), title ? {\n    'title': title\n  } : {}), {}, {\n    'class': extra.classes.join(' ')\n  });\n  var styleString = joinStyles(extra.styles);\n  if (styleString.length > 0) {\n    attributes['style'] = styleString;\n  }\n  var val = [];\n  val.push({\n    tag: 'span',\n    attributes: attributes,\n    children: [content]\n  });\n  if (title) {\n    val.push({\n      tag: 'span',\n      attributes: {\n        class: 'sr-only'\n      },\n      children: [title]\n    });\n  }\n  return val;\n}\nvar styles$1 = namespace.styles;\nfunction asFoundIcon(icon) {\n  var width = icon[0];\n  var height = icon[1];\n  var _icon$slice = icon.slice(4),\n    _icon$slice2 = _slicedToArray(_icon$slice, 1),\n    vectorData = _icon$slice2[0];\n  var element = null;\n  if (Array.isArray(vectorData)) {\n    element = {\n      tag: 'g',\n      attributes: {\n        class: \"\".concat(config.cssPrefix, \"-\").concat(DUOTONE_CLASSES.GROUP)\n      },\n      children: [{\n        tag: 'path',\n        attributes: {\n          class: \"\".concat(config.cssPrefix, \"-\").concat(DUOTONE_CLASSES.SECONDARY),\n          fill: 'currentColor',\n          d: vectorData[0]\n        }\n      }, {\n        tag: 'path',\n        attributes: {\n          class: \"\".concat(config.cssPrefix, \"-\").concat(DUOTONE_CLASSES.PRIMARY),\n          fill: 'currentColor',\n          d: vectorData[1]\n        }\n      }]\n    };\n  } else {\n    element = {\n      tag: 'path',\n      attributes: {\n        fill: 'currentColor',\n        d: vectorData\n      }\n    };\n  }\n  return {\n    found: true,\n    width: width,\n    height: height,\n    icon: element\n  };\n}\nvar missingIconResolutionMixin = {\n  found: false,\n  width: 512,\n  height: 512\n};\nfunction maybeNotifyMissing(iconName, prefix) {\n  if (!PRODUCTION && !config.showMissingIcons && iconName) {\n    console.error(\"Icon with name \\\"\".concat(iconName, \"\\\" and prefix \\\"\").concat(prefix, \"\\\" is missing.\"));\n  }\n}\nfunction findIcon(iconName, prefix) {\n  var givenPrefix = prefix;\n  if (prefix === 'fa' && config.styleDefault !== null) {\n    prefix = getDefaultUsablePrefix();\n  }\n  return new Promise(function (resolve, reject) {\n    var val = {\n      found: false,\n      width: 512,\n      height: 512,\n      icon: callProvided('missingIconAbstract') || {}\n    };\n    if (givenPrefix === 'fa') {\n      var shim = byOldName(iconName) || {};\n      iconName = shim.iconName || iconName;\n      prefix = shim.prefix || prefix;\n    }\n    if (iconName && prefix && styles$1[prefix] && styles$1[prefix][iconName]) {\n      var icon = styles$1[prefix][iconName];\n      return resolve(asFoundIcon(icon));\n    }\n    maybeNotifyMissing(iconName, prefix);\n    resolve(_objectSpread2(_objectSpread2({}, missingIconResolutionMixin), {}, {\n      icon: config.showMissingIcons && iconName ? callProvided('missingIconAbstract') || {} : {}\n    }));\n  });\n}\nvar noop$1 = function noop() {};\nvar p = config.measurePerformance && PERFORMANCE && PERFORMANCE.mark && PERFORMANCE.measure ? PERFORMANCE : {\n  mark: noop$1,\n  measure: noop$1\n};\nvar preamble = \"FA \\\"6.5.2\\\"\";\nvar begin = function begin(name) {\n  p.mark(\"\".concat(preamble, \" \").concat(name, \" begins\"));\n  return function () {\n    return end(name);\n  };\n};\nvar end = function end(name) {\n  p.mark(\"\".concat(preamble, \" \").concat(name, \" ends\"));\n  p.measure(\"\".concat(preamble, \" \").concat(name), \"\".concat(preamble, \" \").concat(name, \" begins\"), \"\".concat(preamble, \" \").concat(name, \" ends\"));\n};\nvar perf = {\n  begin: begin,\n  end: end\n};\nvar noop$2 = function noop() {};\nfunction isWatched(node) {\n  var i2svg = node.getAttribute ? node.getAttribute(DATA_FA_I2SVG) : null;\n  return typeof i2svg === 'string';\n}\nfunction hasPrefixAndIcon(node) {\n  var prefix = node.getAttribute ? node.getAttribute(DATA_PREFIX) : null;\n  var icon = node.getAttribute ? node.getAttribute(DATA_ICON) : null;\n  return prefix && icon;\n}\nfunction hasBeenReplaced(node) {\n  return node && node.classList && node.classList.contains && node.classList.contains(config.replacementClass);\n}\nfunction getMutator() {\n  if (config.autoReplaceSvg === true) {\n    return mutators.replace;\n  }\n  var mutator = mutators[config.autoReplaceSvg];\n  return mutator || mutators.replace;\n}\nfunction createElementNS(tag) {\n  return DOCUMENT.createElementNS('http://www.w3.org/2000/svg', tag);\n}\nfunction createElement(tag) {\n  return DOCUMENT.createElement(tag);\n}\nfunction convertSVG(abstractObj) {\n  var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var _params$ceFn = params.ceFn,\n    ceFn = _params$ceFn === void 0 ? abstractObj.tag === 'svg' ? createElementNS : createElement : _params$ceFn;\n  if (typeof abstractObj === 'string') {\n    return DOCUMENT.createTextNode(abstractObj);\n  }\n  var tag = ceFn(abstractObj.tag);\n  Object.keys(abstractObj.attributes || []).forEach(function (key) {\n    tag.setAttribute(key, abstractObj.attributes[key]);\n  });\n  var children = abstractObj.children || [];\n  children.forEach(function (child) {\n    tag.appendChild(convertSVG(child, {\n      ceFn: ceFn\n    }));\n  });\n  return tag;\n}\nfunction nodeAsComment(node) {\n  var comment = \" \".concat(node.outerHTML, \" \");\n  /* BEGIN.ATTRIBUTION */\n\n  comment = \"\".concat(comment, \"Font Awesome fontawesome.com \");\n  /* END.ATTRIBUTION */\n\n  return comment;\n}\nvar mutators = {\n  replace: function replace(mutation) {\n    var node = mutation[0];\n    if (node.parentNode) {\n      mutation[1].forEach(function (_abstract) {\n        node.parentNode.insertBefore(convertSVG(_abstract), node);\n      });\n      if (node.getAttribute(DATA_FA_I2SVG) === null && config.keepOriginalSource) {\n        var comment = DOCUMENT.createComment(nodeAsComment(node));\n        node.parentNode.replaceChild(comment, node);\n      } else {\n        node.remove();\n      }\n    }\n  },\n  nest: function nest(mutation) {\n    var node = mutation[0];\n    var _abstract2 = mutation[1]; // If we already have a replaced node we do not want to continue nesting within it.\n    // Short-circuit to the standard replacement\n\n    if (~classArray(node).indexOf(config.replacementClass)) {\n      return mutators.replace(mutation);\n    }\n    var forSvg = new RegExp(\"\".concat(config.cssPrefix, \"-.*\"));\n    delete _abstract2[0].attributes.id;\n    if (_abstract2[0].attributes.class) {\n      var splitClasses = _abstract2[0].attributes.class.split(' ').reduce(function (acc, cls) {\n        if (cls === config.replacementClass || cls.match(forSvg)) {\n          acc.toSvg.push(cls);\n        } else {\n          acc.toNode.push(cls);\n        }\n        return acc;\n      }, {\n        toNode: [],\n        toSvg: []\n      });\n      _abstract2[0].attributes.class = splitClasses.toSvg.join(' ');\n      if (splitClasses.toNode.length === 0) {\n        node.removeAttribute('class');\n      } else {\n        node.setAttribute('class', splitClasses.toNode.join(' '));\n      }\n    }\n    var newInnerHTML = _abstract2.map(function (a) {\n      return toHtml(a);\n    }).join('\\n');\n    node.setAttribute(DATA_FA_I2SVG, '');\n    node.innerHTML = newInnerHTML;\n  }\n};\nfunction performOperationSync(op) {\n  op();\n}\nfunction perform(mutations, callback) {\n  var callbackFunction = typeof callback === 'function' ? callback : noop$2;\n  if (mutations.length === 0) {\n    callbackFunction();\n  } else {\n    var frame = performOperationSync;\n    if (config.mutateApproach === MUTATION_APPROACH_ASYNC) {\n      frame = WINDOW.requestAnimationFrame || performOperationSync;\n    }\n    frame(function () {\n      var mutator = getMutator();\n      var mark = perf.begin('mutate');\n      mutations.map(mutator);\n      mark();\n      callbackFunction();\n    });\n  }\n}\nvar disabled = false;\nfunction disableObservation() {\n  disabled = true;\n}\nfunction enableObservation() {\n  disabled = false;\n}\nvar mo = null;\nfunction observe(options) {\n  if (!MUTATION_OBSERVER) {\n    return;\n  }\n  if (!config.observeMutations) {\n    return;\n  }\n  var _options$treeCallback = options.treeCallback,\n    treeCallback = _options$treeCallback === void 0 ? noop$2 : _options$treeCallback,\n    _options$nodeCallback = options.nodeCallback,\n    nodeCallback = _options$nodeCallback === void 0 ? noop$2 : _options$nodeCallback,\n    _options$pseudoElemen = options.pseudoElementsCallback,\n    pseudoElementsCallback = _options$pseudoElemen === void 0 ? noop$2 : _options$pseudoElemen,\n    _options$observeMutat = options.observeMutationsRoot,\n    observeMutationsRoot = _options$observeMutat === void 0 ? DOCUMENT : _options$observeMutat;\n  mo = new MUTATION_OBSERVER(function (objects) {\n    if (disabled) return;\n    var defaultPrefix = getDefaultUsablePrefix();\n    toArray(objects).forEach(function (mutationRecord) {\n      if (mutationRecord.type === 'childList' && mutationRecord.addedNodes.length > 0 && !isWatched(mutationRecord.addedNodes[0])) {\n        if (config.searchPseudoElements) {\n          pseudoElementsCallback(mutationRecord.target);\n        }\n        treeCallback(mutationRecord.target);\n      }\n      if (mutationRecord.type === 'attributes' && mutationRecord.target.parentNode && config.searchPseudoElements) {\n        pseudoElementsCallback(mutationRecord.target.parentNode);\n      }\n      if (mutationRecord.type === 'attributes' && isWatched(mutationRecord.target) && ~ATTRIBUTES_WATCHED_FOR_MUTATION.indexOf(mutationRecord.attributeName)) {\n        if (mutationRecord.attributeName === 'class' && hasPrefixAndIcon(mutationRecord.target)) {\n          var _getCanonicalIcon = getCanonicalIcon(classArray(mutationRecord.target)),\n            prefix = _getCanonicalIcon.prefix,\n            iconName = _getCanonicalIcon.iconName;\n          mutationRecord.target.setAttribute(DATA_PREFIX, prefix || defaultPrefix);\n          if (iconName) mutationRecord.target.setAttribute(DATA_ICON, iconName);\n        } else if (hasBeenReplaced(mutationRecord.target)) {\n          nodeCallback(mutationRecord.target);\n        }\n      }\n    });\n  });\n  if (!IS_DOM) return;\n  mo.observe(observeMutationsRoot, {\n    childList: true,\n    attributes: true,\n    characterData: true,\n    subtree: true\n  });\n}\nfunction disconnect() {\n  if (!mo) return;\n  mo.disconnect();\n}\nfunction styleParser(node) {\n  var style = node.getAttribute('style');\n  var val = [];\n  if (style) {\n    val = style.split(';').reduce(function (acc, style) {\n      var styles = style.split(':');\n      var prop = styles[0];\n      var value = styles.slice(1);\n      if (prop && value.length > 0) {\n        acc[prop] = value.join(':').trim();\n      }\n      return acc;\n    }, {});\n  }\n  return val;\n}\nfunction classParser(node) {\n  var existingPrefix = node.getAttribute('data-prefix');\n  var existingIconName = node.getAttribute('data-icon');\n  var innerText = node.innerText !== undefined ? node.innerText.trim() : '';\n  var val = getCanonicalIcon(classArray(node));\n  if (!val.prefix) {\n    val.prefix = getDefaultUsablePrefix();\n  }\n  if (existingPrefix && existingIconName) {\n    val.prefix = existingPrefix;\n    val.iconName = existingIconName;\n  }\n  if (val.iconName && val.prefix) {\n    return val;\n  }\n  if (val.prefix && innerText.length > 0) {\n    val.iconName = byLigature(val.prefix, node.innerText) || byUnicode(val.prefix, toHex(node.innerText));\n  }\n  if (!val.iconName && config.autoFetchSvg && node.firstChild && node.firstChild.nodeType === Node.TEXT_NODE) {\n    val.iconName = node.firstChild.data;\n  }\n  return val;\n}\nfunction attributesParser(node) {\n  var extraAttributes = toArray(node.attributes).reduce(function (acc, attr) {\n    if (acc.name !== 'class' && acc.name !== 'style') {\n      acc[attr.name] = attr.value;\n    }\n    return acc;\n  }, {});\n  var title = node.getAttribute('title');\n  var titleId = node.getAttribute('data-fa-title-id');\n  if (config.autoA11y) {\n    if (title) {\n      extraAttributes['aria-labelledby'] = \"\".concat(config.replacementClass, \"-title-\").concat(titleId || nextUniqueId());\n    } else {\n      extraAttributes['aria-hidden'] = 'true';\n      extraAttributes['focusable'] = 'false';\n    }\n  }\n  return extraAttributes;\n}\nfunction blankMeta() {\n  return {\n    iconName: null,\n    title: null,\n    titleId: null,\n    prefix: null,\n    transform: meaninglessTransform,\n    symbol: false,\n    mask: {\n      iconName: null,\n      prefix: null,\n      rest: []\n    },\n    maskId: null,\n    extra: {\n      classes: [],\n      styles: {},\n      attributes: {}\n    }\n  };\n}\nfunction parseMeta(node) {\n  var parser = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    styleParser: true\n  };\n  var _classParser = classParser(node),\n    iconName = _classParser.iconName,\n    prefix = _classParser.prefix,\n    extraClasses = _classParser.rest;\n  var extraAttributes = attributesParser(node);\n  var pluginMeta = chainHooks('parseNodeAttributes', {}, node);\n  var extraStyles = parser.styleParser ? styleParser(node) : [];\n  return _objectSpread2({\n    iconName: iconName,\n    title: node.getAttribute('title'),\n    titleId: node.getAttribute('data-fa-title-id'),\n    prefix: prefix,\n    transform: meaninglessTransform,\n    mask: {\n      iconName: null,\n      prefix: null,\n      rest: []\n    },\n    maskId: null,\n    symbol: false,\n    extra: {\n      classes: extraClasses,\n      styles: extraStyles,\n      attributes: extraAttributes\n    }\n  }, pluginMeta);\n}\nvar styles$2 = namespace.styles;\nfunction generateMutation(node) {\n  var nodeMeta = config.autoReplaceSvg === 'nest' ? parseMeta(node, {\n    styleParser: false\n  }) : parseMeta(node);\n  if (~nodeMeta.extra.classes.indexOf(LAYERS_TEXT_CLASSNAME)) {\n    return callProvided('generateLayersText', node, nodeMeta);\n  } else {\n    return callProvided('generateSvgReplacementMutation', node, nodeMeta);\n  }\n}\nvar knownPrefixes = new Set();\nFAMILIES.map(function (family) {\n  knownPrefixes.add(\"fa-\".concat(family));\n});\nObject.keys(PREFIX_TO_STYLE[FAMILY_CLASSIC]).map(knownPrefixes.add.bind(knownPrefixes));\nObject.keys(PREFIX_TO_STYLE[FAMILY_SHARP]).map(knownPrefixes.add.bind(knownPrefixes));\nknownPrefixes = _toConsumableArray(knownPrefixes);\nfunction onTree(root) {\n  var callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n  if (!IS_DOM) return Promise.resolve();\n  var htmlClassList = DOCUMENT.documentElement.classList;\n  var hclAdd = function hclAdd(suffix) {\n    return htmlClassList.add(\"\".concat(HTML_CLASS_I2SVG_BASE_CLASS, \"-\").concat(suffix));\n  };\n  var hclRemove = function hclRemove(suffix) {\n    return htmlClassList.remove(\"\".concat(HTML_CLASS_I2SVG_BASE_CLASS, \"-\").concat(suffix));\n  };\n  var prefixes = config.autoFetchSvg ? knownPrefixes : FAMILIES.map(function (f) {\n    return \"fa-\".concat(f);\n  }).concat(Object.keys(styles$2));\n  if (!prefixes.includes('fa')) {\n    prefixes.push('fa');\n  }\n  var prefixesDomQuery = [\".\".concat(LAYERS_TEXT_CLASSNAME, \":not([\").concat(DATA_FA_I2SVG, \"])\")].concat(prefixes.map(function (p) {\n    return \".\".concat(p, \":not([\").concat(DATA_FA_I2SVG, \"])\");\n  })).join(', ');\n  if (prefixesDomQuery.length === 0) {\n    return Promise.resolve();\n  }\n  var candidates = [];\n  try {\n    candidates = toArray(root.querySelectorAll(prefixesDomQuery));\n  } catch (e) {// noop\n  }\n  if (candidates.length > 0) {\n    hclAdd('pending');\n    hclRemove('complete');\n  } else {\n    return Promise.resolve();\n  }\n  var mark = perf.begin('onTree');\n  var mutations = candidates.reduce(function (acc, node) {\n    try {\n      var mutation = generateMutation(node);\n      if (mutation) {\n        acc.push(mutation);\n      }\n    } catch (e) {\n      if (!PRODUCTION) {\n        if (e.name === 'MissingIcon') {\n          console.error(e);\n        }\n      }\n    }\n    return acc;\n  }, []);\n  return new Promise(function (resolve, reject) {\n    Promise.all(mutations).then(function (resolvedMutations) {\n      perform(resolvedMutations, function () {\n        hclAdd('active');\n        hclAdd('complete');\n        hclRemove('pending');\n        if (typeof callback === 'function') callback();\n        mark();\n        resolve();\n      });\n    }).catch(function (e) {\n      mark();\n      reject(e);\n    });\n  });\n}\nfunction onNode(node) {\n  var callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n  generateMutation(node).then(function (mutation) {\n    if (mutation) {\n      perform([mutation], callback);\n    }\n  });\n}\nfunction resolveIcons(next) {\n  return function (maybeIconDefinition) {\n    var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var iconDefinition = (maybeIconDefinition || {}).icon ? maybeIconDefinition : findIconDefinition(maybeIconDefinition || {});\n    var mask = params.mask;\n    if (mask) {\n      mask = (mask || {}).icon ? mask : findIconDefinition(mask || {});\n    }\n    return next(iconDefinition, _objectSpread2(_objectSpread2({}, params), {}, {\n      mask: mask\n    }));\n  };\n}\nvar render = function render(iconDefinition) {\n  var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var _params$transform = params.transform,\n    transform = _params$transform === void 0 ? meaninglessTransform : _params$transform,\n    _params$symbol = params.symbol,\n    symbol = _params$symbol === void 0 ? false : _params$symbol,\n    _params$mask = params.mask,\n    mask = _params$mask === void 0 ? null : _params$mask,\n    _params$maskId = params.maskId,\n    maskId = _params$maskId === void 0 ? null : _params$maskId,\n    _params$title = params.title,\n    title = _params$title === void 0 ? null : _params$title,\n    _params$titleId = params.titleId,\n    titleId = _params$titleId === void 0 ? null : _params$titleId,\n    _params$classes = params.classes,\n    classes = _params$classes === void 0 ? [] : _params$classes,\n    _params$attributes = params.attributes,\n    attributes = _params$attributes === void 0 ? {} : _params$attributes,\n    _params$styles = params.styles,\n    styles = _params$styles === void 0 ? {} : _params$styles;\n  if (!iconDefinition) return;\n  var prefix = iconDefinition.prefix,\n    iconName = iconDefinition.iconName,\n    icon = iconDefinition.icon;\n  return domVariants(_objectSpread2({\n    type: 'icon'\n  }, iconDefinition), function () {\n    callHooks('beforeDOMElementCreation', {\n      iconDefinition: iconDefinition,\n      params: params\n    });\n    if (config.autoA11y) {\n      if (title) {\n        attributes['aria-labelledby'] = \"\".concat(config.replacementClass, \"-title-\").concat(titleId || nextUniqueId());\n      } else {\n        attributes['aria-hidden'] = 'true';\n        attributes['focusable'] = 'false';\n      }\n    }\n    return makeInlineSvgAbstract({\n      icons: {\n        main: asFoundIcon(icon),\n        mask: mask ? asFoundIcon(mask.icon) : {\n          found: false,\n          width: null,\n          height: null,\n          icon: {}\n        }\n      },\n      prefix: prefix,\n      iconName: iconName,\n      transform: _objectSpread2(_objectSpread2({}, meaninglessTransform), transform),\n      symbol: symbol,\n      title: title,\n      maskId: maskId,\n      titleId: titleId,\n      extra: {\n        attributes: attributes,\n        styles: styles,\n        classes: classes\n      }\n    });\n  });\n};\nvar ReplaceElements = {\n  mixout: function mixout() {\n    return {\n      icon: resolveIcons(render)\n    };\n  },\n  hooks: function hooks() {\n    return {\n      mutationObserverCallbacks: function mutationObserverCallbacks(accumulator) {\n        accumulator.treeCallback = onTree;\n        accumulator.nodeCallback = onNode;\n        return accumulator;\n      }\n    };\n  },\n  provides: function provides(providers$$1) {\n    providers$$1.i2svg = function (params) {\n      var _params$node = params.node,\n        node = _params$node === void 0 ? DOCUMENT : _params$node,\n        _params$callback = params.callback,\n        callback = _params$callback === void 0 ? function () {} : _params$callback;\n      return onTree(node, callback);\n    };\n    providers$$1.generateSvgReplacementMutation = function (node, nodeMeta) {\n      var iconName = nodeMeta.iconName,\n        title = nodeMeta.title,\n        titleId = nodeMeta.titleId,\n        prefix = nodeMeta.prefix,\n        transform = nodeMeta.transform,\n        symbol = nodeMeta.symbol,\n        mask = nodeMeta.mask,\n        maskId = nodeMeta.maskId,\n        extra = nodeMeta.extra;\n      return new Promise(function (resolve, reject) {\n        Promise.all([findIcon(iconName, prefix), mask.iconName ? findIcon(mask.iconName, mask.prefix) : Promise.resolve({\n          found: false,\n          width: 512,\n          height: 512,\n          icon: {}\n        })]).then(function (_ref) {\n          var _ref2 = _slicedToArray(_ref, 2),\n            main = _ref2[0],\n            mask = _ref2[1];\n          resolve([node, makeInlineSvgAbstract({\n            icons: {\n              main: main,\n              mask: mask\n            },\n            prefix: prefix,\n            iconName: iconName,\n            transform: transform,\n            symbol: symbol,\n            maskId: maskId,\n            title: title,\n            titleId: titleId,\n            extra: extra,\n            watchable: true\n          })]);\n        }).catch(reject);\n      });\n    };\n    providers$$1.generateAbstractIcon = function (_ref3) {\n      var children = _ref3.children,\n        attributes = _ref3.attributes,\n        main = _ref3.main,\n        transform = _ref3.transform,\n        styles = _ref3.styles;\n      var styleString = joinStyles(styles);\n      if (styleString.length > 0) {\n        attributes['style'] = styleString;\n      }\n      var nextChild;\n      if (transformIsMeaningful(transform)) {\n        nextChild = callProvided('generateAbstractTransformGrouping', {\n          main: main,\n          transform: transform,\n          containerWidth: main.width,\n          iconWidth: main.width\n        });\n      }\n      children.push(nextChild || main.icon);\n      return {\n        children: children,\n        attributes: attributes\n      };\n    };\n  }\n};\nvar Layers = {\n  mixout: function mixout() {\n    return {\n      layer: function layer(assembler) {\n        var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        var _params$classes = params.classes,\n          classes = _params$classes === void 0 ? [] : _params$classes;\n        return domVariants({\n          type: 'layer'\n        }, function () {\n          callHooks('beforeDOMElementCreation', {\n            assembler: assembler,\n            params: params\n          });\n          var children = [];\n          assembler(function (args) {\n            Array.isArray(args) ? args.map(function (a) {\n              children = children.concat(a.abstract);\n            }) : children = children.concat(args.abstract);\n          });\n          return [{\n            tag: 'span',\n            attributes: {\n              class: [\"\".concat(config.cssPrefix, \"-layers\")].concat(_toConsumableArray(classes)).join(' ')\n            },\n            children: children\n          }];\n        });\n      }\n    };\n  }\n};\nvar LayersCounter = {\n  mixout: function mixout() {\n    return {\n      counter: function counter(content) {\n        var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        var _params$title = params.title,\n          title = _params$title === void 0 ? null : _params$title,\n          _params$classes = params.classes,\n          classes = _params$classes === void 0 ? [] : _params$classes,\n          _params$attributes = params.attributes,\n          attributes = _params$attributes === void 0 ? {} : _params$attributes,\n          _params$styles = params.styles,\n          styles = _params$styles === void 0 ? {} : _params$styles;\n        return domVariants({\n          type: 'counter',\n          content: content\n        }, function () {\n          callHooks('beforeDOMElementCreation', {\n            content: content,\n            params: params\n          });\n          return makeLayersCounterAbstract({\n            content: content.toString(),\n            title: title,\n            extra: {\n              attributes: attributes,\n              styles: styles,\n              classes: [\"\".concat(config.cssPrefix, \"-layers-counter\")].concat(_toConsumableArray(classes))\n            }\n          });\n        });\n      }\n    };\n  }\n};\nvar LayersText = {\n  mixout: function mixout() {\n    return {\n      text: function text(content) {\n        var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        var _params$transform = params.transform,\n          transform = _params$transform === void 0 ? meaninglessTransform : _params$transform,\n          _params$title = params.title,\n          title = _params$title === void 0 ? null : _params$title,\n          _params$classes = params.classes,\n          classes = _params$classes === void 0 ? [] : _params$classes,\n          _params$attributes = params.attributes,\n          attributes = _params$attributes === void 0 ? {} : _params$attributes,\n          _params$styles = params.styles,\n          styles = _params$styles === void 0 ? {} : _params$styles;\n        return domVariants({\n          type: 'text',\n          content: content\n        }, function () {\n          callHooks('beforeDOMElementCreation', {\n            content: content,\n            params: params\n          });\n          return makeLayersTextAbstract({\n            content: content,\n            transform: _objectSpread2(_objectSpread2({}, meaninglessTransform), transform),\n            title: title,\n            extra: {\n              attributes: attributes,\n              styles: styles,\n              classes: [\"\".concat(config.cssPrefix, \"-layers-text\")].concat(_toConsumableArray(classes))\n            }\n          });\n        });\n      }\n    };\n  },\n  provides: function provides(providers$$1) {\n    providers$$1.generateLayersText = function (node, nodeMeta) {\n      var title = nodeMeta.title,\n        transform = nodeMeta.transform,\n        extra = nodeMeta.extra;\n      var width = null;\n      var height = null;\n      if (IS_IE) {\n        var computedFontSize = parseInt(getComputedStyle(node).fontSize, 10);\n        var boundingClientRect = node.getBoundingClientRect();\n        width = boundingClientRect.width / computedFontSize;\n        height = boundingClientRect.height / computedFontSize;\n      }\n      if (config.autoA11y && !title) {\n        extra.attributes['aria-hidden'] = 'true';\n      }\n      return Promise.resolve([node, makeLayersTextAbstract({\n        content: node.innerHTML,\n        width: width,\n        height: height,\n        transform: transform,\n        title: title,\n        extra: extra,\n        watchable: true\n      })]);\n    };\n  }\n};\nvar CLEAN_CONTENT_PATTERN = new RegExp(\"\\\"\", 'ug');\nvar SECONDARY_UNICODE_RANGE = [1105920, 1112319];\nfunction hexValueFromContent(content) {\n  var cleaned = content.replace(CLEAN_CONTENT_PATTERN, '');\n  var codePoint = codePointAt(cleaned, 0);\n  var isPrependTen = codePoint >= SECONDARY_UNICODE_RANGE[0] && codePoint <= SECONDARY_UNICODE_RANGE[1];\n  var isDoubled = cleaned.length === 2 ? cleaned[0] === cleaned[1] : false;\n  return {\n    value: isDoubled ? toHex(cleaned[0]) : toHex(cleaned),\n    isSecondary: isPrependTen || isDoubled\n  };\n}\nfunction replaceForPosition(node, position) {\n  var pendingAttribute = \"\".concat(DATA_FA_PSEUDO_ELEMENT_PENDING).concat(position.replace(':', '-'));\n  return new Promise(function (resolve, reject) {\n    if (node.getAttribute(pendingAttribute) !== null) {\n      // This node is already being processed\n      return resolve();\n    }\n    var children = toArray(node.children);\n    var alreadyProcessedPseudoElement = children.filter(function (c) {\n      return c.getAttribute(DATA_FA_PSEUDO_ELEMENT) === position;\n    })[0];\n    var styles = WINDOW.getComputedStyle(node, position);\n    var fontFamily = styles.getPropertyValue('font-family').match(FONT_FAMILY_PATTERN);\n    var fontWeight = styles.getPropertyValue('font-weight');\n    var content = styles.getPropertyValue('content');\n    if (alreadyProcessedPseudoElement && !fontFamily) {\n      // If we've already processed it but the current computed style does not result in a font-family,\n      // that probably means that a class name that was previously present to make the icon has been\n      // removed. So we now should delete the icon.\n      node.removeChild(alreadyProcessedPseudoElement);\n      return resolve();\n    } else if (fontFamily && content !== 'none' && content !== '') {\n      var _content = styles.getPropertyValue('content');\n      var family = ~['Sharp'].indexOf(fontFamily[2]) ? FAMILY_SHARP : FAMILY_CLASSIC;\n      var prefix = ~['Solid', 'Regular', 'Light', 'Thin', 'Duotone', 'Brands', 'Kit'].indexOf(fontFamily[2]) ? STYLE_TO_PREFIX[family][fontFamily[2].toLowerCase()] : FONT_WEIGHT_TO_PREFIX[family][fontWeight];\n      var _hexValueFromContent = hexValueFromContent(_content),\n        hexValue = _hexValueFromContent.value,\n        isSecondary = _hexValueFromContent.isSecondary;\n      var isV4 = fontFamily[0].startsWith('FontAwesome');\n      var iconName = byUnicode(prefix, hexValue);\n      var iconIdentifier = iconName;\n      if (isV4) {\n        var iconName4 = byOldUnicode(hexValue);\n        if (iconName4.iconName && iconName4.prefix) {\n          iconName = iconName4.iconName;\n          prefix = iconName4.prefix;\n        }\n      } // Only convert the pseudo element in this ::before/::after position into an icon if we haven't\n      // already done so with the same prefix and iconName\n\n      if (iconName && !isSecondary && (!alreadyProcessedPseudoElement || alreadyProcessedPseudoElement.getAttribute(DATA_PREFIX) !== prefix || alreadyProcessedPseudoElement.getAttribute(DATA_ICON) !== iconIdentifier)) {\n        node.setAttribute(pendingAttribute, iconIdentifier);\n        if (alreadyProcessedPseudoElement) {\n          // Delete the old one, since we're replacing it with a new one\n          node.removeChild(alreadyProcessedPseudoElement);\n        }\n        var meta = blankMeta();\n        var extra = meta.extra;\n        extra.attributes[DATA_FA_PSEUDO_ELEMENT] = position;\n        findIcon(iconName, prefix).then(function (main) {\n          var _abstract = makeInlineSvgAbstract(_objectSpread2(_objectSpread2({}, meta), {}, {\n            icons: {\n              main: main,\n              mask: emptyCanonicalIcon()\n            },\n            prefix: prefix,\n            iconName: iconIdentifier,\n            extra: extra,\n            watchable: true\n          }));\n          var element = DOCUMENT.createElementNS('http://www.w3.org/2000/svg', 'svg');\n          if (position === '::before') {\n            node.insertBefore(element, node.firstChild);\n          } else {\n            node.appendChild(element);\n          }\n          element.outerHTML = _abstract.map(function (a) {\n            return toHtml(a);\n          }).join('\\n');\n          node.removeAttribute(pendingAttribute);\n          resolve();\n        }).catch(reject);\n      } else {\n        resolve();\n      }\n    } else {\n      resolve();\n    }\n  });\n}\nfunction replace(node) {\n  return Promise.all([replaceForPosition(node, '::before'), replaceForPosition(node, '::after')]);\n}\nfunction processable(node) {\n  return node.parentNode !== document.head && !~TAGNAMES_TO_SKIP_FOR_PSEUDOELEMENTS.indexOf(node.tagName.toUpperCase()) && !node.getAttribute(DATA_FA_PSEUDO_ELEMENT) && (!node.parentNode || node.parentNode.tagName !== 'svg');\n}\nfunction searchPseudoElements(root) {\n  if (!IS_DOM) return;\n  return new Promise(function (resolve, reject) {\n    var operations = toArray(root.querySelectorAll('*')).filter(processable).map(replace);\n    var end = perf.begin('searchPseudoElements');\n    disableObservation();\n    Promise.all(operations).then(function () {\n      end();\n      enableObservation();\n      resolve();\n    }).catch(function () {\n      end();\n      enableObservation();\n      reject();\n    });\n  });\n}\nvar PseudoElements = {\n  hooks: function hooks() {\n    return {\n      mutationObserverCallbacks: function mutationObserverCallbacks(accumulator) {\n        accumulator.pseudoElementsCallback = searchPseudoElements;\n        return accumulator;\n      }\n    };\n  },\n  provides: function provides(providers$$1) {\n    providers$$1.pseudoElements2svg = function (params) {\n      var _params$node = params.node,\n        node = _params$node === void 0 ? DOCUMENT : _params$node;\n      if (config.searchPseudoElements) {\n        searchPseudoElements(node);\n      }\n    };\n  }\n};\nvar _unwatched = false;\nvar MutationObserver$1 = {\n  mixout: function mixout() {\n    return {\n      dom: {\n        unwatch: function unwatch() {\n          disableObservation();\n          _unwatched = true;\n        }\n      }\n    };\n  },\n  hooks: function hooks() {\n    return {\n      bootstrap: function bootstrap() {\n        observe(chainHooks('mutationObserverCallbacks', {}));\n      },\n      noAuto: function noAuto() {\n        disconnect();\n      },\n      watch: function watch(params) {\n        var observeMutationsRoot = params.observeMutationsRoot;\n        if (_unwatched) {\n          enableObservation();\n        } else {\n          observe(chainHooks('mutationObserverCallbacks', {\n            observeMutationsRoot: observeMutationsRoot\n          }));\n        }\n      }\n    };\n  }\n};\nvar parseTransformString = function parseTransformString(transformString) {\n  var transform = {\n    size: 16,\n    x: 0,\n    y: 0,\n    flipX: false,\n    flipY: false,\n    rotate: 0\n  };\n  return transformString.toLowerCase().split(' ').reduce(function (acc, n) {\n    var parts = n.toLowerCase().split('-');\n    var first = parts[0];\n    var rest = parts.slice(1).join('-');\n    if (first && rest === 'h') {\n      acc.flipX = true;\n      return acc;\n    }\n    if (first && rest === 'v') {\n      acc.flipY = true;\n      return acc;\n    }\n    rest = parseFloat(rest);\n    if (isNaN(rest)) {\n      return acc;\n    }\n    switch (first) {\n      case 'grow':\n        acc.size = acc.size + rest;\n        break;\n      case 'shrink':\n        acc.size = acc.size - rest;\n        break;\n      case 'left':\n        acc.x = acc.x - rest;\n        break;\n      case 'right':\n        acc.x = acc.x + rest;\n        break;\n      case 'up':\n        acc.y = acc.y - rest;\n        break;\n      case 'down':\n        acc.y = acc.y + rest;\n        break;\n      case 'rotate':\n        acc.rotate = acc.rotate + rest;\n        break;\n    }\n    return acc;\n  }, transform);\n};\nvar PowerTransforms = {\n  mixout: function mixout() {\n    return {\n      parse: {\n        transform: function transform(transformString) {\n          return parseTransformString(transformString);\n        }\n      }\n    };\n  },\n  hooks: function hooks() {\n    return {\n      parseNodeAttributes: function parseNodeAttributes(accumulator, node) {\n        var transformString = node.getAttribute('data-fa-transform');\n        if (transformString) {\n          accumulator.transform = parseTransformString(transformString);\n        }\n        return accumulator;\n      }\n    };\n  },\n  provides: function provides(providers) {\n    providers.generateAbstractTransformGrouping = function (_ref) {\n      var main = _ref.main,\n        transform = _ref.transform,\n        containerWidth = _ref.containerWidth,\n        iconWidth = _ref.iconWidth;\n      var outer = {\n        transform: \"translate(\".concat(containerWidth / 2, \" 256)\")\n      };\n      var innerTranslate = \"translate(\".concat(transform.x * 32, \", \").concat(transform.y * 32, \") \");\n      var innerScale = \"scale(\".concat(transform.size / 16 * (transform.flipX ? -1 : 1), \", \").concat(transform.size / 16 * (transform.flipY ? -1 : 1), \") \");\n      var innerRotate = \"rotate(\".concat(transform.rotate, \" 0 0)\");\n      var inner = {\n        transform: \"\".concat(innerTranslate, \" \").concat(innerScale, \" \").concat(innerRotate)\n      };\n      var path = {\n        transform: \"translate(\".concat(iconWidth / 2 * -1, \" -256)\")\n      };\n      var operations = {\n        outer: outer,\n        inner: inner,\n        path: path\n      };\n      return {\n        tag: 'g',\n        attributes: _objectSpread2({}, operations.outer),\n        children: [{\n          tag: 'g',\n          attributes: _objectSpread2({}, operations.inner),\n          children: [{\n            tag: main.icon.tag,\n            children: main.icon.children,\n            attributes: _objectSpread2(_objectSpread2({}, main.icon.attributes), operations.path)\n          }]\n        }]\n      };\n    };\n  }\n};\nvar ALL_SPACE = {\n  x: 0,\n  y: 0,\n  width: '100%',\n  height: '100%'\n};\nfunction fillBlack(_abstract) {\n  var force = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  if (_abstract.attributes && (_abstract.attributes.fill || force)) {\n    _abstract.attributes.fill = 'black';\n  }\n  return _abstract;\n}\nfunction deGroup(_abstract2) {\n  if (_abstract2.tag === 'g') {\n    return _abstract2.children;\n  } else {\n    return [_abstract2];\n  }\n}\nvar Masks = {\n  hooks: function hooks() {\n    return {\n      parseNodeAttributes: function parseNodeAttributes(accumulator, node) {\n        var maskData = node.getAttribute('data-fa-mask');\n        var mask = !maskData ? emptyCanonicalIcon() : getCanonicalIcon(maskData.split(' ').map(function (i) {\n          return i.trim();\n        }));\n        if (!mask.prefix) {\n          mask.prefix = getDefaultUsablePrefix();\n        }\n        accumulator.mask = mask;\n        accumulator.maskId = node.getAttribute('data-fa-mask-id');\n        return accumulator;\n      }\n    };\n  },\n  provides: function provides(providers) {\n    providers.generateAbstractMask = function (_ref) {\n      var children = _ref.children,\n        attributes = _ref.attributes,\n        main = _ref.main,\n        mask = _ref.mask,\n        explicitMaskId = _ref.maskId,\n        transform = _ref.transform;\n      var mainWidth = main.width,\n        mainPath = main.icon;\n      var maskWidth = mask.width,\n        maskPath = mask.icon;\n      var trans = transformForSvg({\n        transform: transform,\n        containerWidth: maskWidth,\n        iconWidth: mainWidth\n      });\n      var maskRect = {\n        tag: 'rect',\n        attributes: _objectSpread2(_objectSpread2({}, ALL_SPACE), {}, {\n          fill: 'white'\n        })\n      };\n      var maskInnerGroupChildrenMixin = mainPath.children ? {\n        children: mainPath.children.map(fillBlack)\n      } : {};\n      var maskInnerGroup = {\n        tag: 'g',\n        attributes: _objectSpread2({}, trans.inner),\n        children: [fillBlack(_objectSpread2({\n          tag: mainPath.tag,\n          attributes: _objectSpread2(_objectSpread2({}, mainPath.attributes), trans.path)\n        }, maskInnerGroupChildrenMixin))]\n      };\n      var maskOuterGroup = {\n        tag: 'g',\n        attributes: _objectSpread2({}, trans.outer),\n        children: [maskInnerGroup]\n      };\n      var maskId = \"mask-\".concat(explicitMaskId || nextUniqueId());\n      var clipId = \"clip-\".concat(explicitMaskId || nextUniqueId());\n      var maskTag = {\n        tag: 'mask',\n        attributes: _objectSpread2(_objectSpread2({}, ALL_SPACE), {}, {\n          id: maskId,\n          maskUnits: 'userSpaceOnUse',\n          maskContentUnits: 'userSpaceOnUse'\n        }),\n        children: [maskRect, maskOuterGroup]\n      };\n      var defs = {\n        tag: 'defs',\n        children: [{\n          tag: 'clipPath',\n          attributes: {\n            id: clipId\n          },\n          children: deGroup(maskPath)\n        }, maskTag]\n      };\n      children.push(defs, {\n        tag: 'rect',\n        attributes: _objectSpread2({\n          fill: 'currentColor',\n          'clip-path': \"url(#\".concat(clipId, \")\"),\n          mask: \"url(#\".concat(maskId, \")\")\n        }, ALL_SPACE)\n      });\n      return {\n        children: children,\n        attributes: attributes\n      };\n    };\n  }\n};\nvar MissingIconIndicator = {\n  provides: function provides(providers) {\n    var reduceMotion = false;\n    if (WINDOW.matchMedia) {\n      reduceMotion = WINDOW.matchMedia('(prefers-reduced-motion: reduce)').matches;\n    }\n    providers.missingIconAbstract = function () {\n      var gChildren = [];\n      var FILL = {\n        fill: 'currentColor'\n      };\n      var ANIMATION_BASE = {\n        attributeType: 'XML',\n        repeatCount: 'indefinite',\n        dur: '2s'\n      }; // Ring\n\n      gChildren.push({\n        tag: 'path',\n        attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\n          d: 'M156.5,447.7l-12.6,29.5c-18.7-9.5-35.9-21.2-51.5-34.9l22.7-22.7C127.6,430.5,141.5,440,156.5,447.7z M40.6,272H8.5 c1.4,21.2,5.4,41.7,11.7,61.1L50,321.2C45.1,305.5,41.8,289,40.6,272z M40.6,240c1.4-18.8,5.2-37,11.1-54.1l-29.5-12.6 C14.7,194.3,10,216.7,8.5,240H40.6z M64.3,156.5c7.8-14.9,17.2-28.8,28.1-41.5L69.7,92.3c-13.7,15.6-25.5,32.8-34.9,51.5 L64.3,156.5z M397,419.6c-13.9,12-29.4,22.3-46.1,30.4l11.9,29.8c20.7-9.9,39.8-22.6,56.9-37.6L397,419.6z M115,92.4 c13.9-12,29.4-22.3,46.1-30.4l-11.9-29.8c-20.7,9.9-39.8,22.6-56.8,37.6L115,92.4z M447.7,355.5c-7.8,14.9-17.2,28.8-28.1,41.5 l22.7,22.7c13.7-15.6,25.5-32.9,34.9-51.5L447.7,355.5z M471.4,272c-1.4,18.8-5.2,37-11.1,54.1l29.5,12.6 c7.5-21.1,12.2-43.5,13.6-66.8H471.4z M321.2,462c-15.7,5-32.2,8.2-49.2,9.4v32.1c21.2-1.4,41.7-5.4,61.1-11.7L321.2,462z M240,471.4c-18.8-1.4-37-5.2-54.1-11.1l-12.6,29.5c21.1,7.5,43.5,12.2,66.8,13.6V471.4z M462,190.8c5,15.7,8.2,32.2,9.4,49.2h32.1 c-1.4-21.2-5.4-41.7-11.7-61.1L462,190.8z M92.4,397c-12-13.9-22.3-29.4-30.4-46.1l-29.8,11.9c9.9,20.7,22.6,39.8,37.6,56.9 L92.4,397z M272,40.6c18.8,1.4,36.9,5.2,54.1,11.1l12.6-29.5C317.7,14.7,295.3,10,272,8.5V40.6z M190.8,50 c15.7-5,32.2-8.2,49.2-9.4V8.5c-21.2,1.4-41.7,5.4-61.1,11.7L190.8,50z M442.3,92.3L419.6,115c12,13.9,22.3,29.4,30.5,46.1 l29.8-11.9C470,128.5,457.3,109.4,442.3,92.3z M397,92.4l22.7-22.7c-15.6-13.7-32.8-25.5-51.5-34.9l-12.6,29.5 C370.4,72.1,384.4,81.5,397,92.4z'\n        })\n      });\n      var OPACITY_ANIMATE = _objectSpread2(_objectSpread2({}, ANIMATION_BASE), {}, {\n        attributeName: 'opacity'\n      });\n      var dot = {\n        tag: 'circle',\n        attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\n          cx: '256',\n          cy: '364',\n          r: '28'\n        }),\n        children: []\n      };\n      if (!reduceMotion) {\n        dot.children.push({\n          tag: 'animate',\n          attributes: _objectSpread2(_objectSpread2({}, ANIMATION_BASE), {}, {\n            attributeName: 'r',\n            values: '28;14;28;28;14;28;'\n          })\n        }, {\n          tag: 'animate',\n          attributes: _objectSpread2(_objectSpread2({}, OPACITY_ANIMATE), {}, {\n            values: '1;0;1;1;0;1;'\n          })\n        });\n      }\n      gChildren.push(dot);\n      gChildren.push({\n        tag: 'path',\n        attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\n          opacity: '1',\n          d: 'M263.7,312h-16c-6.6,0-12-5.4-12-12c0-71,77.4-63.9,77.4-107.8c0-20-17.8-40.2-57.4-40.2c-29.1,0-44.3,9.6-59.2,28.7 c-3.9,5-11.1,6-16.2,2.4l-13.1-9.2c-5.6-3.9-6.9-11.8-2.6-17.2c21.2-27.2,46.4-44.7,91.2-44.7c52.3,0,97.4,29.8,97.4,80.2 c0,67.6-77.4,63.5-77.4,107.8C275.7,306.6,270.3,312,263.7,312z'\n        }),\n        children: reduceMotion ? [] : [{\n          tag: 'animate',\n          attributes: _objectSpread2(_objectSpread2({}, OPACITY_ANIMATE), {}, {\n            values: '1;0;0;0;0;1;'\n          })\n        }]\n      });\n      if (!reduceMotion) {\n        // Exclamation\n        gChildren.push({\n          tag: 'path',\n          attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\n            opacity: '0',\n            d: 'M232.5,134.5l7,168c0.3,6.4,5.6,11.5,12,11.5h9c6.4,0,11.7-5.1,12-11.5l7-168c0.3-6.8-5.2-12.5-12-12.5h-23 C237.7,122,232.2,127.7,232.5,134.5z'\n          }),\n          children: [{\n            tag: 'animate',\n            attributes: _objectSpread2(_objectSpread2({}, OPACITY_ANIMATE), {}, {\n              values: '0;0;1;1;0;0;'\n            })\n          }]\n        });\n      }\n      return {\n        tag: 'g',\n        attributes: {\n          'class': 'missing'\n        },\n        children: gChildren\n      };\n    };\n  }\n};\nvar SvgSymbols = {\n  hooks: function hooks() {\n    return {\n      parseNodeAttributes: function parseNodeAttributes(accumulator, node) {\n        var symbolData = node.getAttribute('data-fa-symbol');\n        var symbol = symbolData === null ? false : symbolData === '' ? true : symbolData;\n        accumulator['symbol'] = symbol;\n        return accumulator;\n      }\n    };\n  }\n};\nvar plugins = [InjectCSS, ReplaceElements, Layers, LayersCounter, LayersText, PseudoElements, MutationObserver$1, PowerTransforms, Masks, MissingIconIndicator, SvgSymbols];\nregisterPlugins(plugins, {\n  mixoutsTo: api\n});\nvar noAuto$1 = api.noAuto;\nvar config$1 = api.config;\nvar library$1 = api.library;\nvar dom$1 = api.dom;\nvar parse$1 = api.parse;\nvar findIconDefinition$1 = api.findIconDefinition;\nvar toHtml$1 = api.toHtml;\nvar icon = api.icon;\nvar layer = api.layer;\nvar text = api.text;\nvar counter = api.counter;\nexport { noAuto$1 as noAuto, config$1 as config, library$1 as library, dom$1 as dom, parse$1 as parse, findIconDefinition$1 as findIconDefinition, toHtml$1 as toHtml, icon, layer, text, counter, api };", "map": {"version": 3, "names": ["ownKeys", "object", "enumerableOnly", "keys", "Object", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread2", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "key", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "_wrapRegExp", "re", "groups", "BabelRegExp", "_super", "RegExp", "_groups", "WeakMap", "flags", "_this", "set", "get", "_setPrototypeOf", "buildGroups", "result", "g", "reduce", "name", "create", "_inherits", "exec", "str", "call", "replace", "substitution", "_", "args", "slice", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "props", "descriptor", "configurable", "writable", "_createClass", "protoProps", "staticProps", "value", "subClass", "superClass", "o", "p", "setPrototypeOf", "__proto__", "_slicedToArray", "arr", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "_toConsumableArray", "_arrayWithoutHoles", "_iterableToArray", "_nonIterableSpread", "Array", "isArray", "_arrayLikeToArray", "iter", "from", "_i", "_arr", "_n", "_d", "_s", "_e", "next", "done", "err", "minLen", "n", "toString", "test", "len", "arr2", "noop", "_WINDOW", "_DOCUMENT", "_MUTATION_OBSERVER", "_PERFORMANCE", "mark", "measure", "window", "document", "MutationObserver", "performance", "e", "_ref", "navigator", "_ref$userAgent", "userAgent", "WINDOW", "DOCUMENT", "MUTATION_OBSERVER", "PERFORMANCE", "IS_BROWSER", "IS_DOM", "documentElement", "head", "addEventListener", "createElement", "IS_IE", "indexOf", "_familyProxy", "_familyProxy2", "_familyProxy3", "_familyProxy4", "_familyProxy5", "NAMESPACE_IDENTIFIER", "UNITS_IN_GRID", "DEFAULT_CSS_PREFIX", "DEFAULT_REPLACEMENT_CLASS", "DATA_FA_I2SVG", "DATA_FA_PSEUDO_ELEMENT", "DATA_FA_PSEUDO_ELEMENT_PENDING", "DATA_PREFIX", "DATA_ICON", "HTML_CLASS_I2SVG_BASE_CLASS", "MUTATION_APPROACH_ASYNC", "TAGNAMES_TO_SKIP_FOR_PSEUDOELEMENTS", "PRODUCTION", "process", "env", "NODE_ENV", "FAMILY_CLASSIC", "FAMILY_SHARP", "FAMILIES", "familyProxy", "Proxy", "prop", "PREFIX_TO_STYLE", "STYLE_TO_PREFIX", "solid", "regular", "light", "thin", "duotone", "brands", "kit", "PREFIX_TO_LONG_STYLE", "fab", "fad", "fak", "fal", "far", "fas", "fat", "fass", "fasr", "fasl", "fast", "LONG_STYLE_TO_PREFIX", "ICON_SELECTION_SYNTAX_PATTERN", "LAYERS_TEXT_CLASSNAME", "FONT_FAMILY_PATTERN", "FONT_WEIGHT_TO_PREFIX", "normal", "oneToTen", "oneToTwenty", "concat", "ATTRIBUTES_WATCHED_FOR_MUTATION", "DUOTONE_CLASSES", "GROUP", "SWAP_OPACITY", "PRIMARY", "SECONDARY", "prefixes", "Set", "map", "add", "bind", "RESERVED_CLASSES", "initial", "FontAwesomeConfig", "getAttrConfig", "attr", "element", "querySelector", "getAttribute", "coerce", "val", "attrs", "_ref2", "undefined", "_default", "styleDefault", "<PERSON><PERSON><PERSON><PERSON>", "cssPrefix", "replacementClass", "autoReplaceSvg", "autoAddCss", "autoA11y", "searchPseudoElements", "observeMutations", "mutateApproach", "keepOriginalSource", "measurePerformance", "showMissingIcons", "familyPrefix", "_config", "config", "_onChangeCb", "cb", "onChange", "splice", "d", "meaninglessTransform", "size", "x", "y", "rotate", "flipX", "flipY", "insertCss", "css", "style", "setAttribute", "innerHTML", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "childNodes", "<PERSON><PERSON><PERSON><PERSON>", "child", "tagName", "toUpperCase", "insertBefore", "idPool", "nextUniqueId", "id", "Math", "random", "toArray", "array", "classArray", "node", "classList", "split", "htmlEscape", "joinAttributes", "attributes", "acc", "attributeName", "trim", "joinStyles", "styles", "styleName", "transformIsMeaningful", "transform", "transformForSvg", "containerWidth", "iconWidth", "outer", "innerTranslate", "innerScale", "innerRotate", "inner", "path", "transformForCss", "_ref2$width", "width", "_ref2$height", "height", "_ref2$startCentered", "startCentered", "baseStyles", "dcp", "drc", "fp", "rc", "s", "dPatt", "customPropPatt", "rPatt", "_cssInserted", "ensureCss", "InjectCSS", "mixout", "dom", "hooks", "beforeDOMElementCreation", "beforeI2svg", "w", "shims", "namespace", "functions", "listener", "removeEventListener", "loaded", "fn", "doScroll", "readyState", "<PERSON><PERSON><PERSON><PERSON>", "setTimeout", "toHtml", "abstractNodes", "tag", "_abstractNodes$attrib", "_abstractNodes$childr", "children", "join", "iconFromMapping", "mapping", "prefix", "iconName", "icon", "bindInternal4", "func", "thisContext", "a", "b", "c", "fastReduceObject", "subject", "initialValue", "ucs2decode", "string", "output", "counter", "charCodeAt", "extra", "toHex", "unicode", "decoded", "codePointAt", "index", "first", "second", "normalizeIcons", "icons", "expanded", "defineIcons", "params", "_params$skipHooks", "skip<PERSON><PERSON>s", "normalized", "addPack", "duotonePathRe", "d1", "d2", "cls1", "cls2", "_LONG_STYLE", "_PREFIXES", "_PREFIXES_FOR_FAMILY", "LONG_STYLE", "values", "_defaultUsablePrefix", "_byUnicode", "_byLigature", "_byOldName", "_byOldUnicode", "_by<PERSON><PERSON><PERSON>", "PREFIXES", "isReserved", "getIconName", "cls", "parts", "build", "lookup", "reducer", "aliases", "alias", "hasRegular", "autoFetchSvg", "shim<PERSON><PERSON><PERSON>", "shim", "maybeNameMaybeUnicode", "names", "unicodes", "getCanonicalPrefix", "family", "byUnicode", "byLigature", "ligature", "<PERSON><PERSON><PERSON><PERSON>", "byOldName", "byOldUnicode", "oldUnicode", "newUnicode", "getDefaultUsablePrefix", "emptyCanonicalIcon", "rest", "styleOrPrefix", "_params$family", "defined", "PREFIXES_FOR_FAMILY", "getCanonicalIcon", "_famProps", "_params$skipLookups", "skipLookups", "famProps", "givenPrefix", "includes", "some", "v", "canonical", "aliasIconName", "Library", "definitions", "_len", "_key", "additions", "_pullDefinitions", "longPrefix", "reset", "definition", "_normalized$key", "_plugins", "_hooks", "providers", "defaultProviderKeys", "registerPlugins", "nextPlugins", "mixoutsTo", "k", "plugin", "tk", "sk", "hook", "provides", "chainHooks", "accumulator", "hookFns", "hookFn", "callHooks", "_len2", "_key2", "callProvided", "findIconDefinition", "iconLookup", "library", "noAuto", "i2svg", "Promise", "reject", "watch", "autoReplaceSvgRoot", "autoReplace", "parse", "_icon", "match", "canonicalIcon", "_prefix", "api", "_params$autoReplaceSv", "dom<PERSON><PERSON><PERSON>", "abstractCreator", "abstract", "container", "html", "asIcon", "main", "mask", "found", "offset", "asSymbol", "symbol", "makeInlineSvgAbstract", "_params$icons", "title", "maskId", "titleId", "_params$watchable", "watchable", "isUploadedIcon", "attrClass", "classes", "content", "role", "uploadedIconWidthStyle", "makeLayersTextAbstract", "_params$watchable2", "styleString", "class", "makeLayersCounterAbstract", "styles$1", "asFoundIcon", "_icon$slice", "_icon$slice2", "vectorData", "fill", "missingIconResolutionMixin", "maybeNotifyMissing", "console", "error", "findIcon", "resolve", "noop$1", "preamble", "begin", "end", "perf", "noop$2", "isWatched", "hasPrefixAndIcon", "hasBeenReplaced", "contains", "getMutator", "mutators", "mutator", "createElementNS", "convertSVG", "abstractObj", "_params$ceFn", "ceFn", "createTextNode", "append<PERSON><PERSON><PERSON>", "nodeAsComment", "comment", "outerHTML", "mutation", "parentNode", "_abstract", "createComment", "<PERSON><PERSON><PERSON><PERSON>", "remove", "nest", "_abstract2", "forSvg", "splitClasses", "toSvg", "toNode", "removeAttribute", "newInnerHTML", "performOperationSync", "op", "perform", "mutations", "callback", "callbackFunction", "frame", "requestAnimationFrame", "disabled", "disableObservation", "enableObservation", "mo", "observe", "options", "_options$treeCallback", "treeCallback", "_options$nodeCallback", "nodeCallback", "_options$pseudoElemen", "pseudoElements<PERSON><PERSON><PERSON>", "_options$observeMutat", "observeMutationsRoot", "objects", "defaultPrefix", "mutationRecord", "type", "addedNodes", "_getCanonicalIcon", "childList", "characterData", "subtree", "disconnect", "<PERSON><PERSON><PERSON><PERSON>", "class<PERSON><PERSON>er", "existingPrefix", "existingIconName", "innerText", "<PERSON><PERSON><PERSON><PERSON>", "nodeType", "Node", "TEXT_NODE", "data", "<PERSON><PERSON><PERSON><PERSON>", "extraAttributes", "blankMeta", "parseMeta", "parser", "_classParser", "extraClasses", "pluginMeta", "extraStyles", "styles$2", "generateMutation", "nodeMeta", "knownPrefixes", "onTree", "root", "htmlClassList", "hclAdd", "suffix", "hclRemove", "f", "prefixesDomQuery", "candidates", "querySelectorAll", "all", "then", "resolvedMutations", "catch", "onNode", "resolveIcons", "maybeIconDefinition", "iconDefinition", "render", "_params$transform", "_params$symbol", "_params$mask", "_params$maskId", "_params$title", "_params$titleId", "_params$classes", "_params$attributes", "_params$styles", "ReplaceElements", "mutationObserverCallbacks", "providers$$1", "_params$node", "_params$callback", "generateSvgReplacementMutation", "generateAbstractIcon", "_ref3", "<PERSON><PERSON><PERSON><PERSON>", "Layers", "layer", "assembler", "LayersCounter", "LayersText", "text", "generateLayersText", "computedFontSize", "parseInt", "getComputedStyle", "fontSize", "boundingClientRect", "getBoundingClientRect", "CLEAN_CONTENT_PATTERN", "SECONDARY_UNICODE_RANGE", "hex<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cleaned", "codePoint", "isPrependTen", "isDoubled", "isSecondary", "replaceForPosition", "position", "pendingAttribute", "alreadyProcessedPseudoElement", "fontFamily", "getPropertyValue", "fontWeight", "<PERSON><PERSON><PERSON><PERSON>", "_content", "toLowerCase", "_hex<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>nt", "hexValue", "isV4", "startsWith", "iconIdentifier", "iconName4", "meta", "processable", "operations", "P<PERSON>udo<PERSON><PERSON><PERSON>", "pseudoElements2svg", "_unwatched", "MutationObserver$1", "unwatch", "bootstrap", "parseTransformString", "transformString", "parseFloat", "isNaN", "PowerTransforms", "parseNodeAttributes", "generateAbstractTransformGrouping", "ALL_SPACE", "fillBlack", "force", "deGroup", "Masks", "maskData", "generateAbstractMask", "explicitMaskId", "mainWidth", "mainP<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "trans", "maskRect", "maskInnerGroupChildrenMixin", "maskInnerGroup", "maskOuterGroup", "clipId", "maskTag", "maskUnits", "maskContentUnits", "defs", "MissingIconIndicator", "reduceMotion", "matchMedia", "matches", "missingIconAbstract", "g<PERSON><PERSON><PERSON><PERSON>", "FILL", "ANIMATION_BASE", "attributeType", "repeatCount", "dur", "OPACITY_ANIMATE", "dot", "cx", "cy", "r", "opacity", "SvgSymbols", "symbolData", "plugins", "noAuto$1", "config$1", "library$1", "dom$1", "parse$1", "findIconDefinition$1", "toHtml$1"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/node_modules/@fortawesome/fontawesome-svg-core/index.mjs"], "sourcesContent": ["function ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n\n  return target;\n}\n\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\n\nfunction _wrapRegExp() {\n  _wrapRegExp = function (re, groups) {\n    return new BabelRegExp(re, void 0, groups);\n  };\n\n  var _super = RegExp.prototype,\n      _groups = new WeakMap();\n\n  function BabelRegExp(re, flags, groups) {\n    var _this = new RegExp(re, flags);\n\n    return _groups.set(_this, groups || _groups.get(re)), _setPrototypeOf(_this, BabelRegExp.prototype);\n  }\n\n  function buildGroups(result, re) {\n    var g = _groups.get(re);\n\n    return Object.keys(g).reduce(function (groups, name) {\n      return groups[name] = result[g[name]], groups;\n    }, Object.create(null));\n  }\n\n  return _inherits(BabelRegExp, RegExp), BabelRegExp.prototype.exec = function (str) {\n    var result = _super.exec.call(this, str);\n\n    return result && (result.groups = buildGroups(result, this)), result;\n  }, BabelRegExp.prototype[Symbol.replace] = function (str, substitution) {\n    if (\"string\" == typeof substitution) {\n      var groups = _groups.get(this);\n\n      return _super[Symbol.replace].call(this, str, substitution.replace(/\\$<([^>]+)>/g, function (_, name) {\n        return \"$\" + groups[name];\n      }));\n    }\n\n    if (\"function\" == typeof substitution) {\n      var _this = this;\n\n      return _super[Symbol.replace].call(this, str, function () {\n        var args = arguments;\n        return \"object\" != typeof args[args.length - 1] && (args = [].slice.call(args)).push(buildGroups(args, _this)), substitution.apply(this, args);\n      });\n    }\n\n    return _super[Symbol.replace].call(this, str, substitution);\n  }, _wrapRegExp.apply(this, arguments);\n}\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\n\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}\n\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\n\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\n\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\n\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\n\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n\n  var _s, _e;\n\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nvar noop = function noop() {};\n\nvar _WINDOW = {};\nvar _DOCUMENT = {};\nvar _MUTATION_OBSERVER = null;\nvar _PERFORMANCE = {\n  mark: noop,\n  measure: noop\n};\n\ntry {\n  if (typeof window !== 'undefined') _WINDOW = window;\n  if (typeof document !== 'undefined') _DOCUMENT = document;\n  if (typeof MutationObserver !== 'undefined') _MUTATION_OBSERVER = MutationObserver;\n  if (typeof performance !== 'undefined') _PERFORMANCE = performance;\n} catch (e) {}\n\nvar _ref = _WINDOW.navigator || {},\n    _ref$userAgent = _ref.userAgent,\n    userAgent = _ref$userAgent === void 0 ? '' : _ref$userAgent;\nvar WINDOW = _WINDOW;\nvar DOCUMENT = _DOCUMENT;\nvar MUTATION_OBSERVER = _MUTATION_OBSERVER;\nvar PERFORMANCE = _PERFORMANCE;\nvar IS_BROWSER = !!WINDOW.document;\nvar IS_DOM = !!DOCUMENT.documentElement && !!DOCUMENT.head && typeof DOCUMENT.addEventListener === 'function' && typeof DOCUMENT.createElement === 'function';\nvar IS_IE = ~userAgent.indexOf('MSIE') || ~userAgent.indexOf('Trident/');\n\nvar _familyProxy, _familyProxy2, _familyProxy3, _familyProxy4, _familyProxy5;\n\nvar NAMESPACE_IDENTIFIER = '___FONT_AWESOME___';\nvar UNITS_IN_GRID = 16;\nvar DEFAULT_CSS_PREFIX = 'fa';\nvar DEFAULT_REPLACEMENT_CLASS = 'svg-inline--fa';\nvar DATA_FA_I2SVG = 'data-fa-i2svg';\nvar DATA_FA_PSEUDO_ELEMENT = 'data-fa-pseudo-element';\nvar DATA_FA_PSEUDO_ELEMENT_PENDING = 'data-fa-pseudo-element-pending';\nvar DATA_PREFIX = 'data-prefix';\nvar DATA_ICON = 'data-icon';\nvar HTML_CLASS_I2SVG_BASE_CLASS = 'fontawesome-i2svg';\nvar MUTATION_APPROACH_ASYNC = 'async';\nvar TAGNAMES_TO_SKIP_FOR_PSEUDOELEMENTS = ['HTML', 'HEAD', 'STYLE', 'SCRIPT'];\nvar PRODUCTION = function () {\n  try {\n    return process.env.NODE_ENV === 'production';\n  } catch (e) {\n    return false;\n  }\n}();\nvar FAMILY_CLASSIC = 'classic';\nvar FAMILY_SHARP = 'sharp';\nvar FAMILIES = [FAMILY_CLASSIC, FAMILY_SHARP];\n\nfunction familyProxy(obj) {\n  // Defaults to the classic family if family is not available\n  return new Proxy(obj, {\n    get: function get(target, prop) {\n      return prop in target ? target[prop] : target[FAMILY_CLASSIC];\n    }\n  });\n}\nvar PREFIX_TO_STYLE = familyProxy((_familyProxy = {}, _defineProperty(_familyProxy, FAMILY_CLASSIC, {\n  'fa': 'solid',\n  'fas': 'solid',\n  'fa-solid': 'solid',\n  'far': 'regular',\n  'fa-regular': 'regular',\n  'fal': 'light',\n  'fa-light': 'light',\n  'fat': 'thin',\n  'fa-thin': 'thin',\n  'fad': 'duotone',\n  'fa-duotone': 'duotone',\n  'fab': 'brands',\n  'fa-brands': 'brands',\n  'fak': 'kit',\n  'fakd': 'kit',\n  'fa-kit': 'kit',\n  'fa-kit-duotone': 'kit'\n}), _defineProperty(_familyProxy, FAMILY_SHARP, {\n  'fa': 'solid',\n  'fass': 'solid',\n  'fa-solid': 'solid',\n  'fasr': 'regular',\n  'fa-regular': 'regular',\n  'fasl': 'light',\n  'fa-light': 'light',\n  'fast': 'thin',\n  'fa-thin': 'thin'\n}), _familyProxy));\nvar STYLE_TO_PREFIX = familyProxy((_familyProxy2 = {}, _defineProperty(_familyProxy2, FAMILY_CLASSIC, {\n  solid: 'fas',\n  regular: 'far',\n  light: 'fal',\n  thin: 'fat',\n  duotone: 'fad',\n  brands: 'fab',\n  kit: 'fak'\n}), _defineProperty(_familyProxy2, FAMILY_SHARP, {\n  solid: 'fass',\n  regular: 'fasr',\n  light: 'fasl',\n  thin: 'fast'\n}), _familyProxy2));\nvar PREFIX_TO_LONG_STYLE = familyProxy((_familyProxy3 = {}, _defineProperty(_familyProxy3, FAMILY_CLASSIC, {\n  fab: 'fa-brands',\n  fad: 'fa-duotone',\n  fak: 'fa-kit',\n  fal: 'fa-light',\n  far: 'fa-regular',\n  fas: 'fa-solid',\n  fat: 'fa-thin'\n}), _defineProperty(_familyProxy3, FAMILY_SHARP, {\n  fass: 'fa-solid',\n  fasr: 'fa-regular',\n  fasl: 'fa-light',\n  fast: 'fa-thin'\n}), _familyProxy3));\nvar LONG_STYLE_TO_PREFIX = familyProxy((_familyProxy4 = {}, _defineProperty(_familyProxy4, FAMILY_CLASSIC, {\n  'fa-brands': 'fab',\n  'fa-duotone': 'fad',\n  'fa-kit': 'fak',\n  'fa-light': 'fal',\n  'fa-regular': 'far',\n  'fa-solid': 'fas',\n  'fa-thin': 'fat'\n}), _defineProperty(_familyProxy4, FAMILY_SHARP, {\n  'fa-solid': 'fass',\n  'fa-regular': 'fasr',\n  'fa-light': 'fasl',\n  'fa-thin': 'fast'\n}), _familyProxy4));\nvar ICON_SELECTION_SYNTAX_PATTERN = /fa(s|r|l|t|d|b|k|ss|sr|sl|st)?[\\-\\ ]/; // eslint-disable-line no-useless-escape\n\nvar LAYERS_TEXT_CLASSNAME = 'fa-layers-text';\nvar FONT_FAMILY_PATTERN = /Font ?Awesome ?([56 ]*)(Solid|Regular|Light|Thin|Duotone|Brands|Free|Pro|Sharp|Kit)?.*/i;\nvar FONT_WEIGHT_TO_PREFIX = familyProxy((_familyProxy5 = {}, _defineProperty(_familyProxy5, FAMILY_CLASSIC, {\n  900: 'fas',\n  400: 'far',\n  normal: 'far',\n  300: 'fal',\n  100: 'fat'\n}), _defineProperty(_familyProxy5, FAMILY_SHARP, {\n  900: 'fass',\n  400: 'fasr',\n  300: 'fasl',\n  100: 'fast'\n}), _familyProxy5));\nvar oneToTen = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];\nvar oneToTwenty = oneToTen.concat([11, 12, 13, 14, 15, 16, 17, 18, 19, 20]);\nvar ATTRIBUTES_WATCHED_FOR_MUTATION = ['class', 'data-prefix', 'data-icon', 'data-fa-transform', 'data-fa-mask'];\nvar DUOTONE_CLASSES = {\n  GROUP: 'duotone-group',\n  SWAP_OPACITY: 'swap-opacity',\n  PRIMARY: 'primary',\n  SECONDARY: 'secondary'\n};\nvar prefixes = new Set();\nObject.keys(STYLE_TO_PREFIX[FAMILY_CLASSIC]).map(prefixes.add.bind(prefixes));\nObject.keys(STYLE_TO_PREFIX[FAMILY_SHARP]).map(prefixes.add.bind(prefixes));\nvar RESERVED_CLASSES = [].concat(FAMILIES, _toConsumableArray(prefixes), ['2xs', 'xs', 'sm', 'lg', 'xl', '2xl', 'beat', 'border', 'fade', 'beat-fade', 'bounce', 'flip-both', 'flip-horizontal', 'flip-vertical', 'flip', 'fw', 'inverse', 'layers-counter', 'layers-text', 'layers', 'li', 'pull-left', 'pull-right', 'pulse', 'rotate-180', 'rotate-270', 'rotate-90', 'rotate-by', 'shake', 'spin-pulse', 'spin-reverse', 'spin', 'stack-1x', 'stack-2x', 'stack', 'ul', DUOTONE_CLASSES.GROUP, DUOTONE_CLASSES.SWAP_OPACITY, DUOTONE_CLASSES.PRIMARY, DUOTONE_CLASSES.SECONDARY]).concat(oneToTen.map(function (n) {\n  return \"\".concat(n, \"x\");\n})).concat(oneToTwenty.map(function (n) {\n  return \"w-\".concat(n);\n}));\n\nvar initial = WINDOW.FontAwesomeConfig || {};\n\nfunction getAttrConfig(attr) {\n  var element = DOCUMENT.querySelector('script[' + attr + ']');\n\n  if (element) {\n    return element.getAttribute(attr);\n  }\n}\n\nfunction coerce(val) {\n  // Getting an empty string will occur if the attribute is set on the HTML tag but without a value\n  // We'll assume that this is an indication that it should be toggled to true\n  if (val === '') return true;\n  if (val === 'false') return false;\n  if (val === 'true') return true;\n  return val;\n}\n\nif (DOCUMENT && typeof DOCUMENT.querySelector === 'function') {\n  var attrs = [['data-family-prefix', 'familyPrefix'], ['data-css-prefix', 'cssPrefix'], ['data-family-default', 'familyDefault'], ['data-style-default', 'styleDefault'], ['data-replacement-class', 'replacementClass'], ['data-auto-replace-svg', 'autoReplaceSvg'], ['data-auto-add-css', 'autoAddCss'], ['data-auto-a11y', 'autoA11y'], ['data-search-pseudo-elements', 'searchPseudoElements'], ['data-observe-mutations', 'observeMutations'], ['data-mutate-approach', 'mutateApproach'], ['data-keep-original-source', 'keepOriginalSource'], ['data-measure-performance', 'measurePerformance'], ['data-show-missing-icons', 'showMissingIcons']];\n  attrs.forEach(function (_ref) {\n    var _ref2 = _slicedToArray(_ref, 2),\n        attr = _ref2[0],\n        key = _ref2[1];\n\n    var val = coerce(getAttrConfig(attr));\n\n    if (val !== undefined && val !== null) {\n      initial[key] = val;\n    }\n  });\n}\n\nvar _default = {\n  styleDefault: 'solid',\n  familyDefault: 'classic',\n  cssPrefix: DEFAULT_CSS_PREFIX,\n  replacementClass: DEFAULT_REPLACEMENT_CLASS,\n  autoReplaceSvg: true,\n  autoAddCss: true,\n  autoA11y: true,\n  searchPseudoElements: false,\n  observeMutations: true,\n  mutateApproach: 'async',\n  keepOriginalSource: true,\n  measurePerformance: false,\n  showMissingIcons: true\n}; // familyPrefix is deprecated but we must still support it if present\n\nif (initial.familyPrefix) {\n  initial.cssPrefix = initial.familyPrefix;\n}\n\nvar _config = _objectSpread2(_objectSpread2({}, _default), initial);\n\nif (!_config.autoReplaceSvg) _config.observeMutations = false;\nvar config = {};\nObject.keys(_default).forEach(function (key) {\n  Object.defineProperty(config, key, {\n    enumerable: true,\n    set: function set(val) {\n      _config[key] = val;\n\n      _onChangeCb.forEach(function (cb) {\n        return cb(config);\n      });\n    },\n    get: function get() {\n      return _config[key];\n    }\n  });\n}); // familyPrefix is deprecated as of 6.2.0 and should be removed in 7.0.0\n\nObject.defineProperty(config, 'familyPrefix', {\n  enumerable: true,\n  set: function set(val) {\n    _config.cssPrefix = val;\n\n    _onChangeCb.forEach(function (cb) {\n      return cb(config);\n    });\n  },\n  get: function get() {\n    return _config.cssPrefix;\n  }\n});\nWINDOW.FontAwesomeConfig = config;\nvar _onChangeCb = [];\nfunction onChange(cb) {\n  _onChangeCb.push(cb);\n\n  return function () {\n    _onChangeCb.splice(_onChangeCb.indexOf(cb), 1);\n  };\n}\n\nvar d = UNITS_IN_GRID;\nvar meaninglessTransform = {\n  size: 16,\n  x: 0,\n  y: 0,\n  rotate: 0,\n  flipX: false,\n  flipY: false\n};\nfunction insertCss(css) {\n  if (!css || !IS_DOM) {\n    return;\n  }\n\n  var style = DOCUMENT.createElement('style');\n  style.setAttribute('type', 'text/css');\n  style.innerHTML = css;\n  var headChildren = DOCUMENT.head.childNodes;\n  var beforeChild = null;\n\n  for (var i = headChildren.length - 1; i > -1; i--) {\n    var child = headChildren[i];\n    var tagName = (child.tagName || '').toUpperCase();\n\n    if (['STYLE', 'LINK'].indexOf(tagName) > -1) {\n      beforeChild = child;\n    }\n  }\n\n  DOCUMENT.head.insertBefore(style, beforeChild);\n  return css;\n}\nvar idPool = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';\nfunction nextUniqueId() {\n  var size = 12;\n  var id = '';\n\n  while (size-- > 0) {\n    id += idPool[Math.random() * 62 | 0];\n  }\n\n  return id;\n}\nfunction toArray(obj) {\n  var array = [];\n\n  for (var i = (obj || []).length >>> 0; i--;) {\n    array[i] = obj[i];\n  }\n\n  return array;\n}\nfunction classArray(node) {\n  if (node.classList) {\n    return toArray(node.classList);\n  } else {\n    return (node.getAttribute('class') || '').split(' ').filter(function (i) {\n      return i;\n    });\n  }\n}\nfunction htmlEscape(str) {\n  return \"\".concat(str).replace(/&/g, '&amp;').replace(/\"/g, '&quot;').replace(/'/g, '&#39;').replace(/</g, '&lt;').replace(/>/g, '&gt;');\n}\nfunction joinAttributes(attributes) {\n  return Object.keys(attributes || {}).reduce(function (acc, attributeName) {\n    return acc + \"\".concat(attributeName, \"=\\\"\").concat(htmlEscape(attributes[attributeName]), \"\\\" \");\n  }, '').trim();\n}\nfunction joinStyles(styles) {\n  return Object.keys(styles || {}).reduce(function (acc, styleName) {\n    return acc + \"\".concat(styleName, \": \").concat(styles[styleName].trim(), \";\");\n  }, '');\n}\nfunction transformIsMeaningful(transform) {\n  return transform.size !== meaninglessTransform.size || transform.x !== meaninglessTransform.x || transform.y !== meaninglessTransform.y || transform.rotate !== meaninglessTransform.rotate || transform.flipX || transform.flipY;\n}\nfunction transformForSvg(_ref) {\n  var transform = _ref.transform,\n      containerWidth = _ref.containerWidth,\n      iconWidth = _ref.iconWidth;\n  var outer = {\n    transform: \"translate(\".concat(containerWidth / 2, \" 256)\")\n  };\n  var innerTranslate = \"translate(\".concat(transform.x * 32, \", \").concat(transform.y * 32, \") \");\n  var innerScale = \"scale(\".concat(transform.size / 16 * (transform.flipX ? -1 : 1), \", \").concat(transform.size / 16 * (transform.flipY ? -1 : 1), \") \");\n  var innerRotate = \"rotate(\".concat(transform.rotate, \" 0 0)\");\n  var inner = {\n    transform: \"\".concat(innerTranslate, \" \").concat(innerScale, \" \").concat(innerRotate)\n  };\n  var path = {\n    transform: \"translate(\".concat(iconWidth / 2 * -1, \" -256)\")\n  };\n  return {\n    outer: outer,\n    inner: inner,\n    path: path\n  };\n}\nfunction transformForCss(_ref2) {\n  var transform = _ref2.transform,\n      _ref2$width = _ref2.width,\n      width = _ref2$width === void 0 ? UNITS_IN_GRID : _ref2$width,\n      _ref2$height = _ref2.height,\n      height = _ref2$height === void 0 ? UNITS_IN_GRID : _ref2$height,\n      _ref2$startCentered = _ref2.startCentered,\n      startCentered = _ref2$startCentered === void 0 ? false : _ref2$startCentered;\n  var val = '';\n\n  if (startCentered && IS_IE) {\n    val += \"translate(\".concat(transform.x / d - width / 2, \"em, \").concat(transform.y / d - height / 2, \"em) \");\n  } else if (startCentered) {\n    val += \"translate(calc(-50% + \".concat(transform.x / d, \"em), calc(-50% + \").concat(transform.y / d, \"em)) \");\n  } else {\n    val += \"translate(\".concat(transform.x / d, \"em, \").concat(transform.y / d, \"em) \");\n  }\n\n  val += \"scale(\".concat(transform.size / d * (transform.flipX ? -1 : 1), \", \").concat(transform.size / d * (transform.flipY ? -1 : 1), \") \");\n  val += \"rotate(\".concat(transform.rotate, \"deg) \");\n  return val;\n}\n\nvar baseStyles = \":root, :host {\\n  --fa-font-solid: normal 900 1em/1 \\\"Font Awesome 6 Solid\\\";\\n  --fa-font-regular: normal 400 1em/1 \\\"Font Awesome 6 Regular\\\";\\n  --fa-font-light: normal 300 1em/1 \\\"Font Awesome 6 Light\\\";\\n  --fa-font-thin: normal 100 1em/1 \\\"Font Awesome 6 Thin\\\";\\n  --fa-font-duotone: normal 900 1em/1 \\\"Font Awesome 6 Duotone\\\";\\n  --fa-font-sharp-solid: normal 900 1em/1 \\\"Font Awesome 6 Sharp\\\";\\n  --fa-font-sharp-regular: normal 400 1em/1 \\\"Font Awesome 6 Sharp\\\";\\n  --fa-font-sharp-light: normal 300 1em/1 \\\"Font Awesome 6 Sharp\\\";\\n  --fa-font-sharp-thin: normal 100 1em/1 \\\"Font Awesome 6 Sharp\\\";\\n  --fa-font-brands: normal 400 1em/1 \\\"Font Awesome 6 Brands\\\";\\n}\\n\\nsvg:not(:root).svg-inline--fa, svg:not(:host).svg-inline--fa {\\n  overflow: visible;\\n  box-sizing: content-box;\\n}\\n\\n.svg-inline--fa {\\n  display: var(--fa-display, inline-block);\\n  height: 1em;\\n  overflow: visible;\\n  vertical-align: -0.125em;\\n}\\n.svg-inline--fa.fa-2xs {\\n  vertical-align: 0.1em;\\n}\\n.svg-inline--fa.fa-xs {\\n  vertical-align: 0em;\\n}\\n.svg-inline--fa.fa-sm {\\n  vertical-align: -0.0714285705em;\\n}\\n.svg-inline--fa.fa-lg {\\n  vertical-align: -0.2em;\\n}\\n.svg-inline--fa.fa-xl {\\n  vertical-align: -0.25em;\\n}\\n.svg-inline--fa.fa-2xl {\\n  vertical-align: -0.3125em;\\n}\\n.svg-inline--fa.fa-pull-left {\\n  margin-right: var(--fa-pull-margin, 0.3em);\\n  width: auto;\\n}\\n.svg-inline--fa.fa-pull-right {\\n  margin-left: var(--fa-pull-margin, 0.3em);\\n  width: auto;\\n}\\n.svg-inline--fa.fa-li {\\n  width: var(--fa-li-width, 2em);\\n  top: 0.25em;\\n}\\n.svg-inline--fa.fa-fw {\\n  width: var(--fa-fw-width, 1.25em);\\n}\\n\\n.fa-layers svg.svg-inline--fa {\\n  bottom: 0;\\n  left: 0;\\n  margin: auto;\\n  position: absolute;\\n  right: 0;\\n  top: 0;\\n}\\n\\n.fa-layers-counter, .fa-layers-text {\\n  display: inline-block;\\n  position: absolute;\\n  text-align: center;\\n}\\n\\n.fa-layers {\\n  display: inline-block;\\n  height: 1em;\\n  position: relative;\\n  text-align: center;\\n  vertical-align: -0.125em;\\n  width: 1em;\\n}\\n.fa-layers svg.svg-inline--fa {\\n  -webkit-transform-origin: center center;\\n          transform-origin: center center;\\n}\\n\\n.fa-layers-text {\\n  left: 50%;\\n  top: 50%;\\n  -webkit-transform: translate(-50%, -50%);\\n          transform: translate(-50%, -50%);\\n  -webkit-transform-origin: center center;\\n          transform-origin: center center;\\n}\\n\\n.fa-layers-counter {\\n  background-color: var(--fa-counter-background-color, #ff253a);\\n  border-radius: var(--fa-counter-border-radius, 1em);\\n  box-sizing: border-box;\\n  color: var(--fa-inverse, #fff);\\n  line-height: var(--fa-counter-line-height, 1);\\n  max-width: var(--fa-counter-max-width, 5em);\\n  min-width: var(--fa-counter-min-width, 1.5em);\\n  overflow: hidden;\\n  padding: var(--fa-counter-padding, 0.25em 0.5em);\\n  right: var(--fa-right, 0);\\n  text-overflow: ellipsis;\\n  top: var(--fa-top, 0);\\n  -webkit-transform: scale(var(--fa-counter-scale, 0.25));\\n          transform: scale(var(--fa-counter-scale, 0.25));\\n  -webkit-transform-origin: top right;\\n          transform-origin: top right;\\n}\\n\\n.fa-layers-bottom-right {\\n  bottom: var(--fa-bottom, 0);\\n  right: var(--fa-right, 0);\\n  top: auto;\\n  -webkit-transform: scale(var(--fa-layers-scale, 0.25));\\n          transform: scale(var(--fa-layers-scale, 0.25));\\n  -webkit-transform-origin: bottom right;\\n          transform-origin: bottom right;\\n}\\n\\n.fa-layers-bottom-left {\\n  bottom: var(--fa-bottom, 0);\\n  left: var(--fa-left, 0);\\n  right: auto;\\n  top: auto;\\n  -webkit-transform: scale(var(--fa-layers-scale, 0.25));\\n          transform: scale(var(--fa-layers-scale, 0.25));\\n  -webkit-transform-origin: bottom left;\\n          transform-origin: bottom left;\\n}\\n\\n.fa-layers-top-right {\\n  top: var(--fa-top, 0);\\n  right: var(--fa-right, 0);\\n  -webkit-transform: scale(var(--fa-layers-scale, 0.25));\\n          transform: scale(var(--fa-layers-scale, 0.25));\\n  -webkit-transform-origin: top right;\\n          transform-origin: top right;\\n}\\n\\n.fa-layers-top-left {\\n  left: var(--fa-left, 0);\\n  right: auto;\\n  top: var(--fa-top, 0);\\n  -webkit-transform: scale(var(--fa-layers-scale, 0.25));\\n          transform: scale(var(--fa-layers-scale, 0.25));\\n  -webkit-transform-origin: top left;\\n          transform-origin: top left;\\n}\\n\\n.fa-1x {\\n  font-size: 1em;\\n}\\n\\n.fa-2x {\\n  font-size: 2em;\\n}\\n\\n.fa-3x {\\n  font-size: 3em;\\n}\\n\\n.fa-4x {\\n  font-size: 4em;\\n}\\n\\n.fa-5x {\\n  font-size: 5em;\\n}\\n\\n.fa-6x {\\n  font-size: 6em;\\n}\\n\\n.fa-7x {\\n  font-size: 7em;\\n}\\n\\n.fa-8x {\\n  font-size: 8em;\\n}\\n\\n.fa-9x {\\n  font-size: 9em;\\n}\\n\\n.fa-10x {\\n  font-size: 10em;\\n}\\n\\n.fa-2xs {\\n  font-size: 0.625em;\\n  line-height: 0.1em;\\n  vertical-align: 0.225em;\\n}\\n\\n.fa-xs {\\n  font-size: 0.75em;\\n  line-height: 0.0833333337em;\\n  vertical-align: 0.125em;\\n}\\n\\n.fa-sm {\\n  font-size: 0.875em;\\n  line-height: 0.0714285718em;\\n  vertical-align: 0.0535714295em;\\n}\\n\\n.fa-lg {\\n  font-size: 1.25em;\\n  line-height: 0.05em;\\n  vertical-align: -0.075em;\\n}\\n\\n.fa-xl {\\n  font-size: 1.5em;\\n  line-height: 0.0416666682em;\\n  vertical-align: -0.125em;\\n}\\n\\n.fa-2xl {\\n  font-size: 2em;\\n  line-height: 0.03125em;\\n  vertical-align: -0.1875em;\\n}\\n\\n.fa-fw {\\n  text-align: center;\\n  width: 1.25em;\\n}\\n\\n.fa-ul {\\n  list-style-type: none;\\n  margin-left: var(--fa-li-margin, 2.5em);\\n  padding-left: 0;\\n}\\n.fa-ul > li {\\n  position: relative;\\n}\\n\\n.fa-li {\\n  left: calc(var(--fa-li-width, 2em) * -1);\\n  position: absolute;\\n  text-align: center;\\n  width: var(--fa-li-width, 2em);\\n  line-height: inherit;\\n}\\n\\n.fa-border {\\n  border-color: var(--fa-border-color, #eee);\\n  border-radius: var(--fa-border-radius, 0.1em);\\n  border-style: var(--fa-border-style, solid);\\n  border-width: var(--fa-border-width, 0.08em);\\n  padding: var(--fa-border-padding, 0.2em 0.25em 0.15em);\\n}\\n\\n.fa-pull-left {\\n  float: left;\\n  margin-right: var(--fa-pull-margin, 0.3em);\\n}\\n\\n.fa-pull-right {\\n  float: right;\\n  margin-left: var(--fa-pull-margin, 0.3em);\\n}\\n\\n.fa-beat {\\n  -webkit-animation-name: fa-beat;\\n          animation-name: fa-beat;\\n  -webkit-animation-delay: var(--fa-animation-delay, 0s);\\n          animation-delay: var(--fa-animation-delay, 0s);\\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\\n          animation-direction: var(--fa-animation-direction, normal);\\n  -webkit-animation-duration: var(--fa-animation-duration, 1s);\\n          animation-duration: var(--fa-animation-duration, 1s);\\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  -webkit-animation-timing-function: var(--fa-animation-timing, ease-in-out);\\n          animation-timing-function: var(--fa-animation-timing, ease-in-out);\\n}\\n\\n.fa-bounce {\\n  -webkit-animation-name: fa-bounce;\\n          animation-name: fa-bounce;\\n  -webkit-animation-delay: var(--fa-animation-delay, 0s);\\n          animation-delay: var(--fa-animation-delay, 0s);\\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\\n          animation-direction: var(--fa-animation-direction, normal);\\n  -webkit-animation-duration: var(--fa-animation-duration, 1s);\\n          animation-duration: var(--fa-animation-duration, 1s);\\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  -webkit-animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1));\\n          animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1));\\n}\\n\\n.fa-fade {\\n  -webkit-animation-name: fa-fade;\\n          animation-name: fa-fade;\\n  -webkit-animation-delay: var(--fa-animation-delay, 0s);\\n          animation-delay: var(--fa-animation-delay, 0s);\\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\\n          animation-direction: var(--fa-animation-direction, normal);\\n  -webkit-animation-duration: var(--fa-animation-duration, 1s);\\n          animation-duration: var(--fa-animation-duration, 1s);\\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  -webkit-animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\\n          animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\\n}\\n\\n.fa-beat-fade {\\n  -webkit-animation-name: fa-beat-fade;\\n          animation-name: fa-beat-fade;\\n  -webkit-animation-delay: var(--fa-animation-delay, 0s);\\n          animation-delay: var(--fa-animation-delay, 0s);\\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\\n          animation-direction: var(--fa-animation-direction, normal);\\n  -webkit-animation-duration: var(--fa-animation-duration, 1s);\\n          animation-duration: var(--fa-animation-duration, 1s);\\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  -webkit-animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\\n          animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\\n}\\n\\n.fa-flip {\\n  -webkit-animation-name: fa-flip;\\n          animation-name: fa-flip;\\n  -webkit-animation-delay: var(--fa-animation-delay, 0s);\\n          animation-delay: var(--fa-animation-delay, 0s);\\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\\n          animation-direction: var(--fa-animation-direction, normal);\\n  -webkit-animation-duration: var(--fa-animation-duration, 1s);\\n          animation-duration: var(--fa-animation-duration, 1s);\\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  -webkit-animation-timing-function: var(--fa-animation-timing, ease-in-out);\\n          animation-timing-function: var(--fa-animation-timing, ease-in-out);\\n}\\n\\n.fa-shake {\\n  -webkit-animation-name: fa-shake;\\n          animation-name: fa-shake;\\n  -webkit-animation-delay: var(--fa-animation-delay, 0s);\\n          animation-delay: var(--fa-animation-delay, 0s);\\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\\n          animation-direction: var(--fa-animation-direction, normal);\\n  -webkit-animation-duration: var(--fa-animation-duration, 1s);\\n          animation-duration: var(--fa-animation-duration, 1s);\\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  -webkit-animation-timing-function: var(--fa-animation-timing, linear);\\n          animation-timing-function: var(--fa-animation-timing, linear);\\n}\\n\\n.fa-spin {\\n  -webkit-animation-name: fa-spin;\\n          animation-name: fa-spin;\\n  -webkit-animation-delay: var(--fa-animation-delay, 0s);\\n          animation-delay: var(--fa-animation-delay, 0s);\\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\\n          animation-direction: var(--fa-animation-direction, normal);\\n  -webkit-animation-duration: var(--fa-animation-duration, 2s);\\n          animation-duration: var(--fa-animation-duration, 2s);\\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  -webkit-animation-timing-function: var(--fa-animation-timing, linear);\\n          animation-timing-function: var(--fa-animation-timing, linear);\\n}\\n\\n.fa-spin-reverse {\\n  --fa-animation-direction: reverse;\\n}\\n\\n.fa-pulse,\\n.fa-spin-pulse {\\n  -webkit-animation-name: fa-spin;\\n          animation-name: fa-spin;\\n  -webkit-animation-direction: var(--fa-animation-direction, normal);\\n          animation-direction: var(--fa-animation-direction, normal);\\n  -webkit-animation-duration: var(--fa-animation-duration, 1s);\\n          animation-duration: var(--fa-animation-duration, 1s);\\n  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n          animation-iteration-count: var(--fa-animation-iteration-count, infinite);\\n  -webkit-animation-timing-function: var(--fa-animation-timing, steps(8));\\n          animation-timing-function: var(--fa-animation-timing, steps(8));\\n}\\n\\n@media (prefers-reduced-motion: reduce) {\\n  .fa-beat,\\n.fa-bounce,\\n.fa-fade,\\n.fa-beat-fade,\\n.fa-flip,\\n.fa-pulse,\\n.fa-shake,\\n.fa-spin,\\n.fa-spin-pulse {\\n    -webkit-animation-delay: -1ms;\\n            animation-delay: -1ms;\\n    -webkit-animation-duration: 1ms;\\n            animation-duration: 1ms;\\n    -webkit-animation-iteration-count: 1;\\n            animation-iteration-count: 1;\\n    -webkit-transition-delay: 0s;\\n            transition-delay: 0s;\\n    -webkit-transition-duration: 0s;\\n            transition-duration: 0s;\\n  }\\n}\\n@-webkit-keyframes fa-beat {\\n  0%, 90% {\\n    -webkit-transform: scale(1);\\n            transform: scale(1);\\n  }\\n  45% {\\n    -webkit-transform: scale(var(--fa-beat-scale, 1.25));\\n            transform: scale(var(--fa-beat-scale, 1.25));\\n  }\\n}\\n@keyframes fa-beat {\\n  0%, 90% {\\n    -webkit-transform: scale(1);\\n            transform: scale(1);\\n  }\\n  45% {\\n    -webkit-transform: scale(var(--fa-beat-scale, 1.25));\\n            transform: scale(var(--fa-beat-scale, 1.25));\\n  }\\n}\\n@-webkit-keyframes fa-bounce {\\n  0% {\\n    -webkit-transform: scale(1, 1) translateY(0);\\n            transform: scale(1, 1) translateY(0);\\n  }\\n  10% {\\n    -webkit-transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);\\n            transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);\\n  }\\n  30% {\\n    -webkit-transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));\\n            transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));\\n  }\\n  50% {\\n    -webkit-transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);\\n            transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);\\n  }\\n  57% {\\n    -webkit-transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));\\n            transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));\\n  }\\n  64% {\\n    -webkit-transform: scale(1, 1) translateY(0);\\n            transform: scale(1, 1) translateY(0);\\n  }\\n  100% {\\n    -webkit-transform: scale(1, 1) translateY(0);\\n            transform: scale(1, 1) translateY(0);\\n  }\\n}\\n@keyframes fa-bounce {\\n  0% {\\n    -webkit-transform: scale(1, 1) translateY(0);\\n            transform: scale(1, 1) translateY(0);\\n  }\\n  10% {\\n    -webkit-transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);\\n            transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);\\n  }\\n  30% {\\n    -webkit-transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));\\n            transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));\\n  }\\n  50% {\\n    -webkit-transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);\\n            transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);\\n  }\\n  57% {\\n    -webkit-transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));\\n            transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));\\n  }\\n  64% {\\n    -webkit-transform: scale(1, 1) translateY(0);\\n            transform: scale(1, 1) translateY(0);\\n  }\\n  100% {\\n    -webkit-transform: scale(1, 1) translateY(0);\\n            transform: scale(1, 1) translateY(0);\\n  }\\n}\\n@-webkit-keyframes fa-fade {\\n  50% {\\n    opacity: var(--fa-fade-opacity, 0.4);\\n  }\\n}\\n@keyframes fa-fade {\\n  50% {\\n    opacity: var(--fa-fade-opacity, 0.4);\\n  }\\n}\\n@-webkit-keyframes fa-beat-fade {\\n  0%, 100% {\\n    opacity: var(--fa-beat-fade-opacity, 0.4);\\n    -webkit-transform: scale(1);\\n            transform: scale(1);\\n  }\\n  50% {\\n    opacity: 1;\\n    -webkit-transform: scale(var(--fa-beat-fade-scale, 1.125));\\n            transform: scale(var(--fa-beat-fade-scale, 1.125));\\n  }\\n}\\n@keyframes fa-beat-fade {\\n  0%, 100% {\\n    opacity: var(--fa-beat-fade-opacity, 0.4);\\n    -webkit-transform: scale(1);\\n            transform: scale(1);\\n  }\\n  50% {\\n    opacity: 1;\\n    -webkit-transform: scale(var(--fa-beat-fade-scale, 1.125));\\n            transform: scale(var(--fa-beat-fade-scale, 1.125));\\n  }\\n}\\n@-webkit-keyframes fa-flip {\\n  50% {\\n    -webkit-transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));\\n            transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));\\n  }\\n}\\n@keyframes fa-flip {\\n  50% {\\n    -webkit-transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));\\n            transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));\\n  }\\n}\\n@-webkit-keyframes fa-shake {\\n  0% {\\n    -webkit-transform: rotate(-15deg);\\n            transform: rotate(-15deg);\\n  }\\n  4% {\\n    -webkit-transform: rotate(15deg);\\n            transform: rotate(15deg);\\n  }\\n  8%, 24% {\\n    -webkit-transform: rotate(-18deg);\\n            transform: rotate(-18deg);\\n  }\\n  12%, 28% {\\n    -webkit-transform: rotate(18deg);\\n            transform: rotate(18deg);\\n  }\\n  16% {\\n    -webkit-transform: rotate(-22deg);\\n            transform: rotate(-22deg);\\n  }\\n  20% {\\n    -webkit-transform: rotate(22deg);\\n            transform: rotate(22deg);\\n  }\\n  32% {\\n    -webkit-transform: rotate(-12deg);\\n            transform: rotate(-12deg);\\n  }\\n  36% {\\n    -webkit-transform: rotate(12deg);\\n            transform: rotate(12deg);\\n  }\\n  40%, 100% {\\n    -webkit-transform: rotate(0deg);\\n            transform: rotate(0deg);\\n  }\\n}\\n@keyframes fa-shake {\\n  0% {\\n    -webkit-transform: rotate(-15deg);\\n            transform: rotate(-15deg);\\n  }\\n  4% {\\n    -webkit-transform: rotate(15deg);\\n            transform: rotate(15deg);\\n  }\\n  8%, 24% {\\n    -webkit-transform: rotate(-18deg);\\n            transform: rotate(-18deg);\\n  }\\n  12%, 28% {\\n    -webkit-transform: rotate(18deg);\\n            transform: rotate(18deg);\\n  }\\n  16% {\\n    -webkit-transform: rotate(-22deg);\\n            transform: rotate(-22deg);\\n  }\\n  20% {\\n    -webkit-transform: rotate(22deg);\\n            transform: rotate(22deg);\\n  }\\n  32% {\\n    -webkit-transform: rotate(-12deg);\\n            transform: rotate(-12deg);\\n  }\\n  36% {\\n    -webkit-transform: rotate(12deg);\\n            transform: rotate(12deg);\\n  }\\n  40%, 100% {\\n    -webkit-transform: rotate(0deg);\\n            transform: rotate(0deg);\\n  }\\n}\\n@-webkit-keyframes fa-spin {\\n  0% {\\n    -webkit-transform: rotate(0deg);\\n            transform: rotate(0deg);\\n  }\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n            transform: rotate(360deg);\\n  }\\n}\\n@keyframes fa-spin {\\n  0% {\\n    -webkit-transform: rotate(0deg);\\n            transform: rotate(0deg);\\n  }\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n            transform: rotate(360deg);\\n  }\\n}\\n.fa-rotate-90 {\\n  -webkit-transform: rotate(90deg);\\n          transform: rotate(90deg);\\n}\\n\\n.fa-rotate-180 {\\n  -webkit-transform: rotate(180deg);\\n          transform: rotate(180deg);\\n}\\n\\n.fa-rotate-270 {\\n  -webkit-transform: rotate(270deg);\\n          transform: rotate(270deg);\\n}\\n\\n.fa-flip-horizontal {\\n  -webkit-transform: scale(-1, 1);\\n          transform: scale(-1, 1);\\n}\\n\\n.fa-flip-vertical {\\n  -webkit-transform: scale(1, -1);\\n          transform: scale(1, -1);\\n}\\n\\n.fa-flip-both,\\n.fa-flip-horizontal.fa-flip-vertical {\\n  -webkit-transform: scale(-1, -1);\\n          transform: scale(-1, -1);\\n}\\n\\n.fa-rotate-by {\\n  -webkit-transform: rotate(var(--fa-rotate-angle, 0));\\n          transform: rotate(var(--fa-rotate-angle, 0));\\n}\\n\\n.fa-stack {\\n  display: inline-block;\\n  vertical-align: middle;\\n  height: 2em;\\n  position: relative;\\n  width: 2.5em;\\n}\\n\\n.fa-stack-1x,\\n.fa-stack-2x {\\n  bottom: 0;\\n  left: 0;\\n  margin: auto;\\n  position: absolute;\\n  right: 0;\\n  top: 0;\\n  z-index: var(--fa-stack-z-index, auto);\\n}\\n\\n.svg-inline--fa.fa-stack-1x {\\n  height: 1em;\\n  width: 1.25em;\\n}\\n.svg-inline--fa.fa-stack-2x {\\n  height: 2em;\\n  width: 2.5em;\\n}\\n\\n.fa-inverse {\\n  color: var(--fa-inverse, #fff);\\n}\\n\\n.sr-only,\\n.fa-sr-only {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\n\\n.sr-only-focusable:not(:focus),\\n.fa-sr-only-focusable:not(:focus) {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\n\\n.svg-inline--fa .fa-primary {\\n  fill: var(--fa-primary-color, currentColor);\\n  opacity: var(--fa-primary-opacity, 1);\\n}\\n\\n.svg-inline--fa .fa-secondary {\\n  fill: var(--fa-secondary-color, currentColor);\\n  opacity: var(--fa-secondary-opacity, 0.4);\\n}\\n\\n.svg-inline--fa.fa-swap-opacity .fa-primary {\\n  opacity: var(--fa-secondary-opacity, 0.4);\\n}\\n\\n.svg-inline--fa.fa-swap-opacity .fa-secondary {\\n  opacity: var(--fa-primary-opacity, 1);\\n}\\n\\n.svg-inline--fa mask .fa-primary,\\n.svg-inline--fa mask .fa-secondary {\\n  fill: black;\\n}\\n\\n.fad.fa-inverse,\\n.fa-duotone.fa-inverse {\\n  color: var(--fa-inverse, #fff);\\n}\";\n\nfunction css() {\n  var dcp = DEFAULT_CSS_PREFIX;\n  var drc = DEFAULT_REPLACEMENT_CLASS;\n  var fp = config.cssPrefix;\n  var rc = config.replacementClass;\n  var s = baseStyles;\n\n  if (fp !== dcp || rc !== drc) {\n    var dPatt = new RegExp(\"\\\\.\".concat(dcp, \"\\\\-\"), 'g');\n    var customPropPatt = new RegExp(\"\\\\--\".concat(dcp, \"\\\\-\"), 'g');\n    var rPatt = new RegExp(\"\\\\.\".concat(drc), 'g');\n    s = s.replace(dPatt, \".\".concat(fp, \"-\")).replace(customPropPatt, \"--\".concat(fp, \"-\")).replace(rPatt, \".\".concat(rc));\n  }\n\n  return s;\n}\n\nvar _cssInserted = false;\n\nfunction ensureCss() {\n  if (config.autoAddCss && !_cssInserted) {\n    insertCss(css());\n    _cssInserted = true;\n  }\n}\n\nvar InjectCSS = {\n  mixout: function mixout() {\n    return {\n      dom: {\n        css: css,\n        insertCss: ensureCss\n      }\n    };\n  },\n  hooks: function hooks() {\n    return {\n      beforeDOMElementCreation: function beforeDOMElementCreation() {\n        ensureCss();\n      },\n      beforeI2svg: function beforeI2svg() {\n        ensureCss();\n      }\n    };\n  }\n};\n\nvar w = WINDOW || {};\nif (!w[NAMESPACE_IDENTIFIER]) w[NAMESPACE_IDENTIFIER] = {};\nif (!w[NAMESPACE_IDENTIFIER].styles) w[NAMESPACE_IDENTIFIER].styles = {};\nif (!w[NAMESPACE_IDENTIFIER].hooks) w[NAMESPACE_IDENTIFIER].hooks = {};\nif (!w[NAMESPACE_IDENTIFIER].shims) w[NAMESPACE_IDENTIFIER].shims = [];\nvar namespace = w[NAMESPACE_IDENTIFIER];\n\nvar functions = [];\n\nvar listener = function listener() {\n  DOCUMENT.removeEventListener('DOMContentLoaded', listener);\n  loaded = 1;\n  functions.map(function (fn) {\n    return fn();\n  });\n};\n\nvar loaded = false;\n\nif (IS_DOM) {\n  loaded = (DOCUMENT.documentElement.doScroll ? /^loaded|^c/ : /^loaded|^i|^c/).test(DOCUMENT.readyState);\n  if (!loaded) DOCUMENT.addEventListener('DOMContentLoaded', listener);\n}\n\nfunction domready (fn) {\n  if (!IS_DOM) return;\n  loaded ? setTimeout(fn, 0) : functions.push(fn);\n}\n\nfunction toHtml(abstractNodes) {\n  var tag = abstractNodes.tag,\n      _abstractNodes$attrib = abstractNodes.attributes,\n      attributes = _abstractNodes$attrib === void 0 ? {} : _abstractNodes$attrib,\n      _abstractNodes$childr = abstractNodes.children,\n      children = _abstractNodes$childr === void 0 ? [] : _abstractNodes$childr;\n\n  if (typeof abstractNodes === 'string') {\n    return htmlEscape(abstractNodes);\n  } else {\n    return \"<\".concat(tag, \" \").concat(joinAttributes(attributes), \">\").concat(children.map(toHtml).join(''), \"</\").concat(tag, \">\");\n  }\n}\n\nfunction iconFromMapping(mapping, prefix, iconName) {\n  if (mapping && mapping[prefix] && mapping[prefix][iconName]) {\n    return {\n      prefix: prefix,\n      iconName: iconName,\n      icon: mapping[prefix][iconName]\n    };\n  }\n}\n\n/**\n * Internal helper to bind a function known to have 4 arguments\n * to a given context.\n */\n\nvar bindInternal4 = function bindInternal4(func, thisContext) {\n  return function (a, b, c, d) {\n    return func.call(thisContext, a, b, c, d);\n  };\n};\n\n/**\n * # Reduce\n *\n * A fast object `.reduce()` implementation.\n *\n * @param  {Object}   subject      The object to reduce over.\n * @param  {Function} fn           The reducer function.\n * @param  {mixed}    initialValue The initial value for the reducer, defaults to subject[0].\n * @param  {Object}   thisContext  The context for the reducer.\n * @return {mixed}                 The final result.\n */\n\n\nvar reduce = function fastReduceObject(subject, fn, initialValue, thisContext) {\n  var keys = Object.keys(subject),\n      length = keys.length,\n      iterator = thisContext !== undefined ? bindInternal4(fn, thisContext) : fn,\n      i,\n      key,\n      result;\n\n  if (initialValue === undefined) {\n    i = 1;\n    result = subject[keys[0]];\n  } else {\n    i = 0;\n    result = initialValue;\n  }\n\n  for (; i < length; i++) {\n    key = keys[i];\n    result = iterator(result, subject[key], key, subject);\n  }\n\n  return result;\n};\n\n/**\n * ucs2decode() and codePointAt() are both works of Mathias Bynens and licensed under MIT\n *\n * Copyright Mathias Bynens <https://mathiasbynens.be/>\n\n * Permission is hereby granted, free of charge, to any person obtaining\n * a copy of this software and associated documentation files (the\n * \"Software\"), to deal in the Software without restriction, including\n * without limitation the rights to use, copy, modify, merge, publish,\n * distribute, sublicense, and/or sell copies of the Software, and to\n * permit persons to whom the Software is furnished to do so, subject to\n * the following conditions:\n\n * The above copyright notice and this permission notice shall be\n * included in all copies or substantial portions of the Software.\n\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\n * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\n * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\n * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n */\nfunction ucs2decode(string) {\n  var output = [];\n  var counter = 0;\n  var length = string.length;\n\n  while (counter < length) {\n    var value = string.charCodeAt(counter++);\n\n    if (value >= 0xD800 && value <= 0xDBFF && counter < length) {\n      var extra = string.charCodeAt(counter++);\n\n      if ((extra & 0xFC00) == 0xDC00) {\n        // eslint-disable-line eqeqeq\n        output.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\n      } else {\n        output.push(value);\n        counter--;\n      }\n    } else {\n      output.push(value);\n    }\n  }\n\n  return output;\n}\n\nfunction toHex(unicode) {\n  var decoded = ucs2decode(unicode);\n  return decoded.length === 1 ? decoded[0].toString(16) : null;\n}\nfunction codePointAt(string, index) {\n  var size = string.length;\n  var first = string.charCodeAt(index);\n  var second;\n\n  if (first >= 0xD800 && first <= 0xDBFF && size > index + 1) {\n    second = string.charCodeAt(index + 1);\n\n    if (second >= 0xDC00 && second <= 0xDFFF) {\n      return (first - 0xD800) * 0x400 + second - 0xDC00 + 0x10000;\n    }\n  }\n\n  return first;\n}\n\nfunction normalizeIcons(icons) {\n  return Object.keys(icons).reduce(function (acc, iconName) {\n    var icon = icons[iconName];\n    var expanded = !!icon.icon;\n\n    if (expanded) {\n      acc[icon.iconName] = icon.icon;\n    } else {\n      acc[iconName] = icon;\n    }\n\n    return acc;\n  }, {});\n}\n\nfunction defineIcons(prefix, icons) {\n  var params = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  var _params$skipHooks = params.skipHooks,\n      skipHooks = _params$skipHooks === void 0 ? false : _params$skipHooks;\n  var normalized = normalizeIcons(icons);\n\n  if (typeof namespace.hooks.addPack === 'function' && !skipHooks) {\n    namespace.hooks.addPack(prefix, normalizeIcons(icons));\n  } else {\n    namespace.styles[prefix] = _objectSpread2(_objectSpread2({}, namespace.styles[prefix] || {}), normalized);\n  }\n  /**\n   * Font Awesome 4 used the prefix of `fa` for all icons. With the introduction\n   * of new styles we needed to differentiate between them. Prefix `fa` is now an alias\n   * for `fas` so we'll ease the upgrade process for our users by automatically defining\n   * this as well.\n   */\n\n\n  if (prefix === 'fas') {\n    defineIcons('fa', icons);\n  }\n}\n\nvar duotonePathRe = [/*#__PURE__*/_wrapRegExp(/path d=\"((?:(?!\")[\\s\\S])+)\".*path d=\"((?:(?!\")[\\s\\S])+)\"/, {\n  d1: 1,\n  d2: 2\n}), /*#__PURE__*/_wrapRegExp(/path class=\"((?:(?!\")[\\s\\S])+)\".*d=\"((?:(?!\")[\\s\\S])+)\".*path class=\"((?:(?!\")[\\s\\S])+)\".*d=\"((?:(?!\")[\\s\\S])+)\"/, {\n  cls1: 1,\n  d1: 2,\n  cls2: 3,\n  d2: 4\n}), /*#__PURE__*/_wrapRegExp(/path class=\"((?:(?!\")[\\s\\S])+)\".*d=\"((?:(?!\")[\\s\\S])+)\"/, {\n  cls1: 1,\n  d1: 2\n})];\n\nvar _LONG_STYLE, _PREFIXES, _PREFIXES_FOR_FAMILY;\nvar styles = namespace.styles,\n    shims = namespace.shims;\nvar LONG_STYLE = (_LONG_STYLE = {}, _defineProperty(_LONG_STYLE, FAMILY_CLASSIC, Object.values(PREFIX_TO_LONG_STYLE[FAMILY_CLASSIC])), _defineProperty(_LONG_STYLE, FAMILY_SHARP, Object.values(PREFIX_TO_LONG_STYLE[FAMILY_SHARP])), _LONG_STYLE);\nvar _defaultUsablePrefix = null;\nvar _byUnicode = {};\nvar _byLigature = {};\nvar _byOldName = {};\nvar _byOldUnicode = {};\nvar _byAlias = {};\nvar PREFIXES = (_PREFIXES = {}, _defineProperty(_PREFIXES, FAMILY_CLASSIC, Object.keys(PREFIX_TO_STYLE[FAMILY_CLASSIC])), _defineProperty(_PREFIXES, FAMILY_SHARP, Object.keys(PREFIX_TO_STYLE[FAMILY_SHARP])), _PREFIXES);\n\nfunction isReserved(name) {\n  return ~RESERVED_CLASSES.indexOf(name);\n}\n\nfunction getIconName(cssPrefix, cls) {\n  var parts = cls.split('-');\n  var prefix = parts[0];\n  var iconName = parts.slice(1).join('-');\n\n  if (prefix === cssPrefix && iconName !== '' && !isReserved(iconName)) {\n    return iconName;\n  } else {\n    return null;\n  }\n}\nvar build = function build() {\n  var lookup = function lookup(reducer) {\n    return reduce(styles, function (o, style, prefix) {\n      o[prefix] = reduce(style, reducer, {});\n      return o;\n    }, {});\n  };\n\n  _byUnicode = lookup(function (acc, icon, iconName) {\n    if (icon[3]) {\n      acc[icon[3]] = iconName;\n    }\n\n    if (icon[2]) {\n      var aliases = icon[2].filter(function (a) {\n        return typeof a === 'number';\n      });\n      aliases.forEach(function (alias) {\n        acc[alias.toString(16)] = iconName;\n      });\n    }\n\n    return acc;\n  });\n  _byLigature = lookup(function (acc, icon, iconName) {\n    acc[iconName] = iconName;\n\n    if (icon[2]) {\n      var aliases = icon[2].filter(function (a) {\n        return typeof a === 'string';\n      });\n      aliases.forEach(function (alias) {\n        acc[alias] = iconName;\n      });\n    }\n\n    return acc;\n  });\n  _byAlias = lookup(function (acc, icon, iconName) {\n    var aliases = icon[2];\n    acc[iconName] = iconName;\n    aliases.forEach(function (alias) {\n      acc[alias] = iconName;\n    });\n    return acc;\n  }); // If we have a Kit, we can't determine if regular is available since we\n  // could be auto-fetching it. We'll have to assume that it is available.\n\n  var hasRegular = 'far' in styles || config.autoFetchSvg;\n  var shimLookups = reduce(shims, function (acc, shim) {\n    var maybeNameMaybeUnicode = shim[0];\n    var prefix = shim[1];\n    var iconName = shim[2];\n\n    if (prefix === 'far' && !hasRegular) {\n      prefix = 'fas';\n    }\n\n    if (typeof maybeNameMaybeUnicode === 'string') {\n      acc.names[maybeNameMaybeUnicode] = {\n        prefix: prefix,\n        iconName: iconName\n      };\n    }\n\n    if (typeof maybeNameMaybeUnicode === 'number') {\n      acc.unicodes[maybeNameMaybeUnicode.toString(16)] = {\n        prefix: prefix,\n        iconName: iconName\n      };\n    }\n\n    return acc;\n  }, {\n    names: {},\n    unicodes: {}\n  });\n  _byOldName = shimLookups.names;\n  _byOldUnicode = shimLookups.unicodes;\n  _defaultUsablePrefix = getCanonicalPrefix(config.styleDefault, {\n    family: config.familyDefault\n  });\n};\nonChange(function (c) {\n  _defaultUsablePrefix = getCanonicalPrefix(c.styleDefault, {\n    family: config.familyDefault\n  });\n});\nbuild();\nfunction byUnicode(prefix, unicode) {\n  return (_byUnicode[prefix] || {})[unicode];\n}\nfunction byLigature(prefix, ligature) {\n  return (_byLigature[prefix] || {})[ligature];\n}\nfunction byAlias(prefix, alias) {\n  return (_byAlias[prefix] || {})[alias];\n}\nfunction byOldName(name) {\n  return _byOldName[name] || {\n    prefix: null,\n    iconName: null\n  };\n}\nfunction byOldUnicode(unicode) {\n  var oldUnicode = _byOldUnicode[unicode];\n  var newUnicode = byUnicode('fas', unicode);\n  return oldUnicode || (newUnicode ? {\n    prefix: 'fas',\n    iconName: newUnicode\n  } : null) || {\n    prefix: null,\n    iconName: null\n  };\n}\nfunction getDefaultUsablePrefix() {\n  return _defaultUsablePrefix;\n}\nvar emptyCanonicalIcon = function emptyCanonicalIcon() {\n  return {\n    prefix: null,\n    iconName: null,\n    rest: []\n  };\n};\nfunction getCanonicalPrefix(styleOrPrefix) {\n  var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var _params$family = params.family,\n      family = _params$family === void 0 ? FAMILY_CLASSIC : _params$family;\n  var style = PREFIX_TO_STYLE[family][styleOrPrefix];\n  var prefix = STYLE_TO_PREFIX[family][styleOrPrefix] || STYLE_TO_PREFIX[family][style];\n  var defined = styleOrPrefix in namespace.styles ? styleOrPrefix : null;\n  return prefix || defined || null;\n}\nvar PREFIXES_FOR_FAMILY = (_PREFIXES_FOR_FAMILY = {}, _defineProperty(_PREFIXES_FOR_FAMILY, FAMILY_CLASSIC, Object.keys(PREFIX_TO_LONG_STYLE[FAMILY_CLASSIC])), _defineProperty(_PREFIXES_FOR_FAMILY, FAMILY_SHARP, Object.keys(PREFIX_TO_LONG_STYLE[FAMILY_SHARP])), _PREFIXES_FOR_FAMILY);\nfunction getCanonicalIcon(values) {\n  var _famProps;\n\n  var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var _params$skipLookups = params.skipLookups,\n      skipLookups = _params$skipLookups === void 0 ? false : _params$skipLookups;\n  var famProps = (_famProps = {}, _defineProperty(_famProps, FAMILY_CLASSIC, \"\".concat(config.cssPrefix, \"-\").concat(FAMILY_CLASSIC)), _defineProperty(_famProps, FAMILY_SHARP, \"\".concat(config.cssPrefix, \"-\").concat(FAMILY_SHARP)), _famProps);\n  var givenPrefix = null;\n  var family = FAMILY_CLASSIC;\n\n  if (values.includes(famProps[FAMILY_CLASSIC]) || values.some(function (v) {\n    return PREFIXES_FOR_FAMILY[FAMILY_CLASSIC].includes(v);\n  })) {\n    family = FAMILY_CLASSIC;\n  }\n\n  if (values.includes(famProps[FAMILY_SHARP]) || values.some(function (v) {\n    return PREFIXES_FOR_FAMILY[FAMILY_SHARP].includes(v);\n  })) {\n    family = FAMILY_SHARP;\n  }\n\n  var canonical = values.reduce(function (acc, cls) {\n    var iconName = getIconName(config.cssPrefix, cls);\n\n    if (styles[cls]) {\n      cls = LONG_STYLE[family].includes(cls) ? LONG_STYLE_TO_PREFIX[family][cls] : cls;\n      givenPrefix = cls;\n      acc.prefix = cls;\n    } else if (PREFIXES[family].indexOf(cls) > -1) {\n      givenPrefix = cls;\n      acc.prefix = getCanonicalPrefix(cls, {\n        family: family\n      });\n    } else if (iconName) {\n      acc.iconName = iconName;\n    } else if (cls !== config.replacementClass && cls !== famProps[FAMILY_CLASSIC] && cls !== famProps[FAMILY_SHARP]) {\n      acc.rest.push(cls);\n    }\n\n    if (!skipLookups && acc.prefix && acc.iconName) {\n      var shim = givenPrefix === 'fa' ? byOldName(acc.iconName) : {};\n      var aliasIconName = byAlias(acc.prefix, acc.iconName);\n\n      if (shim.prefix) {\n        givenPrefix = null;\n      }\n\n      acc.iconName = shim.iconName || aliasIconName || acc.iconName;\n      acc.prefix = shim.prefix || acc.prefix;\n\n      if (acc.prefix === 'far' && !styles['far'] && styles['fas'] && !config.autoFetchSvg) {\n        // Allow a fallback from the regular style to solid if regular is not available\n        // but only if we aren't auto-fetching SVGs\n        acc.prefix = 'fas';\n      }\n    }\n\n    return acc;\n  }, emptyCanonicalIcon());\n\n  if (values.includes('fa-brands') || values.includes('fab')) {\n    canonical.prefix = 'fab';\n  }\n\n  if (values.includes('fa-duotone') || values.includes('fad')) {\n    canonical.prefix = 'fad';\n  }\n\n  if (!canonical.prefix && family === FAMILY_SHARP && (styles['fass'] || config.autoFetchSvg)) {\n    canonical.prefix = 'fass';\n    canonical.iconName = byAlias(canonical.prefix, canonical.iconName) || canonical.iconName;\n  }\n\n  if (canonical.prefix === 'fa' || givenPrefix === 'fa') {\n    // The fa prefix is not canonical. So if it has made it through until this point\n    // we will shift it to the correct prefix.\n    canonical.prefix = getDefaultUsablePrefix() || 'fas';\n  }\n\n  return canonical;\n}\n\nvar Library = /*#__PURE__*/function () {\n  function Library() {\n    _classCallCheck(this, Library);\n\n    this.definitions = {};\n  }\n\n  _createClass(Library, [{\n    key: \"add\",\n    value: function add() {\n      var _this = this;\n\n      for (var _len = arguments.length, definitions = new Array(_len), _key = 0; _key < _len; _key++) {\n        definitions[_key] = arguments[_key];\n      }\n\n      var additions = definitions.reduce(this._pullDefinitions, {});\n      Object.keys(additions).forEach(function (key) {\n        _this.definitions[key] = _objectSpread2(_objectSpread2({}, _this.definitions[key] || {}), additions[key]);\n        defineIcons(key, additions[key]); // TODO can we stop doing this? We can't get the icons by 'fa-solid' any longer so this probably needs to change\n\n        var longPrefix = PREFIX_TO_LONG_STYLE[FAMILY_CLASSIC][key];\n        if (longPrefix) defineIcons(longPrefix, additions[key]);\n        build();\n      });\n    }\n  }, {\n    key: \"reset\",\n    value: function reset() {\n      this.definitions = {};\n    }\n  }, {\n    key: \"_pullDefinitions\",\n    value: function _pullDefinitions(additions, definition) {\n      var normalized = definition.prefix && definition.iconName && definition.icon ? {\n        0: definition\n      } : definition;\n      Object.keys(normalized).map(function (key) {\n        var _normalized$key = normalized[key],\n            prefix = _normalized$key.prefix,\n            iconName = _normalized$key.iconName,\n            icon = _normalized$key.icon;\n        var aliases = icon[2];\n        if (!additions[prefix]) additions[prefix] = {};\n\n        if (aliases.length > 0) {\n          aliases.forEach(function (alias) {\n            if (typeof alias === 'string') {\n              additions[prefix][alias] = icon;\n            }\n          });\n        }\n\n        additions[prefix][iconName] = icon;\n      });\n      return additions;\n    }\n  }]);\n\n  return Library;\n}();\n\nvar _plugins = [];\nvar _hooks = {};\nvar providers = {};\nvar defaultProviderKeys = Object.keys(providers);\nfunction registerPlugins(nextPlugins, _ref) {\n  var obj = _ref.mixoutsTo;\n  _plugins = nextPlugins;\n  _hooks = {};\n  Object.keys(providers).forEach(function (k) {\n    if (defaultProviderKeys.indexOf(k) === -1) {\n      delete providers[k];\n    }\n  });\n\n  _plugins.forEach(function (plugin) {\n    var mixout = plugin.mixout ? plugin.mixout() : {};\n    Object.keys(mixout).forEach(function (tk) {\n      if (typeof mixout[tk] === 'function') {\n        obj[tk] = mixout[tk];\n      }\n\n      if (_typeof(mixout[tk]) === 'object') {\n        Object.keys(mixout[tk]).forEach(function (sk) {\n          if (!obj[tk]) {\n            obj[tk] = {};\n          }\n\n          obj[tk][sk] = mixout[tk][sk];\n        });\n      }\n    });\n\n    if (plugin.hooks) {\n      var hooks = plugin.hooks();\n      Object.keys(hooks).forEach(function (hook) {\n        if (!_hooks[hook]) {\n          _hooks[hook] = [];\n        }\n\n        _hooks[hook].push(hooks[hook]);\n      });\n    }\n\n    if (plugin.provides) {\n      plugin.provides(providers);\n    }\n  });\n\n  return obj;\n}\nfunction chainHooks(hook, accumulator) {\n  for (var _len = arguments.length, args = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n    args[_key - 2] = arguments[_key];\n  }\n\n  var hookFns = _hooks[hook] || [];\n  hookFns.forEach(function (hookFn) {\n    accumulator = hookFn.apply(null, [accumulator].concat(args)); // eslint-disable-line no-useless-call\n  });\n  return accumulator;\n}\nfunction callHooks(hook) {\n  for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n    args[_key2 - 1] = arguments[_key2];\n  }\n\n  var hookFns = _hooks[hook] || [];\n  hookFns.forEach(function (hookFn) {\n    hookFn.apply(null, args);\n  });\n  return undefined;\n}\nfunction callProvided() {\n  var hook = arguments[0];\n  var args = Array.prototype.slice.call(arguments, 1);\n  return providers[hook] ? providers[hook].apply(null, args) : undefined;\n}\n\nfunction findIconDefinition(iconLookup) {\n  if (iconLookup.prefix === 'fa') {\n    iconLookup.prefix = 'fas';\n  }\n\n  var iconName = iconLookup.iconName;\n  var prefix = iconLookup.prefix || getDefaultUsablePrefix();\n  if (!iconName) return;\n  iconName = byAlias(prefix, iconName) || iconName;\n  return iconFromMapping(library.definitions, prefix, iconName) || iconFromMapping(namespace.styles, prefix, iconName);\n}\nvar library = new Library();\nvar noAuto = function noAuto() {\n  config.autoReplaceSvg = false;\n  config.observeMutations = false;\n  callHooks('noAuto');\n};\nvar dom = {\n  i2svg: function i2svg() {\n    var params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n    if (IS_DOM) {\n      callHooks('beforeI2svg', params);\n      callProvided('pseudoElements2svg', params);\n      return callProvided('i2svg', params);\n    } else {\n      return Promise.reject('Operation requires a DOM of some kind.');\n    }\n  },\n  watch: function watch() {\n    var params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var autoReplaceSvgRoot = params.autoReplaceSvgRoot;\n\n    if (config.autoReplaceSvg === false) {\n      config.autoReplaceSvg = true;\n    }\n\n    config.observeMutations = true;\n    domready(function () {\n      autoReplace({\n        autoReplaceSvgRoot: autoReplaceSvgRoot\n      });\n      callHooks('watch', params);\n    });\n  }\n};\nvar parse = {\n  icon: function icon(_icon) {\n    if (_icon === null) {\n      return null;\n    }\n\n    if (_typeof(_icon) === 'object' && _icon.prefix && _icon.iconName) {\n      return {\n        prefix: _icon.prefix,\n        iconName: byAlias(_icon.prefix, _icon.iconName) || _icon.iconName\n      };\n    }\n\n    if (Array.isArray(_icon) && _icon.length === 2) {\n      var iconName = _icon[1].indexOf('fa-') === 0 ? _icon[1].slice(3) : _icon[1];\n      var prefix = getCanonicalPrefix(_icon[0]);\n      return {\n        prefix: prefix,\n        iconName: byAlias(prefix, iconName) || iconName\n      };\n    }\n\n    if (typeof _icon === 'string' && (_icon.indexOf(\"\".concat(config.cssPrefix, \"-\")) > -1 || _icon.match(ICON_SELECTION_SYNTAX_PATTERN))) {\n      var canonicalIcon = getCanonicalIcon(_icon.split(' '), {\n        skipLookups: true\n      });\n      return {\n        prefix: canonicalIcon.prefix || getDefaultUsablePrefix(),\n        iconName: byAlias(canonicalIcon.prefix, canonicalIcon.iconName) || canonicalIcon.iconName\n      };\n    }\n\n    if (typeof _icon === 'string') {\n      var _prefix = getDefaultUsablePrefix();\n\n      return {\n        prefix: _prefix,\n        iconName: byAlias(_prefix, _icon) || _icon\n      };\n    }\n  }\n};\nvar api = {\n  noAuto: noAuto,\n  config: config,\n  dom: dom,\n  parse: parse,\n  library: library,\n  findIconDefinition: findIconDefinition,\n  toHtml: toHtml\n};\n\nvar autoReplace = function autoReplace() {\n  var params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var _params$autoReplaceSv = params.autoReplaceSvgRoot,\n      autoReplaceSvgRoot = _params$autoReplaceSv === void 0 ? DOCUMENT : _params$autoReplaceSv;\n  if ((Object.keys(namespace.styles).length > 0 || config.autoFetchSvg) && IS_DOM && config.autoReplaceSvg) api.dom.i2svg({\n    node: autoReplaceSvgRoot\n  });\n};\n\nfunction domVariants(val, abstractCreator) {\n  Object.defineProperty(val, 'abstract', {\n    get: abstractCreator\n  });\n  Object.defineProperty(val, 'html', {\n    get: function get() {\n      return val.abstract.map(function (a) {\n        return toHtml(a);\n      });\n    }\n  });\n  Object.defineProperty(val, 'node', {\n    get: function get() {\n      if (!IS_DOM) return;\n      var container = DOCUMENT.createElement('div');\n      container.innerHTML = val.html;\n      return container.children;\n    }\n  });\n  return val;\n}\n\nfunction asIcon (_ref) {\n  var children = _ref.children,\n      main = _ref.main,\n      mask = _ref.mask,\n      attributes = _ref.attributes,\n      styles = _ref.styles,\n      transform = _ref.transform;\n\n  if (transformIsMeaningful(transform) && main.found && !mask.found) {\n    var width = main.width,\n        height = main.height;\n    var offset = {\n      x: width / height / 2,\n      y: 0.5\n    };\n    attributes['style'] = joinStyles(_objectSpread2(_objectSpread2({}, styles), {}, {\n      'transform-origin': \"\".concat(offset.x + transform.x / 16, \"em \").concat(offset.y + transform.y / 16, \"em\")\n    }));\n  }\n\n  return [{\n    tag: 'svg',\n    attributes: attributes,\n    children: children\n  }];\n}\n\nfunction asSymbol (_ref) {\n  var prefix = _ref.prefix,\n      iconName = _ref.iconName,\n      children = _ref.children,\n      attributes = _ref.attributes,\n      symbol = _ref.symbol;\n  var id = symbol === true ? \"\".concat(prefix, \"-\").concat(config.cssPrefix, \"-\").concat(iconName) : symbol;\n  return [{\n    tag: 'svg',\n    attributes: {\n      style: 'display: none;'\n    },\n    children: [{\n      tag: 'symbol',\n      attributes: _objectSpread2(_objectSpread2({}, attributes), {}, {\n        id: id\n      }),\n      children: children\n    }]\n  }];\n}\n\nfunction makeInlineSvgAbstract(params) {\n  var _params$icons = params.icons,\n      main = _params$icons.main,\n      mask = _params$icons.mask,\n      prefix = params.prefix,\n      iconName = params.iconName,\n      transform = params.transform,\n      symbol = params.symbol,\n      title = params.title,\n      maskId = params.maskId,\n      titleId = params.titleId,\n      extra = params.extra,\n      _params$watchable = params.watchable,\n      watchable = _params$watchable === void 0 ? false : _params$watchable;\n\n  var _ref = mask.found ? mask : main,\n      width = _ref.width,\n      height = _ref.height;\n\n  var isUploadedIcon = prefix === 'fak';\n  var attrClass = [config.replacementClass, iconName ? \"\".concat(config.cssPrefix, \"-\").concat(iconName) : ''].filter(function (c) {\n    return extra.classes.indexOf(c) === -1;\n  }).filter(function (c) {\n    return c !== '' || !!c;\n  }).concat(extra.classes).join(' ');\n  var content = {\n    children: [],\n    attributes: _objectSpread2(_objectSpread2({}, extra.attributes), {}, {\n      'data-prefix': prefix,\n      'data-icon': iconName,\n      'class': attrClass,\n      'role': extra.attributes.role || 'img',\n      'xmlns': 'http://www.w3.org/2000/svg',\n      'viewBox': \"0 0 \".concat(width, \" \").concat(height)\n    })\n  };\n  var uploadedIconWidthStyle = isUploadedIcon && !~extra.classes.indexOf('fa-fw') ? {\n    width: \"\".concat(width / height * 16 * 0.0625, \"em\")\n  } : {};\n\n  if (watchable) {\n    content.attributes[DATA_FA_I2SVG] = '';\n  }\n\n  if (title) {\n    content.children.push({\n      tag: 'title',\n      attributes: {\n        id: content.attributes['aria-labelledby'] || \"title-\".concat(titleId || nextUniqueId())\n      },\n      children: [title]\n    });\n    delete content.attributes.title;\n  }\n\n  var args = _objectSpread2(_objectSpread2({}, content), {}, {\n    prefix: prefix,\n    iconName: iconName,\n    main: main,\n    mask: mask,\n    maskId: maskId,\n    transform: transform,\n    symbol: symbol,\n    styles: _objectSpread2(_objectSpread2({}, uploadedIconWidthStyle), extra.styles)\n  });\n\n  var _ref2 = mask.found && main.found ? callProvided('generateAbstractMask', args) || {\n    children: [],\n    attributes: {}\n  } : callProvided('generateAbstractIcon', args) || {\n    children: [],\n    attributes: {}\n  },\n      children = _ref2.children,\n      attributes = _ref2.attributes;\n\n  args.children = children;\n  args.attributes = attributes;\n\n  if (symbol) {\n    return asSymbol(args);\n  } else {\n    return asIcon(args);\n  }\n}\nfunction makeLayersTextAbstract(params) {\n  var content = params.content,\n      width = params.width,\n      height = params.height,\n      transform = params.transform,\n      title = params.title,\n      extra = params.extra,\n      _params$watchable2 = params.watchable,\n      watchable = _params$watchable2 === void 0 ? false : _params$watchable2;\n\n  var attributes = _objectSpread2(_objectSpread2(_objectSpread2({}, extra.attributes), title ? {\n    'title': title\n  } : {}), {}, {\n    'class': extra.classes.join(' ')\n  });\n\n  if (watchable) {\n    attributes[DATA_FA_I2SVG] = '';\n  }\n\n  var styles = _objectSpread2({}, extra.styles);\n\n  if (transformIsMeaningful(transform)) {\n    styles['transform'] = transformForCss({\n      transform: transform,\n      startCentered: true,\n      width: width,\n      height: height\n    });\n    styles['-webkit-transform'] = styles['transform'];\n  }\n\n  var styleString = joinStyles(styles);\n\n  if (styleString.length > 0) {\n    attributes['style'] = styleString;\n  }\n\n  var val = [];\n  val.push({\n    tag: 'span',\n    attributes: attributes,\n    children: [content]\n  });\n\n  if (title) {\n    val.push({\n      tag: 'span',\n      attributes: {\n        class: 'sr-only'\n      },\n      children: [title]\n    });\n  }\n\n  return val;\n}\nfunction makeLayersCounterAbstract(params) {\n  var content = params.content,\n      title = params.title,\n      extra = params.extra;\n\n  var attributes = _objectSpread2(_objectSpread2(_objectSpread2({}, extra.attributes), title ? {\n    'title': title\n  } : {}), {}, {\n    'class': extra.classes.join(' ')\n  });\n\n  var styleString = joinStyles(extra.styles);\n\n  if (styleString.length > 0) {\n    attributes['style'] = styleString;\n  }\n\n  var val = [];\n  val.push({\n    tag: 'span',\n    attributes: attributes,\n    children: [content]\n  });\n\n  if (title) {\n    val.push({\n      tag: 'span',\n      attributes: {\n        class: 'sr-only'\n      },\n      children: [title]\n    });\n  }\n\n  return val;\n}\n\nvar styles$1 = namespace.styles;\nfunction asFoundIcon(icon) {\n  var width = icon[0];\n  var height = icon[1];\n\n  var _icon$slice = icon.slice(4),\n      _icon$slice2 = _slicedToArray(_icon$slice, 1),\n      vectorData = _icon$slice2[0];\n\n  var element = null;\n\n  if (Array.isArray(vectorData)) {\n    element = {\n      tag: 'g',\n      attributes: {\n        class: \"\".concat(config.cssPrefix, \"-\").concat(DUOTONE_CLASSES.GROUP)\n      },\n      children: [{\n        tag: 'path',\n        attributes: {\n          class: \"\".concat(config.cssPrefix, \"-\").concat(DUOTONE_CLASSES.SECONDARY),\n          fill: 'currentColor',\n          d: vectorData[0]\n        }\n      }, {\n        tag: 'path',\n        attributes: {\n          class: \"\".concat(config.cssPrefix, \"-\").concat(DUOTONE_CLASSES.PRIMARY),\n          fill: 'currentColor',\n          d: vectorData[1]\n        }\n      }]\n    };\n  } else {\n    element = {\n      tag: 'path',\n      attributes: {\n        fill: 'currentColor',\n        d: vectorData\n      }\n    };\n  }\n\n  return {\n    found: true,\n    width: width,\n    height: height,\n    icon: element\n  };\n}\nvar missingIconResolutionMixin = {\n  found: false,\n  width: 512,\n  height: 512\n};\n\nfunction maybeNotifyMissing(iconName, prefix) {\n  if (!PRODUCTION && !config.showMissingIcons && iconName) {\n    console.error(\"Icon with name \\\"\".concat(iconName, \"\\\" and prefix \\\"\").concat(prefix, \"\\\" is missing.\"));\n  }\n}\n\nfunction findIcon(iconName, prefix) {\n  var givenPrefix = prefix;\n\n  if (prefix === 'fa' && config.styleDefault !== null) {\n    prefix = getDefaultUsablePrefix();\n  }\n\n  return new Promise(function (resolve, reject) {\n    var val = {\n      found: false,\n      width: 512,\n      height: 512,\n      icon: callProvided('missingIconAbstract') || {}\n    };\n\n    if (givenPrefix === 'fa') {\n      var shim = byOldName(iconName) || {};\n      iconName = shim.iconName || iconName;\n      prefix = shim.prefix || prefix;\n    }\n\n    if (iconName && prefix && styles$1[prefix] && styles$1[prefix][iconName]) {\n      var icon = styles$1[prefix][iconName];\n      return resolve(asFoundIcon(icon));\n    }\n\n    maybeNotifyMissing(iconName, prefix);\n    resolve(_objectSpread2(_objectSpread2({}, missingIconResolutionMixin), {}, {\n      icon: config.showMissingIcons && iconName ? callProvided('missingIconAbstract') || {} : {}\n    }));\n  });\n}\n\nvar noop$1 = function noop() {};\n\nvar p = config.measurePerformance && PERFORMANCE && PERFORMANCE.mark && PERFORMANCE.measure ? PERFORMANCE : {\n  mark: noop$1,\n  measure: noop$1\n};\nvar preamble = \"FA \\\"6.5.2\\\"\";\n\nvar begin = function begin(name) {\n  p.mark(\"\".concat(preamble, \" \").concat(name, \" begins\"));\n  return function () {\n    return end(name);\n  };\n};\n\nvar end = function end(name) {\n  p.mark(\"\".concat(preamble, \" \").concat(name, \" ends\"));\n  p.measure(\"\".concat(preamble, \" \").concat(name), \"\".concat(preamble, \" \").concat(name, \" begins\"), \"\".concat(preamble, \" \").concat(name, \" ends\"));\n};\n\nvar perf = {\n  begin: begin,\n  end: end\n};\n\nvar noop$2 = function noop() {};\n\nfunction isWatched(node) {\n  var i2svg = node.getAttribute ? node.getAttribute(DATA_FA_I2SVG) : null;\n  return typeof i2svg === 'string';\n}\n\nfunction hasPrefixAndIcon(node) {\n  var prefix = node.getAttribute ? node.getAttribute(DATA_PREFIX) : null;\n  var icon = node.getAttribute ? node.getAttribute(DATA_ICON) : null;\n  return prefix && icon;\n}\n\nfunction hasBeenReplaced(node) {\n  return node && node.classList && node.classList.contains && node.classList.contains(config.replacementClass);\n}\n\nfunction getMutator() {\n  if (config.autoReplaceSvg === true) {\n    return mutators.replace;\n  }\n\n  var mutator = mutators[config.autoReplaceSvg];\n  return mutator || mutators.replace;\n}\n\nfunction createElementNS(tag) {\n  return DOCUMENT.createElementNS('http://www.w3.org/2000/svg', tag);\n}\n\nfunction createElement(tag) {\n  return DOCUMENT.createElement(tag);\n}\n\nfunction convertSVG(abstractObj) {\n  var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var _params$ceFn = params.ceFn,\n      ceFn = _params$ceFn === void 0 ? abstractObj.tag === 'svg' ? createElementNS : createElement : _params$ceFn;\n\n  if (typeof abstractObj === 'string') {\n    return DOCUMENT.createTextNode(abstractObj);\n  }\n\n  var tag = ceFn(abstractObj.tag);\n  Object.keys(abstractObj.attributes || []).forEach(function (key) {\n    tag.setAttribute(key, abstractObj.attributes[key]);\n  });\n  var children = abstractObj.children || [];\n  children.forEach(function (child) {\n    tag.appendChild(convertSVG(child, {\n      ceFn: ceFn\n    }));\n  });\n  return tag;\n}\n\nfunction nodeAsComment(node) {\n  var comment = \" \".concat(node.outerHTML, \" \");\n  /* BEGIN.ATTRIBUTION */\n\n  comment = \"\".concat(comment, \"Font Awesome fontawesome.com \");\n  /* END.ATTRIBUTION */\n\n  return comment;\n}\n\nvar mutators = {\n  replace: function replace(mutation) {\n    var node = mutation[0];\n\n    if (node.parentNode) {\n      mutation[1].forEach(function (_abstract) {\n        node.parentNode.insertBefore(convertSVG(_abstract), node);\n      });\n\n      if (node.getAttribute(DATA_FA_I2SVG) === null && config.keepOriginalSource) {\n        var comment = DOCUMENT.createComment(nodeAsComment(node));\n        node.parentNode.replaceChild(comment, node);\n      } else {\n        node.remove();\n      }\n    }\n  },\n  nest: function nest(mutation) {\n    var node = mutation[0];\n    var _abstract2 = mutation[1]; // If we already have a replaced node we do not want to continue nesting within it.\n    // Short-circuit to the standard replacement\n\n    if (~classArray(node).indexOf(config.replacementClass)) {\n      return mutators.replace(mutation);\n    }\n\n    var forSvg = new RegExp(\"\".concat(config.cssPrefix, \"-.*\"));\n    delete _abstract2[0].attributes.id;\n\n    if (_abstract2[0].attributes.class) {\n      var splitClasses = _abstract2[0].attributes.class.split(' ').reduce(function (acc, cls) {\n        if (cls === config.replacementClass || cls.match(forSvg)) {\n          acc.toSvg.push(cls);\n        } else {\n          acc.toNode.push(cls);\n        }\n\n        return acc;\n      }, {\n        toNode: [],\n        toSvg: []\n      });\n\n      _abstract2[0].attributes.class = splitClasses.toSvg.join(' ');\n\n      if (splitClasses.toNode.length === 0) {\n        node.removeAttribute('class');\n      } else {\n        node.setAttribute('class', splitClasses.toNode.join(' '));\n      }\n    }\n\n    var newInnerHTML = _abstract2.map(function (a) {\n      return toHtml(a);\n    }).join('\\n');\n\n    node.setAttribute(DATA_FA_I2SVG, '');\n    node.innerHTML = newInnerHTML;\n  }\n};\n\nfunction performOperationSync(op) {\n  op();\n}\n\nfunction perform(mutations, callback) {\n  var callbackFunction = typeof callback === 'function' ? callback : noop$2;\n\n  if (mutations.length === 0) {\n    callbackFunction();\n  } else {\n    var frame = performOperationSync;\n\n    if (config.mutateApproach === MUTATION_APPROACH_ASYNC) {\n      frame = WINDOW.requestAnimationFrame || performOperationSync;\n    }\n\n    frame(function () {\n      var mutator = getMutator();\n      var mark = perf.begin('mutate');\n      mutations.map(mutator);\n      mark();\n      callbackFunction();\n    });\n  }\n}\nvar disabled = false;\nfunction disableObservation() {\n  disabled = true;\n}\nfunction enableObservation() {\n  disabled = false;\n}\nvar mo = null;\nfunction observe(options) {\n  if (!MUTATION_OBSERVER) {\n    return;\n  }\n\n  if (!config.observeMutations) {\n    return;\n  }\n\n  var _options$treeCallback = options.treeCallback,\n      treeCallback = _options$treeCallback === void 0 ? noop$2 : _options$treeCallback,\n      _options$nodeCallback = options.nodeCallback,\n      nodeCallback = _options$nodeCallback === void 0 ? noop$2 : _options$nodeCallback,\n      _options$pseudoElemen = options.pseudoElementsCallback,\n      pseudoElementsCallback = _options$pseudoElemen === void 0 ? noop$2 : _options$pseudoElemen,\n      _options$observeMutat = options.observeMutationsRoot,\n      observeMutationsRoot = _options$observeMutat === void 0 ? DOCUMENT : _options$observeMutat;\n  mo = new MUTATION_OBSERVER(function (objects) {\n    if (disabled) return;\n    var defaultPrefix = getDefaultUsablePrefix();\n    toArray(objects).forEach(function (mutationRecord) {\n      if (mutationRecord.type === 'childList' && mutationRecord.addedNodes.length > 0 && !isWatched(mutationRecord.addedNodes[0])) {\n        if (config.searchPseudoElements) {\n          pseudoElementsCallback(mutationRecord.target);\n        }\n\n        treeCallback(mutationRecord.target);\n      }\n\n      if (mutationRecord.type === 'attributes' && mutationRecord.target.parentNode && config.searchPseudoElements) {\n        pseudoElementsCallback(mutationRecord.target.parentNode);\n      }\n\n      if (mutationRecord.type === 'attributes' && isWatched(mutationRecord.target) && ~ATTRIBUTES_WATCHED_FOR_MUTATION.indexOf(mutationRecord.attributeName)) {\n        if (mutationRecord.attributeName === 'class' && hasPrefixAndIcon(mutationRecord.target)) {\n          var _getCanonicalIcon = getCanonicalIcon(classArray(mutationRecord.target)),\n              prefix = _getCanonicalIcon.prefix,\n              iconName = _getCanonicalIcon.iconName;\n\n          mutationRecord.target.setAttribute(DATA_PREFIX, prefix || defaultPrefix);\n          if (iconName) mutationRecord.target.setAttribute(DATA_ICON, iconName);\n        } else if (hasBeenReplaced(mutationRecord.target)) {\n          nodeCallback(mutationRecord.target);\n        }\n      }\n    });\n  });\n  if (!IS_DOM) return;\n  mo.observe(observeMutationsRoot, {\n    childList: true,\n    attributes: true,\n    characterData: true,\n    subtree: true\n  });\n}\nfunction disconnect() {\n  if (!mo) return;\n  mo.disconnect();\n}\n\nfunction styleParser (node) {\n  var style = node.getAttribute('style');\n  var val = [];\n\n  if (style) {\n    val = style.split(';').reduce(function (acc, style) {\n      var styles = style.split(':');\n      var prop = styles[0];\n      var value = styles.slice(1);\n\n      if (prop && value.length > 0) {\n        acc[prop] = value.join(':').trim();\n      }\n\n      return acc;\n    }, {});\n  }\n\n  return val;\n}\n\nfunction classParser (node) {\n  var existingPrefix = node.getAttribute('data-prefix');\n  var existingIconName = node.getAttribute('data-icon');\n  var innerText = node.innerText !== undefined ? node.innerText.trim() : '';\n  var val = getCanonicalIcon(classArray(node));\n\n  if (!val.prefix) {\n    val.prefix = getDefaultUsablePrefix();\n  }\n\n  if (existingPrefix && existingIconName) {\n    val.prefix = existingPrefix;\n    val.iconName = existingIconName;\n  }\n\n  if (val.iconName && val.prefix) {\n    return val;\n  }\n\n  if (val.prefix && innerText.length > 0) {\n    val.iconName = byLigature(val.prefix, node.innerText) || byUnicode(val.prefix, toHex(node.innerText));\n  }\n\n  if (!val.iconName && config.autoFetchSvg && node.firstChild && node.firstChild.nodeType === Node.TEXT_NODE) {\n    val.iconName = node.firstChild.data;\n  }\n\n  return val;\n}\n\nfunction attributesParser (node) {\n  var extraAttributes = toArray(node.attributes).reduce(function (acc, attr) {\n    if (acc.name !== 'class' && acc.name !== 'style') {\n      acc[attr.name] = attr.value;\n    }\n\n    return acc;\n  }, {});\n  var title = node.getAttribute('title');\n  var titleId = node.getAttribute('data-fa-title-id');\n\n  if (config.autoA11y) {\n    if (title) {\n      extraAttributes['aria-labelledby'] = \"\".concat(config.replacementClass, \"-title-\").concat(titleId || nextUniqueId());\n    } else {\n      extraAttributes['aria-hidden'] = 'true';\n      extraAttributes['focusable'] = 'false';\n    }\n  }\n\n  return extraAttributes;\n}\n\nfunction blankMeta() {\n  return {\n    iconName: null,\n    title: null,\n    titleId: null,\n    prefix: null,\n    transform: meaninglessTransform,\n    symbol: false,\n    mask: {\n      iconName: null,\n      prefix: null,\n      rest: []\n    },\n    maskId: null,\n    extra: {\n      classes: [],\n      styles: {},\n      attributes: {}\n    }\n  };\n}\nfunction parseMeta(node) {\n  var parser = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    styleParser: true\n  };\n\n  var _classParser = classParser(node),\n      iconName = _classParser.iconName,\n      prefix = _classParser.prefix,\n      extraClasses = _classParser.rest;\n\n  var extraAttributes = attributesParser(node);\n  var pluginMeta = chainHooks('parseNodeAttributes', {}, node);\n  var extraStyles = parser.styleParser ? styleParser(node) : [];\n  return _objectSpread2({\n    iconName: iconName,\n    title: node.getAttribute('title'),\n    titleId: node.getAttribute('data-fa-title-id'),\n    prefix: prefix,\n    transform: meaninglessTransform,\n    mask: {\n      iconName: null,\n      prefix: null,\n      rest: []\n    },\n    maskId: null,\n    symbol: false,\n    extra: {\n      classes: extraClasses,\n      styles: extraStyles,\n      attributes: extraAttributes\n    }\n  }, pluginMeta);\n}\n\nvar styles$2 = namespace.styles;\n\nfunction generateMutation(node) {\n  var nodeMeta = config.autoReplaceSvg === 'nest' ? parseMeta(node, {\n    styleParser: false\n  }) : parseMeta(node);\n\n  if (~nodeMeta.extra.classes.indexOf(LAYERS_TEXT_CLASSNAME)) {\n    return callProvided('generateLayersText', node, nodeMeta);\n  } else {\n    return callProvided('generateSvgReplacementMutation', node, nodeMeta);\n  }\n}\n\nvar knownPrefixes = new Set();\nFAMILIES.map(function (family) {\n  knownPrefixes.add(\"fa-\".concat(family));\n});\nObject.keys(PREFIX_TO_STYLE[FAMILY_CLASSIC]).map(knownPrefixes.add.bind(knownPrefixes));\nObject.keys(PREFIX_TO_STYLE[FAMILY_SHARP]).map(knownPrefixes.add.bind(knownPrefixes));\nknownPrefixes = _toConsumableArray(knownPrefixes);\n\nfunction onTree(root) {\n  var callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n  if (!IS_DOM) return Promise.resolve();\n  var htmlClassList = DOCUMENT.documentElement.classList;\n\n  var hclAdd = function hclAdd(suffix) {\n    return htmlClassList.add(\"\".concat(HTML_CLASS_I2SVG_BASE_CLASS, \"-\").concat(suffix));\n  };\n\n  var hclRemove = function hclRemove(suffix) {\n    return htmlClassList.remove(\"\".concat(HTML_CLASS_I2SVG_BASE_CLASS, \"-\").concat(suffix));\n  };\n\n  var prefixes = config.autoFetchSvg ? knownPrefixes : FAMILIES.map(function (f) {\n    return \"fa-\".concat(f);\n  }).concat(Object.keys(styles$2));\n\n  if (!prefixes.includes('fa')) {\n    prefixes.push('fa');\n  }\n\n  var prefixesDomQuery = [\".\".concat(LAYERS_TEXT_CLASSNAME, \":not([\").concat(DATA_FA_I2SVG, \"])\")].concat(prefixes.map(function (p) {\n    return \".\".concat(p, \":not([\").concat(DATA_FA_I2SVG, \"])\");\n  })).join(', ');\n\n  if (prefixesDomQuery.length === 0) {\n    return Promise.resolve();\n  }\n\n  var candidates = [];\n\n  try {\n    candidates = toArray(root.querySelectorAll(prefixesDomQuery));\n  } catch (e) {// noop\n  }\n\n  if (candidates.length > 0) {\n    hclAdd('pending');\n    hclRemove('complete');\n  } else {\n    return Promise.resolve();\n  }\n\n  var mark = perf.begin('onTree');\n  var mutations = candidates.reduce(function (acc, node) {\n    try {\n      var mutation = generateMutation(node);\n\n      if (mutation) {\n        acc.push(mutation);\n      }\n    } catch (e) {\n      if (!PRODUCTION) {\n        if (e.name === 'MissingIcon') {\n          console.error(e);\n        }\n      }\n    }\n\n    return acc;\n  }, []);\n  return new Promise(function (resolve, reject) {\n    Promise.all(mutations).then(function (resolvedMutations) {\n      perform(resolvedMutations, function () {\n        hclAdd('active');\n        hclAdd('complete');\n        hclRemove('pending');\n        if (typeof callback === 'function') callback();\n        mark();\n        resolve();\n      });\n    }).catch(function (e) {\n      mark();\n      reject(e);\n    });\n  });\n}\n\nfunction onNode(node) {\n  var callback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n  generateMutation(node).then(function (mutation) {\n    if (mutation) {\n      perform([mutation], callback);\n    }\n  });\n}\n\nfunction resolveIcons(next) {\n  return function (maybeIconDefinition) {\n    var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var iconDefinition = (maybeIconDefinition || {}).icon ? maybeIconDefinition : findIconDefinition(maybeIconDefinition || {});\n    var mask = params.mask;\n\n    if (mask) {\n      mask = (mask || {}).icon ? mask : findIconDefinition(mask || {});\n    }\n\n    return next(iconDefinition, _objectSpread2(_objectSpread2({}, params), {}, {\n      mask: mask\n    }));\n  };\n}\n\nvar render = function render(iconDefinition) {\n  var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var _params$transform = params.transform,\n      transform = _params$transform === void 0 ? meaninglessTransform : _params$transform,\n      _params$symbol = params.symbol,\n      symbol = _params$symbol === void 0 ? false : _params$symbol,\n      _params$mask = params.mask,\n      mask = _params$mask === void 0 ? null : _params$mask,\n      _params$maskId = params.maskId,\n      maskId = _params$maskId === void 0 ? null : _params$maskId,\n      _params$title = params.title,\n      title = _params$title === void 0 ? null : _params$title,\n      _params$titleId = params.titleId,\n      titleId = _params$titleId === void 0 ? null : _params$titleId,\n      _params$classes = params.classes,\n      classes = _params$classes === void 0 ? [] : _params$classes,\n      _params$attributes = params.attributes,\n      attributes = _params$attributes === void 0 ? {} : _params$attributes,\n      _params$styles = params.styles,\n      styles = _params$styles === void 0 ? {} : _params$styles;\n  if (!iconDefinition) return;\n  var prefix = iconDefinition.prefix,\n      iconName = iconDefinition.iconName,\n      icon = iconDefinition.icon;\n  return domVariants(_objectSpread2({\n    type: 'icon'\n  }, iconDefinition), function () {\n    callHooks('beforeDOMElementCreation', {\n      iconDefinition: iconDefinition,\n      params: params\n    });\n\n    if (config.autoA11y) {\n      if (title) {\n        attributes['aria-labelledby'] = \"\".concat(config.replacementClass, \"-title-\").concat(titleId || nextUniqueId());\n      } else {\n        attributes['aria-hidden'] = 'true';\n        attributes['focusable'] = 'false';\n      }\n    }\n\n    return makeInlineSvgAbstract({\n      icons: {\n        main: asFoundIcon(icon),\n        mask: mask ? asFoundIcon(mask.icon) : {\n          found: false,\n          width: null,\n          height: null,\n          icon: {}\n        }\n      },\n      prefix: prefix,\n      iconName: iconName,\n      transform: _objectSpread2(_objectSpread2({}, meaninglessTransform), transform),\n      symbol: symbol,\n      title: title,\n      maskId: maskId,\n      titleId: titleId,\n      extra: {\n        attributes: attributes,\n        styles: styles,\n        classes: classes\n      }\n    });\n  });\n};\nvar ReplaceElements = {\n  mixout: function mixout() {\n    return {\n      icon: resolveIcons(render)\n    };\n  },\n  hooks: function hooks() {\n    return {\n      mutationObserverCallbacks: function mutationObserverCallbacks(accumulator) {\n        accumulator.treeCallback = onTree;\n        accumulator.nodeCallback = onNode;\n        return accumulator;\n      }\n    };\n  },\n  provides: function provides(providers$$1) {\n    providers$$1.i2svg = function (params) {\n      var _params$node = params.node,\n          node = _params$node === void 0 ? DOCUMENT : _params$node,\n          _params$callback = params.callback,\n          callback = _params$callback === void 0 ? function () {} : _params$callback;\n      return onTree(node, callback);\n    };\n\n    providers$$1.generateSvgReplacementMutation = function (node, nodeMeta) {\n      var iconName = nodeMeta.iconName,\n          title = nodeMeta.title,\n          titleId = nodeMeta.titleId,\n          prefix = nodeMeta.prefix,\n          transform = nodeMeta.transform,\n          symbol = nodeMeta.symbol,\n          mask = nodeMeta.mask,\n          maskId = nodeMeta.maskId,\n          extra = nodeMeta.extra;\n      return new Promise(function (resolve, reject) {\n        Promise.all([findIcon(iconName, prefix), mask.iconName ? findIcon(mask.iconName, mask.prefix) : Promise.resolve({\n          found: false,\n          width: 512,\n          height: 512,\n          icon: {}\n        })]).then(function (_ref) {\n          var _ref2 = _slicedToArray(_ref, 2),\n              main = _ref2[0],\n              mask = _ref2[1];\n\n          resolve([node, makeInlineSvgAbstract({\n            icons: {\n              main: main,\n              mask: mask\n            },\n            prefix: prefix,\n            iconName: iconName,\n            transform: transform,\n            symbol: symbol,\n            maskId: maskId,\n            title: title,\n            titleId: titleId,\n            extra: extra,\n            watchable: true\n          })]);\n        }).catch(reject);\n      });\n    };\n\n    providers$$1.generateAbstractIcon = function (_ref3) {\n      var children = _ref3.children,\n          attributes = _ref3.attributes,\n          main = _ref3.main,\n          transform = _ref3.transform,\n          styles = _ref3.styles;\n      var styleString = joinStyles(styles);\n\n      if (styleString.length > 0) {\n        attributes['style'] = styleString;\n      }\n\n      var nextChild;\n\n      if (transformIsMeaningful(transform)) {\n        nextChild = callProvided('generateAbstractTransformGrouping', {\n          main: main,\n          transform: transform,\n          containerWidth: main.width,\n          iconWidth: main.width\n        });\n      }\n\n      children.push(nextChild || main.icon);\n      return {\n        children: children,\n        attributes: attributes\n      };\n    };\n  }\n};\n\nvar Layers = {\n  mixout: function mixout() {\n    return {\n      layer: function layer(assembler) {\n        var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        var _params$classes = params.classes,\n            classes = _params$classes === void 0 ? [] : _params$classes;\n        return domVariants({\n          type: 'layer'\n        }, function () {\n          callHooks('beforeDOMElementCreation', {\n            assembler: assembler,\n            params: params\n          });\n          var children = [];\n          assembler(function (args) {\n            Array.isArray(args) ? args.map(function (a) {\n              children = children.concat(a.abstract);\n            }) : children = children.concat(args.abstract);\n          });\n          return [{\n            tag: 'span',\n            attributes: {\n              class: [\"\".concat(config.cssPrefix, \"-layers\")].concat(_toConsumableArray(classes)).join(' ')\n            },\n            children: children\n          }];\n        });\n      }\n    };\n  }\n};\n\nvar LayersCounter = {\n  mixout: function mixout() {\n    return {\n      counter: function counter(content) {\n        var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        var _params$title = params.title,\n            title = _params$title === void 0 ? null : _params$title,\n            _params$classes = params.classes,\n            classes = _params$classes === void 0 ? [] : _params$classes,\n            _params$attributes = params.attributes,\n            attributes = _params$attributes === void 0 ? {} : _params$attributes,\n            _params$styles = params.styles,\n            styles = _params$styles === void 0 ? {} : _params$styles;\n        return domVariants({\n          type: 'counter',\n          content: content\n        }, function () {\n          callHooks('beforeDOMElementCreation', {\n            content: content,\n            params: params\n          });\n          return makeLayersCounterAbstract({\n            content: content.toString(),\n            title: title,\n            extra: {\n              attributes: attributes,\n              styles: styles,\n              classes: [\"\".concat(config.cssPrefix, \"-layers-counter\")].concat(_toConsumableArray(classes))\n            }\n          });\n        });\n      }\n    };\n  }\n};\n\nvar LayersText = {\n  mixout: function mixout() {\n    return {\n      text: function text(content) {\n        var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        var _params$transform = params.transform,\n            transform = _params$transform === void 0 ? meaninglessTransform : _params$transform,\n            _params$title = params.title,\n            title = _params$title === void 0 ? null : _params$title,\n            _params$classes = params.classes,\n            classes = _params$classes === void 0 ? [] : _params$classes,\n            _params$attributes = params.attributes,\n            attributes = _params$attributes === void 0 ? {} : _params$attributes,\n            _params$styles = params.styles,\n            styles = _params$styles === void 0 ? {} : _params$styles;\n        return domVariants({\n          type: 'text',\n          content: content\n        }, function () {\n          callHooks('beforeDOMElementCreation', {\n            content: content,\n            params: params\n          });\n          return makeLayersTextAbstract({\n            content: content,\n            transform: _objectSpread2(_objectSpread2({}, meaninglessTransform), transform),\n            title: title,\n            extra: {\n              attributes: attributes,\n              styles: styles,\n              classes: [\"\".concat(config.cssPrefix, \"-layers-text\")].concat(_toConsumableArray(classes))\n            }\n          });\n        });\n      }\n    };\n  },\n  provides: function provides(providers$$1) {\n    providers$$1.generateLayersText = function (node, nodeMeta) {\n      var title = nodeMeta.title,\n          transform = nodeMeta.transform,\n          extra = nodeMeta.extra;\n      var width = null;\n      var height = null;\n\n      if (IS_IE) {\n        var computedFontSize = parseInt(getComputedStyle(node).fontSize, 10);\n        var boundingClientRect = node.getBoundingClientRect();\n        width = boundingClientRect.width / computedFontSize;\n        height = boundingClientRect.height / computedFontSize;\n      }\n\n      if (config.autoA11y && !title) {\n        extra.attributes['aria-hidden'] = 'true';\n      }\n\n      return Promise.resolve([node, makeLayersTextAbstract({\n        content: node.innerHTML,\n        width: width,\n        height: height,\n        transform: transform,\n        title: title,\n        extra: extra,\n        watchable: true\n      })]);\n    };\n  }\n};\n\nvar CLEAN_CONTENT_PATTERN = new RegExp(\"\\\"\", 'ug');\nvar SECONDARY_UNICODE_RANGE = [1105920, 1112319];\nfunction hexValueFromContent(content) {\n  var cleaned = content.replace(CLEAN_CONTENT_PATTERN, '');\n  var codePoint = codePointAt(cleaned, 0);\n  var isPrependTen = codePoint >= SECONDARY_UNICODE_RANGE[0] && codePoint <= SECONDARY_UNICODE_RANGE[1];\n  var isDoubled = cleaned.length === 2 ? cleaned[0] === cleaned[1] : false;\n  return {\n    value: isDoubled ? toHex(cleaned[0]) : toHex(cleaned),\n    isSecondary: isPrependTen || isDoubled\n  };\n}\n\nfunction replaceForPosition(node, position) {\n  var pendingAttribute = \"\".concat(DATA_FA_PSEUDO_ELEMENT_PENDING).concat(position.replace(':', '-'));\n  return new Promise(function (resolve, reject) {\n    if (node.getAttribute(pendingAttribute) !== null) {\n      // This node is already being processed\n      return resolve();\n    }\n\n    var children = toArray(node.children);\n    var alreadyProcessedPseudoElement = children.filter(function (c) {\n      return c.getAttribute(DATA_FA_PSEUDO_ELEMENT) === position;\n    })[0];\n    var styles = WINDOW.getComputedStyle(node, position);\n    var fontFamily = styles.getPropertyValue('font-family').match(FONT_FAMILY_PATTERN);\n    var fontWeight = styles.getPropertyValue('font-weight');\n    var content = styles.getPropertyValue('content');\n\n    if (alreadyProcessedPseudoElement && !fontFamily) {\n      // If we've already processed it but the current computed style does not result in a font-family,\n      // that probably means that a class name that was previously present to make the icon has been\n      // removed. So we now should delete the icon.\n      node.removeChild(alreadyProcessedPseudoElement);\n      return resolve();\n    } else if (fontFamily && content !== 'none' && content !== '') {\n      var _content = styles.getPropertyValue('content');\n\n      var family = ~['Sharp'].indexOf(fontFamily[2]) ? FAMILY_SHARP : FAMILY_CLASSIC;\n      var prefix = ~['Solid', 'Regular', 'Light', 'Thin', 'Duotone', 'Brands', 'Kit'].indexOf(fontFamily[2]) ? STYLE_TO_PREFIX[family][fontFamily[2].toLowerCase()] : FONT_WEIGHT_TO_PREFIX[family][fontWeight];\n\n      var _hexValueFromContent = hexValueFromContent(_content),\n          hexValue = _hexValueFromContent.value,\n          isSecondary = _hexValueFromContent.isSecondary;\n\n      var isV4 = fontFamily[0].startsWith('FontAwesome');\n      var iconName = byUnicode(prefix, hexValue);\n      var iconIdentifier = iconName;\n\n      if (isV4) {\n        var iconName4 = byOldUnicode(hexValue);\n\n        if (iconName4.iconName && iconName4.prefix) {\n          iconName = iconName4.iconName;\n          prefix = iconName4.prefix;\n        }\n      } // Only convert the pseudo element in this ::before/::after position into an icon if we haven't\n      // already done so with the same prefix and iconName\n\n\n      if (iconName && !isSecondary && (!alreadyProcessedPseudoElement || alreadyProcessedPseudoElement.getAttribute(DATA_PREFIX) !== prefix || alreadyProcessedPseudoElement.getAttribute(DATA_ICON) !== iconIdentifier)) {\n        node.setAttribute(pendingAttribute, iconIdentifier);\n\n        if (alreadyProcessedPseudoElement) {\n          // Delete the old one, since we're replacing it with a new one\n          node.removeChild(alreadyProcessedPseudoElement);\n        }\n\n        var meta = blankMeta();\n        var extra = meta.extra;\n        extra.attributes[DATA_FA_PSEUDO_ELEMENT] = position;\n        findIcon(iconName, prefix).then(function (main) {\n          var _abstract = makeInlineSvgAbstract(_objectSpread2(_objectSpread2({}, meta), {}, {\n            icons: {\n              main: main,\n              mask: emptyCanonicalIcon()\n            },\n            prefix: prefix,\n            iconName: iconIdentifier,\n            extra: extra,\n            watchable: true\n          }));\n\n          var element = DOCUMENT.createElementNS('http://www.w3.org/2000/svg', 'svg');\n\n          if (position === '::before') {\n            node.insertBefore(element, node.firstChild);\n          } else {\n            node.appendChild(element);\n          }\n\n          element.outerHTML = _abstract.map(function (a) {\n            return toHtml(a);\n          }).join('\\n');\n          node.removeAttribute(pendingAttribute);\n          resolve();\n        }).catch(reject);\n      } else {\n        resolve();\n      }\n    } else {\n      resolve();\n    }\n  });\n}\n\nfunction replace(node) {\n  return Promise.all([replaceForPosition(node, '::before'), replaceForPosition(node, '::after')]);\n}\n\nfunction processable(node) {\n  return node.parentNode !== document.head && !~TAGNAMES_TO_SKIP_FOR_PSEUDOELEMENTS.indexOf(node.tagName.toUpperCase()) && !node.getAttribute(DATA_FA_PSEUDO_ELEMENT) && (!node.parentNode || node.parentNode.tagName !== 'svg');\n}\n\nfunction searchPseudoElements(root) {\n  if (!IS_DOM) return;\n  return new Promise(function (resolve, reject) {\n    var operations = toArray(root.querySelectorAll('*')).filter(processable).map(replace);\n    var end = perf.begin('searchPseudoElements');\n    disableObservation();\n    Promise.all(operations).then(function () {\n      end();\n      enableObservation();\n      resolve();\n    }).catch(function () {\n      end();\n      enableObservation();\n      reject();\n    });\n  });\n}\n\nvar PseudoElements = {\n  hooks: function hooks() {\n    return {\n      mutationObserverCallbacks: function mutationObserverCallbacks(accumulator) {\n        accumulator.pseudoElementsCallback = searchPseudoElements;\n        return accumulator;\n      }\n    };\n  },\n  provides: function provides(providers$$1) {\n    providers$$1.pseudoElements2svg = function (params) {\n      var _params$node = params.node,\n          node = _params$node === void 0 ? DOCUMENT : _params$node;\n\n      if (config.searchPseudoElements) {\n        searchPseudoElements(node);\n      }\n    };\n  }\n};\n\nvar _unwatched = false;\nvar MutationObserver$1 = {\n  mixout: function mixout() {\n    return {\n      dom: {\n        unwatch: function unwatch() {\n          disableObservation();\n          _unwatched = true;\n        }\n      }\n    };\n  },\n  hooks: function hooks() {\n    return {\n      bootstrap: function bootstrap() {\n        observe(chainHooks('mutationObserverCallbacks', {}));\n      },\n      noAuto: function noAuto() {\n        disconnect();\n      },\n      watch: function watch(params) {\n        var observeMutationsRoot = params.observeMutationsRoot;\n\n        if (_unwatched) {\n          enableObservation();\n        } else {\n          observe(chainHooks('mutationObserverCallbacks', {\n            observeMutationsRoot: observeMutationsRoot\n          }));\n        }\n      }\n    };\n  }\n};\n\nvar parseTransformString = function parseTransformString(transformString) {\n  var transform = {\n    size: 16,\n    x: 0,\n    y: 0,\n    flipX: false,\n    flipY: false,\n    rotate: 0\n  };\n  return transformString.toLowerCase().split(' ').reduce(function (acc, n) {\n    var parts = n.toLowerCase().split('-');\n    var first = parts[0];\n    var rest = parts.slice(1).join('-');\n\n    if (first && rest === 'h') {\n      acc.flipX = true;\n      return acc;\n    }\n\n    if (first && rest === 'v') {\n      acc.flipY = true;\n      return acc;\n    }\n\n    rest = parseFloat(rest);\n\n    if (isNaN(rest)) {\n      return acc;\n    }\n\n    switch (first) {\n      case 'grow':\n        acc.size = acc.size + rest;\n        break;\n\n      case 'shrink':\n        acc.size = acc.size - rest;\n        break;\n\n      case 'left':\n        acc.x = acc.x - rest;\n        break;\n\n      case 'right':\n        acc.x = acc.x + rest;\n        break;\n\n      case 'up':\n        acc.y = acc.y - rest;\n        break;\n\n      case 'down':\n        acc.y = acc.y + rest;\n        break;\n\n      case 'rotate':\n        acc.rotate = acc.rotate + rest;\n        break;\n    }\n\n    return acc;\n  }, transform);\n};\nvar PowerTransforms = {\n  mixout: function mixout() {\n    return {\n      parse: {\n        transform: function transform(transformString) {\n          return parseTransformString(transformString);\n        }\n      }\n    };\n  },\n  hooks: function hooks() {\n    return {\n      parseNodeAttributes: function parseNodeAttributes(accumulator, node) {\n        var transformString = node.getAttribute('data-fa-transform');\n\n        if (transformString) {\n          accumulator.transform = parseTransformString(transformString);\n        }\n\n        return accumulator;\n      }\n    };\n  },\n  provides: function provides(providers) {\n    providers.generateAbstractTransformGrouping = function (_ref) {\n      var main = _ref.main,\n          transform = _ref.transform,\n          containerWidth = _ref.containerWidth,\n          iconWidth = _ref.iconWidth;\n      var outer = {\n        transform: \"translate(\".concat(containerWidth / 2, \" 256)\")\n      };\n      var innerTranslate = \"translate(\".concat(transform.x * 32, \", \").concat(transform.y * 32, \") \");\n      var innerScale = \"scale(\".concat(transform.size / 16 * (transform.flipX ? -1 : 1), \", \").concat(transform.size / 16 * (transform.flipY ? -1 : 1), \") \");\n      var innerRotate = \"rotate(\".concat(transform.rotate, \" 0 0)\");\n      var inner = {\n        transform: \"\".concat(innerTranslate, \" \").concat(innerScale, \" \").concat(innerRotate)\n      };\n      var path = {\n        transform: \"translate(\".concat(iconWidth / 2 * -1, \" -256)\")\n      };\n      var operations = {\n        outer: outer,\n        inner: inner,\n        path: path\n      };\n      return {\n        tag: 'g',\n        attributes: _objectSpread2({}, operations.outer),\n        children: [{\n          tag: 'g',\n          attributes: _objectSpread2({}, operations.inner),\n          children: [{\n            tag: main.icon.tag,\n            children: main.icon.children,\n            attributes: _objectSpread2(_objectSpread2({}, main.icon.attributes), operations.path)\n          }]\n        }]\n      };\n    };\n  }\n};\n\nvar ALL_SPACE = {\n  x: 0,\n  y: 0,\n  width: '100%',\n  height: '100%'\n};\n\nfunction fillBlack(_abstract) {\n  var force = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n\n  if (_abstract.attributes && (_abstract.attributes.fill || force)) {\n    _abstract.attributes.fill = 'black';\n  }\n\n  return _abstract;\n}\n\nfunction deGroup(_abstract2) {\n  if (_abstract2.tag === 'g') {\n    return _abstract2.children;\n  } else {\n    return [_abstract2];\n  }\n}\n\nvar Masks = {\n  hooks: function hooks() {\n    return {\n      parseNodeAttributes: function parseNodeAttributes(accumulator, node) {\n        var maskData = node.getAttribute('data-fa-mask');\n        var mask = !maskData ? emptyCanonicalIcon() : getCanonicalIcon(maskData.split(' ').map(function (i) {\n          return i.trim();\n        }));\n\n        if (!mask.prefix) {\n          mask.prefix = getDefaultUsablePrefix();\n        }\n\n        accumulator.mask = mask;\n        accumulator.maskId = node.getAttribute('data-fa-mask-id');\n        return accumulator;\n      }\n    };\n  },\n  provides: function provides(providers) {\n    providers.generateAbstractMask = function (_ref) {\n      var children = _ref.children,\n          attributes = _ref.attributes,\n          main = _ref.main,\n          mask = _ref.mask,\n          explicitMaskId = _ref.maskId,\n          transform = _ref.transform;\n      var mainWidth = main.width,\n          mainPath = main.icon;\n      var maskWidth = mask.width,\n          maskPath = mask.icon;\n      var trans = transformForSvg({\n        transform: transform,\n        containerWidth: maskWidth,\n        iconWidth: mainWidth\n      });\n      var maskRect = {\n        tag: 'rect',\n        attributes: _objectSpread2(_objectSpread2({}, ALL_SPACE), {}, {\n          fill: 'white'\n        })\n      };\n      var maskInnerGroupChildrenMixin = mainPath.children ? {\n        children: mainPath.children.map(fillBlack)\n      } : {};\n      var maskInnerGroup = {\n        tag: 'g',\n        attributes: _objectSpread2({}, trans.inner),\n        children: [fillBlack(_objectSpread2({\n          tag: mainPath.tag,\n          attributes: _objectSpread2(_objectSpread2({}, mainPath.attributes), trans.path)\n        }, maskInnerGroupChildrenMixin))]\n      };\n      var maskOuterGroup = {\n        tag: 'g',\n        attributes: _objectSpread2({}, trans.outer),\n        children: [maskInnerGroup]\n      };\n      var maskId = \"mask-\".concat(explicitMaskId || nextUniqueId());\n      var clipId = \"clip-\".concat(explicitMaskId || nextUniqueId());\n      var maskTag = {\n        tag: 'mask',\n        attributes: _objectSpread2(_objectSpread2({}, ALL_SPACE), {}, {\n          id: maskId,\n          maskUnits: 'userSpaceOnUse',\n          maskContentUnits: 'userSpaceOnUse'\n        }),\n        children: [maskRect, maskOuterGroup]\n      };\n      var defs = {\n        tag: 'defs',\n        children: [{\n          tag: 'clipPath',\n          attributes: {\n            id: clipId\n          },\n          children: deGroup(maskPath)\n        }, maskTag]\n      };\n      children.push(defs, {\n        tag: 'rect',\n        attributes: _objectSpread2({\n          fill: 'currentColor',\n          'clip-path': \"url(#\".concat(clipId, \")\"),\n          mask: \"url(#\".concat(maskId, \")\")\n        }, ALL_SPACE)\n      });\n      return {\n        children: children,\n        attributes: attributes\n      };\n    };\n  }\n};\n\nvar MissingIconIndicator = {\n  provides: function provides(providers) {\n    var reduceMotion = false;\n\n    if (WINDOW.matchMedia) {\n      reduceMotion = WINDOW.matchMedia('(prefers-reduced-motion: reduce)').matches;\n    }\n\n    providers.missingIconAbstract = function () {\n      var gChildren = [];\n      var FILL = {\n        fill: 'currentColor'\n      };\n      var ANIMATION_BASE = {\n        attributeType: 'XML',\n        repeatCount: 'indefinite',\n        dur: '2s'\n      }; // Ring\n\n      gChildren.push({\n        tag: 'path',\n        attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\n          d: 'M156.5,447.7l-12.6,29.5c-18.7-9.5-35.9-21.2-51.5-34.9l22.7-22.7C127.6,430.5,141.5,440,156.5,447.7z M40.6,272H8.5 c1.4,21.2,5.4,41.7,11.7,61.1L50,321.2C45.1,305.5,41.8,289,40.6,272z M40.6,240c1.4-18.8,5.2-37,11.1-54.1l-29.5-12.6 C14.7,194.3,10,216.7,8.5,240H40.6z M64.3,156.5c7.8-14.9,17.2-28.8,28.1-41.5L69.7,92.3c-13.7,15.6-25.5,32.8-34.9,51.5 L64.3,156.5z M397,419.6c-13.9,12-29.4,22.3-46.1,30.4l11.9,29.8c20.7-9.9,39.8-22.6,56.9-37.6L397,419.6z M115,92.4 c13.9-12,29.4-22.3,46.1-30.4l-11.9-29.8c-20.7,9.9-39.8,22.6-56.8,37.6L115,92.4z M447.7,355.5c-7.8,14.9-17.2,28.8-28.1,41.5 l22.7,22.7c13.7-15.6,25.5-32.9,34.9-51.5L447.7,355.5z M471.4,272c-1.4,18.8-5.2,37-11.1,54.1l29.5,12.6 c7.5-21.1,12.2-43.5,13.6-66.8H471.4z M321.2,462c-15.7,5-32.2,8.2-49.2,9.4v32.1c21.2-1.4,41.7-5.4,61.1-11.7L321.2,462z M240,471.4c-18.8-1.4-37-5.2-54.1-11.1l-12.6,29.5c21.1,7.5,43.5,12.2,66.8,13.6V471.4z M462,190.8c5,15.7,8.2,32.2,9.4,49.2h32.1 c-1.4-21.2-5.4-41.7-11.7-61.1L462,190.8z M92.4,397c-12-13.9-22.3-29.4-30.4-46.1l-29.8,11.9c9.9,20.7,22.6,39.8,37.6,56.9 L92.4,397z M272,40.6c18.8,1.4,36.9,5.2,54.1,11.1l12.6-29.5C317.7,14.7,295.3,10,272,8.5V40.6z M190.8,50 c15.7-5,32.2-8.2,49.2-9.4V8.5c-21.2,1.4-41.7,5.4-61.1,11.7L190.8,50z M442.3,92.3L419.6,115c12,13.9,22.3,29.4,30.5,46.1 l29.8-11.9C470,128.5,457.3,109.4,442.3,92.3z M397,92.4l22.7-22.7c-15.6-13.7-32.8-25.5-51.5-34.9l-12.6,29.5 C370.4,72.1,384.4,81.5,397,92.4z'\n        })\n      });\n\n      var OPACITY_ANIMATE = _objectSpread2(_objectSpread2({}, ANIMATION_BASE), {}, {\n        attributeName: 'opacity'\n      });\n\n      var dot = {\n        tag: 'circle',\n        attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\n          cx: '256',\n          cy: '364',\n          r: '28'\n        }),\n        children: []\n      };\n\n      if (!reduceMotion) {\n        dot.children.push({\n          tag: 'animate',\n          attributes: _objectSpread2(_objectSpread2({}, ANIMATION_BASE), {}, {\n            attributeName: 'r',\n            values: '28;14;28;28;14;28;'\n          })\n        }, {\n          tag: 'animate',\n          attributes: _objectSpread2(_objectSpread2({}, OPACITY_ANIMATE), {}, {\n            values: '1;0;1;1;0;1;'\n          })\n        });\n      }\n\n      gChildren.push(dot);\n      gChildren.push({\n        tag: 'path',\n        attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\n          opacity: '1',\n          d: 'M263.7,312h-16c-6.6,0-12-5.4-12-12c0-71,77.4-63.9,77.4-107.8c0-20-17.8-40.2-57.4-40.2c-29.1,0-44.3,9.6-59.2,28.7 c-3.9,5-11.1,6-16.2,2.4l-13.1-9.2c-5.6-3.9-6.9-11.8-2.6-17.2c21.2-27.2,46.4-44.7,91.2-44.7c52.3,0,97.4,29.8,97.4,80.2 c0,67.6-77.4,63.5-77.4,107.8C275.7,306.6,270.3,312,263.7,312z'\n        }),\n        children: reduceMotion ? [] : [{\n          tag: 'animate',\n          attributes: _objectSpread2(_objectSpread2({}, OPACITY_ANIMATE), {}, {\n            values: '1;0;0;0;0;1;'\n          })\n        }]\n      });\n\n      if (!reduceMotion) {\n        // Exclamation\n        gChildren.push({\n          tag: 'path',\n          attributes: _objectSpread2(_objectSpread2({}, FILL), {}, {\n            opacity: '0',\n            d: 'M232.5,134.5l7,168c0.3,6.4,5.6,11.5,12,11.5h9c6.4,0,11.7-5.1,12-11.5l7-168c0.3-6.8-5.2-12.5-12-12.5h-23 C237.7,122,232.2,127.7,232.5,134.5z'\n          }),\n          children: [{\n            tag: 'animate',\n            attributes: _objectSpread2(_objectSpread2({}, OPACITY_ANIMATE), {}, {\n              values: '0;0;1;1;0;0;'\n            })\n          }]\n        });\n      }\n\n      return {\n        tag: 'g',\n        attributes: {\n          'class': 'missing'\n        },\n        children: gChildren\n      };\n    };\n  }\n};\n\nvar SvgSymbols = {\n  hooks: function hooks() {\n    return {\n      parseNodeAttributes: function parseNodeAttributes(accumulator, node) {\n        var symbolData = node.getAttribute('data-fa-symbol');\n        var symbol = symbolData === null ? false : symbolData === '' ? true : symbolData;\n        accumulator['symbol'] = symbol;\n        return accumulator;\n      }\n    };\n  }\n};\n\nvar plugins = [InjectCSS, ReplaceElements, Layers, LayersCounter, LayersText, PseudoElements, MutationObserver$1, PowerTransforms, Masks, MissingIconIndicator, SvgSymbols];\n\nregisterPlugins(plugins, {\n  mixoutsTo: api\n});\nvar noAuto$1 = api.noAuto;\nvar config$1 = api.config;\nvar library$1 = api.library;\nvar dom$1 = api.dom;\nvar parse$1 = api.parse;\nvar findIconDefinition$1 = api.findIconDefinition;\nvar toHtml$1 = api.toHtml;\nvar icon = api.icon;\nvar layer = api.layer;\nvar text = api.text;\nvar counter = api.counter;\n\nexport { noAuto$1 as noAuto, config$1 as config, library$1 as library, dom$1 as dom, parse$1 as parse, findIconDefinition$1 as findIconDefinition, toHtml$1 as toHtml, icon, layer, text, counter, api };\n"], "mappings": "AAAA,SAASA,OAAO,CAACC,MAAM,EAAEC,cAAc,EAAE;EACvC,IAAIC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACF,MAAM,CAAC;EAE9B,IAAIG,MAAM,CAACC,qBAAqB,EAAE;IAChC,IAAIC,OAAO,GAAGF,MAAM,CAACC,qBAAqB,CAACJ,MAAM,CAAC;IAClDC,cAAc,KAAKI,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MACzD,OAAOJ,MAAM,CAACK,wBAAwB,CAACR,MAAM,EAAEO,GAAG,CAAC,CAACE,UAAU;IAChE,CAAC,CAAC,CAAC,EAAEP,IAAI,CAACQ,IAAI,CAACC,KAAK,CAACT,IAAI,EAAEG,OAAO,CAAC;EACrC;EAEA,OAAOH,IAAI;AACb;AAEA,SAASU,cAAc,CAACC,MAAM,EAAE;EAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IACzC,IAAIG,MAAM,GAAG,IAAI,IAAIF,SAAS,CAACD,CAAC,CAAC,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IACrDA,CAAC,GAAG,CAAC,GAAGf,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MACzDC,eAAe,CAACP,MAAM,EAAEM,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;IAC3C,CAAC,CAAC,GAAGhB,MAAM,CAACkB,yBAAyB,GAAGlB,MAAM,CAACmB,gBAAgB,CAACT,MAAM,EAAEV,MAAM,CAACkB,yBAAyB,CAACJ,MAAM,CAAC,CAAC,GAAGlB,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MACjKhB,MAAM,CAACoB,cAAc,CAACV,MAAM,EAAEM,GAAG,EAAEhB,MAAM,CAACK,wBAAwB,CAACS,MAAM,EAAEE,GAAG,CAAC,CAAC;IAClF,CAAC,CAAC;EACJ;EAEA,OAAON,MAAM;AACf;AAEA,SAASW,OAAO,CAACC,GAAG,EAAE;EACpB,yBAAyB;;EAEzB,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,GAAG,EAAE;IAClG,OAAO,OAAOA,GAAG;EACnB,CAAC,GAAG,UAAUA,GAAG,EAAE;IACjB,OAAOA,GAAG,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;EAC7H,CAAC,EAAED,OAAO,CAACC,GAAG,CAAC;AACjB;AAEA,SAASK,WAAW,GAAG;EACrBA,WAAW,GAAG,UAAUC,EAAE,EAAEC,MAAM,EAAE;IAClC,OAAO,IAAIC,WAAW,CAACF,EAAE,EAAE,KAAK,CAAC,EAAEC,MAAM,CAAC;EAC5C,CAAC;EAED,IAAIE,MAAM,GAAGC,MAAM,CAACN,SAAS;IACzBO,OAAO,GAAG,IAAIC,OAAO,EAAE;EAE3B,SAASJ,WAAW,CAACF,EAAE,EAAEO,KAAK,EAAEN,MAAM,EAAE;IACtC,IAAIO,KAAK,GAAG,IAAIJ,MAAM,CAACJ,EAAE,EAAEO,KAAK,CAAC;IAEjC,OAAOF,OAAO,CAACI,GAAG,CAACD,KAAK,EAAEP,MAAM,IAAII,OAAO,CAACK,GAAG,CAACV,EAAE,CAAC,CAAC,EAAEW,eAAe,CAACH,KAAK,EAAEN,WAAW,CAACJ,SAAS,CAAC;EACrG;EAEA,SAASc,WAAW,CAACC,MAAM,EAAEb,EAAE,EAAE;IAC/B,IAAIc,CAAC,GAAGT,OAAO,CAACK,GAAG,CAACV,EAAE,CAAC;IAEvB,OAAO5B,MAAM,CAACD,IAAI,CAAC2C,CAAC,CAAC,CAACC,MAAM,CAAC,UAAUd,MAAM,EAAEe,IAAI,EAAE;MACnD,OAAOf,MAAM,CAACe,IAAI,CAAC,GAAGH,MAAM,CAACC,CAAC,CAACE,IAAI,CAAC,CAAC,EAAEf,MAAM;IAC/C,CAAC,EAAE7B,MAAM,CAAC6C,MAAM,CAAC,IAAI,CAAC,CAAC;EACzB;EAEA,OAAOC,SAAS,CAAChB,WAAW,EAAEE,MAAM,CAAC,EAAEF,WAAW,CAACJ,SAAS,CAACqB,IAAI,GAAG,UAAUC,GAAG,EAAE;IACjF,IAAIP,MAAM,GAAGV,MAAM,CAACgB,IAAI,CAACE,IAAI,CAAC,IAAI,EAAED,GAAG,CAAC;IAExC,OAAOP,MAAM,KAAKA,MAAM,CAACZ,MAAM,GAAGW,WAAW,CAACC,MAAM,EAAE,IAAI,CAAC,CAAC,EAAEA,MAAM;EACtE,CAAC,EAAEX,WAAW,CAACJ,SAAS,CAACH,MAAM,CAAC2B,OAAO,CAAC,GAAG,UAAUF,GAAG,EAAEG,YAAY,EAAE;IACtE,IAAI,QAAQ,IAAI,OAAOA,YAAY,EAAE;MACnC,IAAItB,MAAM,GAAGI,OAAO,CAACK,GAAG,CAAC,IAAI,CAAC;MAE9B,OAAOP,MAAM,CAACR,MAAM,CAAC2B,OAAO,CAAC,CAACD,IAAI,CAAC,IAAI,EAAED,GAAG,EAAEG,YAAY,CAACD,OAAO,CAAC,cAAc,EAAE,UAAUE,CAAC,EAAER,IAAI,EAAE;QACpG,OAAO,GAAG,GAAGf,MAAM,CAACe,IAAI,CAAC;MAC3B,CAAC,CAAC,CAAC;IACL;IAEA,IAAI,UAAU,IAAI,OAAOO,YAAY,EAAE;MACrC,IAAIf,KAAK,GAAG,IAAI;MAEhB,OAAOL,MAAM,CAACR,MAAM,CAAC2B,OAAO,CAAC,CAACD,IAAI,CAAC,IAAI,EAAED,GAAG,EAAE,YAAY;QACxD,IAAIK,IAAI,GAAGzC,SAAS;QACpB,OAAO,QAAQ,IAAI,OAAOyC,IAAI,CAACA,IAAI,CAACxC,MAAM,GAAG,CAAC,CAAC,IAAI,CAACwC,IAAI,GAAG,EAAE,CAACC,KAAK,CAACL,IAAI,CAACI,IAAI,CAAC,EAAE9C,IAAI,CAACiC,WAAW,CAACa,IAAI,EAAEjB,KAAK,CAAC,CAAC,EAAEe,YAAY,CAAC3C,KAAK,CAAC,IAAI,EAAE6C,IAAI,CAAC;MAChJ,CAAC,CAAC;IACJ;IAEA,OAAOtB,MAAM,CAACR,MAAM,CAAC2B,OAAO,CAAC,CAACD,IAAI,CAAC,IAAI,EAAED,GAAG,EAAEG,YAAY,CAAC;EAC7D,CAAC,EAAExB,WAAW,CAACnB,KAAK,CAAC,IAAI,EAAEI,SAAS,CAAC;AACvC;AAEA,SAAS2C,eAAe,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAC9C,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IACtC,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAC1D;AACF;AAEA,SAASC,iBAAiB,CAACjD,MAAM,EAAEkD,KAAK,EAAE;EACxC,KAAK,IAAIjD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiD,KAAK,CAAC/C,MAAM,EAAEF,CAAC,EAAE,EAAE;IACrC,IAAIkD,UAAU,GAAGD,KAAK,CAACjD,CAAC,CAAC;IACzBkD,UAAU,CAACvD,UAAU,GAAGuD,UAAU,CAACvD,UAAU,IAAI,KAAK;IACtDuD,UAAU,CAACC,YAAY,GAAG,IAAI;IAC9B,IAAI,OAAO,IAAID,UAAU,EAAEA,UAAU,CAACE,QAAQ,GAAG,IAAI;IACrD/D,MAAM,CAACoB,cAAc,CAACV,MAAM,EAAEmD,UAAU,CAAC7C,GAAG,EAAE6C,UAAU,CAAC;EAC3D;AACF;AAEA,SAASG,YAAY,CAACP,WAAW,EAAEQ,UAAU,EAAEC,WAAW,EAAE;EAC1D,IAAID,UAAU,EAAEN,iBAAiB,CAACF,WAAW,CAAC/B,SAAS,EAAEuC,UAAU,CAAC;EACpE,IAAIC,WAAW,EAAEP,iBAAiB,CAACF,WAAW,EAAES,WAAW,CAAC;EAC5DlE,MAAM,CAACoB,cAAc,CAACqC,WAAW,EAAE,WAAW,EAAE;IAC9CM,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,OAAON,WAAW;AACpB;AAEA,SAASxC,eAAe,CAACK,GAAG,EAAEN,GAAG,EAAEmD,KAAK,EAAE;EACxC,IAAInD,GAAG,IAAIM,GAAG,EAAE;IACdtB,MAAM,CAACoB,cAAc,CAACE,GAAG,EAAEN,GAAG,EAAE;MAC9BmD,KAAK,EAAEA,KAAK;MACZ7D,UAAU,EAAE,IAAI;MAChBwD,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,MAAM;IACLzC,GAAG,CAACN,GAAG,CAAC,GAAGmD,KAAK;EAClB;EAEA,OAAO7C,GAAG;AACZ;AAEA,SAASwB,SAAS,CAACsB,QAAQ,EAAEC,UAAU,EAAE;EACvC,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAC3D,MAAM,IAAIX,SAAS,CAAC,oDAAoD,CAAC;EAC3E;EAEAU,QAAQ,CAAC1C,SAAS,GAAG1B,MAAM,CAAC6C,MAAM,CAACwB,UAAU,IAAIA,UAAU,CAAC3C,SAAS,EAAE;IACrED,WAAW,EAAE;MACX0C,KAAK,EAAEC,QAAQ;MACfL,QAAQ,EAAE,IAAI;MACdD,YAAY,EAAE;IAChB;EACF,CAAC,CAAC;EACF9D,MAAM,CAACoB,cAAc,CAACgD,QAAQ,EAAE,WAAW,EAAE;IAC3CL,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,IAAIM,UAAU,EAAE9B,eAAe,CAAC6B,QAAQ,EAAEC,UAAU,CAAC;AACvD;AAEA,SAAS9B,eAAe,CAAC+B,CAAC,EAAEC,CAAC,EAAE;EAC7BhC,eAAe,GAAGvC,MAAM,CAACwE,cAAc,IAAI,SAASjC,eAAe,CAAC+B,CAAC,EAAEC,CAAC,EAAE;IACxED,CAAC,CAACG,SAAS,GAAGF,CAAC;IACf,OAAOD,CAAC;EACV,CAAC;EAED,OAAO/B,eAAe,CAAC+B,CAAC,EAAEC,CAAC,CAAC;AAC9B;AAEA,SAASG,cAAc,CAACC,GAAG,EAAEhE,CAAC,EAAE;EAC9B,OAAOiE,eAAe,CAACD,GAAG,CAAC,IAAIE,qBAAqB,CAACF,GAAG,EAAEhE,CAAC,CAAC,IAAImE,2BAA2B,CAACH,GAAG,EAAEhE,CAAC,CAAC,IAAIoE,gBAAgB,EAAE;AAC3H;AAEA,SAASC,kBAAkB,CAACL,GAAG,EAAE;EAC/B,OAAOM,kBAAkB,CAACN,GAAG,CAAC,IAAIO,gBAAgB,CAACP,GAAG,CAAC,IAAIG,2BAA2B,CAACH,GAAG,CAAC,IAAIQ,kBAAkB,EAAE;AACrH;AAEA,SAASF,kBAAkB,CAACN,GAAG,EAAE;EAC/B,IAAIS,KAAK,CAACC,OAAO,CAACV,GAAG,CAAC,EAAE,OAAOW,iBAAiB,CAACX,GAAG,CAAC;AACvD;AAEA,SAASC,eAAe,CAACD,GAAG,EAAE;EAC5B,IAAIS,KAAK,CAACC,OAAO,CAACV,GAAG,CAAC,EAAE,OAAOA,GAAG;AACpC;AAEA,SAASO,gBAAgB,CAACK,IAAI,EAAE;EAC9B,IAAI,OAAOhE,MAAM,KAAK,WAAW,IAAIgE,IAAI,CAAChE,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,IAAI+D,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,OAAOH,KAAK,CAACI,IAAI,CAACD,IAAI,CAAC;AAC3H;AAEA,SAASV,qBAAqB,CAACF,GAAG,EAAEhE,CAAC,EAAE;EACrC,IAAI8E,EAAE,GAAGd,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,OAAOpD,MAAM,KAAK,WAAW,IAAIoD,GAAG,CAACpD,MAAM,CAACC,QAAQ,CAAC,IAAImD,GAAG,CAAC,YAAY,CAAC;EAExG,IAAIc,EAAE,IAAI,IAAI,EAAE;EAChB,IAAIC,IAAI,GAAG,EAAE;EACb,IAAIC,EAAE,GAAG,IAAI;EACb,IAAIC,EAAE,GAAG,KAAK;EAEd,IAAIC,EAAE,EAAEC,EAAE;EAEV,IAAI;IACF,KAAKL,EAAE,GAAGA,EAAE,CAACxC,IAAI,CAAC0B,GAAG,CAAC,EAAE,EAAEgB,EAAE,GAAG,CAACE,EAAE,GAAGJ,EAAE,CAACM,IAAI,EAAE,EAAEC,IAAI,CAAC,EAAEL,EAAE,GAAG,IAAI,EAAE;MAChED,IAAI,CAACnF,IAAI,CAACsF,EAAE,CAAC1B,KAAK,CAAC;MAEnB,IAAIxD,CAAC,IAAI+E,IAAI,CAAC7E,MAAM,KAAKF,CAAC,EAAE;IAC9B;EACF,CAAC,CAAC,OAAOsF,GAAG,EAAE;IACZL,EAAE,GAAG,IAAI;IACTE,EAAE,GAAGG,GAAG;EACV,CAAC,SAAS;IACR,IAAI;MACF,IAAI,CAACN,EAAE,IAAIF,EAAE,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAEA,EAAE,CAAC,QAAQ,CAAC,EAAE;IACjD,CAAC,SAAS;MACR,IAAIG,EAAE,EAAE,MAAME,EAAE;IAClB;EACF;EAEA,OAAOJ,IAAI;AACb;AAEA,SAASZ,2BAA2B,CAACR,CAAC,EAAE4B,MAAM,EAAE;EAC9C,IAAI,CAAC5B,CAAC,EAAE;EACR,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOgB,iBAAiB,CAAChB,CAAC,EAAE4B,MAAM,CAAC;EAC9D,IAAIC,CAAC,GAAGnG,MAAM,CAAC0B,SAAS,CAAC0E,QAAQ,CAACnD,IAAI,CAACqB,CAAC,CAAC,CAAChB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACtD,IAAI6C,CAAC,KAAK,QAAQ,IAAI7B,CAAC,CAAC7C,WAAW,EAAE0E,CAAC,GAAG7B,CAAC,CAAC7C,WAAW,CAACmB,IAAI;EAC3D,IAAIuD,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOf,KAAK,CAACI,IAAI,CAAClB,CAAC,CAAC;EACpD,IAAI6B,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACE,IAAI,CAACF,CAAC,CAAC,EAAE,OAAOb,iBAAiB,CAAChB,CAAC,EAAE4B,MAAM,CAAC;AAClH;AAEA,SAASZ,iBAAiB,CAACX,GAAG,EAAE2B,GAAG,EAAE;EACnC,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAG3B,GAAG,CAAC9D,MAAM,EAAEyF,GAAG,GAAG3B,GAAG,CAAC9D,MAAM;EAErD,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAE4F,IAAI,GAAG,IAAInB,KAAK,CAACkB,GAAG,CAAC,EAAE3F,CAAC,GAAG2F,GAAG,EAAE3F,CAAC,EAAE,EAAE4F,IAAI,CAAC5F,CAAC,CAAC,GAAGgE,GAAG,CAAChE,CAAC,CAAC;EAErE,OAAO4F,IAAI;AACb;AAEA,SAASpB,kBAAkB,GAAG;EAC5B,MAAM,IAAIzB,SAAS,CAAC,sIAAsI,CAAC;AAC7J;AAEA,SAASqB,gBAAgB,GAAG;EAC1B,MAAM,IAAIrB,SAAS,CAAC,2IAA2I,CAAC;AAClK;AAEA,IAAI8C,IAAI,GAAG,SAASA,IAAI,GAAG,CAAC,CAAC;AAE7B,IAAIC,OAAO,GAAG,CAAC,CAAC;AAChB,IAAIC,SAAS,GAAG,CAAC,CAAC;AAClB,IAAIC,kBAAkB,GAAG,IAAI;AAC7B,IAAIC,YAAY,GAAG;EACjBC,IAAI,EAAEL,IAAI;EACVM,OAAO,EAAEN;AACX,CAAC;AAED,IAAI;EACF,IAAI,OAAOO,MAAM,KAAK,WAAW,EAAEN,OAAO,GAAGM,MAAM;EACnD,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAEN,SAAS,GAAGM,QAAQ;EACzD,IAAI,OAAOC,gBAAgB,KAAK,WAAW,EAAEN,kBAAkB,GAAGM,gBAAgB;EAClF,IAAI,OAAOC,WAAW,KAAK,WAAW,EAAEN,YAAY,GAAGM,WAAW;AACpE,CAAC,CAAC,OAAOC,CAAC,EAAE,CAAC;AAEb,IAAIC,IAAI,GAAGX,OAAO,CAACY,SAAS,IAAI,CAAC,CAAC;EAC9BC,cAAc,GAAGF,IAAI,CAACG,SAAS;EAC/BA,SAAS,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,cAAc;AAC/D,IAAIE,MAAM,GAAGf,OAAO;AACpB,IAAIgB,QAAQ,GAAGf,SAAS;AACxB,IAAIgB,iBAAiB,GAAGf,kBAAkB;AAC1C,IAAIgB,WAAW,GAAGf,YAAY;AAC9B,IAAIgB,UAAU,GAAG,CAAC,CAACJ,MAAM,CAACR,QAAQ;AAClC,IAAIa,MAAM,GAAG,CAAC,CAACJ,QAAQ,CAACK,eAAe,IAAI,CAAC,CAACL,QAAQ,CAACM,IAAI,IAAI,OAAON,QAAQ,CAACO,gBAAgB,KAAK,UAAU,IAAI,OAAOP,QAAQ,CAACQ,aAAa,KAAK,UAAU;AAC7J,IAAIC,KAAK,GAAG,CAACX,SAAS,CAACY,OAAO,CAAC,MAAM,CAAC,IAAI,CAACZ,SAAS,CAACY,OAAO,CAAC,UAAU,CAAC;AAExE,IAAIC,YAAY,EAAEC,aAAa,EAAEC,aAAa,EAAEC,aAAa,EAAEC,aAAa;AAE5E,IAAIC,oBAAoB,GAAG,oBAAoB;AAC/C,IAAIC,aAAa,GAAG,EAAE;AACtB,IAAIC,kBAAkB,GAAG,IAAI;AAC7B,IAAIC,yBAAyB,GAAG,gBAAgB;AAChD,IAAIC,aAAa,GAAG,eAAe;AACnC,IAAIC,sBAAsB,GAAG,wBAAwB;AACrD,IAAIC,8BAA8B,GAAG,gCAAgC;AACrE,IAAIC,WAAW,GAAG,aAAa;AAC/B,IAAIC,SAAS,GAAG,WAAW;AAC3B,IAAIC,2BAA2B,GAAG,mBAAmB;AACrD,IAAIC,uBAAuB,GAAG,OAAO;AACrC,IAAIC,mCAAmC,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC;AAC7E,IAAIC,UAAU,GAAG,YAAY;EAC3B,IAAI;IACF,OAAOC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY;EAC9C,CAAC,CAAC,OAAOrC,CAAC,EAAE;IACV,OAAO,KAAK;EACd;AACF,CAAC,EAAE;AACH,IAAIsC,cAAc,GAAG,SAAS;AAC9B,IAAIC,YAAY,GAAG,OAAO;AAC1B,IAAIC,QAAQ,GAAG,CAACF,cAAc,EAAEC,YAAY,CAAC;AAE7C,SAASE,WAAW,CAACtI,GAAG,EAAE;EACxB;EACA,OAAO,IAAIuI,KAAK,CAACvI,GAAG,EAAE;IACpBgB,GAAG,EAAE,SAASA,GAAG,CAAC5B,MAAM,EAAEoJ,IAAI,EAAE;MAC9B,OAAOA,IAAI,IAAIpJ,MAAM,GAAGA,MAAM,CAACoJ,IAAI,CAAC,GAAGpJ,MAAM,CAAC+I,cAAc,CAAC;IAC/D;EACF,CAAC,CAAC;AACJ;AACA,IAAIM,eAAe,GAAGH,WAAW,EAAExB,YAAY,GAAG,CAAC,CAAC,EAAEnH,eAAe,CAACmH,YAAY,EAAEqB,cAAc,EAAE;EAClG,IAAI,EAAE,OAAO;EACb,KAAK,EAAE,OAAO;EACd,UAAU,EAAE,OAAO;EACnB,KAAK,EAAE,SAAS;EAChB,YAAY,EAAE,SAAS;EACvB,KAAK,EAAE,OAAO;EACd,UAAU,EAAE,OAAO;EACnB,KAAK,EAAE,MAAM;EACb,SAAS,EAAE,MAAM;EACjB,KAAK,EAAE,SAAS;EAChB,YAAY,EAAE,SAAS;EACvB,KAAK,EAAE,QAAQ;EACf,WAAW,EAAE,QAAQ;EACrB,KAAK,EAAE,KAAK;EACZ,MAAM,EAAE,KAAK;EACb,QAAQ,EAAE,KAAK;EACf,gBAAgB,EAAE;AACpB,CAAC,CAAC,EAAExI,eAAe,CAACmH,YAAY,EAAEsB,YAAY,EAAE;EAC9C,IAAI,EAAE,OAAO;EACb,MAAM,EAAE,OAAO;EACf,UAAU,EAAE,OAAO;EACnB,MAAM,EAAE,SAAS;EACjB,YAAY,EAAE,SAAS;EACvB,MAAM,EAAE,OAAO;EACf,UAAU,EAAE,OAAO;EACnB,MAAM,EAAE,MAAM;EACd,SAAS,EAAE;AACb,CAAC,CAAC,EAAEtB,YAAY,EAAE;AAClB,IAAI4B,eAAe,GAAGJ,WAAW,EAAEvB,aAAa,GAAG,CAAC,CAAC,EAAEpH,eAAe,CAACoH,aAAa,EAAEoB,cAAc,EAAE;EACpGQ,KAAK,EAAE,KAAK;EACZC,OAAO,EAAE,KAAK;EACdC,KAAK,EAAE,KAAK;EACZC,IAAI,EAAE,KAAK;EACXC,OAAO,EAAE,KAAK;EACdC,MAAM,EAAE,KAAK;EACbC,GAAG,EAAE;AACP,CAAC,CAAC,EAAEtJ,eAAe,CAACoH,aAAa,EAAEqB,YAAY,EAAE;EAC/CO,KAAK,EAAE,MAAM;EACbC,OAAO,EAAE,MAAM;EACfC,KAAK,EAAE,MAAM;EACbC,IAAI,EAAE;AACR,CAAC,CAAC,EAAE/B,aAAa,EAAE;AACnB,IAAImC,oBAAoB,GAAGZ,WAAW,EAAEtB,aAAa,GAAG,CAAC,CAAC,EAAErH,eAAe,CAACqH,aAAa,EAAEmB,cAAc,EAAE;EACzGgB,GAAG,EAAE,WAAW;EAChBC,GAAG,EAAE,YAAY;EACjBC,GAAG,EAAE,QAAQ;EACbC,GAAG,EAAE,UAAU;EACfC,GAAG,EAAE,YAAY;EACjBC,GAAG,EAAE,UAAU;EACfC,GAAG,EAAE;AACP,CAAC,CAAC,EAAE9J,eAAe,CAACqH,aAAa,EAAEoB,YAAY,EAAE;EAC/CsB,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE;AACR,CAAC,CAAC,EAAE7C,aAAa,EAAE;AACnB,IAAI8C,oBAAoB,GAAGxB,WAAW,EAAErB,aAAa,GAAG,CAAC,CAAC,EAAEtH,eAAe,CAACsH,aAAa,EAAEkB,cAAc,EAAE;EACzG,WAAW,EAAE,KAAK;EAClB,YAAY,EAAE,KAAK;EACnB,QAAQ,EAAE,KAAK;EACf,UAAU,EAAE,KAAK;EACjB,YAAY,EAAE,KAAK;EACnB,UAAU,EAAE,KAAK;EACjB,SAAS,EAAE;AACb,CAAC,CAAC,EAAExI,eAAe,CAACsH,aAAa,EAAEmB,YAAY,EAAE;EAC/C,UAAU,EAAE,MAAM;EAClB,YAAY,EAAE,MAAM;EACpB,UAAU,EAAE,MAAM;EAClB,SAAS,EAAE;AACb,CAAC,CAAC,EAAEnB,aAAa,EAAE;AACnB,IAAI8C,6BAA6B,GAAG,sCAAsC,CAAC,CAAC;;AAE5E,IAAIC,qBAAqB,GAAG,gBAAgB;AAC5C,IAAIC,mBAAmB,GAAG,yFAAyF;AACnH,IAAIC,qBAAqB,GAAG5B,WAAW,EAAEpB,aAAa,GAAG,CAAC,CAAC,EAAEvH,eAAe,CAACuH,aAAa,EAAEiB,cAAc,EAAE;EAC1G,GAAG,EAAE,KAAK;EACV,GAAG,EAAE,KAAK;EACVgC,MAAM,EAAE,KAAK;EACb,GAAG,EAAE,KAAK;EACV,GAAG,EAAE;AACP,CAAC,CAAC,EAAExK,eAAe,CAACuH,aAAa,EAAEkB,YAAY,EAAE;EAC/C,GAAG,EAAE,MAAM;EACX,GAAG,EAAE,MAAM;EACX,GAAG,EAAE,MAAM;EACX,GAAG,EAAE;AACP,CAAC,CAAC,EAAElB,aAAa,EAAE;AACnB,IAAIkD,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;AAC9C,IAAIC,WAAW,GAAGD,QAAQ,CAACE,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AAC3E,IAAIC,+BAA+B,GAAG,CAAC,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,mBAAmB,EAAE,cAAc,CAAC;AAChH,IAAIC,eAAe,GAAG;EACpBC,KAAK,EAAE,eAAe;EACtBC,YAAY,EAAE,cAAc;EAC5BC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE;AACb,CAAC;AACD,IAAIC,QAAQ,GAAG,IAAIC,GAAG,EAAE;AACxBpM,MAAM,CAACD,IAAI,CAACiK,eAAe,CAACP,cAAc,CAAC,CAAC,CAAC4C,GAAG,CAACF,QAAQ,CAACG,GAAG,CAACC,IAAI,CAACJ,QAAQ,CAAC,CAAC;AAC7EnM,MAAM,CAACD,IAAI,CAACiK,eAAe,CAACN,YAAY,CAAC,CAAC,CAAC2C,GAAG,CAACF,QAAQ,CAACG,GAAG,CAACC,IAAI,CAACJ,QAAQ,CAAC,CAAC;AAC3E,IAAIK,gBAAgB,GAAG,EAAE,CAACZ,MAAM,CAACjC,QAAQ,EAAE3E,kBAAkB,CAACmH,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,iBAAiB,EAAE,eAAe,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,gBAAgB,EAAE,aAAa,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,YAAY,EAAE,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,YAAY,EAAE,cAAc,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,IAAI,EAAEL,eAAe,CAACC,KAAK,EAAED,eAAe,CAACE,YAAY,EAAEF,eAAe,CAACG,OAAO,EAAEH,eAAe,CAACI,SAAS,CAAC,CAAC,CAACN,MAAM,CAACF,QAAQ,CAACW,GAAG,CAAC,UAAUlG,CAAC,EAAE;EACrlB,OAAO,EAAE,CAACyF,MAAM,CAACzF,CAAC,EAAE,GAAG,CAAC;AAC1B,CAAC,CAAC,CAAC,CAACyF,MAAM,CAACD,WAAW,CAACU,GAAG,CAAC,UAAUlG,CAAC,EAAE;EACtC,OAAO,IAAI,CAACyF,MAAM,CAACzF,CAAC,CAAC;AACvB,CAAC,CAAC,CAAC;AAEH,IAAIsG,OAAO,GAAGjF,MAAM,CAACkF,iBAAiB,IAAI,CAAC,CAAC;AAE5C,SAASC,aAAa,CAACC,IAAI,EAAE;EAC3B,IAAIC,OAAO,GAAGpF,QAAQ,CAACqF,aAAa,CAAC,SAAS,GAAGF,IAAI,GAAG,GAAG,CAAC;EAE5D,IAAIC,OAAO,EAAE;IACX,OAAOA,OAAO,CAACE,YAAY,CAACH,IAAI,CAAC;EACnC;AACF;AAEA,SAASI,MAAM,CAACC,GAAG,EAAE;EACnB;EACA;EACA,IAAIA,GAAG,KAAK,EAAE,EAAE,OAAO,IAAI;EAC3B,IAAIA,GAAG,KAAK,OAAO,EAAE,OAAO,KAAK;EACjC,IAAIA,GAAG,KAAK,MAAM,EAAE,OAAO,IAAI;EAC/B,OAAOA,GAAG;AACZ;AAEA,IAAIxF,QAAQ,IAAI,OAAOA,QAAQ,CAACqF,aAAa,KAAK,UAAU,EAAE;EAC5D,IAAII,KAAK,GAAG,CAAC,CAAC,oBAAoB,EAAE,cAAc,CAAC,EAAE,CAAC,iBAAiB,EAAE,WAAW,CAAC,EAAE,CAAC,qBAAqB,EAAE,eAAe,CAAC,EAAE,CAAC,oBAAoB,EAAE,cAAc,CAAC,EAAE,CAAC,wBAAwB,EAAE,kBAAkB,CAAC,EAAE,CAAC,uBAAuB,EAAE,gBAAgB,CAAC,EAAE,CAAC,mBAAmB,EAAE,YAAY,CAAC,EAAE,CAAC,gBAAgB,EAAE,UAAU,CAAC,EAAE,CAAC,6BAA6B,EAAE,sBAAsB,CAAC,EAAE,CAAC,wBAAwB,EAAE,kBAAkB,CAAC,EAAE,CAAC,sBAAsB,EAAE,gBAAgB,CAAC,EAAE,CAAC,2BAA2B,EAAE,oBAAoB,CAAC,EAAE,CAAC,0BAA0B,EAAE,oBAAoB,CAAC,EAAE,CAAC,yBAAyB,EAAE,kBAAkB,CAAC,CAAC;EACznBA,KAAK,CAACnM,OAAO,CAAC,UAAUqG,IAAI,EAAE;IAC5B,IAAI+F,KAAK,GAAGzI,cAAc,CAAC0C,IAAI,EAAE,CAAC,CAAC;MAC/BwF,IAAI,GAAGO,KAAK,CAAC,CAAC,CAAC;MACfnM,GAAG,GAAGmM,KAAK,CAAC,CAAC,CAAC;IAElB,IAAIF,GAAG,GAAGD,MAAM,CAACL,aAAa,CAACC,IAAI,CAAC,CAAC;IAErC,IAAIK,GAAG,KAAKG,SAAS,IAAIH,GAAG,KAAK,IAAI,EAAE;MACrCR,OAAO,CAACzL,GAAG,CAAC,GAAGiM,GAAG;IACpB;EACF,CAAC,CAAC;AACJ;AAEA,IAAII,QAAQ,GAAG;EACbC,YAAY,EAAE,OAAO;EACrBC,aAAa,EAAE,SAAS;EACxBC,SAAS,EAAE7E,kBAAkB;EAC7B8E,gBAAgB,EAAE7E,yBAAyB;EAC3C8E,cAAc,EAAE,IAAI;EACpBC,UAAU,EAAE,IAAI;EAChBC,QAAQ,EAAE,IAAI;EACdC,oBAAoB,EAAE,KAAK;EAC3BC,gBAAgB,EAAE,IAAI;EACtBC,cAAc,EAAE,OAAO;EACvBC,kBAAkB,EAAE,IAAI;EACxBC,kBAAkB,EAAE,KAAK;EACzBC,gBAAgB,EAAE;AACpB,CAAC,CAAC,CAAC;;AAEH,IAAIzB,OAAO,CAAC0B,YAAY,EAAE;EACxB1B,OAAO,CAACe,SAAS,GAAGf,OAAO,CAAC0B,YAAY;AAC1C;AAEA,IAAIC,OAAO,GAAG3N,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE4M,QAAQ,CAAC,EAAEZ,OAAO,CAAC;AAEnE,IAAI,CAAC2B,OAAO,CAACV,cAAc,EAAEU,OAAO,CAACN,gBAAgB,GAAG,KAAK;AAC7D,IAAIO,MAAM,GAAG,CAAC,CAAC;AACfrO,MAAM,CAACD,IAAI,CAACsN,QAAQ,CAAC,CAACtM,OAAO,CAAC,UAAUC,GAAG,EAAE;EAC3ChB,MAAM,CAACoB,cAAc,CAACiN,MAAM,EAAErN,GAAG,EAAE;IACjCV,UAAU,EAAE,IAAI;IAChB+B,GAAG,EAAE,SAASA,GAAG,CAAC4K,GAAG,EAAE;MACrBmB,OAAO,CAACpN,GAAG,CAAC,GAAGiM,GAAG;MAElBqB,WAAW,CAACvN,OAAO,CAAC,UAAUwN,EAAE,EAAE;QAChC,OAAOA,EAAE,CAACF,MAAM,CAAC;MACnB,CAAC,CAAC;IACJ,CAAC;IACD/L,GAAG,EAAE,SAASA,GAAG,GAAG;MAClB,OAAO8L,OAAO,CAACpN,GAAG,CAAC;IACrB;EACF,CAAC,CAAC;AACJ,CAAC,CAAC,CAAC,CAAC;;AAEJhB,MAAM,CAACoB,cAAc,CAACiN,MAAM,EAAE,cAAc,EAAE;EAC5C/N,UAAU,EAAE,IAAI;EAChB+B,GAAG,EAAE,SAASA,GAAG,CAAC4K,GAAG,EAAE;IACrBmB,OAAO,CAACZ,SAAS,GAAGP,GAAG;IAEvBqB,WAAW,CAACvN,OAAO,CAAC,UAAUwN,EAAE,EAAE;MAChC,OAAOA,EAAE,CAACF,MAAM,CAAC;IACnB,CAAC,CAAC;EACJ,CAAC;EACD/L,GAAG,EAAE,SAASA,GAAG,GAAG;IAClB,OAAO8L,OAAO,CAACZ,SAAS;EAC1B;AACF,CAAC,CAAC;AACFhG,MAAM,CAACkF,iBAAiB,GAAG2B,MAAM;AACjC,IAAIC,WAAW,GAAG,EAAE;AACpB,SAASE,QAAQ,CAACD,EAAE,EAAE;EACpBD,WAAW,CAAC/N,IAAI,CAACgO,EAAE,CAAC;EAEpB,OAAO,YAAY;IACjBD,WAAW,CAACG,MAAM,CAACH,WAAW,CAACnG,OAAO,CAACoG,EAAE,CAAC,EAAE,CAAC,CAAC;EAChD,CAAC;AACH;AAEA,IAAIG,CAAC,GAAGhG,aAAa;AACrB,IAAIiG,oBAAoB,GAAG;EACzBC,IAAI,EAAE,EAAE;EACRC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE,KAAK;EACZC,KAAK,EAAE;AACT,CAAC;AACD,SAASC,SAAS,CAACC,GAAG,EAAE;EACtB,IAAI,CAACA,GAAG,IAAI,CAACtH,MAAM,EAAE;IACnB;EACF;EAEA,IAAIuH,KAAK,GAAG3H,QAAQ,CAACQ,aAAa,CAAC,OAAO,CAAC;EAC3CmH,KAAK,CAACC,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC;EACtCD,KAAK,CAACE,SAAS,GAAGH,GAAG;EACrB,IAAII,YAAY,GAAG9H,QAAQ,CAACM,IAAI,CAACyH,UAAU;EAC3C,IAAIC,WAAW,GAAG,IAAI;EAEtB,KAAK,IAAI9O,CAAC,GAAG4O,YAAY,CAAC1O,MAAM,GAAG,CAAC,EAAEF,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;IACjD,IAAI+O,KAAK,GAAGH,YAAY,CAAC5O,CAAC,CAAC;IAC3B,IAAIgP,OAAO,GAAG,CAACD,KAAK,CAACC,OAAO,IAAI,EAAE,EAAEC,WAAW,EAAE;IAEjD,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAACzH,OAAO,CAACwH,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;MAC3CF,WAAW,GAAGC,KAAK;IACrB;EACF;EAEAjI,QAAQ,CAACM,IAAI,CAAC8H,YAAY,CAACT,KAAK,EAAEK,WAAW,CAAC;EAC9C,OAAON,GAAG;AACZ;AACA,IAAIW,MAAM,GAAG,gEAAgE;AAC7E,SAASC,YAAY,GAAG;EACtB,IAAInB,IAAI,GAAG,EAAE;EACb,IAAIoB,EAAE,GAAG,EAAE;EAEX,OAAOpB,IAAI,EAAE,GAAG,CAAC,EAAE;IACjBoB,EAAE,IAAIF,MAAM,CAACG,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;EACtC;EAEA,OAAOF,EAAE;AACX;AACA,SAASG,OAAO,CAAC7O,GAAG,EAAE;EACpB,IAAI8O,KAAK,GAAG,EAAE;EAEd,KAAK,IAAIzP,CAAC,GAAG,CAACW,GAAG,IAAI,EAAE,EAAET,MAAM,KAAK,CAAC,EAAEF,CAAC,EAAE,GAAG;IAC3CyP,KAAK,CAACzP,CAAC,CAAC,GAAGW,GAAG,CAACX,CAAC,CAAC;EACnB;EAEA,OAAOyP,KAAK;AACd;AACA,SAASC,UAAU,CAACC,IAAI,EAAE;EACxB,IAAIA,IAAI,CAACC,SAAS,EAAE;IAClB,OAAOJ,OAAO,CAACG,IAAI,CAACC,SAAS,CAAC;EAChC,CAAC,MAAM;IACL,OAAO,CAACD,IAAI,CAACvD,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,EAAEyD,KAAK,CAAC,GAAG,CAAC,CAACrQ,MAAM,CAAC,UAAUQ,CAAC,EAAE;MACvE,OAAOA,CAAC;IACV,CAAC,CAAC;EACJ;AACF;AACA,SAAS8P,UAAU,CAACzN,GAAG,EAAE;EACvB,OAAO,EAAE,CAAC4I,MAAM,CAAC5I,GAAG,CAAC,CAACE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;AACzI;AACA,SAASwN,cAAc,CAACC,UAAU,EAAE;EAClC,OAAO3Q,MAAM,CAACD,IAAI,CAAC4Q,UAAU,IAAI,CAAC,CAAC,CAAC,CAAChO,MAAM,CAAC,UAAUiO,GAAG,EAAEC,aAAa,EAAE;IACxE,OAAOD,GAAG,GAAG,EAAE,CAAChF,MAAM,CAACiF,aAAa,EAAE,KAAK,CAAC,CAACjF,MAAM,CAAC6E,UAAU,CAACE,UAAU,CAACE,aAAa,CAAC,CAAC,EAAE,KAAK,CAAC;EACnG,CAAC,EAAE,EAAE,CAAC,CAACC,IAAI,EAAE;AACf;AACA,SAASC,UAAU,CAACC,MAAM,EAAE;EAC1B,OAAOhR,MAAM,CAACD,IAAI,CAACiR,MAAM,IAAI,CAAC,CAAC,CAAC,CAACrO,MAAM,CAAC,UAAUiO,GAAG,EAAEK,SAAS,EAAE;IAChE,OAAOL,GAAG,GAAG,EAAE,CAAChF,MAAM,CAACqF,SAAS,EAAE,IAAI,CAAC,CAACrF,MAAM,CAACoF,MAAM,CAACC,SAAS,CAAC,CAACH,IAAI,EAAE,EAAE,GAAG,CAAC;EAC/E,CAAC,EAAE,EAAE,CAAC;AACR;AACA,SAASI,qBAAqB,CAACC,SAAS,EAAE;EACxC,OAAOA,SAAS,CAACvC,IAAI,KAAKD,oBAAoB,CAACC,IAAI,IAAIuC,SAAS,CAACtC,CAAC,KAAKF,oBAAoB,CAACE,CAAC,IAAIsC,SAAS,CAACrC,CAAC,KAAKH,oBAAoB,CAACG,CAAC,IAAIqC,SAAS,CAACpC,MAAM,KAAKJ,oBAAoB,CAACI,MAAM,IAAIoC,SAAS,CAACnC,KAAK,IAAImC,SAAS,CAAClC,KAAK;AACnO;AACA,SAASmC,eAAe,CAAChK,IAAI,EAAE;EAC7B,IAAI+J,SAAS,GAAG/J,IAAI,CAAC+J,SAAS;IAC1BE,cAAc,GAAGjK,IAAI,CAACiK,cAAc;IACpCC,SAAS,GAAGlK,IAAI,CAACkK,SAAS;EAC9B,IAAIC,KAAK,GAAG;IACVJ,SAAS,EAAE,YAAY,CAACvF,MAAM,CAACyF,cAAc,GAAG,CAAC,EAAE,OAAO;EAC5D,CAAC;EACD,IAAIG,cAAc,GAAG,YAAY,CAAC5F,MAAM,CAACuF,SAAS,CAACtC,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,CAACjD,MAAM,CAACuF,SAAS,CAACrC,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC;EAC/F,IAAI2C,UAAU,GAAG,QAAQ,CAAC7F,MAAM,CAACuF,SAAS,CAACvC,IAAI,GAAG,EAAE,IAAIuC,SAAS,CAACnC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAACpD,MAAM,CAACuF,SAAS,CAACvC,IAAI,GAAG,EAAE,IAAIuC,SAAS,CAAClC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;EACvJ,IAAIyC,WAAW,GAAG,SAAS,CAAC9F,MAAM,CAACuF,SAAS,CAACpC,MAAM,EAAE,OAAO,CAAC;EAC7D,IAAI4C,KAAK,GAAG;IACVR,SAAS,EAAE,EAAE,CAACvF,MAAM,CAAC4F,cAAc,EAAE,GAAG,CAAC,CAAC5F,MAAM,CAAC6F,UAAU,EAAE,GAAG,CAAC,CAAC7F,MAAM,CAAC8F,WAAW;EACtF,CAAC;EACD,IAAIE,IAAI,GAAG;IACTT,SAAS,EAAE,YAAY,CAACvF,MAAM,CAAC0F,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ;EAC7D,CAAC;EACD,OAAO;IACLC,KAAK,EAAEA,KAAK;IACZI,KAAK,EAAEA,KAAK;IACZC,IAAI,EAAEA;EACR,CAAC;AACH;AACA,SAASC,eAAe,CAAC1E,KAAK,EAAE;EAC9B,IAAIgE,SAAS,GAAGhE,KAAK,CAACgE,SAAS;IAC3BW,WAAW,GAAG3E,KAAK,CAAC4E,KAAK;IACzBA,KAAK,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAGpJ,aAAa,GAAGoJ,WAAW;IAC5DE,YAAY,GAAG7E,KAAK,CAAC8E,MAAM;IAC3BA,MAAM,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAGtJ,aAAa,GAAGsJ,YAAY;IAC/DE,mBAAmB,GAAG/E,KAAK,CAACgF,aAAa;IACzCA,aAAa,GAAGD,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,mBAAmB;EAChF,IAAIjF,GAAG,GAAG,EAAE;EAEZ,IAAIkF,aAAa,IAAIjK,KAAK,EAAE;IAC1B+E,GAAG,IAAI,YAAY,CAACrB,MAAM,CAACuF,SAAS,CAACtC,CAAC,GAAGH,CAAC,GAAGqD,KAAK,GAAG,CAAC,EAAE,MAAM,CAAC,CAACnG,MAAM,CAACuF,SAAS,CAACrC,CAAC,GAAGJ,CAAC,GAAGuD,MAAM,GAAG,CAAC,EAAE,MAAM,CAAC;EAC9G,CAAC,MAAM,IAAIE,aAAa,EAAE;IACxBlF,GAAG,IAAI,wBAAwB,CAACrB,MAAM,CAACuF,SAAS,CAACtC,CAAC,GAAGH,CAAC,EAAE,mBAAmB,CAAC,CAAC9C,MAAM,CAACuF,SAAS,CAACrC,CAAC,GAAGJ,CAAC,EAAE,OAAO,CAAC;EAC/G,CAAC,MAAM;IACLzB,GAAG,IAAI,YAAY,CAACrB,MAAM,CAACuF,SAAS,CAACtC,CAAC,GAAGH,CAAC,EAAE,MAAM,CAAC,CAAC9C,MAAM,CAACuF,SAAS,CAACrC,CAAC,GAAGJ,CAAC,EAAE,MAAM,CAAC;EACrF;EAEAzB,GAAG,IAAI,QAAQ,CAACrB,MAAM,CAACuF,SAAS,CAACvC,IAAI,GAAGF,CAAC,IAAIyC,SAAS,CAACnC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAACpD,MAAM,CAACuF,SAAS,CAACvC,IAAI,GAAGF,CAAC,IAAIyC,SAAS,CAAClC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;EAC3IhC,GAAG,IAAI,SAAS,CAACrB,MAAM,CAACuF,SAAS,CAACpC,MAAM,EAAE,OAAO,CAAC;EAClD,OAAO9B,GAAG;AACZ;AAEA,IAAImF,UAAU,GAAG,oxrBAAoxrB;AAEryrB,SAASjD,GAAG,GAAG;EACb,IAAIkD,GAAG,GAAG1J,kBAAkB;EAC5B,IAAI2J,GAAG,GAAG1J,yBAAyB;EACnC,IAAI2J,EAAE,GAAGlE,MAAM,CAACb,SAAS;EACzB,IAAIgF,EAAE,GAAGnE,MAAM,CAACZ,gBAAgB;EAChC,IAAIgF,CAAC,GAAGL,UAAU;EAElB,IAAIG,EAAE,KAAKF,GAAG,IAAIG,EAAE,KAAKF,GAAG,EAAE;IAC5B,IAAII,KAAK,GAAG,IAAI1Q,MAAM,CAAC,KAAK,CAAC4J,MAAM,CAACyG,GAAG,EAAE,KAAK,CAAC,EAAE,GAAG,CAAC;IACrD,IAAIM,cAAc,GAAG,IAAI3Q,MAAM,CAAC,MAAM,CAAC4J,MAAM,CAACyG,GAAG,EAAE,KAAK,CAAC,EAAE,GAAG,CAAC;IAC/D,IAAIO,KAAK,GAAG,IAAI5Q,MAAM,CAAC,KAAK,CAAC4J,MAAM,CAAC0G,GAAG,CAAC,EAAE,GAAG,CAAC;IAC9CG,CAAC,GAAGA,CAAC,CAACvP,OAAO,CAACwP,KAAK,EAAE,GAAG,CAAC9G,MAAM,CAAC2G,EAAE,EAAE,GAAG,CAAC,CAAC,CAACrP,OAAO,CAACyP,cAAc,EAAE,IAAI,CAAC/G,MAAM,CAAC2G,EAAE,EAAE,GAAG,CAAC,CAAC,CAACrP,OAAO,CAAC0P,KAAK,EAAE,GAAG,CAAChH,MAAM,CAAC4G,EAAE,CAAC,CAAC;EACxH;EAEA,OAAOC,CAAC;AACV;AAEA,IAAII,YAAY,GAAG,KAAK;AAExB,SAASC,SAAS,GAAG;EACnB,IAAIzE,MAAM,CAACV,UAAU,IAAI,CAACkF,YAAY,EAAE;IACtC3D,SAAS,CAACC,GAAG,EAAE,CAAC;IAChB0D,YAAY,GAAG,IAAI;EACrB;AACF;AAEA,IAAIE,SAAS,GAAG;EACdC,MAAM,EAAE,SAASA,MAAM,GAAG;IACxB,OAAO;MACLC,GAAG,EAAE;QACH9D,GAAG,EAAEA,GAAG;QACRD,SAAS,EAAE4D;MACb;IACF,CAAC;EACH,CAAC;EACDI,KAAK,EAAE,SAASA,KAAK,GAAG;IACtB,OAAO;MACLC,wBAAwB,EAAE,SAASA,wBAAwB,GAAG;QAC5DL,SAAS,EAAE;MACb,CAAC;MACDM,WAAW,EAAE,SAASA,WAAW,GAAG;QAClCN,SAAS,EAAE;MACb;IACF,CAAC;EACH;AACF,CAAC;AAED,IAAIO,CAAC,GAAG7L,MAAM,IAAI,CAAC,CAAC;AACpB,IAAI,CAAC6L,CAAC,CAAC5K,oBAAoB,CAAC,EAAE4K,CAAC,CAAC5K,oBAAoB,CAAC,GAAG,CAAC,CAAC;AAC1D,IAAI,CAAC4K,CAAC,CAAC5K,oBAAoB,CAAC,CAACuI,MAAM,EAAEqC,CAAC,CAAC5K,oBAAoB,CAAC,CAACuI,MAAM,GAAG,CAAC,CAAC;AACxE,IAAI,CAACqC,CAAC,CAAC5K,oBAAoB,CAAC,CAACyK,KAAK,EAAEG,CAAC,CAAC5K,oBAAoB,CAAC,CAACyK,KAAK,GAAG,CAAC,CAAC;AACtE,IAAI,CAACG,CAAC,CAAC5K,oBAAoB,CAAC,CAAC6K,KAAK,EAAED,CAAC,CAAC5K,oBAAoB,CAAC,CAAC6K,KAAK,GAAG,EAAE;AACtE,IAAIC,SAAS,GAAGF,CAAC,CAAC5K,oBAAoB,CAAC;AAEvC,IAAI+K,SAAS,GAAG,EAAE;AAElB,IAAIC,QAAQ,GAAG,SAASA,QAAQ,GAAG;EACjChM,QAAQ,CAACiM,mBAAmB,CAAC,kBAAkB,EAAED,QAAQ,CAAC;EAC1DE,MAAM,GAAG,CAAC;EACVH,SAAS,CAACnH,GAAG,CAAC,UAAUuH,EAAE,EAAE;IAC1B,OAAOA,EAAE,EAAE;EACb,CAAC,CAAC;AACJ,CAAC;AAED,IAAID,MAAM,GAAG,KAAK;AAElB,IAAI9L,MAAM,EAAE;EACV8L,MAAM,GAAG,CAAClM,QAAQ,CAACK,eAAe,CAAC+L,QAAQ,GAAG,YAAY,GAAG,eAAe,EAAExN,IAAI,CAACoB,QAAQ,CAACqM,UAAU,CAAC;EACvG,IAAI,CAACH,MAAM,EAAElM,QAAQ,CAACO,gBAAgB,CAAC,kBAAkB,EAAEyL,QAAQ,CAAC;AACtE;AAEA,SAASM,QAAQ,CAAEH,EAAE,EAAE;EACrB,IAAI,CAAC/L,MAAM,EAAE;EACb8L,MAAM,GAAGK,UAAU,CAACJ,EAAE,EAAE,CAAC,CAAC,GAAGJ,SAAS,CAACjT,IAAI,CAACqT,EAAE,CAAC;AACjD;AAEA,SAASK,MAAM,CAACC,aAAa,EAAE;EAC7B,IAAIC,GAAG,GAAGD,aAAa,CAACC,GAAG;IACvBC,qBAAqB,GAAGF,aAAa,CAACvD,UAAU;IAChDA,UAAU,GAAGyD,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,qBAAqB;IAC1EC,qBAAqB,GAAGH,aAAa,CAACI,QAAQ;IAC9CA,QAAQ,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;EAE5E,IAAI,OAAOH,aAAa,KAAK,QAAQ,EAAE;IACrC,OAAOzD,UAAU,CAACyD,aAAa,CAAC;EAClC,CAAC,MAAM;IACL,OAAO,GAAG,CAACtI,MAAM,CAACuI,GAAG,EAAE,GAAG,CAAC,CAACvI,MAAM,CAAC8E,cAAc,CAACC,UAAU,CAAC,EAAE,GAAG,CAAC,CAAC/E,MAAM,CAAC0I,QAAQ,CAACjI,GAAG,CAAC4H,MAAM,CAAC,CAACM,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC3I,MAAM,CAACuI,GAAG,EAAE,GAAG,CAAC;EAClI;AACF;AAEA,SAASK,eAAe,CAACC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,EAAE;EAClD,IAAIF,OAAO,IAAIA,OAAO,CAACC,MAAM,CAAC,IAAID,OAAO,CAACC,MAAM,CAAC,CAACC,QAAQ,CAAC,EAAE;IAC3D,OAAO;MACLD,MAAM,EAAEA,MAAM;MACdC,QAAQ,EAAEA,QAAQ;MAClBC,IAAI,EAAEH,OAAO,CAACC,MAAM,CAAC,CAACC,QAAQ;IAChC,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA;;AAEA,IAAIE,aAAa,GAAG,SAASA,aAAa,CAACC,IAAI,EAAEC,WAAW,EAAE;EAC5D,OAAO,UAAUC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAExG,CAAC,EAAE;IAC3B,OAAOoG,IAAI,CAAC7R,IAAI,CAAC8R,WAAW,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAExG,CAAC,CAAC;EAC3C,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,IAAI/L,MAAM,GAAG,SAASwS,gBAAgB,CAACC,OAAO,EAAExB,EAAE,EAAEyB,YAAY,EAAEN,WAAW,EAAE;EAC7E,IAAIhV,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACqV,OAAO,CAAC;IAC3BvU,MAAM,GAAGd,IAAI,CAACc,MAAM;IACpBW,QAAQ,GAAGuT,WAAW,KAAK3H,SAAS,GAAGyH,aAAa,CAACjB,EAAE,EAAEmB,WAAW,CAAC,GAAGnB,EAAE;IAC1EjT,CAAC;IACDK,GAAG;IACHyB,MAAM;EAEV,IAAI4S,YAAY,KAAKjI,SAAS,EAAE;IAC9BzM,CAAC,GAAG,CAAC;IACL8B,MAAM,GAAG2S,OAAO,CAACrV,IAAI,CAAC,CAAC,CAAC,CAAC;EAC3B,CAAC,MAAM;IACLY,CAAC,GAAG,CAAC;IACL8B,MAAM,GAAG4S,YAAY;EACvB;EAEA,OAAO1U,CAAC,GAAGE,MAAM,EAAEF,CAAC,EAAE,EAAE;IACtBK,GAAG,GAAGjB,IAAI,CAACY,CAAC,CAAC;IACb8B,MAAM,GAAGjB,QAAQ,CAACiB,MAAM,EAAE2S,OAAO,CAACpU,GAAG,CAAC,EAAEA,GAAG,EAAEoU,OAAO,CAAC;EACvD;EAEA,OAAO3S,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS6S,UAAU,CAACC,MAAM,EAAE;EAC1B,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,OAAO,GAAG,CAAC;EACf,IAAI5U,MAAM,GAAG0U,MAAM,CAAC1U,MAAM;EAE1B,OAAO4U,OAAO,GAAG5U,MAAM,EAAE;IACvB,IAAIsD,KAAK,GAAGoR,MAAM,CAACG,UAAU,CAACD,OAAO,EAAE,CAAC;IAExC,IAAItR,KAAK,IAAI,MAAM,IAAIA,KAAK,IAAI,MAAM,IAAIsR,OAAO,GAAG5U,MAAM,EAAE;MAC1D,IAAI8U,KAAK,GAAGJ,MAAM,CAACG,UAAU,CAACD,OAAO,EAAE,CAAC;MAExC,IAAI,CAACE,KAAK,GAAG,MAAM,KAAK,MAAM,EAAE;QAC9B;QACAH,MAAM,CAACjV,IAAI,CAAC,CAAC,CAAC4D,KAAK,GAAG,KAAK,KAAK,EAAE,KAAKwR,KAAK,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC;MAClE,CAAC,MAAM;QACLH,MAAM,CAACjV,IAAI,CAAC4D,KAAK,CAAC;QAClBsR,OAAO,EAAE;MACX;IACF,CAAC,MAAM;MACLD,MAAM,CAACjV,IAAI,CAAC4D,KAAK,CAAC;IACpB;EACF;EAEA,OAAOqR,MAAM;AACf;AAEA,SAASI,KAAK,CAACC,OAAO,EAAE;EACtB,IAAIC,OAAO,GAAGR,UAAU,CAACO,OAAO,CAAC;EACjC,OAAOC,OAAO,CAACjV,MAAM,KAAK,CAAC,GAAGiV,OAAO,CAAC,CAAC,CAAC,CAAC1P,QAAQ,CAAC,EAAE,CAAC,GAAG,IAAI;AAC9D;AACA,SAAS2P,WAAW,CAACR,MAAM,EAAES,KAAK,EAAE;EAClC,IAAIpH,IAAI,GAAG2G,MAAM,CAAC1U,MAAM;EACxB,IAAIoV,KAAK,GAAGV,MAAM,CAACG,UAAU,CAACM,KAAK,CAAC;EACpC,IAAIE,MAAM;EAEV,IAAID,KAAK,IAAI,MAAM,IAAIA,KAAK,IAAI,MAAM,IAAIrH,IAAI,GAAGoH,KAAK,GAAG,CAAC,EAAE;IAC1DE,MAAM,GAAGX,MAAM,CAACG,UAAU,CAACM,KAAK,GAAG,CAAC,CAAC;IAErC,IAAIE,MAAM,IAAI,MAAM,IAAIA,MAAM,IAAI,MAAM,EAAE;MACxC,OAAO,CAACD,KAAK,GAAG,MAAM,IAAI,KAAK,GAAGC,MAAM,GAAG,MAAM,GAAG,OAAO;IAC7D;EACF;EAEA,OAAOD,KAAK;AACd;AAEA,SAASE,cAAc,CAACC,KAAK,EAAE;EAC7B,OAAOpW,MAAM,CAACD,IAAI,CAACqW,KAAK,CAAC,CAACzT,MAAM,CAAC,UAAUiO,GAAG,EAAE+D,QAAQ,EAAE;IACxD,IAAIC,IAAI,GAAGwB,KAAK,CAACzB,QAAQ,CAAC;IAC1B,IAAI0B,QAAQ,GAAG,CAAC,CAACzB,IAAI,CAACA,IAAI;IAE1B,IAAIyB,QAAQ,EAAE;MACZzF,GAAG,CAACgE,IAAI,CAACD,QAAQ,CAAC,GAAGC,IAAI,CAACA,IAAI;IAChC,CAAC,MAAM;MACLhE,GAAG,CAAC+D,QAAQ,CAAC,GAAGC,IAAI;IACtB;IAEA,OAAOhE,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;AAEA,SAAS0F,WAAW,CAAC5B,MAAM,EAAE0B,KAAK,EAAE;EAClC,IAAIG,MAAM,GAAG3V,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwM,SAAS,GAAGxM,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACnF,IAAI4V,iBAAiB,GAAGD,MAAM,CAACE,SAAS;IACpCA,SAAS,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,iBAAiB;EACxE,IAAIE,UAAU,GAAGP,cAAc,CAACC,KAAK,CAAC;EAEtC,IAAI,OAAO7C,SAAS,CAACL,KAAK,CAACyD,OAAO,KAAK,UAAU,IAAI,CAACF,SAAS,EAAE;IAC/DlD,SAAS,CAACL,KAAK,CAACyD,OAAO,CAACjC,MAAM,EAAEyB,cAAc,CAACC,KAAK,CAAC,CAAC;EACxD,CAAC,MAAM;IACL7C,SAAS,CAACvC,MAAM,CAAC0D,MAAM,CAAC,GAAGjU,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE8S,SAAS,CAACvC,MAAM,CAAC0D,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAEgC,UAAU,CAAC;EAC3G;EACA;AACF;AACA;AACA;AACA;AACA;;EAGE,IAAIhC,MAAM,KAAK,KAAK,EAAE;IACpB4B,WAAW,CAAC,IAAI,EAAEF,KAAK,CAAC;EAC1B;AACF;AAEA,IAAIQ,aAAa,GAAG,CAAC,aAAajV,WAAW,CAAC,0DAA0D,EAAE;EACxGkV,EAAE,EAAE,CAAC;EACLC,EAAE,EAAE;AACN,CAAC,CAAC,EAAE,aAAanV,WAAW,CAAC,kHAAkH,EAAE;EAC/IoV,IAAI,EAAE,CAAC;EACPF,EAAE,EAAE,CAAC;EACLG,IAAI,EAAE,CAAC;EACPF,EAAE,EAAE;AACN,CAAC,CAAC,EAAE,aAAanV,WAAW,CAAC,yDAAyD,EAAE;EACtFoV,IAAI,EAAE,CAAC;EACPF,EAAE,EAAE;AACN,CAAC,CAAC,CAAC;AAEH,IAAII,WAAW,EAAEC,SAAS,EAAEC,oBAAoB;AAChD,IAAInG,MAAM,GAAGuC,SAAS,CAACvC,MAAM;EACzBsC,KAAK,GAAGC,SAAS,CAACD,KAAK;AAC3B,IAAI8D,UAAU,IAAIH,WAAW,GAAG,CAAC,CAAC,EAAEhW,eAAe,CAACgW,WAAW,EAAExN,cAAc,EAAEzJ,MAAM,CAACqX,MAAM,CAAC7M,oBAAoB,CAACf,cAAc,CAAC,CAAC,CAAC,EAAExI,eAAe,CAACgW,WAAW,EAAEvN,YAAY,EAAE1J,MAAM,CAACqX,MAAM,CAAC7M,oBAAoB,CAACd,YAAY,CAAC,CAAC,CAAC,EAAEuN,WAAW,CAAC;AAClP,IAAIK,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,UAAU,GAAG,CAAC,CAAC;AACnB,IAAIC,WAAW,GAAG,CAAC,CAAC;AACpB,IAAIC,UAAU,GAAG,CAAC,CAAC;AACnB,IAAIC,aAAa,GAAG,CAAC,CAAC;AACtB,IAAIC,QAAQ,GAAG,CAAC,CAAC;AACjB,IAAIC,QAAQ,IAAIV,SAAS,GAAG,CAAC,CAAC,EAAEjW,eAAe,CAACiW,SAAS,EAAEzN,cAAc,EAAEzJ,MAAM,CAACD,IAAI,CAACgK,eAAe,CAACN,cAAc,CAAC,CAAC,CAAC,EAAExI,eAAe,CAACiW,SAAS,EAAExN,YAAY,EAAE1J,MAAM,CAACD,IAAI,CAACgK,eAAe,CAACL,YAAY,CAAC,CAAC,CAAC,EAAEwN,SAAS,CAAC;AAE1N,SAASW,UAAU,CAACjV,IAAI,EAAE;EACxB,OAAO,CAAC4J,gBAAgB,CAACrE,OAAO,CAACvF,IAAI,CAAC;AACxC;AAEA,SAASkV,WAAW,CAACtK,SAAS,EAAEuK,GAAG,EAAE;EACnC,IAAIC,KAAK,GAAGD,GAAG,CAACvH,KAAK,CAAC,GAAG,CAAC;EAC1B,IAAIkE,MAAM,GAAGsD,KAAK,CAAC,CAAC,CAAC;EACrB,IAAIrD,QAAQ,GAAGqD,KAAK,CAAC1U,KAAK,CAAC,CAAC,CAAC,CAACiR,IAAI,CAAC,GAAG,CAAC;EAEvC,IAAIG,MAAM,KAAKlH,SAAS,IAAImH,QAAQ,KAAK,EAAE,IAAI,CAACkD,UAAU,CAAClD,QAAQ,CAAC,EAAE;IACpE,OAAOA,QAAQ;EACjB,CAAC,MAAM;IACL,OAAO,IAAI;EACb;AACF;AACA,IAAIsD,KAAK,GAAG,SAASA,KAAK,GAAG;EAC3B,IAAIC,MAAM,GAAG,SAASA,MAAM,CAACC,OAAO,EAAE;IACpC,OAAOxV,MAAM,CAACqO,MAAM,EAAE,UAAU1M,CAAC,EAAE8K,KAAK,EAAEsF,MAAM,EAAE;MAChDpQ,CAAC,CAACoQ,MAAM,CAAC,GAAG/R,MAAM,CAACyM,KAAK,EAAE+I,OAAO,EAAE,CAAC,CAAC,CAAC;MACtC,OAAO7T,CAAC;IACV,CAAC,EAAE,CAAC,CAAC,CAAC;EACR,CAAC;EAEDiT,UAAU,GAAGW,MAAM,CAAC,UAAUtH,GAAG,EAAEgE,IAAI,EAAED,QAAQ,EAAE;IACjD,IAAIC,IAAI,CAAC,CAAC,CAAC,EAAE;MACXhE,GAAG,CAACgE,IAAI,CAAC,CAAC,CAAC,CAAC,GAAGD,QAAQ;IACzB;IAEA,IAAIC,IAAI,CAAC,CAAC,CAAC,EAAE;MACX,IAAIwD,OAAO,GAAGxD,IAAI,CAAC,CAAC,CAAC,CAACzU,MAAM,CAAC,UAAU6U,CAAC,EAAE;QACxC,OAAO,OAAOA,CAAC,KAAK,QAAQ;MAC9B,CAAC,CAAC;MACFoD,OAAO,CAACrX,OAAO,CAAC,UAAUsX,KAAK,EAAE;QAC/BzH,GAAG,CAACyH,KAAK,CAACjS,QAAQ,CAAC,EAAE,CAAC,CAAC,GAAGuO,QAAQ;MACpC,CAAC,CAAC;IACJ;IAEA,OAAO/D,GAAG;EACZ,CAAC,CAAC;EACF4G,WAAW,GAAGU,MAAM,CAAC,UAAUtH,GAAG,EAAEgE,IAAI,EAAED,QAAQ,EAAE;IAClD/D,GAAG,CAAC+D,QAAQ,CAAC,GAAGA,QAAQ;IAExB,IAAIC,IAAI,CAAC,CAAC,CAAC,EAAE;MACX,IAAIwD,OAAO,GAAGxD,IAAI,CAAC,CAAC,CAAC,CAACzU,MAAM,CAAC,UAAU6U,CAAC,EAAE;QACxC,OAAO,OAAOA,CAAC,KAAK,QAAQ;MAC9B,CAAC,CAAC;MACFoD,OAAO,CAACrX,OAAO,CAAC,UAAUsX,KAAK,EAAE;QAC/BzH,GAAG,CAACyH,KAAK,CAAC,GAAG1D,QAAQ;MACvB,CAAC,CAAC;IACJ;IAEA,OAAO/D,GAAG;EACZ,CAAC,CAAC;EACF+G,QAAQ,GAAGO,MAAM,CAAC,UAAUtH,GAAG,EAAEgE,IAAI,EAAED,QAAQ,EAAE;IAC/C,IAAIyD,OAAO,GAAGxD,IAAI,CAAC,CAAC,CAAC;IACrBhE,GAAG,CAAC+D,QAAQ,CAAC,GAAGA,QAAQ;IACxByD,OAAO,CAACrX,OAAO,CAAC,UAAUsX,KAAK,EAAE;MAC/BzH,GAAG,CAACyH,KAAK,CAAC,GAAG1D,QAAQ;IACvB,CAAC,CAAC;IACF,OAAO/D,GAAG;EACZ,CAAC,CAAC,CAAC,CAAC;EACJ;;EAEA,IAAI0H,UAAU,GAAG,KAAK,IAAItH,MAAM,IAAI3C,MAAM,CAACkK,YAAY;EACvD,IAAIC,WAAW,GAAG7V,MAAM,CAAC2Q,KAAK,EAAE,UAAU1C,GAAG,EAAE6H,IAAI,EAAE;IACnD,IAAIC,qBAAqB,GAAGD,IAAI,CAAC,CAAC,CAAC;IACnC,IAAI/D,MAAM,GAAG+D,IAAI,CAAC,CAAC,CAAC;IACpB,IAAI9D,QAAQ,GAAG8D,IAAI,CAAC,CAAC,CAAC;IAEtB,IAAI/D,MAAM,KAAK,KAAK,IAAI,CAAC4D,UAAU,EAAE;MACnC5D,MAAM,GAAG,KAAK;IAChB;IAEA,IAAI,OAAOgE,qBAAqB,KAAK,QAAQ,EAAE;MAC7C9H,GAAG,CAAC+H,KAAK,CAACD,qBAAqB,CAAC,GAAG;QACjChE,MAAM,EAAEA,MAAM;QACdC,QAAQ,EAAEA;MACZ,CAAC;IACH;IAEA,IAAI,OAAO+D,qBAAqB,KAAK,QAAQ,EAAE;MAC7C9H,GAAG,CAACgI,QAAQ,CAACF,qBAAqB,CAACtS,QAAQ,CAAC,EAAE,CAAC,CAAC,GAAG;QACjDsO,MAAM,EAAEA,MAAM;QACdC,QAAQ,EAAEA;MACZ,CAAC;IACH;IAEA,OAAO/D,GAAG;EACZ,CAAC,EAAE;IACD+H,KAAK,EAAE,CAAC,CAAC;IACTC,QAAQ,EAAE,CAAC;EACb,CAAC,CAAC;EACFnB,UAAU,GAAGe,WAAW,CAACG,KAAK;EAC9BjB,aAAa,GAAGc,WAAW,CAACI,QAAQ;EACpCtB,oBAAoB,GAAGuB,kBAAkB,CAACxK,MAAM,CAACf,YAAY,EAAE;IAC7DwL,MAAM,EAAEzK,MAAM,CAACd;EACjB,CAAC,CAAC;AACJ,CAAC;AACDiB,QAAQ,CAAC,UAAU0G,CAAC,EAAE;EACpBoC,oBAAoB,GAAGuB,kBAAkB,CAAC3D,CAAC,CAAC5H,YAAY,EAAE;IACxDwL,MAAM,EAAEzK,MAAM,CAACd;EACjB,CAAC,CAAC;AACJ,CAAC,CAAC;AACF0K,KAAK,EAAE;AACP,SAASc,SAAS,CAACrE,MAAM,EAAEmB,OAAO,EAAE;EAClC,OAAO,CAAC0B,UAAU,CAAC7C,MAAM,CAAC,IAAI,CAAC,CAAC,EAAEmB,OAAO,CAAC;AAC5C;AACA,SAASmD,UAAU,CAACtE,MAAM,EAAEuE,QAAQ,EAAE;EACpC,OAAO,CAACzB,WAAW,CAAC9C,MAAM,CAAC,IAAI,CAAC,CAAC,EAAEuE,QAAQ,CAAC;AAC9C;AACA,SAASC,OAAO,CAACxE,MAAM,EAAE2D,KAAK,EAAE;EAC9B,OAAO,CAACV,QAAQ,CAACjD,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE2D,KAAK,CAAC;AACxC;AACA,SAASc,SAAS,CAACvW,IAAI,EAAE;EACvB,OAAO6U,UAAU,CAAC7U,IAAI,CAAC,IAAI;IACzB8R,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE;EACZ,CAAC;AACH;AACA,SAASyE,YAAY,CAACvD,OAAO,EAAE;EAC7B,IAAIwD,UAAU,GAAG3B,aAAa,CAAC7B,OAAO,CAAC;EACvC,IAAIyD,UAAU,GAAGP,SAAS,CAAC,KAAK,EAAElD,OAAO,CAAC;EAC1C,OAAOwD,UAAU,KAAKC,UAAU,GAAG;IACjC5E,MAAM,EAAE,KAAK;IACbC,QAAQ,EAAE2E;EACZ,CAAC,GAAG,IAAI,CAAC,IAAI;IACX5E,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE;EACZ,CAAC;AACH;AACA,SAAS4E,sBAAsB,GAAG;EAChC,OAAOjC,oBAAoB;AAC7B;AACA,IAAIkC,kBAAkB,GAAG,SAASA,kBAAkB,GAAG;EACrD,OAAO;IACL9E,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE,IAAI;IACd8E,IAAI,EAAE;EACR,CAAC;AACH,CAAC;AACD,SAASZ,kBAAkB,CAACa,aAAa,EAAE;EACzC,IAAInD,MAAM,GAAG3V,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwM,SAAS,GAAGxM,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACnF,IAAI+Y,cAAc,GAAGpD,MAAM,CAACuC,MAAM;IAC9BA,MAAM,GAAGa,cAAc,KAAK,KAAK,CAAC,GAAGlQ,cAAc,GAAGkQ,cAAc;EACxE,IAAIvK,KAAK,GAAGrF,eAAe,CAAC+O,MAAM,CAAC,CAACY,aAAa,CAAC;EAClD,IAAIhF,MAAM,GAAG1K,eAAe,CAAC8O,MAAM,CAAC,CAACY,aAAa,CAAC,IAAI1P,eAAe,CAAC8O,MAAM,CAAC,CAAC1J,KAAK,CAAC;EACrF,IAAIwK,OAAO,GAAGF,aAAa,IAAInG,SAAS,CAACvC,MAAM,GAAG0I,aAAa,GAAG,IAAI;EACtE,OAAOhF,MAAM,IAAIkF,OAAO,IAAI,IAAI;AAClC;AACA,IAAIC,mBAAmB,IAAI1C,oBAAoB,GAAG,CAAC,CAAC,EAAElW,eAAe,CAACkW,oBAAoB,EAAE1N,cAAc,EAAEzJ,MAAM,CAACD,IAAI,CAACyK,oBAAoB,CAACf,cAAc,CAAC,CAAC,CAAC,EAAExI,eAAe,CAACkW,oBAAoB,EAAEzN,YAAY,EAAE1J,MAAM,CAACD,IAAI,CAACyK,oBAAoB,CAACd,YAAY,CAAC,CAAC,CAAC,EAAEyN,oBAAoB,CAAC;AAC3R,SAAS2C,gBAAgB,CAACzC,MAAM,EAAE;EAChC,IAAI0C,SAAS;EAEb,IAAIxD,MAAM,GAAG3V,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwM,SAAS,GAAGxM,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACnF,IAAIoZ,mBAAmB,GAAGzD,MAAM,CAAC0D,WAAW;IACxCA,WAAW,GAAGD,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,mBAAmB;EAC9E,IAAIE,QAAQ,IAAIH,SAAS,GAAG,CAAC,CAAC,EAAE9Y,eAAe,CAAC8Y,SAAS,EAAEtQ,cAAc,EAAE,EAAE,CAACmC,MAAM,CAACyC,MAAM,CAACb,SAAS,EAAE,GAAG,CAAC,CAAC5B,MAAM,CAACnC,cAAc,CAAC,CAAC,EAAExI,eAAe,CAAC8Y,SAAS,EAAErQ,YAAY,EAAE,EAAE,CAACkC,MAAM,CAACyC,MAAM,CAACb,SAAS,EAAE,GAAG,CAAC,CAAC5B,MAAM,CAAClC,YAAY,CAAC,CAAC,EAAEqQ,SAAS,CAAC;EAChP,IAAII,WAAW,GAAG,IAAI;EACtB,IAAIrB,MAAM,GAAGrP,cAAc;EAE3B,IAAI4N,MAAM,CAAC+C,QAAQ,CAACF,QAAQ,CAACzQ,cAAc,CAAC,CAAC,IAAI4N,MAAM,CAACgD,IAAI,CAAC,UAAUC,CAAC,EAAE;IACxE,OAAOT,mBAAmB,CAACpQ,cAAc,CAAC,CAAC2Q,QAAQ,CAACE,CAAC,CAAC;EACxD,CAAC,CAAC,EAAE;IACFxB,MAAM,GAAGrP,cAAc;EACzB;EAEA,IAAI4N,MAAM,CAAC+C,QAAQ,CAACF,QAAQ,CAACxQ,YAAY,CAAC,CAAC,IAAI2N,MAAM,CAACgD,IAAI,CAAC,UAAUC,CAAC,EAAE;IACtE,OAAOT,mBAAmB,CAACnQ,YAAY,CAAC,CAAC0Q,QAAQ,CAACE,CAAC,CAAC;EACtD,CAAC,CAAC,EAAE;IACFxB,MAAM,GAAGpP,YAAY;EACvB;EAEA,IAAI6Q,SAAS,GAAGlD,MAAM,CAAC1U,MAAM,CAAC,UAAUiO,GAAG,EAAEmH,GAAG,EAAE;IAChD,IAAIpD,QAAQ,GAAGmD,WAAW,CAACzJ,MAAM,CAACb,SAAS,EAAEuK,GAAG,CAAC;IAEjD,IAAI/G,MAAM,CAAC+G,GAAG,CAAC,EAAE;MACfA,GAAG,GAAGX,UAAU,CAAC0B,MAAM,CAAC,CAACsB,QAAQ,CAACrC,GAAG,CAAC,GAAG3M,oBAAoB,CAAC0N,MAAM,CAAC,CAACf,GAAG,CAAC,GAAGA,GAAG;MAChFoC,WAAW,GAAGpC,GAAG;MACjBnH,GAAG,CAAC8D,MAAM,GAAGqD,GAAG;IAClB,CAAC,MAAM,IAAIH,QAAQ,CAACkB,MAAM,CAAC,CAAC3Q,OAAO,CAAC4P,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;MAC7CoC,WAAW,GAAGpC,GAAG;MACjBnH,GAAG,CAAC8D,MAAM,GAAGmE,kBAAkB,CAACd,GAAG,EAAE;QACnCe,MAAM,EAAEA;MACV,CAAC,CAAC;IACJ,CAAC,MAAM,IAAInE,QAAQ,EAAE;MACnB/D,GAAG,CAAC+D,QAAQ,GAAGA,QAAQ;IACzB,CAAC,MAAM,IAAIoD,GAAG,KAAK1J,MAAM,CAACZ,gBAAgB,IAAIsK,GAAG,KAAKmC,QAAQ,CAACzQ,cAAc,CAAC,IAAIsO,GAAG,KAAKmC,QAAQ,CAACxQ,YAAY,CAAC,EAAE;MAChHkH,GAAG,CAAC6I,IAAI,CAAClZ,IAAI,CAACwX,GAAG,CAAC;IACpB;IAEA,IAAI,CAACkC,WAAW,IAAIrJ,GAAG,CAAC8D,MAAM,IAAI9D,GAAG,CAAC+D,QAAQ,EAAE;MAC9C,IAAI8D,IAAI,GAAG0B,WAAW,KAAK,IAAI,GAAGhB,SAAS,CAACvI,GAAG,CAAC+D,QAAQ,CAAC,GAAG,CAAC,CAAC;MAC9D,IAAI6F,aAAa,GAAGtB,OAAO,CAACtI,GAAG,CAAC8D,MAAM,EAAE9D,GAAG,CAAC+D,QAAQ,CAAC;MAErD,IAAI8D,IAAI,CAAC/D,MAAM,EAAE;QACfyF,WAAW,GAAG,IAAI;MACpB;MAEAvJ,GAAG,CAAC+D,QAAQ,GAAG8D,IAAI,CAAC9D,QAAQ,IAAI6F,aAAa,IAAI5J,GAAG,CAAC+D,QAAQ;MAC7D/D,GAAG,CAAC8D,MAAM,GAAG+D,IAAI,CAAC/D,MAAM,IAAI9D,GAAG,CAAC8D,MAAM;MAEtC,IAAI9D,GAAG,CAAC8D,MAAM,KAAK,KAAK,IAAI,CAAC1D,MAAM,CAAC,KAAK,CAAC,IAAIA,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC3C,MAAM,CAACkK,YAAY,EAAE;QACnF;QACA;QACA3H,GAAG,CAAC8D,MAAM,GAAG,KAAK;MACpB;IACF;IAEA,OAAO9D,GAAG;EACZ,CAAC,EAAE4I,kBAAkB,EAAE,CAAC;EAExB,IAAInC,MAAM,CAAC+C,QAAQ,CAAC,WAAW,CAAC,IAAI/C,MAAM,CAAC+C,QAAQ,CAAC,KAAK,CAAC,EAAE;IAC1DG,SAAS,CAAC7F,MAAM,GAAG,KAAK;EAC1B;EAEA,IAAI2C,MAAM,CAAC+C,QAAQ,CAAC,YAAY,CAAC,IAAI/C,MAAM,CAAC+C,QAAQ,CAAC,KAAK,CAAC,EAAE;IAC3DG,SAAS,CAAC7F,MAAM,GAAG,KAAK;EAC1B;EAEA,IAAI,CAAC6F,SAAS,CAAC7F,MAAM,IAAIoE,MAAM,KAAKpP,YAAY,KAAKsH,MAAM,CAAC,MAAM,CAAC,IAAI3C,MAAM,CAACkK,YAAY,CAAC,EAAE;IAC3FgC,SAAS,CAAC7F,MAAM,GAAG,MAAM;IACzB6F,SAAS,CAAC5F,QAAQ,GAAGuE,OAAO,CAACqB,SAAS,CAAC7F,MAAM,EAAE6F,SAAS,CAAC5F,QAAQ,CAAC,IAAI4F,SAAS,CAAC5F,QAAQ;EAC1F;EAEA,IAAI4F,SAAS,CAAC7F,MAAM,KAAK,IAAI,IAAIyF,WAAW,KAAK,IAAI,EAAE;IACrD;IACA;IACAI,SAAS,CAAC7F,MAAM,GAAG6E,sBAAsB,EAAE,IAAI,KAAK;EACtD;EAEA,OAAOgB,SAAS;AAClB;AAEA,IAAIE,OAAO,GAAG,aAAa,YAAY;EACrC,SAASA,OAAO,GAAG;IACjBlX,eAAe,CAAC,IAAI,EAAEkX,OAAO,CAAC;IAE9B,IAAI,CAACC,WAAW,GAAG,CAAC,CAAC;EACvB;EAEA1W,YAAY,CAACyW,OAAO,EAAE,CAAC;IACrBzZ,GAAG,EAAE,KAAK;IACVmD,KAAK,EAAE,SAASmI,GAAG,GAAG;MACpB,IAAIlK,KAAK,GAAG,IAAI;MAEhB,KAAK,IAAIuY,IAAI,GAAG/Z,SAAS,CAACC,MAAM,EAAE6Z,WAAW,GAAG,IAAItV,KAAK,CAACuV,IAAI,CAAC,EAAEC,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGD,IAAI,EAAEC,IAAI,EAAE,EAAE;QAC9FF,WAAW,CAACE,IAAI,CAAC,GAAGha,SAAS,CAACga,IAAI,CAAC;MACrC;MAEA,IAAIC,SAAS,GAAGH,WAAW,CAAC/X,MAAM,CAAC,IAAI,CAACmY,gBAAgB,EAAE,CAAC,CAAC,CAAC;MAC7D9a,MAAM,CAACD,IAAI,CAAC8a,SAAS,CAAC,CAAC9Z,OAAO,CAAC,UAAUC,GAAG,EAAE;QAC5CoB,KAAK,CAACsY,WAAW,CAAC1Z,GAAG,CAAC,GAAGP,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE2B,KAAK,CAACsY,WAAW,CAAC1Z,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE6Z,SAAS,CAAC7Z,GAAG,CAAC,CAAC;QACzGsV,WAAW,CAACtV,GAAG,EAAE6Z,SAAS,CAAC7Z,GAAG,CAAC,CAAC,CAAC,CAAC;;QAElC,IAAI+Z,UAAU,GAAGvQ,oBAAoB,CAACf,cAAc,CAAC,CAACzI,GAAG,CAAC;QAC1D,IAAI+Z,UAAU,EAAEzE,WAAW,CAACyE,UAAU,EAAEF,SAAS,CAAC7Z,GAAG,CAAC,CAAC;QACvDiX,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDjX,GAAG,EAAE,OAAO;IACZmD,KAAK,EAAE,SAAS6W,KAAK,GAAG;MACtB,IAAI,CAACN,WAAW,GAAG,CAAC,CAAC;IACvB;EACF,CAAC,EAAE;IACD1Z,GAAG,EAAE,kBAAkB;IACvBmD,KAAK,EAAE,SAAS2W,gBAAgB,CAACD,SAAS,EAAEI,UAAU,EAAE;MACtD,IAAIvE,UAAU,GAAGuE,UAAU,CAACvG,MAAM,IAAIuG,UAAU,CAACtG,QAAQ,IAAIsG,UAAU,CAACrG,IAAI,GAAG;QAC7E,CAAC,EAAEqG;MACL,CAAC,GAAGA,UAAU;MACdjb,MAAM,CAACD,IAAI,CAAC2W,UAAU,CAAC,CAACrK,GAAG,CAAC,UAAUrL,GAAG,EAAE;QACzC,IAAIka,eAAe,GAAGxE,UAAU,CAAC1V,GAAG,CAAC;UACjC0T,MAAM,GAAGwG,eAAe,CAACxG,MAAM;UAC/BC,QAAQ,GAAGuG,eAAe,CAACvG,QAAQ;UACnCC,IAAI,GAAGsG,eAAe,CAACtG,IAAI;QAC/B,IAAIwD,OAAO,GAAGxD,IAAI,CAAC,CAAC,CAAC;QACrB,IAAI,CAACiG,SAAS,CAACnG,MAAM,CAAC,EAAEmG,SAAS,CAACnG,MAAM,CAAC,GAAG,CAAC,CAAC;QAE9C,IAAI0D,OAAO,CAACvX,MAAM,GAAG,CAAC,EAAE;UACtBuX,OAAO,CAACrX,OAAO,CAAC,UAAUsX,KAAK,EAAE;YAC/B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;cAC7BwC,SAAS,CAACnG,MAAM,CAAC,CAAC2D,KAAK,CAAC,GAAGzD,IAAI;YACjC;UACF,CAAC,CAAC;QACJ;QAEAiG,SAAS,CAACnG,MAAM,CAAC,CAACC,QAAQ,CAAC,GAAGC,IAAI;MACpC,CAAC,CAAC;MACF,OAAOiG,SAAS;IAClB;EACF,CAAC,CAAC,CAAC;EAEH,OAAOJ,OAAO;AAChB,CAAC,EAAE;AAEH,IAAIU,QAAQ,GAAG,EAAE;AACjB,IAAIC,MAAM,GAAG,CAAC,CAAC;AACf,IAAIC,SAAS,GAAG,CAAC,CAAC;AAClB,IAAIC,mBAAmB,GAAGtb,MAAM,CAACD,IAAI,CAACsb,SAAS,CAAC;AAChD,SAASE,eAAe,CAACC,WAAW,EAAEpU,IAAI,EAAE;EAC1C,IAAI9F,GAAG,GAAG8F,IAAI,CAACqU,SAAS;EACxBN,QAAQ,GAAGK,WAAW;EACtBJ,MAAM,GAAG,CAAC,CAAC;EACXpb,MAAM,CAACD,IAAI,CAACsb,SAAS,CAAC,CAACta,OAAO,CAAC,UAAU2a,CAAC,EAAE;IAC1C,IAAIJ,mBAAmB,CAACnT,OAAO,CAACuT,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;MACzC,OAAOL,SAAS,CAACK,CAAC,CAAC;IACrB;EACF,CAAC,CAAC;EAEFP,QAAQ,CAACpa,OAAO,CAAC,UAAU4a,MAAM,EAAE;IACjC,IAAI3I,MAAM,GAAG2I,MAAM,CAAC3I,MAAM,GAAG2I,MAAM,CAAC3I,MAAM,EAAE,GAAG,CAAC,CAAC;IACjDhT,MAAM,CAACD,IAAI,CAACiT,MAAM,CAAC,CAACjS,OAAO,CAAC,UAAU6a,EAAE,EAAE;MACxC,IAAI,OAAO5I,MAAM,CAAC4I,EAAE,CAAC,KAAK,UAAU,EAAE;QACpCta,GAAG,CAACsa,EAAE,CAAC,GAAG5I,MAAM,CAAC4I,EAAE,CAAC;MACtB;MAEA,IAAIva,OAAO,CAAC2R,MAAM,CAAC4I,EAAE,CAAC,CAAC,KAAK,QAAQ,EAAE;QACpC5b,MAAM,CAACD,IAAI,CAACiT,MAAM,CAAC4I,EAAE,CAAC,CAAC,CAAC7a,OAAO,CAAC,UAAU8a,EAAE,EAAE;UAC5C,IAAI,CAACva,GAAG,CAACsa,EAAE,CAAC,EAAE;YACZta,GAAG,CAACsa,EAAE,CAAC,GAAG,CAAC,CAAC;UACd;UAEAta,GAAG,CAACsa,EAAE,CAAC,CAACC,EAAE,CAAC,GAAG7I,MAAM,CAAC4I,EAAE,CAAC,CAACC,EAAE,CAAC;QAC9B,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,IAAIF,MAAM,CAACzI,KAAK,EAAE;MAChB,IAAIA,KAAK,GAAGyI,MAAM,CAACzI,KAAK,EAAE;MAC1BlT,MAAM,CAACD,IAAI,CAACmT,KAAK,CAAC,CAACnS,OAAO,CAAC,UAAU+a,IAAI,EAAE;QACzC,IAAI,CAACV,MAAM,CAACU,IAAI,CAAC,EAAE;UACjBV,MAAM,CAACU,IAAI,CAAC,GAAG,EAAE;QACnB;QAEAV,MAAM,CAACU,IAAI,CAAC,CAACvb,IAAI,CAAC2S,KAAK,CAAC4I,IAAI,CAAC,CAAC;MAChC,CAAC,CAAC;IACJ;IAEA,IAAIH,MAAM,CAACI,QAAQ,EAAE;MACnBJ,MAAM,CAACI,QAAQ,CAACV,SAAS,CAAC;IAC5B;EACF,CAAC,CAAC;EAEF,OAAO/Z,GAAG;AACZ;AACA,SAAS0a,UAAU,CAACF,IAAI,EAAEG,WAAW,EAAE;EACrC,KAAK,IAAItB,IAAI,GAAG/Z,SAAS,CAACC,MAAM,EAAEwC,IAAI,GAAG,IAAI+B,KAAK,CAACuV,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEC,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGD,IAAI,EAAEC,IAAI,EAAE,EAAE;IAC1GvX,IAAI,CAACuX,IAAI,GAAG,CAAC,CAAC,GAAGha,SAAS,CAACga,IAAI,CAAC;EAClC;EAEA,IAAIsB,OAAO,GAAGd,MAAM,CAACU,IAAI,CAAC,IAAI,EAAE;EAChCI,OAAO,CAACnb,OAAO,CAAC,UAAUob,MAAM,EAAE;IAChCF,WAAW,GAAGE,MAAM,CAAC3b,KAAK,CAAC,IAAI,EAAE,CAACyb,WAAW,CAAC,CAACrQ,MAAM,CAACvI,IAAI,CAAC,CAAC,CAAC,CAAC;EAChE,CAAC,CAAC;;EACF,OAAO4Y,WAAW;AACpB;AACA,SAASG,SAAS,CAACN,IAAI,EAAE;EACvB,KAAK,IAAIO,KAAK,GAAGzb,SAAS,CAACC,MAAM,EAAEwC,IAAI,GAAG,IAAI+B,KAAK,CAACiX,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;IACjHjZ,IAAI,CAACiZ,KAAK,GAAG,CAAC,CAAC,GAAG1b,SAAS,CAAC0b,KAAK,CAAC;EACpC;EAEA,IAAIJ,OAAO,GAAGd,MAAM,CAACU,IAAI,CAAC,IAAI,EAAE;EAChCI,OAAO,CAACnb,OAAO,CAAC,UAAUob,MAAM,EAAE;IAChCA,MAAM,CAAC3b,KAAK,CAAC,IAAI,EAAE6C,IAAI,CAAC;EAC1B,CAAC,CAAC;EACF,OAAO+J,SAAS;AAClB;AACA,SAASmP,YAAY,GAAG;EACtB,IAAIT,IAAI,GAAGlb,SAAS,CAAC,CAAC,CAAC;EACvB,IAAIyC,IAAI,GAAG+B,KAAK,CAAC1D,SAAS,CAAC4B,KAAK,CAACL,IAAI,CAACrC,SAAS,EAAE,CAAC,CAAC;EACnD,OAAOya,SAAS,CAACS,IAAI,CAAC,GAAGT,SAAS,CAACS,IAAI,CAAC,CAACtb,KAAK,CAAC,IAAI,EAAE6C,IAAI,CAAC,GAAG+J,SAAS;AACxE;AAEA,SAASoP,kBAAkB,CAACC,UAAU,EAAE;EACtC,IAAIA,UAAU,CAAC/H,MAAM,KAAK,IAAI,EAAE;IAC9B+H,UAAU,CAAC/H,MAAM,GAAG,KAAK;EAC3B;EAEA,IAAIC,QAAQ,GAAG8H,UAAU,CAAC9H,QAAQ;EAClC,IAAID,MAAM,GAAG+H,UAAU,CAAC/H,MAAM,IAAI6E,sBAAsB,EAAE;EAC1D,IAAI,CAAC5E,QAAQ,EAAE;EACfA,QAAQ,GAAGuE,OAAO,CAACxE,MAAM,EAAEC,QAAQ,CAAC,IAAIA,QAAQ;EAChD,OAAOH,eAAe,CAACkI,OAAO,CAAChC,WAAW,EAAEhG,MAAM,EAAEC,QAAQ,CAAC,IAAIH,eAAe,CAACjB,SAAS,CAACvC,MAAM,EAAE0D,MAAM,EAAEC,QAAQ,CAAC;AACtH;AACA,IAAI+H,OAAO,GAAG,IAAIjC,OAAO,EAAE;AAC3B,IAAIkC,MAAM,GAAG,SAASA,MAAM,GAAG;EAC7BtO,MAAM,CAACX,cAAc,GAAG,KAAK;EAC7BW,MAAM,CAACP,gBAAgB,GAAG,KAAK;EAC/BsO,SAAS,CAAC,QAAQ,CAAC;AACrB,CAAC;AACD,IAAInJ,GAAG,GAAG;EACR2J,KAAK,EAAE,SAASA,KAAK,GAAG;IACtB,IAAIrG,MAAM,GAAG3V,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwM,SAAS,GAAGxM,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAEnF,IAAIiH,MAAM,EAAE;MACVuU,SAAS,CAAC,aAAa,EAAE7F,MAAM,CAAC;MAChCgG,YAAY,CAAC,oBAAoB,EAAEhG,MAAM,CAAC;MAC1C,OAAOgG,YAAY,CAAC,OAAO,EAAEhG,MAAM,CAAC;IACtC,CAAC,MAAM;MACL,OAAOsG,OAAO,CAACC,MAAM,CAAC,wCAAwC,CAAC;IACjE;EACF,CAAC;EACDC,KAAK,EAAE,SAASA,KAAK,GAAG;IACtB,IAAIxG,MAAM,GAAG3V,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwM,SAAS,GAAGxM,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACnF,IAAIoc,kBAAkB,GAAGzG,MAAM,CAACyG,kBAAkB;IAElD,IAAI3O,MAAM,CAACX,cAAc,KAAK,KAAK,EAAE;MACnCW,MAAM,CAACX,cAAc,GAAG,IAAI;IAC9B;IAEAW,MAAM,CAACP,gBAAgB,GAAG,IAAI;IAC9BiG,QAAQ,CAAC,YAAY;MACnBkJ,WAAW,CAAC;QACVD,kBAAkB,EAAEA;MACtB,CAAC,CAAC;MACFZ,SAAS,CAAC,OAAO,EAAE7F,MAAM,CAAC;IAC5B,CAAC,CAAC;EACJ;AACF,CAAC;AACD,IAAI2G,KAAK,GAAG;EACVtI,IAAI,EAAE,SAASA,IAAI,CAACuI,KAAK,EAAE;IACzB,IAAIA,KAAK,KAAK,IAAI,EAAE;MAClB,OAAO,IAAI;IACb;IAEA,IAAI9b,OAAO,CAAC8b,KAAK,CAAC,KAAK,QAAQ,IAAIA,KAAK,CAACzI,MAAM,IAAIyI,KAAK,CAACxI,QAAQ,EAAE;MACjE,OAAO;QACLD,MAAM,EAAEyI,KAAK,CAACzI,MAAM;QACpBC,QAAQ,EAAEuE,OAAO,CAACiE,KAAK,CAACzI,MAAM,EAAEyI,KAAK,CAACxI,QAAQ,CAAC,IAAIwI,KAAK,CAACxI;MAC3D,CAAC;IACH;IAEA,IAAIvP,KAAK,CAACC,OAAO,CAAC8X,KAAK,CAAC,IAAIA,KAAK,CAACtc,MAAM,KAAK,CAAC,EAAE;MAC9C,IAAI8T,QAAQ,GAAGwI,KAAK,CAAC,CAAC,CAAC,CAAChV,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAGgV,KAAK,CAAC,CAAC,CAAC,CAAC7Z,KAAK,CAAC,CAAC,CAAC,GAAG6Z,KAAK,CAAC,CAAC,CAAC;MAC3E,IAAIzI,MAAM,GAAGmE,kBAAkB,CAACsE,KAAK,CAAC,CAAC,CAAC,CAAC;MACzC,OAAO;QACLzI,MAAM,EAAEA,MAAM;QACdC,QAAQ,EAAEuE,OAAO,CAACxE,MAAM,EAAEC,QAAQ,CAAC,IAAIA;MACzC,CAAC;IACH;IAEA,IAAI,OAAOwI,KAAK,KAAK,QAAQ,KAAKA,KAAK,CAAChV,OAAO,CAAC,EAAE,CAACyD,MAAM,CAACyC,MAAM,CAACb,SAAS,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI2P,KAAK,CAACC,KAAK,CAAC/R,6BAA6B,CAAC,CAAC,EAAE;MACrI,IAAIgS,aAAa,GAAGvD,gBAAgB,CAACqD,KAAK,CAAC3M,KAAK,CAAC,GAAG,CAAC,EAAE;QACrDyJ,WAAW,EAAE;MACf,CAAC,CAAC;MACF,OAAO;QACLvF,MAAM,EAAE2I,aAAa,CAAC3I,MAAM,IAAI6E,sBAAsB,EAAE;QACxD5E,QAAQ,EAAEuE,OAAO,CAACmE,aAAa,CAAC3I,MAAM,EAAE2I,aAAa,CAAC1I,QAAQ,CAAC,IAAI0I,aAAa,CAAC1I;MACnF,CAAC;IACH;IAEA,IAAI,OAAOwI,KAAK,KAAK,QAAQ,EAAE;MAC7B,IAAIG,OAAO,GAAG/D,sBAAsB,EAAE;MAEtC,OAAO;QACL7E,MAAM,EAAE4I,OAAO;QACf3I,QAAQ,EAAEuE,OAAO,CAACoE,OAAO,EAAEH,KAAK,CAAC,IAAIA;MACvC,CAAC;IACH;EACF;AACF,CAAC;AACD,IAAII,GAAG,GAAG;EACRZ,MAAM,EAAEA,MAAM;EACdtO,MAAM,EAAEA,MAAM;EACd4E,GAAG,EAAEA,GAAG;EACRiK,KAAK,EAAEA,KAAK;EACZR,OAAO,EAAEA,OAAO;EAChBF,kBAAkB,EAAEA,kBAAkB;EACtCvI,MAAM,EAAEA;AACV,CAAC;AAED,IAAIgJ,WAAW,GAAG,SAASA,WAAW,GAAG;EACvC,IAAI1G,MAAM,GAAG3V,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwM,SAAS,GAAGxM,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACnF,IAAI4c,qBAAqB,GAAGjH,MAAM,CAACyG,kBAAkB;IACjDA,kBAAkB,GAAGQ,qBAAqB,KAAK,KAAK,CAAC,GAAG/V,QAAQ,GAAG+V,qBAAqB;EAC5F,IAAI,CAACxd,MAAM,CAACD,IAAI,CAACwT,SAAS,CAACvC,MAAM,CAAC,CAACnQ,MAAM,GAAG,CAAC,IAAIwN,MAAM,CAACkK,YAAY,KAAK1Q,MAAM,IAAIwG,MAAM,CAACX,cAAc,EAAE6P,GAAG,CAACtK,GAAG,CAAC2J,KAAK,CAAC;IACtHtM,IAAI,EAAE0M;EACR,CAAC,CAAC;AACJ,CAAC;AAED,SAASS,WAAW,CAACxQ,GAAG,EAAEyQ,eAAe,EAAE;EACzC1d,MAAM,CAACoB,cAAc,CAAC6L,GAAG,EAAE,UAAU,EAAE;IACrC3K,GAAG,EAAEob;EACP,CAAC,CAAC;EACF1d,MAAM,CAACoB,cAAc,CAAC6L,GAAG,EAAE,MAAM,EAAE;IACjC3K,GAAG,EAAE,SAASA,GAAG,GAAG;MAClB,OAAO2K,GAAG,CAAC0Q,QAAQ,CAACtR,GAAG,CAAC,UAAU2I,CAAC,EAAE;QACnC,OAAOf,MAAM,CAACe,CAAC,CAAC;MAClB,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACFhV,MAAM,CAACoB,cAAc,CAAC6L,GAAG,EAAE,MAAM,EAAE;IACjC3K,GAAG,EAAE,SAASA,GAAG,GAAG;MAClB,IAAI,CAACuF,MAAM,EAAE;MACb,IAAI+V,SAAS,GAAGnW,QAAQ,CAACQ,aAAa,CAAC,KAAK,CAAC;MAC7C2V,SAAS,CAACtO,SAAS,GAAGrC,GAAG,CAAC4Q,IAAI;MAC9B,OAAOD,SAAS,CAACtJ,QAAQ;IAC3B;EACF,CAAC,CAAC;EACF,OAAOrH,GAAG;AACZ;AAEA,SAAS6Q,MAAM,CAAE1W,IAAI,EAAE;EACrB,IAAIkN,QAAQ,GAAGlN,IAAI,CAACkN,QAAQ;IACxByJ,IAAI,GAAG3W,IAAI,CAAC2W,IAAI;IAChBC,IAAI,GAAG5W,IAAI,CAAC4W,IAAI;IAChBrN,UAAU,GAAGvJ,IAAI,CAACuJ,UAAU;IAC5BK,MAAM,GAAG5J,IAAI,CAAC4J,MAAM;IACpBG,SAAS,GAAG/J,IAAI,CAAC+J,SAAS;EAE9B,IAAID,qBAAqB,CAACC,SAAS,CAAC,IAAI4M,IAAI,CAACE,KAAK,IAAI,CAACD,IAAI,CAACC,KAAK,EAAE;IACjE,IAAIlM,KAAK,GAAGgM,IAAI,CAAChM,KAAK;MAClBE,MAAM,GAAG8L,IAAI,CAAC9L,MAAM;IACxB,IAAIiM,MAAM,GAAG;MACXrP,CAAC,EAAEkD,KAAK,GAAGE,MAAM,GAAG,CAAC;MACrBnD,CAAC,EAAE;IACL,CAAC;IACD6B,UAAU,CAAC,OAAO,CAAC,GAAGI,UAAU,CAACtQ,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEuQ,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;MAC9E,kBAAkB,EAAE,EAAE,CAACpF,MAAM,CAACsS,MAAM,CAACrP,CAAC,GAAGsC,SAAS,CAACtC,CAAC,GAAG,EAAE,EAAE,KAAK,CAAC,CAACjD,MAAM,CAACsS,MAAM,CAACpP,CAAC,GAAGqC,SAAS,CAACrC,CAAC,GAAG,EAAE,EAAE,IAAI;IAC5G,CAAC,CAAC,CAAC;EACL;EAEA,OAAO,CAAC;IACNqF,GAAG,EAAE,KAAK;IACVxD,UAAU,EAAEA,UAAU;IACtB2D,QAAQ,EAAEA;EACZ,CAAC,CAAC;AACJ;AAEA,SAAS6J,QAAQ,CAAE/W,IAAI,EAAE;EACvB,IAAIsN,MAAM,GAAGtN,IAAI,CAACsN,MAAM;IACpBC,QAAQ,GAAGvN,IAAI,CAACuN,QAAQ;IACxBL,QAAQ,GAAGlN,IAAI,CAACkN,QAAQ;IACxB3D,UAAU,GAAGvJ,IAAI,CAACuJ,UAAU;IAC5ByN,MAAM,GAAGhX,IAAI,CAACgX,MAAM;EACxB,IAAIpO,EAAE,GAAGoO,MAAM,KAAK,IAAI,GAAG,EAAE,CAACxS,MAAM,CAAC8I,MAAM,EAAE,GAAG,CAAC,CAAC9I,MAAM,CAACyC,MAAM,CAACb,SAAS,EAAE,GAAG,CAAC,CAAC5B,MAAM,CAAC+I,QAAQ,CAAC,GAAGyJ,MAAM;EACzG,OAAO,CAAC;IACNjK,GAAG,EAAE,KAAK;IACVxD,UAAU,EAAE;MACVvB,KAAK,EAAE;IACT,CAAC;IACDkF,QAAQ,EAAE,CAAC;MACTH,GAAG,EAAE,QAAQ;MACbxD,UAAU,EAAElQ,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEkQ,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;QAC7DX,EAAE,EAAEA;MACN,CAAC,CAAC;MACFsE,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,CAAC;AACJ;AAEA,SAAS+J,qBAAqB,CAAC9H,MAAM,EAAE;EACrC,IAAI+H,aAAa,GAAG/H,MAAM,CAACH,KAAK;IAC5B2H,IAAI,GAAGO,aAAa,CAACP,IAAI;IACzBC,IAAI,GAAGM,aAAa,CAACN,IAAI;IACzBtJ,MAAM,GAAG6B,MAAM,CAAC7B,MAAM;IACtBC,QAAQ,GAAG4B,MAAM,CAAC5B,QAAQ;IAC1BxD,SAAS,GAAGoF,MAAM,CAACpF,SAAS;IAC5BiN,MAAM,GAAG7H,MAAM,CAAC6H,MAAM;IACtBG,KAAK,GAAGhI,MAAM,CAACgI,KAAK;IACpBC,MAAM,GAAGjI,MAAM,CAACiI,MAAM;IACtBC,OAAO,GAAGlI,MAAM,CAACkI,OAAO;IACxB9I,KAAK,GAAGY,MAAM,CAACZ,KAAK;IACpB+I,iBAAiB,GAAGnI,MAAM,CAACoI,SAAS;IACpCA,SAAS,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,iBAAiB;EAExE,IAAItX,IAAI,GAAG4W,IAAI,CAACC,KAAK,GAAGD,IAAI,GAAGD,IAAI;IAC/BhM,KAAK,GAAG3K,IAAI,CAAC2K,KAAK;IAClBE,MAAM,GAAG7K,IAAI,CAAC6K,MAAM;EAExB,IAAI2M,cAAc,GAAGlK,MAAM,KAAK,KAAK;EACrC,IAAImK,SAAS,GAAG,CAACxQ,MAAM,CAACZ,gBAAgB,EAAEkH,QAAQ,GAAG,EAAE,CAAC/I,MAAM,CAACyC,MAAM,CAACb,SAAS,EAAE,GAAG,CAAC,CAAC5B,MAAM,CAAC+I,QAAQ,CAAC,GAAG,EAAE,CAAC,CAACxU,MAAM,CAAC,UAAU+U,CAAC,EAAE;IAC/H,OAAOS,KAAK,CAACmJ,OAAO,CAAC3W,OAAO,CAAC+M,CAAC,CAAC,KAAK,CAAC,CAAC;EACxC,CAAC,CAAC,CAAC/U,MAAM,CAAC,UAAU+U,CAAC,EAAE;IACrB,OAAOA,CAAC,KAAK,EAAE,IAAI,CAAC,CAACA,CAAC;EACxB,CAAC,CAAC,CAACtJ,MAAM,CAAC+J,KAAK,CAACmJ,OAAO,CAAC,CAACvK,IAAI,CAAC,GAAG,CAAC;EAClC,IAAIwK,OAAO,GAAG;IACZzK,QAAQ,EAAE,EAAE;IACZ3D,UAAU,EAAElQ,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEkV,KAAK,CAAChF,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;MACnE,aAAa,EAAE+D,MAAM;MACrB,WAAW,EAAEC,QAAQ;MACrB,OAAO,EAAEkK,SAAS;MAClB,MAAM,EAAElJ,KAAK,CAAChF,UAAU,CAACqO,IAAI,IAAI,KAAK;MACtC,OAAO,EAAE,4BAA4B;MACrC,SAAS,EAAE,MAAM,CAACpT,MAAM,CAACmG,KAAK,EAAE,GAAG,CAAC,CAACnG,MAAM,CAACqG,MAAM;IACpD,CAAC;EACH,CAAC;EACD,IAAIgN,sBAAsB,GAAGL,cAAc,IAAI,CAAC,CAACjJ,KAAK,CAACmJ,OAAO,CAAC3W,OAAO,CAAC,OAAO,CAAC,GAAG;IAChF4J,KAAK,EAAE,EAAE,CAACnG,MAAM,CAACmG,KAAK,GAAGE,MAAM,GAAG,EAAE,GAAG,MAAM,EAAE,IAAI;EACrD,CAAC,GAAG,CAAC,CAAC;EAEN,IAAI0M,SAAS,EAAE;IACbI,OAAO,CAACpO,UAAU,CAAC9H,aAAa,CAAC,GAAG,EAAE;EACxC;EAEA,IAAI0V,KAAK,EAAE;IACTQ,OAAO,CAACzK,QAAQ,CAAC/T,IAAI,CAAC;MACpB4T,GAAG,EAAE,OAAO;MACZxD,UAAU,EAAE;QACVX,EAAE,EAAE+O,OAAO,CAACpO,UAAU,CAAC,iBAAiB,CAAC,IAAI,QAAQ,CAAC/E,MAAM,CAAC6S,OAAO,IAAI1O,YAAY,EAAE;MACxF,CAAC;MACDuE,QAAQ,EAAE,CAACiK,KAAK;IAClB,CAAC,CAAC;IACF,OAAOQ,OAAO,CAACpO,UAAU,CAAC4N,KAAK;EACjC;EAEA,IAAIlb,IAAI,GAAG5C,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEse,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;IACzDrK,MAAM,EAAEA,MAAM;IACdC,QAAQ,EAAEA,QAAQ;IAClBoJ,IAAI,EAAEA,IAAI;IACVC,IAAI,EAAEA,IAAI;IACVQ,MAAM,EAAEA,MAAM;IACdrN,SAAS,EAAEA,SAAS;IACpBiN,MAAM,EAAEA,MAAM;IACdpN,MAAM,EAAEvQ,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEwe,sBAAsB,CAAC,EAAEtJ,KAAK,CAAC3E,MAAM;EACjF,CAAC,CAAC;EAEF,IAAI7D,KAAK,GAAG6Q,IAAI,CAACC,KAAK,IAAIF,IAAI,CAACE,KAAK,GAAG1B,YAAY,CAAC,sBAAsB,EAAElZ,IAAI,CAAC,IAAI;MACnFiR,QAAQ,EAAE,EAAE;MACZ3D,UAAU,EAAE,CAAC;IACf,CAAC,GAAG4L,YAAY,CAAC,sBAAsB,EAAElZ,IAAI,CAAC,IAAI;MAChDiR,QAAQ,EAAE,EAAE;MACZ3D,UAAU,EAAE,CAAC;IACf,CAAC;IACG2D,QAAQ,GAAGnH,KAAK,CAACmH,QAAQ;IACzB3D,UAAU,GAAGxD,KAAK,CAACwD,UAAU;EAEjCtN,IAAI,CAACiR,QAAQ,GAAGA,QAAQ;EACxBjR,IAAI,CAACsN,UAAU,GAAGA,UAAU;EAE5B,IAAIyN,MAAM,EAAE;IACV,OAAOD,QAAQ,CAAC9a,IAAI,CAAC;EACvB,CAAC,MAAM;IACL,OAAOya,MAAM,CAACza,IAAI,CAAC;EACrB;AACF;AACA,SAAS6b,sBAAsB,CAAC3I,MAAM,EAAE;EACtC,IAAIwI,OAAO,GAAGxI,MAAM,CAACwI,OAAO;IACxBhN,KAAK,GAAGwE,MAAM,CAACxE,KAAK;IACpBE,MAAM,GAAGsE,MAAM,CAACtE,MAAM;IACtBd,SAAS,GAAGoF,MAAM,CAACpF,SAAS;IAC5BoN,KAAK,GAAGhI,MAAM,CAACgI,KAAK;IACpB5I,KAAK,GAAGY,MAAM,CAACZ,KAAK;IACpBwJ,kBAAkB,GAAG5I,MAAM,CAACoI,SAAS;IACrCA,SAAS,GAAGQ,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,kBAAkB;EAE1E,IAAIxO,UAAU,GAAGlQ,cAAc,CAACA,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEkV,KAAK,CAAChF,UAAU,CAAC,EAAE4N,KAAK,GAAG;IAC3F,OAAO,EAAEA;EACX,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IACX,OAAO,EAAE5I,KAAK,CAACmJ,OAAO,CAACvK,IAAI,CAAC,GAAG;EACjC,CAAC,CAAC;EAEF,IAAIoK,SAAS,EAAE;IACbhO,UAAU,CAAC9H,aAAa,CAAC,GAAG,EAAE;EAChC;EAEA,IAAImI,MAAM,GAAGvQ,cAAc,CAAC,CAAC,CAAC,EAAEkV,KAAK,CAAC3E,MAAM,CAAC;EAE7C,IAAIE,qBAAqB,CAACC,SAAS,CAAC,EAAE;IACpCH,MAAM,CAAC,WAAW,CAAC,GAAGa,eAAe,CAAC;MACpCV,SAAS,EAAEA,SAAS;MACpBgB,aAAa,EAAE,IAAI;MACnBJ,KAAK,EAAEA,KAAK;MACZE,MAAM,EAAEA;IACV,CAAC,CAAC;IACFjB,MAAM,CAAC,mBAAmB,CAAC,GAAGA,MAAM,CAAC,WAAW,CAAC;EACnD;EAEA,IAAIoO,WAAW,GAAGrO,UAAU,CAACC,MAAM,CAAC;EAEpC,IAAIoO,WAAW,CAACve,MAAM,GAAG,CAAC,EAAE;IAC1B8P,UAAU,CAAC,OAAO,CAAC,GAAGyO,WAAW;EACnC;EAEA,IAAInS,GAAG,GAAG,EAAE;EACZA,GAAG,CAAC1M,IAAI,CAAC;IACP4T,GAAG,EAAE,MAAM;IACXxD,UAAU,EAAEA,UAAU;IACtB2D,QAAQ,EAAE,CAACyK,OAAO;EACpB,CAAC,CAAC;EAEF,IAAIR,KAAK,EAAE;IACTtR,GAAG,CAAC1M,IAAI,CAAC;MACP4T,GAAG,EAAE,MAAM;MACXxD,UAAU,EAAE;QACV0O,KAAK,EAAE;MACT,CAAC;MACD/K,QAAQ,EAAE,CAACiK,KAAK;IAClB,CAAC,CAAC;EACJ;EAEA,OAAOtR,GAAG;AACZ;AACA,SAASqS,yBAAyB,CAAC/I,MAAM,EAAE;EACzC,IAAIwI,OAAO,GAAGxI,MAAM,CAACwI,OAAO;IACxBR,KAAK,GAAGhI,MAAM,CAACgI,KAAK;IACpB5I,KAAK,GAAGY,MAAM,CAACZ,KAAK;EAExB,IAAIhF,UAAU,GAAGlQ,cAAc,CAACA,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEkV,KAAK,CAAChF,UAAU,CAAC,EAAE4N,KAAK,GAAG;IAC3F,OAAO,EAAEA;EACX,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IACX,OAAO,EAAE5I,KAAK,CAACmJ,OAAO,CAACvK,IAAI,CAAC,GAAG;EACjC,CAAC,CAAC;EAEF,IAAI6K,WAAW,GAAGrO,UAAU,CAAC4E,KAAK,CAAC3E,MAAM,CAAC;EAE1C,IAAIoO,WAAW,CAACve,MAAM,GAAG,CAAC,EAAE;IAC1B8P,UAAU,CAAC,OAAO,CAAC,GAAGyO,WAAW;EACnC;EAEA,IAAInS,GAAG,GAAG,EAAE;EACZA,GAAG,CAAC1M,IAAI,CAAC;IACP4T,GAAG,EAAE,MAAM;IACXxD,UAAU,EAAEA,UAAU;IACtB2D,QAAQ,EAAE,CAACyK,OAAO;EACpB,CAAC,CAAC;EAEF,IAAIR,KAAK,EAAE;IACTtR,GAAG,CAAC1M,IAAI,CAAC;MACP4T,GAAG,EAAE,MAAM;MACXxD,UAAU,EAAE;QACV0O,KAAK,EAAE;MACT,CAAC;MACD/K,QAAQ,EAAE,CAACiK,KAAK;IAClB,CAAC,CAAC;EACJ;EAEA,OAAOtR,GAAG;AACZ;AAEA,IAAIsS,QAAQ,GAAGhM,SAAS,CAACvC,MAAM;AAC/B,SAASwO,WAAW,CAAC5K,IAAI,EAAE;EACzB,IAAI7C,KAAK,GAAG6C,IAAI,CAAC,CAAC,CAAC;EACnB,IAAI3C,MAAM,GAAG2C,IAAI,CAAC,CAAC,CAAC;EAEpB,IAAI6K,WAAW,GAAG7K,IAAI,CAACtR,KAAK,CAAC,CAAC,CAAC;IAC3Boc,YAAY,GAAGhb,cAAc,CAAC+a,WAAW,EAAE,CAAC,CAAC;IAC7CE,UAAU,GAAGD,YAAY,CAAC,CAAC,CAAC;EAEhC,IAAI7S,OAAO,GAAG,IAAI;EAElB,IAAIzH,KAAK,CAACC,OAAO,CAACsa,UAAU,CAAC,EAAE;IAC7B9S,OAAO,GAAG;MACRsH,GAAG,EAAE,GAAG;MACRxD,UAAU,EAAE;QACV0O,KAAK,EAAE,EAAE,CAACzT,MAAM,CAACyC,MAAM,CAACb,SAAS,EAAE,GAAG,CAAC,CAAC5B,MAAM,CAACE,eAAe,CAACC,KAAK;MACtE,CAAC;MACDuI,QAAQ,EAAE,CAAC;QACTH,GAAG,EAAE,MAAM;QACXxD,UAAU,EAAE;UACV0O,KAAK,EAAE,EAAE,CAACzT,MAAM,CAACyC,MAAM,CAACb,SAAS,EAAE,GAAG,CAAC,CAAC5B,MAAM,CAACE,eAAe,CAACI,SAAS,CAAC;UACzE0T,IAAI,EAAE,cAAc;UACpBlR,CAAC,EAAEiR,UAAU,CAAC,CAAC;QACjB;MACF,CAAC,EAAE;QACDxL,GAAG,EAAE,MAAM;QACXxD,UAAU,EAAE;UACV0O,KAAK,EAAE,EAAE,CAACzT,MAAM,CAACyC,MAAM,CAACb,SAAS,EAAE,GAAG,CAAC,CAAC5B,MAAM,CAACE,eAAe,CAACG,OAAO,CAAC;UACvE2T,IAAI,EAAE,cAAc;UACpBlR,CAAC,EAAEiR,UAAU,CAAC,CAAC;QACjB;MACF,CAAC;IACH,CAAC;EACH,CAAC,MAAM;IACL9S,OAAO,GAAG;MACRsH,GAAG,EAAE,MAAM;MACXxD,UAAU,EAAE;QACViP,IAAI,EAAE,cAAc;QACpBlR,CAAC,EAAEiR;MACL;IACF,CAAC;EACH;EAEA,OAAO;IACL1B,KAAK,EAAE,IAAI;IACXlM,KAAK,EAAEA,KAAK;IACZE,MAAM,EAAEA,MAAM;IACd2C,IAAI,EAAE/H;EACR,CAAC;AACH;AACA,IAAIgT,0BAA0B,GAAG;EAC/B5B,KAAK,EAAE,KAAK;EACZlM,KAAK,EAAE,GAAG;EACVE,MAAM,EAAE;AACV,CAAC;AAED,SAAS6N,kBAAkB,CAACnL,QAAQ,EAAED,MAAM,EAAE;EAC5C,IAAI,CAACrL,UAAU,IAAI,CAACgF,MAAM,CAACH,gBAAgB,IAAIyG,QAAQ,EAAE;IACvDoL,OAAO,CAACC,KAAK,CAAC,mBAAmB,CAACpU,MAAM,CAAC+I,QAAQ,EAAE,kBAAkB,CAAC,CAAC/I,MAAM,CAAC8I,MAAM,EAAE,gBAAgB,CAAC,CAAC;EAC1G;AACF;AAEA,SAASuL,QAAQ,CAACtL,QAAQ,EAAED,MAAM,EAAE;EAClC,IAAIyF,WAAW,GAAGzF,MAAM;EAExB,IAAIA,MAAM,KAAK,IAAI,IAAIrG,MAAM,CAACf,YAAY,KAAK,IAAI,EAAE;IACnDoH,MAAM,GAAG6E,sBAAsB,EAAE;EACnC;EAEA,OAAO,IAAIsD,OAAO,CAAC,UAAUqD,OAAO,EAAEpD,MAAM,EAAE;IAC5C,IAAI7P,GAAG,GAAG;MACRgR,KAAK,EAAE,KAAK;MACZlM,KAAK,EAAE,GAAG;MACVE,MAAM,EAAE,GAAG;MACX2C,IAAI,EAAE2H,YAAY,CAAC,qBAAqB,CAAC,IAAI,CAAC;IAChD,CAAC;IAED,IAAIpC,WAAW,KAAK,IAAI,EAAE;MACxB,IAAI1B,IAAI,GAAGU,SAAS,CAACxE,QAAQ,CAAC,IAAI,CAAC,CAAC;MACpCA,QAAQ,GAAG8D,IAAI,CAAC9D,QAAQ,IAAIA,QAAQ;MACpCD,MAAM,GAAG+D,IAAI,CAAC/D,MAAM,IAAIA,MAAM;IAChC;IAEA,IAAIC,QAAQ,IAAID,MAAM,IAAI6K,QAAQ,CAAC7K,MAAM,CAAC,IAAI6K,QAAQ,CAAC7K,MAAM,CAAC,CAACC,QAAQ,CAAC,EAAE;MACxE,IAAIC,IAAI,GAAG2K,QAAQ,CAAC7K,MAAM,CAAC,CAACC,QAAQ,CAAC;MACrC,OAAOuL,OAAO,CAACV,WAAW,CAAC5K,IAAI,CAAC,CAAC;IACnC;IAEAkL,kBAAkB,CAACnL,QAAQ,EAAED,MAAM,CAAC;IACpCwL,OAAO,CAACzf,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEof,0BAA0B,CAAC,EAAE,CAAC,CAAC,EAAE;MACzEjL,IAAI,EAAEvG,MAAM,CAACH,gBAAgB,IAAIyG,QAAQ,GAAG4H,YAAY,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;IAC3F,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ;AAEA,IAAI4D,MAAM,GAAG,SAAS3Z,IAAI,GAAG,CAAC,CAAC;AAE/B,IAAIjC,CAAC,GAAG8J,MAAM,CAACJ,kBAAkB,IAAItG,WAAW,IAAIA,WAAW,CAACd,IAAI,IAAIc,WAAW,CAACb,OAAO,GAAGa,WAAW,GAAG;EAC1Gd,IAAI,EAAEsZ,MAAM;EACZrZ,OAAO,EAAEqZ;AACX,CAAC;AACD,IAAIC,QAAQ,GAAG,cAAc;AAE7B,IAAIC,KAAK,GAAG,SAASA,KAAK,CAACzd,IAAI,EAAE;EAC/B2B,CAAC,CAACsC,IAAI,CAAC,EAAE,CAAC+E,MAAM,CAACwU,QAAQ,EAAE,GAAG,CAAC,CAACxU,MAAM,CAAChJ,IAAI,EAAE,SAAS,CAAC,CAAC;EACxD,OAAO,YAAY;IACjB,OAAO0d,GAAG,CAAC1d,IAAI,CAAC;EAClB,CAAC;AACH,CAAC;AAED,IAAI0d,GAAG,GAAG,SAASA,GAAG,CAAC1d,IAAI,EAAE;EAC3B2B,CAAC,CAACsC,IAAI,CAAC,EAAE,CAAC+E,MAAM,CAACwU,QAAQ,EAAE,GAAG,CAAC,CAACxU,MAAM,CAAChJ,IAAI,EAAE,OAAO,CAAC,CAAC;EACtD2B,CAAC,CAACuC,OAAO,CAAC,EAAE,CAAC8E,MAAM,CAACwU,QAAQ,EAAE,GAAG,CAAC,CAACxU,MAAM,CAAChJ,IAAI,CAAC,EAAE,EAAE,CAACgJ,MAAM,CAACwU,QAAQ,EAAE,GAAG,CAAC,CAACxU,MAAM,CAAChJ,IAAI,EAAE,SAAS,CAAC,EAAE,EAAE,CAACgJ,MAAM,CAACwU,QAAQ,EAAE,GAAG,CAAC,CAACxU,MAAM,CAAChJ,IAAI,EAAE,OAAO,CAAC,CAAC;AACpJ,CAAC;AAED,IAAI2d,IAAI,GAAG;EACTF,KAAK,EAAEA,KAAK;EACZC,GAAG,EAAEA;AACP,CAAC;AAED,IAAIE,MAAM,GAAG,SAASha,IAAI,GAAG,CAAC,CAAC;AAE/B,SAASia,SAAS,CAACnQ,IAAI,EAAE;EACvB,IAAIsM,KAAK,GAAGtM,IAAI,CAACvD,YAAY,GAAGuD,IAAI,CAACvD,YAAY,CAAClE,aAAa,CAAC,GAAG,IAAI;EACvE,OAAO,OAAO+T,KAAK,KAAK,QAAQ;AAClC;AAEA,SAAS8D,gBAAgB,CAACpQ,IAAI,EAAE;EAC9B,IAAIoE,MAAM,GAAGpE,IAAI,CAACvD,YAAY,GAAGuD,IAAI,CAACvD,YAAY,CAAC/D,WAAW,CAAC,GAAG,IAAI;EACtE,IAAI4L,IAAI,GAAGtE,IAAI,CAACvD,YAAY,GAAGuD,IAAI,CAACvD,YAAY,CAAC9D,SAAS,CAAC,GAAG,IAAI;EAClE,OAAOyL,MAAM,IAAIE,IAAI;AACvB;AAEA,SAAS+L,eAAe,CAACrQ,IAAI,EAAE;EAC7B,OAAOA,IAAI,IAAIA,IAAI,CAACC,SAAS,IAAID,IAAI,CAACC,SAAS,CAACqQ,QAAQ,IAAItQ,IAAI,CAACC,SAAS,CAACqQ,QAAQ,CAACvS,MAAM,CAACZ,gBAAgB,CAAC;AAC9G;AAEA,SAASoT,UAAU,GAAG;EACpB,IAAIxS,MAAM,CAACX,cAAc,KAAK,IAAI,EAAE;IAClC,OAAOoT,QAAQ,CAAC5d,OAAO;EACzB;EAEA,IAAI6d,OAAO,GAAGD,QAAQ,CAACzS,MAAM,CAACX,cAAc,CAAC;EAC7C,OAAOqT,OAAO,IAAID,QAAQ,CAAC5d,OAAO;AACpC;AAEA,SAAS8d,eAAe,CAAC7M,GAAG,EAAE;EAC5B,OAAO1M,QAAQ,CAACuZ,eAAe,CAAC,4BAA4B,EAAE7M,GAAG,CAAC;AACpE;AAEA,SAASlM,aAAa,CAACkM,GAAG,EAAE;EAC1B,OAAO1M,QAAQ,CAACQ,aAAa,CAACkM,GAAG,CAAC;AACpC;AAEA,SAAS8M,UAAU,CAACC,WAAW,EAAE;EAC/B,IAAI3K,MAAM,GAAG3V,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwM,SAAS,GAAGxM,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACnF,IAAIugB,YAAY,GAAG5K,MAAM,CAAC6K,IAAI;IAC1BA,IAAI,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAGD,WAAW,CAAC/M,GAAG,KAAK,KAAK,GAAG6M,eAAe,GAAG/Y,aAAa,GAAGkZ,YAAY;EAE/G,IAAI,OAAOD,WAAW,KAAK,QAAQ,EAAE;IACnC,OAAOzZ,QAAQ,CAAC4Z,cAAc,CAACH,WAAW,CAAC;EAC7C;EAEA,IAAI/M,GAAG,GAAGiN,IAAI,CAACF,WAAW,CAAC/M,GAAG,CAAC;EAC/BnU,MAAM,CAACD,IAAI,CAACmhB,WAAW,CAACvQ,UAAU,IAAI,EAAE,CAAC,CAAC5P,OAAO,CAAC,UAAUC,GAAG,EAAE;IAC/DmT,GAAG,CAAC9E,YAAY,CAACrO,GAAG,EAAEkgB,WAAW,CAACvQ,UAAU,CAAC3P,GAAG,CAAC,CAAC;EACpD,CAAC,CAAC;EACF,IAAIsT,QAAQ,GAAG4M,WAAW,CAAC5M,QAAQ,IAAI,EAAE;EACzCA,QAAQ,CAACvT,OAAO,CAAC,UAAU2O,KAAK,EAAE;IAChCyE,GAAG,CAACmN,WAAW,CAACL,UAAU,CAACvR,KAAK,EAAE;MAChC0R,IAAI,EAAEA;IACR,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EACF,OAAOjN,GAAG;AACZ;AAEA,SAASoN,aAAa,CAACjR,IAAI,EAAE;EAC3B,IAAIkR,OAAO,GAAG,GAAG,CAAC5V,MAAM,CAAC0E,IAAI,CAACmR,SAAS,EAAE,GAAG,CAAC;EAC7C;;EAEAD,OAAO,GAAG,EAAE,CAAC5V,MAAM,CAAC4V,OAAO,EAAE,+BAA+B,CAAC;EAC7D;;EAEA,OAAOA,OAAO;AAChB;AAEA,IAAIV,QAAQ,GAAG;EACb5d,OAAO,EAAE,SAASA,OAAO,CAACwe,QAAQ,EAAE;IAClC,IAAIpR,IAAI,GAAGoR,QAAQ,CAAC,CAAC,CAAC;IAEtB,IAAIpR,IAAI,CAACqR,UAAU,EAAE;MACnBD,QAAQ,CAAC,CAAC,CAAC,CAAC3gB,OAAO,CAAC,UAAU6gB,SAAS,EAAE;QACvCtR,IAAI,CAACqR,UAAU,CAAC9R,YAAY,CAACoR,UAAU,CAACW,SAAS,CAAC,EAAEtR,IAAI,CAAC;MAC3D,CAAC,CAAC;MAEF,IAAIA,IAAI,CAACvD,YAAY,CAAClE,aAAa,CAAC,KAAK,IAAI,IAAIwF,MAAM,CAACL,kBAAkB,EAAE;QAC1E,IAAIwT,OAAO,GAAG/Z,QAAQ,CAACoa,aAAa,CAACN,aAAa,CAACjR,IAAI,CAAC,CAAC;QACzDA,IAAI,CAACqR,UAAU,CAACG,YAAY,CAACN,OAAO,EAAElR,IAAI,CAAC;MAC7C,CAAC,MAAM;QACLA,IAAI,CAACyR,MAAM,EAAE;MACf;IACF;EACF,CAAC;EACDC,IAAI,EAAE,SAASA,IAAI,CAACN,QAAQ,EAAE;IAC5B,IAAIpR,IAAI,GAAGoR,QAAQ,CAAC,CAAC,CAAC;IACtB,IAAIO,UAAU,GAAGP,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B;;IAEA,IAAI,CAACrR,UAAU,CAACC,IAAI,CAAC,CAACnI,OAAO,CAACkG,MAAM,CAACZ,gBAAgB,CAAC,EAAE;MACtD,OAAOqT,QAAQ,CAAC5d,OAAO,CAACwe,QAAQ,CAAC;IACnC;IAEA,IAAIQ,MAAM,GAAG,IAAIlgB,MAAM,CAAC,EAAE,CAAC4J,MAAM,CAACyC,MAAM,CAACb,SAAS,EAAE,KAAK,CAAC,CAAC;IAC3D,OAAOyU,UAAU,CAAC,CAAC,CAAC,CAACtR,UAAU,CAACX,EAAE;IAElC,IAAIiS,UAAU,CAAC,CAAC,CAAC,CAACtR,UAAU,CAAC0O,KAAK,EAAE;MAClC,IAAI8C,YAAY,GAAGF,UAAU,CAAC,CAAC,CAAC,CAACtR,UAAU,CAAC0O,KAAK,CAAC7O,KAAK,CAAC,GAAG,CAAC,CAAC7N,MAAM,CAAC,UAAUiO,GAAG,EAAEmH,GAAG,EAAE;QACtF,IAAIA,GAAG,KAAK1J,MAAM,CAACZ,gBAAgB,IAAIsK,GAAG,CAACqF,KAAK,CAAC8E,MAAM,CAAC,EAAE;UACxDtR,GAAG,CAACwR,KAAK,CAAC7hB,IAAI,CAACwX,GAAG,CAAC;QACrB,CAAC,MAAM;UACLnH,GAAG,CAACyR,MAAM,CAAC9hB,IAAI,CAACwX,GAAG,CAAC;QACtB;QAEA,OAAOnH,GAAG;MACZ,CAAC,EAAE;QACDyR,MAAM,EAAE,EAAE;QACVD,KAAK,EAAE;MACT,CAAC,CAAC;MAEFH,UAAU,CAAC,CAAC,CAAC,CAACtR,UAAU,CAAC0O,KAAK,GAAG8C,YAAY,CAACC,KAAK,CAAC7N,IAAI,CAAC,GAAG,CAAC;MAE7D,IAAI4N,YAAY,CAACE,MAAM,CAACxhB,MAAM,KAAK,CAAC,EAAE;QACpCyP,IAAI,CAACgS,eAAe,CAAC,OAAO,CAAC;MAC/B,CAAC,MAAM;QACLhS,IAAI,CAACjB,YAAY,CAAC,OAAO,EAAE8S,YAAY,CAACE,MAAM,CAAC9N,IAAI,CAAC,GAAG,CAAC,CAAC;MAC3D;IACF;IAEA,IAAIgO,YAAY,GAAGN,UAAU,CAAC5V,GAAG,CAAC,UAAU2I,CAAC,EAAE;MAC7C,OAAOf,MAAM,CAACe,CAAC,CAAC;IAClB,CAAC,CAAC,CAACT,IAAI,CAAC,IAAI,CAAC;IAEbjE,IAAI,CAACjB,YAAY,CAACxG,aAAa,EAAE,EAAE,CAAC;IACpCyH,IAAI,CAAChB,SAAS,GAAGiT,YAAY;EAC/B;AACF,CAAC;AAED,SAASC,oBAAoB,CAACC,EAAE,EAAE;EAChCA,EAAE,EAAE;AACN;AAEA,SAASC,OAAO,CAACC,SAAS,EAAEC,QAAQ,EAAE;EACpC,IAAIC,gBAAgB,GAAG,OAAOD,QAAQ,KAAK,UAAU,GAAGA,QAAQ,GAAGpC,MAAM;EAEzE,IAAImC,SAAS,CAAC9hB,MAAM,KAAK,CAAC,EAAE;IAC1BgiB,gBAAgB,EAAE;EACpB,CAAC,MAAM;IACL,IAAIC,KAAK,GAAGN,oBAAoB;IAEhC,IAAInU,MAAM,CAACN,cAAc,KAAK5E,uBAAuB,EAAE;MACrD2Z,KAAK,GAAGtb,MAAM,CAACub,qBAAqB,IAAIP,oBAAoB;IAC9D;IAEAM,KAAK,CAAC,YAAY;MAChB,IAAI/B,OAAO,GAAGF,UAAU,EAAE;MAC1B,IAAIha,IAAI,GAAG0Z,IAAI,CAACF,KAAK,CAAC,QAAQ,CAAC;MAC/BsC,SAAS,CAACtW,GAAG,CAAC0U,OAAO,CAAC;MACtBla,IAAI,EAAE;MACNgc,gBAAgB,EAAE;IACpB,CAAC,CAAC;EACJ;AACF;AACA,IAAIG,QAAQ,GAAG,KAAK;AACpB,SAASC,kBAAkB,GAAG;EAC5BD,QAAQ,GAAG,IAAI;AACjB;AACA,SAASE,iBAAiB,GAAG;EAC3BF,QAAQ,GAAG,KAAK;AAClB;AACA,IAAIG,EAAE,GAAG,IAAI;AACb,SAASC,OAAO,CAACC,OAAO,EAAE;EACxB,IAAI,CAAC3b,iBAAiB,EAAE;IACtB;EACF;EAEA,IAAI,CAAC2G,MAAM,CAACP,gBAAgB,EAAE;IAC5B;EACF;EAEA,IAAIwV,qBAAqB,GAAGD,OAAO,CAACE,YAAY;IAC5CA,YAAY,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG9C,MAAM,GAAG8C,qBAAqB;IAChFE,qBAAqB,GAAGH,OAAO,CAACI,YAAY;IAC5CA,YAAY,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAGhD,MAAM,GAAGgD,qBAAqB;IAChFE,qBAAqB,GAAGL,OAAO,CAACM,sBAAsB;IACtDA,sBAAsB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAGlD,MAAM,GAAGkD,qBAAqB;IAC1FE,qBAAqB,GAAGP,OAAO,CAACQ,oBAAoB;IACpDA,oBAAoB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAGnc,QAAQ,GAAGmc,qBAAqB;EAC9FT,EAAE,GAAG,IAAIzb,iBAAiB,CAAC,UAAUoc,OAAO,EAAE;IAC5C,IAAId,QAAQ,EAAE;IACd,IAAIe,aAAa,GAAGxK,sBAAsB,EAAE;IAC5CpJ,OAAO,CAAC2T,OAAO,CAAC,CAAC/iB,OAAO,CAAC,UAAUijB,cAAc,EAAE;MACjD,IAAIA,cAAc,CAACC,IAAI,KAAK,WAAW,IAAID,cAAc,CAACE,UAAU,CAACrjB,MAAM,GAAG,CAAC,IAAI,CAAC4f,SAAS,CAACuD,cAAc,CAACE,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3H,IAAI7V,MAAM,CAACR,oBAAoB,EAAE;UAC/B8V,sBAAsB,CAACK,cAAc,CAACtjB,MAAM,CAAC;QAC/C;QAEA6iB,YAAY,CAACS,cAAc,CAACtjB,MAAM,CAAC;MACrC;MAEA,IAAIsjB,cAAc,CAACC,IAAI,KAAK,YAAY,IAAID,cAAc,CAACtjB,MAAM,CAACihB,UAAU,IAAItT,MAAM,CAACR,oBAAoB,EAAE;QAC3G8V,sBAAsB,CAACK,cAAc,CAACtjB,MAAM,CAACihB,UAAU,CAAC;MAC1D;MAEA,IAAIqC,cAAc,CAACC,IAAI,KAAK,YAAY,IAAIxD,SAAS,CAACuD,cAAc,CAACtjB,MAAM,CAAC,IAAI,CAACmL,+BAA+B,CAAC1D,OAAO,CAAC6b,cAAc,CAACnT,aAAa,CAAC,EAAE;QACtJ,IAAImT,cAAc,CAACnT,aAAa,KAAK,OAAO,IAAI6P,gBAAgB,CAACsD,cAAc,CAACtjB,MAAM,CAAC,EAAE;UACvF,IAAIyjB,iBAAiB,GAAGrK,gBAAgB,CAACzJ,UAAU,CAAC2T,cAAc,CAACtjB,MAAM,CAAC,CAAC;YACvEgU,MAAM,GAAGyP,iBAAiB,CAACzP,MAAM;YACjCC,QAAQ,GAAGwP,iBAAiB,CAACxP,QAAQ;UAEzCqP,cAAc,CAACtjB,MAAM,CAAC2O,YAAY,CAACrG,WAAW,EAAE0L,MAAM,IAAIqP,aAAa,CAAC;UACxE,IAAIpP,QAAQ,EAAEqP,cAAc,CAACtjB,MAAM,CAAC2O,YAAY,CAACpG,SAAS,EAAE0L,QAAQ,CAAC;QACvE,CAAC,MAAM,IAAIgM,eAAe,CAACqD,cAAc,CAACtjB,MAAM,CAAC,EAAE;UACjD+iB,YAAY,CAACO,cAAc,CAACtjB,MAAM,CAAC;QACrC;MACF;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,IAAI,CAACmH,MAAM,EAAE;EACbsb,EAAE,CAACC,OAAO,CAACS,oBAAoB,EAAE;IAC/BO,SAAS,EAAE,IAAI;IACfzT,UAAU,EAAE,IAAI;IAChB0T,aAAa,EAAE,IAAI;IACnBC,OAAO,EAAE;EACX,CAAC,CAAC;AACJ;AACA,SAASC,UAAU,GAAG;EACpB,IAAI,CAACpB,EAAE,EAAE;EACTA,EAAE,CAACoB,UAAU,EAAE;AACjB;AAEA,SAASC,WAAW,CAAElU,IAAI,EAAE;EAC1B,IAAIlB,KAAK,GAAGkB,IAAI,CAACvD,YAAY,CAAC,OAAO,CAAC;EACtC,IAAIE,GAAG,GAAG,EAAE;EAEZ,IAAImC,KAAK,EAAE;IACTnC,GAAG,GAAGmC,KAAK,CAACoB,KAAK,CAAC,GAAG,CAAC,CAAC7N,MAAM,CAAC,UAAUiO,GAAG,EAAExB,KAAK,EAAE;MAClD,IAAI4B,MAAM,GAAG5B,KAAK,CAACoB,KAAK,CAAC,GAAG,CAAC;MAC7B,IAAI1G,IAAI,GAAGkH,MAAM,CAAC,CAAC,CAAC;MACpB,IAAI7M,KAAK,GAAG6M,MAAM,CAAC1N,KAAK,CAAC,CAAC,CAAC;MAE3B,IAAIwG,IAAI,IAAI3F,KAAK,CAACtD,MAAM,GAAG,CAAC,EAAE;QAC5B+P,GAAG,CAAC9G,IAAI,CAAC,GAAG3F,KAAK,CAACoQ,IAAI,CAAC,GAAG,CAAC,CAACzD,IAAI,EAAE;MACpC;MAEA,OAAOF,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EACR;EAEA,OAAO3D,GAAG;AACZ;AAEA,SAASwX,WAAW,CAAEnU,IAAI,EAAE;EAC1B,IAAIoU,cAAc,GAAGpU,IAAI,CAACvD,YAAY,CAAC,aAAa,CAAC;EACrD,IAAI4X,gBAAgB,GAAGrU,IAAI,CAACvD,YAAY,CAAC,WAAW,CAAC;EACrD,IAAI6X,SAAS,GAAGtU,IAAI,CAACsU,SAAS,KAAKxX,SAAS,GAAGkD,IAAI,CAACsU,SAAS,CAAC9T,IAAI,EAAE,GAAG,EAAE;EACzE,IAAI7D,GAAG,GAAG6M,gBAAgB,CAACzJ,UAAU,CAACC,IAAI,CAAC,CAAC;EAE5C,IAAI,CAACrD,GAAG,CAACyH,MAAM,EAAE;IACfzH,GAAG,CAACyH,MAAM,GAAG6E,sBAAsB,EAAE;EACvC;EAEA,IAAImL,cAAc,IAAIC,gBAAgB,EAAE;IACtC1X,GAAG,CAACyH,MAAM,GAAGgQ,cAAc;IAC3BzX,GAAG,CAAC0H,QAAQ,GAAGgQ,gBAAgB;EACjC;EAEA,IAAI1X,GAAG,CAAC0H,QAAQ,IAAI1H,GAAG,CAACyH,MAAM,EAAE;IAC9B,OAAOzH,GAAG;EACZ;EAEA,IAAIA,GAAG,CAACyH,MAAM,IAAIkQ,SAAS,CAAC/jB,MAAM,GAAG,CAAC,EAAE;IACtCoM,GAAG,CAAC0H,QAAQ,GAAGqE,UAAU,CAAC/L,GAAG,CAACyH,MAAM,EAAEpE,IAAI,CAACsU,SAAS,CAAC,IAAI7L,SAAS,CAAC9L,GAAG,CAACyH,MAAM,EAAEkB,KAAK,CAACtF,IAAI,CAACsU,SAAS,CAAC,CAAC;EACvG;EAEA,IAAI,CAAC3X,GAAG,CAAC0H,QAAQ,IAAItG,MAAM,CAACkK,YAAY,IAAIjI,IAAI,CAACuU,UAAU,IAAIvU,IAAI,CAACuU,UAAU,CAACC,QAAQ,KAAKC,IAAI,CAACC,SAAS,EAAE;IAC1G/X,GAAG,CAAC0H,QAAQ,GAAGrE,IAAI,CAACuU,UAAU,CAACI,IAAI;EACrC;EAEA,OAAOhY,GAAG;AACZ;AAEA,SAASiY,gBAAgB,CAAE5U,IAAI,EAAE;EAC/B,IAAI6U,eAAe,GAAGhV,OAAO,CAACG,IAAI,CAACK,UAAU,CAAC,CAAChO,MAAM,CAAC,UAAUiO,GAAG,EAAEhE,IAAI,EAAE;IACzE,IAAIgE,GAAG,CAAChO,IAAI,KAAK,OAAO,IAAIgO,GAAG,CAAChO,IAAI,KAAK,OAAO,EAAE;MAChDgO,GAAG,CAAChE,IAAI,CAAChK,IAAI,CAAC,GAAGgK,IAAI,CAACzI,KAAK;IAC7B;IAEA,OAAOyM,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EACN,IAAI2N,KAAK,GAAGjO,IAAI,CAACvD,YAAY,CAAC,OAAO,CAAC;EACtC,IAAI0R,OAAO,GAAGnO,IAAI,CAACvD,YAAY,CAAC,kBAAkB,CAAC;EAEnD,IAAIsB,MAAM,CAACT,QAAQ,EAAE;IACnB,IAAI2Q,KAAK,EAAE;MACT4G,eAAe,CAAC,iBAAiB,CAAC,GAAG,EAAE,CAACvZ,MAAM,CAACyC,MAAM,CAACZ,gBAAgB,EAAE,SAAS,CAAC,CAAC7B,MAAM,CAAC6S,OAAO,IAAI1O,YAAY,EAAE,CAAC;IACtH,CAAC,MAAM;MACLoV,eAAe,CAAC,aAAa,CAAC,GAAG,MAAM;MACvCA,eAAe,CAAC,WAAW,CAAC,GAAG,OAAO;IACxC;EACF;EAEA,OAAOA,eAAe;AACxB;AAEA,SAASC,SAAS,GAAG;EACnB,OAAO;IACLzQ,QAAQ,EAAE,IAAI;IACd4J,KAAK,EAAE,IAAI;IACXE,OAAO,EAAE,IAAI;IACb/J,MAAM,EAAE,IAAI;IACZvD,SAAS,EAAExC,oBAAoB;IAC/ByP,MAAM,EAAE,KAAK;IACbJ,IAAI,EAAE;MACJrJ,QAAQ,EAAE,IAAI;MACdD,MAAM,EAAE,IAAI;MACZ+E,IAAI,EAAE;IACR,CAAC;IACD+E,MAAM,EAAE,IAAI;IACZ7I,KAAK,EAAE;MACLmJ,OAAO,EAAE,EAAE;MACX9N,MAAM,EAAE,CAAC,CAAC;MACVL,UAAU,EAAE,CAAC;IACf;EACF,CAAC;AACH;AACA,SAAS0U,SAAS,CAAC/U,IAAI,EAAE;EACvB,IAAIgV,MAAM,GAAG1kB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwM,SAAS,GAAGxM,SAAS,CAAC,CAAC,CAAC,GAAG;IAC/E4jB,WAAW,EAAE;EACf,CAAC;EAED,IAAIe,YAAY,GAAGd,WAAW,CAACnU,IAAI,CAAC;IAChCqE,QAAQ,GAAG4Q,YAAY,CAAC5Q,QAAQ;IAChCD,MAAM,GAAG6Q,YAAY,CAAC7Q,MAAM;IAC5B8Q,YAAY,GAAGD,YAAY,CAAC9L,IAAI;EAEpC,IAAI0L,eAAe,GAAGD,gBAAgB,CAAC5U,IAAI,CAAC;EAC5C,IAAImV,UAAU,GAAGzJ,UAAU,CAAC,qBAAqB,EAAE,CAAC,CAAC,EAAE1L,IAAI,CAAC;EAC5D,IAAIoV,WAAW,GAAGJ,MAAM,CAACd,WAAW,GAAGA,WAAW,CAAClU,IAAI,CAAC,GAAG,EAAE;EAC7D,OAAO7P,cAAc,CAAC;IACpBkU,QAAQ,EAAEA,QAAQ;IAClB4J,KAAK,EAAEjO,IAAI,CAACvD,YAAY,CAAC,OAAO,CAAC;IACjC0R,OAAO,EAAEnO,IAAI,CAACvD,YAAY,CAAC,kBAAkB,CAAC;IAC9C2H,MAAM,EAAEA,MAAM;IACdvD,SAAS,EAAExC,oBAAoB;IAC/BqP,IAAI,EAAE;MACJrJ,QAAQ,EAAE,IAAI;MACdD,MAAM,EAAE,IAAI;MACZ+E,IAAI,EAAE;IACR,CAAC;IACD+E,MAAM,EAAE,IAAI;IACZJ,MAAM,EAAE,KAAK;IACbzI,KAAK,EAAE;MACLmJ,OAAO,EAAE0G,YAAY;MACrBxU,MAAM,EAAE0U,WAAW;MACnB/U,UAAU,EAAEwU;IACd;EACF,CAAC,EAAEM,UAAU,CAAC;AAChB;AAEA,IAAIE,QAAQ,GAAGpS,SAAS,CAACvC,MAAM;AAE/B,SAAS4U,gBAAgB,CAACtV,IAAI,EAAE;EAC9B,IAAIuV,QAAQ,GAAGxX,MAAM,CAACX,cAAc,KAAK,MAAM,GAAG2X,SAAS,CAAC/U,IAAI,EAAE;IAChEkU,WAAW,EAAE;EACf,CAAC,CAAC,GAAGa,SAAS,CAAC/U,IAAI,CAAC;EAEpB,IAAI,CAACuV,QAAQ,CAAClQ,KAAK,CAACmJ,OAAO,CAAC3W,OAAO,CAACmD,qBAAqB,CAAC,EAAE;IAC1D,OAAOiR,YAAY,CAAC,oBAAoB,EAAEjM,IAAI,EAAEuV,QAAQ,CAAC;EAC3D,CAAC,MAAM;IACL,OAAOtJ,YAAY,CAAC,gCAAgC,EAAEjM,IAAI,EAAEuV,QAAQ,CAAC;EACvE;AACF;AAEA,IAAIC,aAAa,GAAG,IAAI1Z,GAAG,EAAE;AAC7BzC,QAAQ,CAAC0C,GAAG,CAAC,UAAUyM,MAAM,EAAE;EAC7BgN,aAAa,CAACxZ,GAAG,CAAC,KAAK,CAACV,MAAM,CAACkN,MAAM,CAAC,CAAC;AACzC,CAAC,CAAC;AACF9Y,MAAM,CAACD,IAAI,CAACgK,eAAe,CAACN,cAAc,CAAC,CAAC,CAAC4C,GAAG,CAACyZ,aAAa,CAACxZ,GAAG,CAACC,IAAI,CAACuZ,aAAa,CAAC,CAAC;AACvF9lB,MAAM,CAACD,IAAI,CAACgK,eAAe,CAACL,YAAY,CAAC,CAAC,CAAC2C,GAAG,CAACyZ,aAAa,CAACxZ,GAAG,CAACC,IAAI,CAACuZ,aAAa,CAAC,CAAC;AACrFA,aAAa,GAAG9gB,kBAAkB,CAAC8gB,aAAa,CAAC;AAEjD,SAASC,MAAM,CAACC,IAAI,EAAE;EACpB,IAAIpD,QAAQ,GAAGhiB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwM,SAAS,GAAGxM,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EACvF,IAAI,CAACiH,MAAM,EAAE,OAAOgV,OAAO,CAACqD,OAAO,EAAE;EACrC,IAAI+F,aAAa,GAAGxe,QAAQ,CAACK,eAAe,CAACyI,SAAS;EAEtD,IAAI2V,MAAM,GAAG,SAASA,MAAM,CAACC,MAAM,EAAE;IACnC,OAAOF,aAAa,CAAC3Z,GAAG,CAAC,EAAE,CAACV,MAAM,CAAC1C,2BAA2B,EAAE,GAAG,CAAC,CAAC0C,MAAM,CAACua,MAAM,CAAC,CAAC;EACtF,CAAC;EAED,IAAIC,SAAS,GAAG,SAASA,SAAS,CAACD,MAAM,EAAE;IACzC,OAAOF,aAAa,CAAClE,MAAM,CAAC,EAAE,CAACnW,MAAM,CAAC1C,2BAA2B,EAAE,GAAG,CAAC,CAAC0C,MAAM,CAACua,MAAM,CAAC,CAAC;EACzF,CAAC;EAED,IAAIha,QAAQ,GAAGkC,MAAM,CAACkK,YAAY,GAAGuN,aAAa,GAAGnc,QAAQ,CAAC0C,GAAG,CAAC,UAAUga,CAAC,EAAE;IAC7E,OAAO,KAAK,CAACza,MAAM,CAACya,CAAC,CAAC;EACxB,CAAC,CAAC,CAACza,MAAM,CAAC5L,MAAM,CAACD,IAAI,CAAC4lB,QAAQ,CAAC,CAAC;EAEhC,IAAI,CAACxZ,QAAQ,CAACiO,QAAQ,CAAC,IAAI,CAAC,EAAE;IAC5BjO,QAAQ,CAAC5L,IAAI,CAAC,IAAI,CAAC;EACrB;EAEA,IAAI+lB,gBAAgB,GAAG,CAAC,GAAG,CAAC1a,MAAM,CAACN,qBAAqB,EAAE,QAAQ,CAAC,CAACM,MAAM,CAAC/C,aAAa,EAAE,IAAI,CAAC,CAAC,CAAC+C,MAAM,CAACO,QAAQ,CAACE,GAAG,CAAC,UAAU9H,CAAC,EAAE;IAChI,OAAO,GAAG,CAACqH,MAAM,CAACrH,CAAC,EAAE,QAAQ,CAAC,CAACqH,MAAM,CAAC/C,aAAa,EAAE,IAAI,CAAC;EAC5D,CAAC,CAAC,CAAC,CAAC0L,IAAI,CAAC,IAAI,CAAC;EAEd,IAAI+R,gBAAgB,CAACzlB,MAAM,KAAK,CAAC,EAAE;IACjC,OAAOgc,OAAO,CAACqD,OAAO,EAAE;EAC1B;EAEA,IAAIqG,UAAU,GAAG,EAAE;EAEnB,IAAI;IACFA,UAAU,GAAGpW,OAAO,CAAC6V,IAAI,CAACQ,gBAAgB,CAACF,gBAAgB,CAAC,CAAC;EAC/D,CAAC,CAAC,OAAOnf,CAAC,EAAE,CAAC;EAAA;EAGb,IAAIof,UAAU,CAAC1lB,MAAM,GAAG,CAAC,EAAE;IACzBqlB,MAAM,CAAC,SAAS,CAAC;IACjBE,SAAS,CAAC,UAAU,CAAC;EACvB,CAAC,MAAM;IACL,OAAOvJ,OAAO,CAACqD,OAAO,EAAE;EAC1B;EAEA,IAAIrZ,IAAI,GAAG0Z,IAAI,CAACF,KAAK,CAAC,QAAQ,CAAC;EAC/B,IAAIsC,SAAS,GAAG4D,UAAU,CAAC5jB,MAAM,CAAC,UAAUiO,GAAG,EAAEN,IAAI,EAAE;IACrD,IAAI;MACF,IAAIoR,QAAQ,GAAGkE,gBAAgB,CAACtV,IAAI,CAAC;MAErC,IAAIoR,QAAQ,EAAE;QACZ9Q,GAAG,CAACrQ,IAAI,CAACmhB,QAAQ,CAAC;MACpB;IACF,CAAC,CAAC,OAAOva,CAAC,EAAE;MACV,IAAI,CAACkC,UAAU,EAAE;QACf,IAAIlC,CAAC,CAACvE,IAAI,KAAK,aAAa,EAAE;UAC5Bmd,OAAO,CAACC,KAAK,CAAC7Y,CAAC,CAAC;QAClB;MACF;IACF;IAEA,OAAOyJ,GAAG;EACZ,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,IAAIiM,OAAO,CAAC,UAAUqD,OAAO,EAAEpD,MAAM,EAAE;IAC5CD,OAAO,CAAC4J,GAAG,CAAC9D,SAAS,CAAC,CAAC+D,IAAI,CAAC,UAAUC,iBAAiB,EAAE;MACvDjE,OAAO,CAACiE,iBAAiB,EAAE,YAAY;QACrCT,MAAM,CAAC,QAAQ,CAAC;QAChBA,MAAM,CAAC,UAAU,CAAC;QAClBE,SAAS,CAAC,SAAS,CAAC;QACpB,IAAI,OAAOxD,QAAQ,KAAK,UAAU,EAAEA,QAAQ,EAAE;QAC9C/b,IAAI,EAAE;QACNqZ,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC0G,KAAK,CAAC,UAAUzf,CAAC,EAAE;MACpBN,IAAI,EAAE;MACNiW,MAAM,CAAC3V,CAAC,CAAC;IACX,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAEA,SAAS0f,MAAM,CAACvW,IAAI,EAAE;EACpB,IAAIsS,QAAQ,GAAGhiB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwM,SAAS,GAAGxM,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EACvFglB,gBAAgB,CAACtV,IAAI,CAAC,CAACoW,IAAI,CAAC,UAAUhF,QAAQ,EAAE;IAC9C,IAAIA,QAAQ,EAAE;MACZgB,OAAO,CAAC,CAAChB,QAAQ,CAAC,EAAEkB,QAAQ,CAAC;IAC/B;EACF,CAAC,CAAC;AACJ;AAEA,SAASkE,YAAY,CAAC/gB,IAAI,EAAE;EAC1B,OAAO,UAAUghB,mBAAmB,EAAE;IACpC,IAAIxQ,MAAM,GAAG3V,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwM,SAAS,GAAGxM,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACnF,IAAIomB,cAAc,GAAG,CAACD,mBAAmB,IAAI,CAAC,CAAC,EAAEnS,IAAI,GAAGmS,mBAAmB,GAAGvK,kBAAkB,CAACuK,mBAAmB,IAAI,CAAC,CAAC,CAAC;IAC3H,IAAI/I,IAAI,GAAGzH,MAAM,CAACyH,IAAI;IAEtB,IAAIA,IAAI,EAAE;MACRA,IAAI,GAAG,CAACA,IAAI,IAAI,CAAC,CAAC,EAAEpJ,IAAI,GAAGoJ,IAAI,GAAGxB,kBAAkB,CAACwB,IAAI,IAAI,CAAC,CAAC,CAAC;IAClE;IAEA,OAAOjY,IAAI,CAACihB,cAAc,EAAEvmB,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE8V,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;MACzEyH,IAAI,EAAEA;IACR,CAAC,CAAC,CAAC;EACL,CAAC;AACH;AAEA,IAAIiJ,MAAM,GAAG,SAASA,MAAM,CAACD,cAAc,EAAE;EAC3C,IAAIzQ,MAAM,GAAG3V,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwM,SAAS,GAAGxM,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACnF,IAAIsmB,iBAAiB,GAAG3Q,MAAM,CAACpF,SAAS;IACpCA,SAAS,GAAG+V,iBAAiB,KAAK,KAAK,CAAC,GAAGvY,oBAAoB,GAAGuY,iBAAiB;IACnFC,cAAc,GAAG5Q,MAAM,CAAC6H,MAAM;IAC9BA,MAAM,GAAG+I,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,cAAc;IAC3DC,YAAY,GAAG7Q,MAAM,CAACyH,IAAI;IAC1BA,IAAI,GAAGoJ,YAAY,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,YAAY;IACpDC,cAAc,GAAG9Q,MAAM,CAACiI,MAAM;IAC9BA,MAAM,GAAG6I,cAAc,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,cAAc;IAC1DC,aAAa,GAAG/Q,MAAM,CAACgI,KAAK;IAC5BA,KAAK,GAAG+I,aAAa,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,aAAa;IACvDC,eAAe,GAAGhR,MAAM,CAACkI,OAAO;IAChCA,OAAO,GAAG8I,eAAe,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,eAAe;IAC7DC,eAAe,GAAGjR,MAAM,CAACuI,OAAO;IAChCA,OAAO,GAAG0I,eAAe,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,eAAe;IAC3DC,kBAAkB,GAAGlR,MAAM,CAAC5F,UAAU;IACtCA,UAAU,GAAG8W,kBAAkB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,kBAAkB;IACpEC,cAAc,GAAGnR,MAAM,CAACvF,MAAM;IAC9BA,MAAM,GAAG0W,cAAc,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,cAAc;EAC5D,IAAI,CAACV,cAAc,EAAE;EACrB,IAAItS,MAAM,GAAGsS,cAAc,CAACtS,MAAM;IAC9BC,QAAQ,GAAGqS,cAAc,CAACrS,QAAQ;IAClCC,IAAI,GAAGoS,cAAc,CAACpS,IAAI;EAC9B,OAAO6I,WAAW,CAAChd,cAAc,CAAC;IAChCwjB,IAAI,EAAE;EACR,CAAC,EAAE+C,cAAc,CAAC,EAAE,YAAY;IAC9B5K,SAAS,CAAC,0BAA0B,EAAE;MACpC4K,cAAc,EAAEA,cAAc;MAC9BzQ,MAAM,EAAEA;IACV,CAAC,CAAC;IAEF,IAAIlI,MAAM,CAACT,QAAQ,EAAE;MACnB,IAAI2Q,KAAK,EAAE;QACT5N,UAAU,CAAC,iBAAiB,CAAC,GAAG,EAAE,CAAC/E,MAAM,CAACyC,MAAM,CAACZ,gBAAgB,EAAE,SAAS,CAAC,CAAC7B,MAAM,CAAC6S,OAAO,IAAI1O,YAAY,EAAE,CAAC;MACjH,CAAC,MAAM;QACLY,UAAU,CAAC,aAAa,CAAC,GAAG,MAAM;QAClCA,UAAU,CAAC,WAAW,CAAC,GAAG,OAAO;MACnC;IACF;IAEA,OAAO0N,qBAAqB,CAAC;MAC3BjI,KAAK,EAAE;QACL2H,IAAI,EAAEyB,WAAW,CAAC5K,IAAI,CAAC;QACvBoJ,IAAI,EAAEA,IAAI,GAAGwB,WAAW,CAACxB,IAAI,CAACpJ,IAAI,CAAC,GAAG;UACpCqJ,KAAK,EAAE,KAAK;UACZlM,KAAK,EAAE,IAAI;UACXE,MAAM,EAAE,IAAI;UACZ2C,IAAI,EAAE,CAAC;QACT;MACF,CAAC;MACDF,MAAM,EAAEA,MAAM;MACdC,QAAQ,EAAEA,QAAQ;MAClBxD,SAAS,EAAE1Q,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEkO,oBAAoB,CAAC,EAAEwC,SAAS,CAAC;MAC9EiN,MAAM,EAAEA,MAAM;MACdG,KAAK,EAAEA,KAAK;MACZC,MAAM,EAAEA,MAAM;MACdC,OAAO,EAAEA,OAAO;MAChB9I,KAAK,EAAE;QACLhF,UAAU,EAAEA,UAAU;QACtBK,MAAM,EAAEA,MAAM;QACd8N,OAAO,EAAEA;MACX;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;AACD,IAAI6I,eAAe,GAAG;EACpB3U,MAAM,EAAE,SAASA,MAAM,GAAG;IACxB,OAAO;MACL4B,IAAI,EAAEkS,YAAY,CAACG,MAAM;IAC3B,CAAC;EACH,CAAC;EACD/T,KAAK,EAAE,SAASA,KAAK,GAAG;IACtB,OAAO;MACL0U,yBAAyB,EAAE,SAASA,yBAAyB,CAAC3L,WAAW,EAAE;QACzEA,WAAW,CAACsH,YAAY,GAAGwC,MAAM;QACjC9J,WAAW,CAACwH,YAAY,GAAGoD,MAAM;QACjC,OAAO5K,WAAW;MACpB;IACF,CAAC;EACH,CAAC;EACDF,QAAQ,EAAE,SAASA,QAAQ,CAAC8L,YAAY,EAAE;IACxCA,YAAY,CAACjL,KAAK,GAAG,UAAUrG,MAAM,EAAE;MACrC,IAAIuR,YAAY,GAAGvR,MAAM,CAACjG,IAAI;QAC1BA,IAAI,GAAGwX,YAAY,KAAK,KAAK,CAAC,GAAGrgB,QAAQ,GAAGqgB,YAAY;QACxDC,gBAAgB,GAAGxR,MAAM,CAACqM,QAAQ;QAClCA,QAAQ,GAAGmF,gBAAgB,KAAK,KAAK,CAAC,GAAG,YAAY,CAAC,CAAC,GAAGA,gBAAgB;MAC9E,OAAOhC,MAAM,CAACzV,IAAI,EAAEsS,QAAQ,CAAC;IAC/B,CAAC;IAEDiF,YAAY,CAACG,8BAA8B,GAAG,UAAU1X,IAAI,EAAEuV,QAAQ,EAAE;MACtE,IAAIlR,QAAQ,GAAGkR,QAAQ,CAAClR,QAAQ;QAC5B4J,KAAK,GAAGsH,QAAQ,CAACtH,KAAK;QACtBE,OAAO,GAAGoH,QAAQ,CAACpH,OAAO;QAC1B/J,MAAM,GAAGmR,QAAQ,CAACnR,MAAM;QACxBvD,SAAS,GAAG0U,QAAQ,CAAC1U,SAAS;QAC9BiN,MAAM,GAAGyH,QAAQ,CAACzH,MAAM;QACxBJ,IAAI,GAAG6H,QAAQ,CAAC7H,IAAI;QACpBQ,MAAM,GAAGqH,QAAQ,CAACrH,MAAM;QACxB7I,KAAK,GAAGkQ,QAAQ,CAAClQ,KAAK;MAC1B,OAAO,IAAIkH,OAAO,CAAC,UAAUqD,OAAO,EAAEpD,MAAM,EAAE;QAC5CD,OAAO,CAAC4J,GAAG,CAAC,CAACxG,QAAQ,CAACtL,QAAQ,EAAED,MAAM,CAAC,EAAEsJ,IAAI,CAACrJ,QAAQ,GAAGsL,QAAQ,CAACjC,IAAI,CAACrJ,QAAQ,EAAEqJ,IAAI,CAACtJ,MAAM,CAAC,GAAGmI,OAAO,CAACqD,OAAO,CAAC;UAC9GjC,KAAK,EAAE,KAAK;UACZlM,KAAK,EAAE,GAAG;UACVE,MAAM,EAAE,GAAG;UACX2C,IAAI,EAAE,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC8R,IAAI,CAAC,UAAUtf,IAAI,EAAE;UACxB,IAAI+F,KAAK,GAAGzI,cAAc,CAAC0C,IAAI,EAAE,CAAC,CAAC;YAC/B2W,IAAI,GAAG5Q,KAAK,CAAC,CAAC,CAAC;YACf6Q,IAAI,GAAG7Q,KAAK,CAAC,CAAC,CAAC;UAEnB+S,OAAO,CAAC,CAAC5P,IAAI,EAAE+N,qBAAqB,CAAC;YACnCjI,KAAK,EAAE;cACL2H,IAAI,EAAEA,IAAI;cACVC,IAAI,EAAEA;YACR,CAAC;YACDtJ,MAAM,EAAEA,MAAM;YACdC,QAAQ,EAAEA,QAAQ;YAClBxD,SAAS,EAAEA,SAAS;YACpBiN,MAAM,EAAEA,MAAM;YACdI,MAAM,EAAEA,MAAM;YACdD,KAAK,EAAEA,KAAK;YACZE,OAAO,EAAEA,OAAO;YAChB9I,KAAK,EAAEA,KAAK;YACZgJ,SAAS,EAAE;UACb,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAACiI,KAAK,CAAC9J,MAAM,CAAC;MAClB,CAAC,CAAC;IACJ,CAAC;IAED+K,YAAY,CAACI,oBAAoB,GAAG,UAAUC,KAAK,EAAE;MACnD,IAAI5T,QAAQ,GAAG4T,KAAK,CAAC5T,QAAQ;QACzB3D,UAAU,GAAGuX,KAAK,CAACvX,UAAU;QAC7BoN,IAAI,GAAGmK,KAAK,CAACnK,IAAI;QACjB5M,SAAS,GAAG+W,KAAK,CAAC/W,SAAS;QAC3BH,MAAM,GAAGkX,KAAK,CAAClX,MAAM;MACzB,IAAIoO,WAAW,GAAGrO,UAAU,CAACC,MAAM,CAAC;MAEpC,IAAIoO,WAAW,CAACve,MAAM,GAAG,CAAC,EAAE;QAC1B8P,UAAU,CAAC,OAAO,CAAC,GAAGyO,WAAW;MACnC;MAEA,IAAI+I,SAAS;MAEb,IAAIjX,qBAAqB,CAACC,SAAS,CAAC,EAAE;QACpCgX,SAAS,GAAG5L,YAAY,CAAC,mCAAmC,EAAE;UAC5DwB,IAAI,EAAEA,IAAI;UACV5M,SAAS,EAAEA,SAAS;UACpBE,cAAc,EAAE0M,IAAI,CAAChM,KAAK;UAC1BT,SAAS,EAAEyM,IAAI,CAAChM;QAClB,CAAC,CAAC;MACJ;MAEAuC,QAAQ,CAAC/T,IAAI,CAAC4nB,SAAS,IAAIpK,IAAI,CAACnJ,IAAI,CAAC;MACrC,OAAO;QACLN,QAAQ,EAAEA,QAAQ;QAClB3D,UAAU,EAAEA;MACd,CAAC;IACH,CAAC;EACH;AACF,CAAC;AAED,IAAIyX,MAAM,GAAG;EACXpV,MAAM,EAAE,SAASA,MAAM,GAAG;IACxB,OAAO;MACLqV,KAAK,EAAE,SAASA,KAAK,CAACC,SAAS,EAAE;QAC/B,IAAI/R,MAAM,GAAG3V,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwM,SAAS,GAAGxM,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACnF,IAAI4mB,eAAe,GAAGjR,MAAM,CAACuI,OAAO;UAChCA,OAAO,GAAG0I,eAAe,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,eAAe;QAC/D,OAAO/J,WAAW,CAAC;UACjBwG,IAAI,EAAE;QACR,CAAC,EAAE,YAAY;UACb7H,SAAS,CAAC,0BAA0B,EAAE;YACpCkM,SAAS,EAAEA,SAAS;YACpB/R,MAAM,EAAEA;UACV,CAAC,CAAC;UACF,IAAIjC,QAAQ,GAAG,EAAE;UACjBgU,SAAS,CAAC,UAAUjlB,IAAI,EAAE;YACxB+B,KAAK,CAACC,OAAO,CAAChC,IAAI,CAAC,GAAGA,IAAI,CAACgJ,GAAG,CAAC,UAAU2I,CAAC,EAAE;cAC1CV,QAAQ,GAAGA,QAAQ,CAAC1I,MAAM,CAACoJ,CAAC,CAAC2I,QAAQ,CAAC;YACxC,CAAC,CAAC,GAAGrJ,QAAQ,GAAGA,QAAQ,CAAC1I,MAAM,CAACvI,IAAI,CAACsa,QAAQ,CAAC;UAChD,CAAC,CAAC;UACF,OAAO,CAAC;YACNxJ,GAAG,EAAE,MAAM;YACXxD,UAAU,EAAE;cACV0O,KAAK,EAAE,CAAC,EAAE,CAACzT,MAAM,CAACyC,MAAM,CAACb,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC5B,MAAM,CAAC5G,kBAAkB,CAAC8Z,OAAO,CAAC,CAAC,CAACvK,IAAI,CAAC,GAAG;YAC9F,CAAC;YACDD,QAAQ,EAAEA;UACZ,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF,CAAC;EACH;AACF,CAAC;AAED,IAAIiU,aAAa,GAAG;EAClBvV,MAAM,EAAE,SAASA,MAAM,GAAG;IACxB,OAAO;MACLyC,OAAO,EAAE,SAASA,OAAO,CAACsJ,OAAO,EAAE;QACjC,IAAIxI,MAAM,GAAG3V,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwM,SAAS,GAAGxM,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACnF,IAAI0mB,aAAa,GAAG/Q,MAAM,CAACgI,KAAK;UAC5BA,KAAK,GAAG+I,aAAa,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,aAAa;UACvDE,eAAe,GAAGjR,MAAM,CAACuI,OAAO;UAChCA,OAAO,GAAG0I,eAAe,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,eAAe;UAC3DC,kBAAkB,GAAGlR,MAAM,CAAC5F,UAAU;UACtCA,UAAU,GAAG8W,kBAAkB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,kBAAkB;UACpEC,cAAc,GAAGnR,MAAM,CAACvF,MAAM;UAC9BA,MAAM,GAAG0W,cAAc,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,cAAc;QAC5D,OAAOjK,WAAW,CAAC;UACjBwG,IAAI,EAAE,SAAS;UACflF,OAAO,EAAEA;QACX,CAAC,EAAE,YAAY;UACb3C,SAAS,CAAC,0BAA0B,EAAE;YACpC2C,OAAO,EAAEA,OAAO;YAChBxI,MAAM,EAAEA;UACV,CAAC,CAAC;UACF,OAAO+I,yBAAyB,CAAC;YAC/BP,OAAO,EAAEA,OAAO,CAAC3Y,QAAQ,EAAE;YAC3BmY,KAAK,EAAEA,KAAK;YACZ5I,KAAK,EAAE;cACLhF,UAAU,EAAEA,UAAU;cACtBK,MAAM,EAAEA,MAAM;cACd8N,OAAO,EAAE,CAAC,EAAE,CAAClT,MAAM,CAACyC,MAAM,CAACb,SAAS,EAAE,iBAAiB,CAAC,CAAC,CAAC5B,MAAM,CAAC5G,kBAAkB,CAAC8Z,OAAO,CAAC;YAC9F;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF,CAAC;EACH;AACF,CAAC;AAED,IAAI0J,UAAU,GAAG;EACfxV,MAAM,EAAE,SAASA,MAAM,GAAG;IACxB,OAAO;MACLyV,IAAI,EAAE,SAASA,IAAI,CAAC1J,OAAO,EAAE;QAC3B,IAAIxI,MAAM,GAAG3V,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwM,SAAS,GAAGxM,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACnF,IAAIsmB,iBAAiB,GAAG3Q,MAAM,CAACpF,SAAS;UACpCA,SAAS,GAAG+V,iBAAiB,KAAK,KAAK,CAAC,GAAGvY,oBAAoB,GAAGuY,iBAAiB;UACnFI,aAAa,GAAG/Q,MAAM,CAACgI,KAAK;UAC5BA,KAAK,GAAG+I,aAAa,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,aAAa;UACvDE,eAAe,GAAGjR,MAAM,CAACuI,OAAO;UAChCA,OAAO,GAAG0I,eAAe,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,eAAe;UAC3DC,kBAAkB,GAAGlR,MAAM,CAAC5F,UAAU;UACtCA,UAAU,GAAG8W,kBAAkB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,kBAAkB;UACpEC,cAAc,GAAGnR,MAAM,CAACvF,MAAM;UAC9BA,MAAM,GAAG0W,cAAc,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,cAAc;QAC5D,OAAOjK,WAAW,CAAC;UACjBwG,IAAI,EAAE,MAAM;UACZlF,OAAO,EAAEA;QACX,CAAC,EAAE,YAAY;UACb3C,SAAS,CAAC,0BAA0B,EAAE;YACpC2C,OAAO,EAAEA,OAAO;YAChBxI,MAAM,EAAEA;UACV,CAAC,CAAC;UACF,OAAO2I,sBAAsB,CAAC;YAC5BH,OAAO,EAAEA,OAAO;YAChB5N,SAAS,EAAE1Q,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEkO,oBAAoB,CAAC,EAAEwC,SAAS,CAAC;YAC9EoN,KAAK,EAAEA,KAAK;YACZ5I,KAAK,EAAE;cACLhF,UAAU,EAAEA,UAAU;cACtBK,MAAM,EAAEA,MAAM;cACd8N,OAAO,EAAE,CAAC,EAAE,CAAClT,MAAM,CAACyC,MAAM,CAACb,SAAS,EAAE,cAAc,CAAC,CAAC,CAAC5B,MAAM,CAAC5G,kBAAkB,CAAC8Z,OAAO,CAAC;YAC3F;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF,CAAC;EACH,CAAC;EACD/C,QAAQ,EAAE,SAASA,QAAQ,CAAC8L,YAAY,EAAE;IACxCA,YAAY,CAACa,kBAAkB,GAAG,UAAUpY,IAAI,EAAEuV,QAAQ,EAAE;MAC1D,IAAItH,KAAK,GAAGsH,QAAQ,CAACtH,KAAK;QACtBpN,SAAS,GAAG0U,QAAQ,CAAC1U,SAAS;QAC9BwE,KAAK,GAAGkQ,QAAQ,CAAClQ,KAAK;MAC1B,IAAI5D,KAAK,GAAG,IAAI;MAChB,IAAIE,MAAM,GAAG,IAAI;MAEjB,IAAI/J,KAAK,EAAE;QACT,IAAIygB,gBAAgB,GAAGC,QAAQ,CAACC,gBAAgB,CAACvY,IAAI,CAAC,CAACwY,QAAQ,EAAE,EAAE,CAAC;QACpE,IAAIC,kBAAkB,GAAGzY,IAAI,CAAC0Y,qBAAqB,EAAE;QACrDjX,KAAK,GAAGgX,kBAAkB,CAAChX,KAAK,GAAG4W,gBAAgB;QACnD1W,MAAM,GAAG8W,kBAAkB,CAAC9W,MAAM,GAAG0W,gBAAgB;MACvD;MAEA,IAAIta,MAAM,CAACT,QAAQ,IAAI,CAAC2Q,KAAK,EAAE;QAC7B5I,KAAK,CAAChF,UAAU,CAAC,aAAa,CAAC,GAAG,MAAM;MAC1C;MAEA,OAAOkM,OAAO,CAACqD,OAAO,CAAC,CAAC5P,IAAI,EAAE4O,sBAAsB,CAAC;QACnDH,OAAO,EAAEzO,IAAI,CAAChB,SAAS;QACvByC,KAAK,EAAEA,KAAK;QACZE,MAAM,EAAEA,MAAM;QACdd,SAAS,EAAEA,SAAS;QACpBoN,KAAK,EAAEA,KAAK;QACZ5I,KAAK,EAAEA,KAAK;QACZgJ,SAAS,EAAE;MACb,CAAC,CAAC,CAAC,CAAC;IACN,CAAC;EACH;AACF,CAAC;AAED,IAAIsK,qBAAqB,GAAG,IAAIjnB,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;AAClD,IAAIknB,uBAAuB,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC;AAChD,SAASC,mBAAmB,CAACpK,OAAO,EAAE;EACpC,IAAIqK,OAAO,GAAGrK,OAAO,CAAC7b,OAAO,CAAC+lB,qBAAqB,EAAE,EAAE,CAAC;EACxD,IAAII,SAAS,GAAGtT,WAAW,CAACqT,OAAO,EAAE,CAAC,CAAC;EACvC,IAAIE,YAAY,GAAGD,SAAS,IAAIH,uBAAuB,CAAC,CAAC,CAAC,IAAIG,SAAS,IAAIH,uBAAuB,CAAC,CAAC,CAAC;EACrG,IAAIK,SAAS,GAAGH,OAAO,CAACvoB,MAAM,KAAK,CAAC,GAAGuoB,OAAO,CAAC,CAAC,CAAC,KAAKA,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;EACxE,OAAO;IACLjlB,KAAK,EAAEolB,SAAS,GAAG3T,KAAK,CAACwT,OAAO,CAAC,CAAC,CAAC,CAAC,GAAGxT,KAAK,CAACwT,OAAO,CAAC;IACrDI,WAAW,EAAEF,YAAY,IAAIC;EAC/B,CAAC;AACH;AAEA,SAASE,kBAAkB,CAACnZ,IAAI,EAAEoZ,QAAQ,EAAE;EAC1C,IAAIC,gBAAgB,GAAG,EAAE,CAAC/d,MAAM,CAAC7C,8BAA8B,CAAC,CAAC6C,MAAM,CAAC8d,QAAQ,CAACxmB,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;EACnG,OAAO,IAAI2Z,OAAO,CAAC,UAAUqD,OAAO,EAAEpD,MAAM,EAAE;IAC5C,IAAIxM,IAAI,CAACvD,YAAY,CAAC4c,gBAAgB,CAAC,KAAK,IAAI,EAAE;MAChD;MACA,OAAOzJ,OAAO,EAAE;IAClB;IAEA,IAAI5L,QAAQ,GAAGnE,OAAO,CAACG,IAAI,CAACgE,QAAQ,CAAC;IACrC,IAAIsV,6BAA6B,GAAGtV,QAAQ,CAACnU,MAAM,CAAC,UAAU+U,CAAC,EAAE;MAC/D,OAAOA,CAAC,CAACnI,YAAY,CAACjE,sBAAsB,CAAC,KAAK4gB,QAAQ;IAC5D,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,IAAI1Y,MAAM,GAAGxJ,MAAM,CAACqhB,gBAAgB,CAACvY,IAAI,EAAEoZ,QAAQ,CAAC;IACpD,IAAIG,UAAU,GAAG7Y,MAAM,CAAC8Y,gBAAgB,CAAC,aAAa,CAAC,CAAC1M,KAAK,CAAC7R,mBAAmB,CAAC;IAClF,IAAIwe,UAAU,GAAG/Y,MAAM,CAAC8Y,gBAAgB,CAAC,aAAa,CAAC;IACvD,IAAI/K,OAAO,GAAG/N,MAAM,CAAC8Y,gBAAgB,CAAC,SAAS,CAAC;IAEhD,IAAIF,6BAA6B,IAAI,CAACC,UAAU,EAAE;MAChD;MACA;MACA;MACAvZ,IAAI,CAAC0Z,WAAW,CAACJ,6BAA6B,CAAC;MAC/C,OAAO1J,OAAO,EAAE;IAClB,CAAC,MAAM,IAAI2J,UAAU,IAAI9K,OAAO,KAAK,MAAM,IAAIA,OAAO,KAAK,EAAE,EAAE;MAC7D,IAAIkL,QAAQ,GAAGjZ,MAAM,CAAC8Y,gBAAgB,CAAC,SAAS,CAAC;MAEjD,IAAIhR,MAAM,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC3Q,OAAO,CAAC0hB,UAAU,CAAC,CAAC,CAAC,CAAC,GAAGngB,YAAY,GAAGD,cAAc;MAC9E,IAAIiL,MAAM,GAAG,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,CAAC,CAACvM,OAAO,CAAC0hB,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG7f,eAAe,CAAC8O,MAAM,CAAC,CAAC+Q,UAAU,CAAC,CAAC,CAAC,CAACK,WAAW,EAAE,CAAC,GAAG1e,qBAAqB,CAACsN,MAAM,CAAC,CAACiR,UAAU,CAAC;MAEzM,IAAII,oBAAoB,GAAGhB,mBAAmB,CAACc,QAAQ,CAAC;QACpDG,QAAQ,GAAGD,oBAAoB,CAAChmB,KAAK;QACrCqlB,WAAW,GAAGW,oBAAoB,CAACX,WAAW;MAElD,IAAIa,IAAI,GAAGR,UAAU,CAAC,CAAC,CAAC,CAACS,UAAU,CAAC,aAAa,CAAC;MAClD,IAAI3V,QAAQ,GAAGoE,SAAS,CAACrE,MAAM,EAAE0V,QAAQ,CAAC;MAC1C,IAAIG,cAAc,GAAG5V,QAAQ;MAE7B,IAAI0V,IAAI,EAAE;QACR,IAAIG,SAAS,GAAGpR,YAAY,CAACgR,QAAQ,CAAC;QAEtC,IAAII,SAAS,CAAC7V,QAAQ,IAAI6V,SAAS,CAAC9V,MAAM,EAAE;UAC1CC,QAAQ,GAAG6V,SAAS,CAAC7V,QAAQ;UAC7BD,MAAM,GAAG8V,SAAS,CAAC9V,MAAM;QAC3B;MACF,CAAC,CAAC;MACF;;MAGA,IAAIC,QAAQ,IAAI,CAAC6U,WAAW,KAAK,CAACI,6BAA6B,IAAIA,6BAA6B,CAAC7c,YAAY,CAAC/D,WAAW,CAAC,KAAK0L,MAAM,IAAIkV,6BAA6B,CAAC7c,YAAY,CAAC9D,SAAS,CAAC,KAAKshB,cAAc,CAAC,EAAE;QAClNja,IAAI,CAACjB,YAAY,CAACsa,gBAAgB,EAAEY,cAAc,CAAC;QAEnD,IAAIX,6BAA6B,EAAE;UACjC;UACAtZ,IAAI,CAAC0Z,WAAW,CAACJ,6BAA6B,CAAC;QACjD;QAEA,IAAIa,IAAI,GAAGrF,SAAS,EAAE;QACtB,IAAIzP,KAAK,GAAG8U,IAAI,CAAC9U,KAAK;QACtBA,KAAK,CAAChF,UAAU,CAAC7H,sBAAsB,CAAC,GAAG4gB,QAAQ;QACnDzJ,QAAQ,CAACtL,QAAQ,EAAED,MAAM,CAAC,CAACgS,IAAI,CAAC,UAAU3I,IAAI,EAAE;UAC9C,IAAI6D,SAAS,GAAGvD,qBAAqB,CAAC5d,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEgqB,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;YACjFrU,KAAK,EAAE;cACL2H,IAAI,EAAEA,IAAI;cACVC,IAAI,EAAExE,kBAAkB;YAC1B,CAAC;YACD9E,MAAM,EAAEA,MAAM;YACdC,QAAQ,EAAE4V,cAAc;YACxB5U,KAAK,EAAEA,KAAK;YACZgJ,SAAS,EAAE;UACb,CAAC,CAAC,CAAC;UAEH,IAAI9R,OAAO,GAAGpF,QAAQ,CAACuZ,eAAe,CAAC,4BAA4B,EAAE,KAAK,CAAC;UAE3E,IAAI0I,QAAQ,KAAK,UAAU,EAAE;YAC3BpZ,IAAI,CAACT,YAAY,CAAChD,OAAO,EAAEyD,IAAI,CAACuU,UAAU,CAAC;UAC7C,CAAC,MAAM;YACLvU,IAAI,CAACgR,WAAW,CAACzU,OAAO,CAAC;UAC3B;UAEAA,OAAO,CAAC4U,SAAS,GAAGG,SAAS,CAACvV,GAAG,CAAC,UAAU2I,CAAC,EAAE;YAC7C,OAAOf,MAAM,CAACe,CAAC,CAAC;UAClB,CAAC,CAAC,CAACT,IAAI,CAAC,IAAI,CAAC;UACbjE,IAAI,CAACgS,eAAe,CAACqH,gBAAgB,CAAC;UACtCzJ,OAAO,EAAE;QACX,CAAC,CAAC,CAAC0G,KAAK,CAAC9J,MAAM,CAAC;MAClB,CAAC,MAAM;QACLoD,OAAO,EAAE;MACX;IACF,CAAC,MAAM;MACLA,OAAO,EAAE;IACX;EACF,CAAC,CAAC;AACJ;AAEA,SAAShd,OAAO,CAACoN,IAAI,EAAE;EACrB,OAAOuM,OAAO,CAAC4J,GAAG,CAAC,CAACgD,kBAAkB,CAACnZ,IAAI,EAAE,UAAU,CAAC,EAAEmZ,kBAAkB,CAACnZ,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;AACjG;AAEA,SAASoa,WAAW,CAACpa,IAAI,EAAE;EACzB,OAAOA,IAAI,CAACqR,UAAU,KAAK3a,QAAQ,CAACe,IAAI,IAAI,CAAC,CAACqB,mCAAmC,CAACjB,OAAO,CAACmI,IAAI,CAACX,OAAO,CAACC,WAAW,EAAE,CAAC,IAAI,CAACU,IAAI,CAACvD,YAAY,CAACjE,sBAAsB,CAAC,KAAK,CAACwH,IAAI,CAACqR,UAAU,IAAIrR,IAAI,CAACqR,UAAU,CAAChS,OAAO,KAAK,KAAK,CAAC;AAChO;AAEA,SAAS9B,oBAAoB,CAACmY,IAAI,EAAE;EAClC,IAAI,CAACne,MAAM,EAAE;EACb,OAAO,IAAIgV,OAAO,CAAC,UAAUqD,OAAO,EAAEpD,MAAM,EAAE;IAC5C,IAAI6N,UAAU,GAAGxa,OAAO,CAAC6V,IAAI,CAACQ,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAACrmB,MAAM,CAACuqB,WAAW,CAAC,CAACre,GAAG,CAACnJ,OAAO,CAAC;IACrF,IAAIod,GAAG,GAAGC,IAAI,CAACF,KAAK,CAAC,sBAAsB,CAAC;IAC5C4C,kBAAkB,EAAE;IACpBpG,OAAO,CAAC4J,GAAG,CAACkE,UAAU,CAAC,CAACjE,IAAI,CAAC,YAAY;MACvCpG,GAAG,EAAE;MACL4C,iBAAiB,EAAE;MACnBhD,OAAO,EAAE;IACX,CAAC,CAAC,CAAC0G,KAAK,CAAC,YAAY;MACnBtG,GAAG,EAAE;MACL4C,iBAAiB,EAAE;MACnBpG,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAEA,IAAI8N,cAAc,GAAG;EACnB1X,KAAK,EAAE,SAASA,KAAK,GAAG;IACtB,OAAO;MACL0U,yBAAyB,EAAE,SAASA,yBAAyB,CAAC3L,WAAW,EAAE;QACzEA,WAAW,CAAC0H,sBAAsB,GAAG9V,oBAAoB;QACzD,OAAOoO,WAAW;MACpB;IACF,CAAC;EACH,CAAC;EACDF,QAAQ,EAAE,SAASA,QAAQ,CAAC8L,YAAY,EAAE;IACxCA,YAAY,CAACgD,kBAAkB,GAAG,UAAUtU,MAAM,EAAE;MAClD,IAAIuR,YAAY,GAAGvR,MAAM,CAACjG,IAAI;QAC1BA,IAAI,GAAGwX,YAAY,KAAK,KAAK,CAAC,GAAGrgB,QAAQ,GAAGqgB,YAAY;MAE5D,IAAIzZ,MAAM,CAACR,oBAAoB,EAAE;QAC/BA,oBAAoB,CAACyC,IAAI,CAAC;MAC5B;IACF,CAAC;EACH;AACF,CAAC;AAED,IAAIwa,UAAU,GAAG,KAAK;AACtB,IAAIC,kBAAkB,GAAG;EACvB/X,MAAM,EAAE,SAASA,MAAM,GAAG;IACxB,OAAO;MACLC,GAAG,EAAE;QACH+X,OAAO,EAAE,SAASA,OAAO,GAAG;UAC1B/H,kBAAkB,EAAE;UACpB6H,UAAU,GAAG,IAAI;QACnB;MACF;IACF,CAAC;EACH,CAAC;EACD5X,KAAK,EAAE,SAASA,KAAK,GAAG;IACtB,OAAO;MACL+X,SAAS,EAAE,SAASA,SAAS,GAAG;QAC9B7H,OAAO,CAACpH,UAAU,CAAC,2BAA2B,EAAE,CAAC,CAAC,CAAC,CAAC;MACtD,CAAC;MACDW,MAAM,EAAE,SAASA,MAAM,GAAG;QACxB4H,UAAU,EAAE;MACd,CAAC;MACDxH,KAAK,EAAE,SAASA,KAAK,CAACxG,MAAM,EAAE;QAC5B,IAAIsN,oBAAoB,GAAGtN,MAAM,CAACsN,oBAAoB;QAEtD,IAAIiH,UAAU,EAAE;UACd5H,iBAAiB,EAAE;QACrB,CAAC,MAAM;UACLE,OAAO,CAACpH,UAAU,CAAC,2BAA2B,EAAE;YAC9C6H,oBAAoB,EAAEA;UACxB,CAAC,CAAC,CAAC;QACL;MACF;IACF,CAAC;EACH;AACF,CAAC;AAED,IAAIqH,oBAAoB,GAAG,SAASA,oBAAoB,CAACC,eAAe,EAAE;EACxE,IAAIha,SAAS,GAAG;IACdvC,IAAI,EAAE,EAAE;IACRC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJE,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE,KAAK;IACZF,MAAM,EAAE;EACV,CAAC;EACD,OAAOoc,eAAe,CAACjB,WAAW,EAAE,CAAC1Z,KAAK,CAAC,GAAG,CAAC,CAAC7N,MAAM,CAAC,UAAUiO,GAAG,EAAEzK,CAAC,EAAE;IACvE,IAAI6R,KAAK,GAAG7R,CAAC,CAAC+jB,WAAW,EAAE,CAAC1Z,KAAK,CAAC,GAAG,CAAC;IACtC,IAAIyF,KAAK,GAAG+B,KAAK,CAAC,CAAC,CAAC;IACpB,IAAIyB,IAAI,GAAGzB,KAAK,CAAC1U,KAAK,CAAC,CAAC,CAAC,CAACiR,IAAI,CAAC,GAAG,CAAC;IAEnC,IAAI0B,KAAK,IAAIwD,IAAI,KAAK,GAAG,EAAE;MACzB7I,GAAG,CAAC5B,KAAK,GAAG,IAAI;MAChB,OAAO4B,GAAG;IACZ;IAEA,IAAIqF,KAAK,IAAIwD,IAAI,KAAK,GAAG,EAAE;MACzB7I,GAAG,CAAC3B,KAAK,GAAG,IAAI;MAChB,OAAO2B,GAAG;IACZ;IAEA6I,IAAI,GAAG2R,UAAU,CAAC3R,IAAI,CAAC;IAEvB,IAAI4R,KAAK,CAAC5R,IAAI,CAAC,EAAE;MACf,OAAO7I,GAAG;IACZ;IAEA,QAAQqF,KAAK;MACX,KAAK,MAAM;QACTrF,GAAG,CAAChC,IAAI,GAAGgC,GAAG,CAAChC,IAAI,GAAG6K,IAAI;QAC1B;MAEF,KAAK,QAAQ;QACX7I,GAAG,CAAChC,IAAI,GAAGgC,GAAG,CAAChC,IAAI,GAAG6K,IAAI;QAC1B;MAEF,KAAK,MAAM;QACT7I,GAAG,CAAC/B,CAAC,GAAG+B,GAAG,CAAC/B,CAAC,GAAG4K,IAAI;QACpB;MAEF,KAAK,OAAO;QACV7I,GAAG,CAAC/B,CAAC,GAAG+B,GAAG,CAAC/B,CAAC,GAAG4K,IAAI;QACpB;MAEF,KAAK,IAAI;QACP7I,GAAG,CAAC9B,CAAC,GAAG8B,GAAG,CAAC9B,CAAC,GAAG2K,IAAI;QACpB;MAEF,KAAK,MAAM;QACT7I,GAAG,CAAC9B,CAAC,GAAG8B,GAAG,CAAC9B,CAAC,GAAG2K,IAAI;QACpB;MAEF,KAAK,QAAQ;QACX7I,GAAG,CAAC7B,MAAM,GAAG6B,GAAG,CAAC7B,MAAM,GAAG0K,IAAI;QAC9B;IAAM;IAGV,OAAO7I,GAAG;EACZ,CAAC,EAAEO,SAAS,CAAC;AACf,CAAC;AACD,IAAIma,eAAe,GAAG;EACpBtY,MAAM,EAAE,SAASA,MAAM,GAAG;IACxB,OAAO;MACLkK,KAAK,EAAE;QACL/L,SAAS,EAAE,SAASA,SAAS,CAACga,eAAe,EAAE;UAC7C,OAAOD,oBAAoB,CAACC,eAAe,CAAC;QAC9C;MACF;IACF,CAAC;EACH,CAAC;EACDjY,KAAK,EAAE,SAASA,KAAK,GAAG;IACtB,OAAO;MACLqY,mBAAmB,EAAE,SAASA,mBAAmB,CAACtP,WAAW,EAAE3L,IAAI,EAAE;QACnE,IAAI6a,eAAe,GAAG7a,IAAI,CAACvD,YAAY,CAAC,mBAAmB,CAAC;QAE5D,IAAIoe,eAAe,EAAE;UACnBlP,WAAW,CAAC9K,SAAS,GAAG+Z,oBAAoB,CAACC,eAAe,CAAC;QAC/D;QAEA,OAAOlP,WAAW;MACpB;IACF,CAAC;EACH,CAAC;EACDF,QAAQ,EAAE,SAASA,QAAQ,CAACV,SAAS,EAAE;IACrCA,SAAS,CAACmQ,iCAAiC,GAAG,UAAUpkB,IAAI,EAAE;MAC5D,IAAI2W,IAAI,GAAG3W,IAAI,CAAC2W,IAAI;QAChB5M,SAAS,GAAG/J,IAAI,CAAC+J,SAAS;QAC1BE,cAAc,GAAGjK,IAAI,CAACiK,cAAc;QACpCC,SAAS,GAAGlK,IAAI,CAACkK,SAAS;MAC9B,IAAIC,KAAK,GAAG;QACVJ,SAAS,EAAE,YAAY,CAACvF,MAAM,CAACyF,cAAc,GAAG,CAAC,EAAE,OAAO;MAC5D,CAAC;MACD,IAAIG,cAAc,GAAG,YAAY,CAAC5F,MAAM,CAACuF,SAAS,CAACtC,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,CAACjD,MAAM,CAACuF,SAAS,CAACrC,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC;MAC/F,IAAI2C,UAAU,GAAG,QAAQ,CAAC7F,MAAM,CAACuF,SAAS,CAACvC,IAAI,GAAG,EAAE,IAAIuC,SAAS,CAACnC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAACpD,MAAM,CAACuF,SAAS,CAACvC,IAAI,GAAG,EAAE,IAAIuC,SAAS,CAAClC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;MACvJ,IAAIyC,WAAW,GAAG,SAAS,CAAC9F,MAAM,CAACuF,SAAS,CAACpC,MAAM,EAAE,OAAO,CAAC;MAC7D,IAAI4C,KAAK,GAAG;QACVR,SAAS,EAAE,EAAE,CAACvF,MAAM,CAAC4F,cAAc,EAAE,GAAG,CAAC,CAAC5F,MAAM,CAAC6F,UAAU,EAAE,GAAG,CAAC,CAAC7F,MAAM,CAAC8F,WAAW;MACtF,CAAC;MACD,IAAIE,IAAI,GAAG;QACTT,SAAS,EAAE,YAAY,CAACvF,MAAM,CAAC0F,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ;MAC7D,CAAC;MACD,IAAIqZ,UAAU,GAAG;QACfpZ,KAAK,EAAEA,KAAK;QACZI,KAAK,EAAEA,KAAK;QACZC,IAAI,EAAEA;MACR,CAAC;MACD,OAAO;QACLuC,GAAG,EAAE,GAAG;QACRxD,UAAU,EAAElQ,cAAc,CAAC,CAAC,CAAC,EAAEkqB,UAAU,CAACpZ,KAAK,CAAC;QAChD+C,QAAQ,EAAE,CAAC;UACTH,GAAG,EAAE,GAAG;UACRxD,UAAU,EAAElQ,cAAc,CAAC,CAAC,CAAC,EAAEkqB,UAAU,CAAChZ,KAAK,CAAC;UAChD2C,QAAQ,EAAE,CAAC;YACTH,GAAG,EAAE4J,IAAI,CAACnJ,IAAI,CAACT,GAAG;YAClBG,QAAQ,EAAEyJ,IAAI,CAACnJ,IAAI,CAACN,QAAQ;YAC5B3D,UAAU,EAAElQ,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEsd,IAAI,CAACnJ,IAAI,CAACjE,UAAU,CAAC,EAAEga,UAAU,CAAC/Y,IAAI;UACtF,CAAC;QACH,CAAC;MACH,CAAC;IACH,CAAC;EACH;AACF,CAAC;AAED,IAAI6Z,SAAS,GAAG;EACd5c,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJiD,KAAK,EAAE,MAAM;EACbE,MAAM,EAAE;AACV,CAAC;AAED,SAASyZ,SAAS,CAAC9J,SAAS,EAAE;EAC5B,IAAI+J,KAAK,GAAG/qB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKwM,SAAS,GAAGxM,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EAEpF,IAAIghB,SAAS,CAACjR,UAAU,KAAKiR,SAAS,CAACjR,UAAU,CAACiP,IAAI,IAAI+L,KAAK,CAAC,EAAE;IAChE/J,SAAS,CAACjR,UAAU,CAACiP,IAAI,GAAG,OAAO;EACrC;EAEA,OAAOgC,SAAS;AAClB;AAEA,SAASgK,OAAO,CAAC3J,UAAU,EAAE;EAC3B,IAAIA,UAAU,CAAC9N,GAAG,KAAK,GAAG,EAAE;IAC1B,OAAO8N,UAAU,CAAC3N,QAAQ;EAC5B,CAAC,MAAM;IACL,OAAO,CAAC2N,UAAU,CAAC;EACrB;AACF;AAEA,IAAI4J,KAAK,GAAG;EACV3Y,KAAK,EAAE,SAASA,KAAK,GAAG;IACtB,OAAO;MACLqY,mBAAmB,EAAE,SAASA,mBAAmB,CAACtP,WAAW,EAAE3L,IAAI,EAAE;QACnE,IAAIwb,QAAQ,GAAGxb,IAAI,CAACvD,YAAY,CAAC,cAAc,CAAC;QAChD,IAAIiR,IAAI,GAAG,CAAC8N,QAAQ,GAAGtS,kBAAkB,EAAE,GAAGM,gBAAgB,CAACgS,QAAQ,CAACtb,KAAK,CAAC,GAAG,CAAC,CAACnE,GAAG,CAAC,UAAU1L,CAAC,EAAE;UAClG,OAAOA,CAAC,CAACmQ,IAAI,EAAE;QACjB,CAAC,CAAC,CAAC;QAEH,IAAI,CAACkN,IAAI,CAACtJ,MAAM,EAAE;UAChBsJ,IAAI,CAACtJ,MAAM,GAAG6E,sBAAsB,EAAE;QACxC;QAEA0C,WAAW,CAAC+B,IAAI,GAAGA,IAAI;QACvB/B,WAAW,CAACuC,MAAM,GAAGlO,IAAI,CAACvD,YAAY,CAAC,iBAAiB,CAAC;QACzD,OAAOkP,WAAW;MACpB;IACF,CAAC;EACH,CAAC;EACDF,QAAQ,EAAE,SAASA,QAAQ,CAACV,SAAS,EAAE;IACrCA,SAAS,CAAC0Q,oBAAoB,GAAG,UAAU3kB,IAAI,EAAE;MAC/C,IAAIkN,QAAQ,GAAGlN,IAAI,CAACkN,QAAQ;QACxB3D,UAAU,GAAGvJ,IAAI,CAACuJ,UAAU;QAC5BoN,IAAI,GAAG3W,IAAI,CAAC2W,IAAI;QAChBC,IAAI,GAAG5W,IAAI,CAAC4W,IAAI;QAChBgO,cAAc,GAAG5kB,IAAI,CAACoX,MAAM;QAC5BrN,SAAS,GAAG/J,IAAI,CAAC+J,SAAS;MAC9B,IAAI8a,SAAS,GAAGlO,IAAI,CAAChM,KAAK;QACtBma,QAAQ,GAAGnO,IAAI,CAACnJ,IAAI;MACxB,IAAIuX,SAAS,GAAGnO,IAAI,CAACjM,KAAK;QACtBqa,QAAQ,GAAGpO,IAAI,CAACpJ,IAAI;MACxB,IAAIyX,KAAK,GAAGjb,eAAe,CAAC;QAC1BD,SAAS,EAAEA,SAAS;QACpBE,cAAc,EAAE8a,SAAS;QACzB7a,SAAS,EAAE2a;MACb,CAAC,CAAC;MACF,IAAIK,QAAQ,GAAG;QACbnY,GAAG,EAAE,MAAM;QACXxD,UAAU,EAAElQ,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEgrB,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;UAC5D7L,IAAI,EAAE;QACR,CAAC;MACH,CAAC;MACD,IAAI2M,2BAA2B,GAAGL,QAAQ,CAAC5X,QAAQ,GAAG;QACpDA,QAAQ,EAAE4X,QAAQ,CAAC5X,QAAQ,CAACjI,GAAG,CAACqf,SAAS;MAC3C,CAAC,GAAG,CAAC,CAAC;MACN,IAAIc,cAAc,GAAG;QACnBrY,GAAG,EAAE,GAAG;QACRxD,UAAU,EAAElQ,cAAc,CAAC,CAAC,CAAC,EAAE4rB,KAAK,CAAC1a,KAAK,CAAC;QAC3C2C,QAAQ,EAAE,CAACoX,SAAS,CAACjrB,cAAc,CAAC;UAClC0T,GAAG,EAAE+X,QAAQ,CAAC/X,GAAG;UACjBxD,UAAU,EAAElQ,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEyrB,QAAQ,CAACvb,UAAU,CAAC,EAAE0b,KAAK,CAACza,IAAI;QAChF,CAAC,EAAE2a,2BAA2B,CAAC,CAAC;MAClC,CAAC;MACD,IAAIE,cAAc,GAAG;QACnBtY,GAAG,EAAE,GAAG;QACRxD,UAAU,EAAElQ,cAAc,CAAC,CAAC,CAAC,EAAE4rB,KAAK,CAAC9a,KAAK,CAAC;QAC3C+C,QAAQ,EAAE,CAACkY,cAAc;MAC3B,CAAC;MACD,IAAIhO,MAAM,GAAG,OAAO,CAAC5S,MAAM,CAACogB,cAAc,IAAIjc,YAAY,EAAE,CAAC;MAC7D,IAAI2c,MAAM,GAAG,OAAO,CAAC9gB,MAAM,CAACogB,cAAc,IAAIjc,YAAY,EAAE,CAAC;MAC7D,IAAI4c,OAAO,GAAG;QACZxY,GAAG,EAAE,MAAM;QACXxD,UAAU,EAAElQ,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEgrB,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;UAC5Dzb,EAAE,EAAEwO,MAAM;UACVoO,SAAS,EAAE,gBAAgB;UAC3BC,gBAAgB,EAAE;QACpB,CAAC,CAAC;QACFvY,QAAQ,EAAE,CAACgY,QAAQ,EAAEG,cAAc;MACrC,CAAC;MACD,IAAIK,IAAI,GAAG;QACT3Y,GAAG,EAAE,MAAM;QACXG,QAAQ,EAAE,CAAC;UACTH,GAAG,EAAE,UAAU;UACfxD,UAAU,EAAE;YACVX,EAAE,EAAE0c;UACN,CAAC;UACDpY,QAAQ,EAAEsX,OAAO,CAACQ,QAAQ;QAC5B,CAAC,EAAEO,OAAO;MACZ,CAAC;MACDrY,QAAQ,CAAC/T,IAAI,CAACusB,IAAI,EAAE;QAClB3Y,GAAG,EAAE,MAAM;QACXxD,UAAU,EAAElQ,cAAc,CAAC;UACzBmf,IAAI,EAAE,cAAc;UACpB,WAAW,EAAE,OAAO,CAAChU,MAAM,CAAC8gB,MAAM,EAAE,GAAG,CAAC;UACxC1O,IAAI,EAAE,OAAO,CAACpS,MAAM,CAAC4S,MAAM,EAAE,GAAG;QAClC,CAAC,EAAEiN,SAAS;MACd,CAAC,CAAC;MACF,OAAO;QACLnX,QAAQ,EAAEA,QAAQ;QAClB3D,UAAU,EAAEA;MACd,CAAC;IACH,CAAC;EACH;AACF,CAAC;AAED,IAAIoc,oBAAoB,GAAG;EACzBhR,QAAQ,EAAE,SAASA,QAAQ,CAACV,SAAS,EAAE;IACrC,IAAI2R,YAAY,GAAG,KAAK;IAExB,IAAIxlB,MAAM,CAACylB,UAAU,EAAE;MACrBD,YAAY,GAAGxlB,MAAM,CAACylB,UAAU,CAAC,kCAAkC,CAAC,CAACC,OAAO;IAC9E;IAEA7R,SAAS,CAAC8R,mBAAmB,GAAG,YAAY;MAC1C,IAAIC,SAAS,GAAG,EAAE;MAClB,IAAIC,IAAI,GAAG;QACTzN,IAAI,EAAE;MACR,CAAC;MACD,IAAI0N,cAAc,GAAG;QACnBC,aAAa,EAAE,KAAK;QACpBC,WAAW,EAAE,YAAY;QACzBC,GAAG,EAAE;MACP,CAAC,CAAC,CAAC;;MAEHL,SAAS,CAAC7sB,IAAI,CAAC;QACb4T,GAAG,EAAE,MAAM;QACXxD,UAAU,EAAElQ,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE4sB,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;UACvD3e,CAAC,EAAE;QACL,CAAC;MACH,CAAC,CAAC;MAEF,IAAIgf,eAAe,GAAGjtB,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE6sB,cAAc,CAAC,EAAE,CAAC,CAAC,EAAE;QAC3Ezc,aAAa,EAAE;MACjB,CAAC,CAAC;MAEF,IAAI8c,GAAG,GAAG;QACRxZ,GAAG,EAAE,QAAQ;QACbxD,UAAU,EAAElQ,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE4sB,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;UACvDO,EAAE,EAAE,KAAK;UACTC,EAAE,EAAE,KAAK;UACTC,CAAC,EAAE;QACL,CAAC,CAAC;QACFxZ,QAAQ,EAAE;MACZ,CAAC;MAED,IAAI,CAAC0Y,YAAY,EAAE;QACjBW,GAAG,CAACrZ,QAAQ,CAAC/T,IAAI,CAAC;UAChB4T,GAAG,EAAE,SAAS;UACdxD,UAAU,EAAElQ,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE6sB,cAAc,CAAC,EAAE,CAAC,CAAC,EAAE;YACjEzc,aAAa,EAAE,GAAG;YAClBwG,MAAM,EAAE;UACV,CAAC;QACH,CAAC,EAAE;UACDlD,GAAG,EAAE,SAAS;UACdxD,UAAU,EAAElQ,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEitB,eAAe,CAAC,EAAE,CAAC,CAAC,EAAE;YAClErW,MAAM,EAAE;UACV,CAAC;QACH,CAAC,CAAC;MACJ;MAEA+V,SAAS,CAAC7sB,IAAI,CAACotB,GAAG,CAAC;MACnBP,SAAS,CAAC7sB,IAAI,CAAC;QACb4T,GAAG,EAAE,MAAM;QACXxD,UAAU,EAAElQ,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE4sB,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;UACvDU,OAAO,EAAE,GAAG;UACZrf,CAAC,EAAE;QACL,CAAC,CAAC;QACF4F,QAAQ,EAAE0Y,YAAY,GAAG,EAAE,GAAG,CAAC;UAC7B7Y,GAAG,EAAE,SAAS;UACdxD,UAAU,EAAElQ,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEitB,eAAe,CAAC,EAAE,CAAC,CAAC,EAAE;YAClErW,MAAM,EAAE;UACV,CAAC;QACH,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAAC2V,YAAY,EAAE;QACjB;QACAI,SAAS,CAAC7sB,IAAI,CAAC;UACb4T,GAAG,EAAE,MAAM;UACXxD,UAAU,EAAElQ,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE4sB,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;YACvDU,OAAO,EAAE,GAAG;YACZrf,CAAC,EAAE;UACL,CAAC,CAAC;UACF4F,QAAQ,EAAE,CAAC;YACTH,GAAG,EAAE,SAAS;YACdxD,UAAU,EAAElQ,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEitB,eAAe,CAAC,EAAE,CAAC,CAAC,EAAE;cAClErW,MAAM,EAAE;YACV,CAAC;UACH,CAAC;QACH,CAAC,CAAC;MACJ;MAEA,OAAO;QACLlD,GAAG,EAAE,GAAG;QACRxD,UAAU,EAAE;UACV,OAAO,EAAE;QACX,CAAC;QACD2D,QAAQ,EAAE8Y;MACZ,CAAC;IACH,CAAC;EACH;AACF,CAAC;AAED,IAAIY,UAAU,GAAG;EACf9a,KAAK,EAAE,SAASA,KAAK,GAAG;IACtB,OAAO;MACLqY,mBAAmB,EAAE,SAASA,mBAAmB,CAACtP,WAAW,EAAE3L,IAAI,EAAE;QACnE,IAAI2d,UAAU,GAAG3d,IAAI,CAACvD,YAAY,CAAC,gBAAgB,CAAC;QACpD,IAAIqR,MAAM,GAAG6P,UAAU,KAAK,IAAI,GAAG,KAAK,GAAGA,UAAU,KAAK,EAAE,GAAG,IAAI,GAAGA,UAAU;QAChFhS,WAAW,CAAC,QAAQ,CAAC,GAAGmC,MAAM;QAC9B,OAAOnC,WAAW;MACpB;IACF,CAAC;EACH;AACF,CAAC;AAED,IAAIiS,OAAO,GAAG,CAACnb,SAAS,EAAE4U,eAAe,EAAES,MAAM,EAAEG,aAAa,EAAEC,UAAU,EAAEoC,cAAc,EAAEG,kBAAkB,EAAEO,eAAe,EAAEO,KAAK,EAAEkB,oBAAoB,EAAEiB,UAAU,CAAC;AAE3KzS,eAAe,CAAC2S,OAAO,EAAE;EACvBzS,SAAS,EAAE8B;AACb,CAAC,CAAC;AACF,IAAI4Q,QAAQ,GAAG5Q,GAAG,CAACZ,MAAM;AACzB,IAAIyR,QAAQ,GAAG7Q,GAAG,CAAClP,MAAM;AACzB,IAAIggB,SAAS,GAAG9Q,GAAG,CAACb,OAAO;AAC3B,IAAI4R,KAAK,GAAG/Q,GAAG,CAACtK,GAAG;AACnB,IAAIsb,OAAO,GAAGhR,GAAG,CAACL,KAAK;AACvB,IAAIsR,oBAAoB,GAAGjR,GAAG,CAACf,kBAAkB;AACjD,IAAIiS,QAAQ,GAAGlR,GAAG,CAACtJ,MAAM;AACzB,IAAIW,IAAI,GAAG2I,GAAG,CAAC3I,IAAI;AACnB,IAAIyT,KAAK,GAAG9K,GAAG,CAAC8K,KAAK;AACrB,IAAII,IAAI,GAAGlL,GAAG,CAACkL,IAAI;AACnB,IAAIhT,OAAO,GAAG8H,GAAG,CAAC9H,OAAO;AAEzB,SAAS0Y,QAAQ,IAAIxR,MAAM,EAAEyR,QAAQ,IAAI/f,MAAM,EAAEggB,SAAS,IAAI3R,OAAO,EAAE4R,KAAK,IAAIrb,GAAG,EAAEsb,OAAO,IAAIrR,KAAK,EAAEsR,oBAAoB,IAAIhS,kBAAkB,EAAEiS,QAAQ,IAAIxa,MAAM,EAAEW,IAAI,EAAEyT,KAAK,EAAEI,IAAI,EAAEhT,OAAO,EAAE8H,GAAG"}, "metadata": {}, "sourceType": "module"}