{"ast": null, "code": "import * as React from 'react';\nexport var ManagerReferenceNodeContext = React.createContext();\nexport var ManagerReferenceNodeSetterContext = React.createContext();\nexport function Manager(_ref) {\n  var children = _ref.children;\n  var _React$useState = React.useState(null),\n    referenceNode = _React$useState[0],\n    setReferenceNode = _React$useState[1];\n  var hasUnmounted = React.useRef(false);\n  React.useEffect(function () {\n    return function () {\n      hasUnmounted.current = true;\n    };\n  }, []);\n  var handleSetReferenceNode = React.useCallback(function (node) {\n    if (!hasUnmounted.current) {\n      setReferenceNode(node);\n    }\n  }, []);\n  return /*#__PURE__*/React.createElement(ManagerReferenceNodeContext.Provider, {\n    value: referenceNode\n  }, /*#__PURE__*/React.createElement(ManagerReferenceNodeSetterContext.Provider, {\n    value: handleSetReferenceNode\n  }, children));\n}", "map": {"version": 3, "names": ["React", "ManagerReferenceNodeContext", "createContext", "ManagerReferenceNodeSetterContext", "Manager", "_ref", "children", "_React$useState", "useState", "referenceNode", "setReferenceNode", "hasUnmounted", "useRef", "useEffect", "current", "handleSetReferenceNode", "useCallback", "node", "createElement", "Provider", "value"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/node_modules/react-popper/lib/esm/Manager.js"], "sourcesContent": ["import * as React from 'react';\nexport var ManagerReferenceNodeContext = React.createContext();\nexport var ManagerReferenceNodeSetterContext = React.createContext();\nexport function Manager(_ref) {\n  var children = _ref.children;\n\n  var _React$useState = React.useState(null),\n      referenceNode = _React$useState[0],\n      setReferenceNode = _React$useState[1];\n\n  var hasUnmounted = React.useRef(false);\n  React.useEffect(function () {\n    return function () {\n      hasUnmounted.current = true;\n    };\n  }, []);\n  var handleSetReferenceNode = React.useCallback(function (node) {\n    if (!hasUnmounted.current) {\n      setReferenceNode(node);\n    }\n  }, []);\n  return /*#__PURE__*/React.createElement(ManagerReferenceNodeContext.Provider, {\n    value: referenceNode\n  }, /*#__PURE__*/React.createElement(ManagerReferenceNodeSetterContext.Provider, {\n    value: handleSetReferenceNode\n  }, children));\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,IAAIC,2BAA2B,GAAGD,KAAK,CAACE,aAAa,EAAE;AAC9D,OAAO,IAAIC,iCAAiC,GAAGH,KAAK,CAACE,aAAa,EAAE;AACpE,OAAO,SAASE,OAAO,CAACC,IAAI,EAAE;EAC5B,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;EAE5B,IAAIC,eAAe,GAAGP,KAAK,CAACQ,QAAQ,CAAC,IAAI,CAAC;IACtCC,aAAa,GAAGF,eAAe,CAAC,CAAC,CAAC;IAClCG,gBAAgB,GAAGH,eAAe,CAAC,CAAC,CAAC;EAEzC,IAAII,YAAY,GAAGX,KAAK,CAACY,MAAM,CAAC,KAAK,CAAC;EACtCZ,KAAK,CAACa,SAAS,CAAC,YAAY;IAC1B,OAAO,YAAY;MACjBF,YAAY,CAACG,OAAO,GAAG,IAAI;IAC7B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,IAAIC,sBAAsB,GAAGf,KAAK,CAACgB,WAAW,CAAC,UAAUC,IAAI,EAAE;IAC7D,IAAI,CAACN,YAAY,CAACG,OAAO,EAAE;MACzBJ,gBAAgB,CAACO,IAAI,CAAC;IACxB;EACF,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,aAAajB,KAAK,CAACkB,aAAa,CAACjB,2BAA2B,CAACkB,QAAQ,EAAE;IAC5EC,KAAK,EAAEX;EACT,CAAC,EAAE,aAAaT,KAAK,CAACkB,aAAa,CAACf,iCAAiC,CAACgB,QAAQ,EAAE;IAC9EC,KAAK,EAAEL;EACT,CAAC,EAAET,QAAQ,CAAC,CAAC;AACf"}, "metadata": {}, "sourceType": "module"}