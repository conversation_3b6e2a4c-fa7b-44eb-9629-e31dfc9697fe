{"ast": null, "code": "export function createProblemDetails(title) {\n  let detail = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n  let status = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n  let instance = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : '';\n  if (isProblemDetails(title)) {\n    return title;\n  }\n  return {\n    title,\n    detail,\n    status,\n    instance\n  };\n}\nexport function isProblemDetails(value) {\n  return value && typeof value.title === 'string' && typeof value.status === 'number' && typeof value.detail === 'string' && typeof value.instance === 'string';\n}", "map": {"version": 3, "names": ["createProblemDetails", "title", "detail", "status", "instance", "isProblemDetails", "value"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/utils/problem-details.ts"], "sourcesContent": ["export interface ProblemDetails {\r\n  title: string;\r\n  status: number;\r\n  detail: string;\r\n  instance: string;\r\n}\r\n\r\nexport function createProblemDetails(title: string, detail: string = '', status = 0, instance: string = ''): ProblemDetails {\r\n  if(isProblemDetails(title)) {\r\n    return title;\r\n  }\r\n  \r\n  return {title, detail, status, instance};\r\n}\r\n\r\nexport function isProblemDetails(value:any):value is ProblemDetails {\r\n  return value && typeof value.title === 'string' && typeof value.status === 'number' && typeof value.detail === 'string' && typeof value.instance === 'string';\r\n}\r\n"], "mappings": "AAOA,OAAO,SAASA,oBAAoB,CAACC,KAAa,EAA0E;EAAA,IAAxEC,MAAc,uEAAG,EAAE;EAAA,IAAEC,MAAM,uEAAG,CAAC;EAAA,IAAEC,QAAgB,uEAAG,EAAE;EACxG,IAAGC,gBAAgB,CAACJ,KAAK,CAAC,EAAE;IAC1B,OAAOA,KAAK;EACd;EAEA,OAAO;IAACA,KAAK;IAAEC,MAAM;IAAEC,MAAM;IAAEC;EAAQ,CAAC;AAC1C;AAEA,OAAO,SAASC,gBAAgB,CAACC,KAAS,EAA0B;EAClE,OAAOA,KAAK,IAAI,OAAOA,KAAK,CAACL,KAAK,KAAK,QAAQ,IAAI,OAAOK,KAAK,CAACH,MAAM,KAAK,QAAQ,IAAI,OAAOG,KAAK,CAACJ,MAAM,KAAK,QAAQ,IAAI,OAAOI,KAAK,CAACF,QAAQ,KAAK,QAAQ;AAC/J"}, "metadata": {}, "sourceType": "module"}