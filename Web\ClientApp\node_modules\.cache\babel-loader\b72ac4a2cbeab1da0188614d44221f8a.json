{"ast": null, "code": "import getParentNode from \"./getParentNode.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nexport default function getScrollParent(node) {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n  return getScrollParent(getParentNode(node));\n}", "map": {"version": 3, "names": ["getParentNode", "isScrollParent", "getNodeName", "isHTMLElement", "getScrollParent", "node", "indexOf", "ownerDocument", "body"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js"], "sourcesContent": ["import getParentNode from \"./getParentNode.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nexport default function getScrollParent(node) {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,cAAc,MAAM,qBAAqB;AAChD,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,eAAe,SAASC,eAAe,CAACC,IAAI,EAAE;EAC5C,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,WAAW,CAAC,CAACC,OAAO,CAACJ,WAAW,CAACG,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE;IACjE;IACA,OAAOA,IAAI,CAACE,aAAa,CAACC,IAAI;EAChC;EAEA,IAAIL,aAAa,CAACE,IAAI,CAAC,IAAIJ,cAAc,CAACI,IAAI,CAAC,EAAE;IAC/C,OAAOA,IAAI;EACb;EAEA,OAAOD,eAAe,CAACJ,aAAa,CAACK,IAAI,CAAC,CAAC;AAC7C"}, "metadata": {}, "sourceType": "module"}