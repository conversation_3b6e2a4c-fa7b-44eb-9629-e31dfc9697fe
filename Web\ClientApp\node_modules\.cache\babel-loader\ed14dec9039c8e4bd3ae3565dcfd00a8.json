{"ast": null, "code": "// The error overlay is inspired (and mostly copied) from Create React App (https://github.com/facebookincubator/create-react-app)\n// They, in turn, got inspired by webpack-hot-middleware (https://github.com/glenjamin/webpack-hot-middleware).\nimport ansiHTML from \"ansi-html-community\";\nimport { encode } from \"html-entities\";\nvar colors = {\n  reset: [\"transparent\", \"transparent\"],\n  black: \"181818\",\n  red: \"E36049\",\n  green: \"B3CB74\",\n  yellow: \"FFD080\",\n  blue: \"7CAFC2\",\n  magenta: \"7FACCA\",\n  cyan: \"C3C2EF\",\n  lightgrey: \"EBE7E3\",\n  darkgrey: \"6D7891\"\n};\n/** @type {HTMLIFrameElement | null | undefined} */\n\nvar iframeContainerElement;\n/** @type {HTMLDivElement | null | undefined} */\n\nvar containerElement;\n/** @type {Array<(element: HTMLDivElement) => void>} */\n\nvar onLoadQueue = [];\nansiHTML.setColors(colors);\nfunction createContainer() {\n  iframeContainerElement = document.createElement(\"iframe\");\n  iframeContainerElement.id = \"webpack-dev-server-client-overlay\";\n  iframeContainerElement.src = \"about:blank\";\n  iframeContainerElement.style.position = \"fixed\";\n  iframeContainerElement.style.left = 0;\n  iframeContainerElement.style.top = 0;\n  iframeContainerElement.style.right = 0;\n  iframeContainerElement.style.bottom = 0;\n  iframeContainerElement.style.width = \"100vw\";\n  iframeContainerElement.style.height = \"100vh\";\n  iframeContainerElement.style.border = \"none\";\n  iframeContainerElement.style.zIndex = 9999999999;\n  iframeContainerElement.onload = function () {\n    containerElement = /** @type {Document} */\n\n    /** @type {HTMLIFrameElement} */\n    iframeContainerElement.contentDocument.createElement(\"div\");\n    containerElement.id = \"webpack-dev-server-client-overlay-div\";\n    containerElement.style.position = \"fixed\";\n    containerElement.style.boxSizing = \"border-box\";\n    containerElement.style.left = 0;\n    containerElement.style.top = 0;\n    containerElement.style.right = 0;\n    containerElement.style.bottom = 0;\n    containerElement.style.width = \"100vw\";\n    containerElement.style.height = \"100vh\";\n    containerElement.style.backgroundColor = \"rgba(0, 0, 0, 0.85)\";\n    containerElement.style.color = \"#E8E8E8\";\n    containerElement.style.fontFamily = \"Menlo, Consolas, monospace\";\n    containerElement.style.fontSize = \"large\";\n    containerElement.style.padding = \"2rem\";\n    containerElement.style.lineHeight = \"1.2\";\n    containerElement.style.whiteSpace = \"pre-wrap\";\n    containerElement.style.overflow = \"auto\";\n    var headerElement = document.createElement(\"span\");\n    headerElement.innerText = \"Compiled with problems:\";\n    var closeButtonElement = document.createElement(\"button\");\n    closeButtonElement.innerText = \"X\";\n    closeButtonElement.style.background = \"transparent\";\n    closeButtonElement.style.border = \"none\";\n    closeButtonElement.style.fontSize = \"20px\";\n    closeButtonElement.style.fontWeight = \"bold\";\n    closeButtonElement.style.color = \"white\";\n    closeButtonElement.style.cursor = \"pointer\";\n    closeButtonElement.style.cssFloat = \"right\"; // @ts-ignore\n\n    closeButtonElement.style.styleFloat = \"right\";\n    closeButtonElement.addEventListener(\"click\", function () {\n      hide();\n    });\n    containerElement.appendChild(headerElement);\n    containerElement.appendChild(closeButtonElement);\n    containerElement.appendChild(document.createElement(\"br\"));\n    containerElement.appendChild(document.createElement(\"br\"));\n    /** @type {Document} */\n\n    /** @type {HTMLIFrameElement} */\n    iframeContainerElement.contentDocument.body.appendChild(containerElement);\n    onLoadQueue.forEach(function (onLoad) {\n      onLoad( /** @type {HTMLDivElement} */\n      containerElement);\n    });\n    onLoadQueue = [];\n    /** @type {HTMLIFrameElement} */\n\n    iframeContainerElement.onload = null;\n  };\n  document.body.appendChild(iframeContainerElement);\n}\n/**\n * @param {(element: HTMLDivElement) => void} callback\n */\n\nfunction ensureOverlayExists(callback) {\n  if (containerElement) {\n    // Everything is ready, call the callback right away.\n    callback(containerElement);\n    return;\n  }\n  onLoadQueue.push(callback);\n  if (iframeContainerElement) {\n    return;\n  }\n  createContainer();\n} // Successful compilation.\n\nfunction hide() {\n  if (!iframeContainerElement) {\n    return;\n  } // Clean up and reset internal state.\n\n  document.body.removeChild(iframeContainerElement);\n  iframeContainerElement = null;\n  containerElement = null;\n}\n/**\n * @param {string} type\n * @param {string  | { file?: string, moduleName?: string, loc?: string, message?: string }} item\n * @returns {{ header: string, body: string }}\n */\n\nfunction formatProblem(type, item) {\n  var header = type === \"warning\" ? \"WARNING\" : \"ERROR\";\n  var body = \"\";\n  if (typeof item === \"string\") {\n    body += item;\n  } else {\n    var file = item.file || \"\"; // eslint-disable-next-line no-nested-ternary\n\n    var moduleName = item.moduleName ? item.moduleName.indexOf(\"!\") !== -1 ? \"\".concat(item.moduleName.replace(/^(\\s|\\S)*!/, \"\"), \" (\").concat(item.moduleName, \")\") : \"\".concat(item.moduleName) : \"\";\n    var loc = item.loc;\n    header += \"\".concat(moduleName || file ? \" in \".concat(moduleName ? \"\".concat(moduleName).concat(file ? \" (\".concat(file, \")\") : \"\") : file).concat(loc ? \" \".concat(loc) : \"\") : \"\");\n    body += item.message || \"\";\n  }\n  return {\n    header: header,\n    body: body\n  };\n} // Compilation with errors (e.g. syntax error or missing modules).\n\n/**\n * @param {string} type\n * @param {Array<string  | { file?: string, moduleName?: string, loc?: string, message?: string }>} messages\n */\n\nfunction show(type, messages) {\n  ensureOverlayExists(function () {\n    messages.forEach(function (message) {\n      var entryElement = document.createElement(\"div\");\n      var typeElement = document.createElement(\"span\");\n      var _formatProblem = formatProblem(type, message),\n        header = _formatProblem.header,\n        body = _formatProblem.body;\n      typeElement.innerText = header;\n      typeElement.style.color = \"#\".concat(colors.red); // Make it look similar to our terminal.\n\n      var text = ansiHTML(encode(body));\n      var messageTextNode = document.createElement(\"div\");\n      messageTextNode.innerHTML = text;\n      entryElement.appendChild(typeElement);\n      entryElement.appendChild(document.createElement(\"br\"));\n      entryElement.appendChild(document.createElement(\"br\"));\n      entryElement.appendChild(messageTextNode);\n      entryElement.appendChild(document.createElement(\"br\"));\n      entryElement.appendChild(document.createElement(\"br\"));\n      /** @type {HTMLDivElement} */\n\n      containerElement.appendChild(entryElement);\n    });\n  });\n}\nexport { formatProblem, show, hide };", "map": {"version": 3, "names": ["ansiHTML", "encode", "colors", "reset", "black", "red", "green", "yellow", "blue", "magenta", "cyan", "<PERSON><PERSON>rey", "<PERSON><PERSON>rey", "iframeContainerElement", "containerElement", "onLoadQueue", "setColors", "createContainer", "document", "createElement", "id", "src", "style", "position", "left", "top", "right", "bottom", "width", "height", "border", "zIndex", "onload", "contentDocument", "boxSizing", "backgroundColor", "color", "fontFamily", "fontSize", "padding", "lineHeight", "whiteSpace", "overflow", "headerElement", "innerText", "closeButtonElement", "background", "fontWeight", "cursor", "cssFloat", "styleFloat", "addEventListener", "hide", "append<PERSON><PERSON><PERSON>", "body", "for<PERSON>ach", "onLoad", "ensureOverlayExists", "callback", "push", "<PERSON><PERSON><PERSON><PERSON>", "formatProblem", "type", "item", "header", "file", "moduleName", "indexOf", "concat", "replace", "loc", "message", "show", "messages", "entryElement", "typeElement", "_formatProblem", "text", "messageTextNode", "innerHTML"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/node_modules/webpack-dev-server/client/overlay.js"], "sourcesContent": ["// The error overlay is inspired (and mostly copied) from Create React App (https://github.com/facebookincubator/create-react-app)\n// They, in turn, got inspired by webpack-hot-middleware (https://github.com/glenjamin/webpack-hot-middleware).\nimport ansiHTML from \"ansi-html-community\";\nimport { encode } from \"html-entities\";\nvar colors = {\n  reset: [\"transparent\", \"transparent\"],\n  black: \"181818\",\n  red: \"E36049\",\n  green: \"B3CB74\",\n  yellow: \"FFD080\",\n  blue: \"7CAFC2\",\n  magenta: \"7FACCA\",\n  cyan: \"C3C2EF\",\n  lightgrey: \"EBE7E3\",\n  darkgrey: \"6D7891\"\n};\n/** @type {HTMLIFrameElement | null | undefined} */\n\nvar iframeContainerElement;\n/** @type {HTMLDivElement | null | undefined} */\n\nvar containerElement;\n/** @type {Array<(element: HTMLDivElement) => void>} */\n\nvar onLoadQueue = [];\nansiHTML.setColors(colors);\n\nfunction createContainer() {\n  iframeContainerElement = document.createElement(\"iframe\");\n  iframeContainerElement.id = \"webpack-dev-server-client-overlay\";\n  iframeContainerElement.src = \"about:blank\";\n  iframeContainerElement.style.position = \"fixed\";\n  iframeContainerElement.style.left = 0;\n  iframeContainerElement.style.top = 0;\n  iframeContainerElement.style.right = 0;\n  iframeContainerElement.style.bottom = 0;\n  iframeContainerElement.style.width = \"100vw\";\n  iframeContainerElement.style.height = \"100vh\";\n  iframeContainerElement.style.border = \"none\";\n  iframeContainerElement.style.zIndex = 9999999999;\n\n  iframeContainerElement.onload = function () {\n    containerElement =\n    /** @type {Document} */\n\n    /** @type {HTMLIFrameElement} */\n    iframeContainerElement.contentDocument.createElement(\"div\");\n    containerElement.id = \"webpack-dev-server-client-overlay-div\";\n    containerElement.style.position = \"fixed\";\n    containerElement.style.boxSizing = \"border-box\";\n    containerElement.style.left = 0;\n    containerElement.style.top = 0;\n    containerElement.style.right = 0;\n    containerElement.style.bottom = 0;\n    containerElement.style.width = \"100vw\";\n    containerElement.style.height = \"100vh\";\n    containerElement.style.backgroundColor = \"rgba(0, 0, 0, 0.85)\";\n    containerElement.style.color = \"#E8E8E8\";\n    containerElement.style.fontFamily = \"Menlo, Consolas, monospace\";\n    containerElement.style.fontSize = \"large\";\n    containerElement.style.padding = \"2rem\";\n    containerElement.style.lineHeight = \"1.2\";\n    containerElement.style.whiteSpace = \"pre-wrap\";\n    containerElement.style.overflow = \"auto\";\n    var headerElement = document.createElement(\"span\");\n    headerElement.innerText = \"Compiled with problems:\";\n    var closeButtonElement = document.createElement(\"button\");\n    closeButtonElement.innerText = \"X\";\n    closeButtonElement.style.background = \"transparent\";\n    closeButtonElement.style.border = \"none\";\n    closeButtonElement.style.fontSize = \"20px\";\n    closeButtonElement.style.fontWeight = \"bold\";\n    closeButtonElement.style.color = \"white\";\n    closeButtonElement.style.cursor = \"pointer\";\n    closeButtonElement.style.cssFloat = \"right\"; // @ts-ignore\n\n    closeButtonElement.style.styleFloat = \"right\";\n    closeButtonElement.addEventListener(\"click\", function () {\n      hide();\n    });\n    containerElement.appendChild(headerElement);\n    containerElement.appendChild(closeButtonElement);\n    containerElement.appendChild(document.createElement(\"br\"));\n    containerElement.appendChild(document.createElement(\"br\"));\n    /** @type {Document} */\n\n    /** @type {HTMLIFrameElement} */\n    iframeContainerElement.contentDocument.body.appendChild(containerElement);\n    onLoadQueue.forEach(function (onLoad) {\n      onLoad(\n      /** @type {HTMLDivElement} */\n      containerElement);\n    });\n    onLoadQueue = [];\n    /** @type {HTMLIFrameElement} */\n\n    iframeContainerElement.onload = null;\n  };\n\n  document.body.appendChild(iframeContainerElement);\n}\n/**\n * @param {(element: HTMLDivElement) => void} callback\n */\n\n\nfunction ensureOverlayExists(callback) {\n  if (containerElement) {\n    // Everything is ready, call the callback right away.\n    callback(containerElement);\n    return;\n  }\n\n  onLoadQueue.push(callback);\n\n  if (iframeContainerElement) {\n    return;\n  }\n\n  createContainer();\n} // Successful compilation.\n\n\nfunction hide() {\n  if (!iframeContainerElement) {\n    return;\n  } // Clean up and reset internal state.\n\n\n  document.body.removeChild(iframeContainerElement);\n  iframeContainerElement = null;\n  containerElement = null;\n}\n/**\n * @param {string} type\n * @param {string  | { file?: string, moduleName?: string, loc?: string, message?: string }} item\n * @returns {{ header: string, body: string }}\n */\n\n\nfunction formatProblem(type, item) {\n  var header = type === \"warning\" ? \"WARNING\" : \"ERROR\";\n  var body = \"\";\n\n  if (typeof item === \"string\") {\n    body += item;\n  } else {\n    var file = item.file || \"\"; // eslint-disable-next-line no-nested-ternary\n\n    var moduleName = item.moduleName ? item.moduleName.indexOf(\"!\") !== -1 ? \"\".concat(item.moduleName.replace(/^(\\s|\\S)*!/, \"\"), \" (\").concat(item.moduleName, \")\") : \"\".concat(item.moduleName) : \"\";\n    var loc = item.loc;\n    header += \"\".concat(moduleName || file ? \" in \".concat(moduleName ? \"\".concat(moduleName).concat(file ? \" (\".concat(file, \")\") : \"\") : file).concat(loc ? \" \".concat(loc) : \"\") : \"\");\n    body += item.message || \"\";\n  }\n\n  return {\n    header: header,\n    body: body\n  };\n} // Compilation with errors (e.g. syntax error or missing modules).\n\n/**\n * @param {string} type\n * @param {Array<string  | { file?: string, moduleName?: string, loc?: string, message?: string }>} messages\n */\n\n\nfunction show(type, messages) {\n  ensureOverlayExists(function () {\n    messages.forEach(function (message) {\n      var entryElement = document.createElement(\"div\");\n      var typeElement = document.createElement(\"span\");\n\n      var _formatProblem = formatProblem(type, message),\n          header = _formatProblem.header,\n          body = _formatProblem.body;\n\n      typeElement.innerText = header;\n      typeElement.style.color = \"#\".concat(colors.red); // Make it look similar to our terminal.\n\n      var text = ansiHTML(encode(body));\n      var messageTextNode = document.createElement(\"div\");\n      messageTextNode.innerHTML = text;\n      entryElement.appendChild(typeElement);\n      entryElement.appendChild(document.createElement(\"br\"));\n      entryElement.appendChild(document.createElement(\"br\"));\n      entryElement.appendChild(messageTextNode);\n      entryElement.appendChild(document.createElement(\"br\"));\n      entryElement.appendChild(document.createElement(\"br\"));\n      /** @type {HTMLDivElement} */\n\n      containerElement.appendChild(entryElement);\n    });\n  });\n}\n\nexport { formatProblem, show, hide };"], "mappings": "AAAA;AACA;AACA,OAAOA,QAAQ,MAAM,qBAAqB;AAC1C,SAASC,MAAM,QAAQ,eAAe;AACtC,IAAIC,MAAM,GAAG;EACXC,KAAK,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC;EACrCC,KAAK,EAAE,QAAQ;EACfC,GAAG,EAAE,QAAQ;EACbC,KAAK,EAAE,QAAQ;EACfC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,QAAQ;EACdC,OAAO,EAAE,QAAQ;EACjBC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,QAAQ;EACnBC,QAAQ,EAAE;AACZ,CAAC;AACD;;AAEA,IAAIC,sBAAsB;AAC1B;;AAEA,IAAIC,gBAAgB;AACpB;;AAEA,IAAIC,WAAW,GAAG,EAAE;AACpBf,QAAQ,CAACgB,SAAS,CAACd,MAAM,CAAC;AAE1B,SAASe,eAAe,GAAG;EACzBJ,sBAAsB,GAAGK,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EACzDN,sBAAsB,CAACO,EAAE,GAAG,mCAAmC;EAC/DP,sBAAsB,CAACQ,GAAG,GAAG,aAAa;EAC1CR,sBAAsB,CAACS,KAAK,CAACC,QAAQ,GAAG,OAAO;EAC/CV,sBAAsB,CAACS,KAAK,CAACE,IAAI,GAAG,CAAC;EACrCX,sBAAsB,CAACS,KAAK,CAACG,GAAG,GAAG,CAAC;EACpCZ,sBAAsB,CAACS,KAAK,CAACI,KAAK,GAAG,CAAC;EACtCb,sBAAsB,CAACS,KAAK,CAACK,MAAM,GAAG,CAAC;EACvCd,sBAAsB,CAACS,KAAK,CAACM,KAAK,GAAG,OAAO;EAC5Cf,sBAAsB,CAACS,KAAK,CAACO,MAAM,GAAG,OAAO;EAC7ChB,sBAAsB,CAACS,KAAK,CAACQ,MAAM,GAAG,MAAM;EAC5CjB,sBAAsB,CAACS,KAAK,CAACS,MAAM,GAAG,UAAU;EAEhDlB,sBAAsB,CAACmB,MAAM,GAAG,YAAY;IAC1ClB,gBAAgB,GAChB;;IAEA;IACAD,sBAAsB,CAACoB,eAAe,CAACd,aAAa,CAAC,KAAK,CAAC;IAC3DL,gBAAgB,CAACM,EAAE,GAAG,uCAAuC;IAC7DN,gBAAgB,CAACQ,KAAK,CAACC,QAAQ,GAAG,OAAO;IACzCT,gBAAgB,CAACQ,KAAK,CAACY,SAAS,GAAG,YAAY;IAC/CpB,gBAAgB,CAACQ,KAAK,CAACE,IAAI,GAAG,CAAC;IAC/BV,gBAAgB,CAACQ,KAAK,CAACG,GAAG,GAAG,CAAC;IAC9BX,gBAAgB,CAACQ,KAAK,CAACI,KAAK,GAAG,CAAC;IAChCZ,gBAAgB,CAACQ,KAAK,CAACK,MAAM,GAAG,CAAC;IACjCb,gBAAgB,CAACQ,KAAK,CAACM,KAAK,GAAG,OAAO;IACtCd,gBAAgB,CAACQ,KAAK,CAACO,MAAM,GAAG,OAAO;IACvCf,gBAAgB,CAACQ,KAAK,CAACa,eAAe,GAAG,qBAAqB;IAC9DrB,gBAAgB,CAACQ,KAAK,CAACc,KAAK,GAAG,SAAS;IACxCtB,gBAAgB,CAACQ,KAAK,CAACe,UAAU,GAAG,4BAA4B;IAChEvB,gBAAgB,CAACQ,KAAK,CAACgB,QAAQ,GAAG,OAAO;IACzCxB,gBAAgB,CAACQ,KAAK,CAACiB,OAAO,GAAG,MAAM;IACvCzB,gBAAgB,CAACQ,KAAK,CAACkB,UAAU,GAAG,KAAK;IACzC1B,gBAAgB,CAACQ,KAAK,CAACmB,UAAU,GAAG,UAAU;IAC9C3B,gBAAgB,CAACQ,KAAK,CAACoB,QAAQ,GAAG,MAAM;IACxC,IAAIC,aAAa,GAAGzB,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IAClDwB,aAAa,CAACC,SAAS,GAAG,yBAAyB;IACnD,IAAIC,kBAAkB,GAAG3B,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IACzD0B,kBAAkB,CAACD,SAAS,GAAG,GAAG;IAClCC,kBAAkB,CAACvB,KAAK,CAACwB,UAAU,GAAG,aAAa;IACnDD,kBAAkB,CAACvB,KAAK,CAACQ,MAAM,GAAG,MAAM;IACxCe,kBAAkB,CAACvB,KAAK,CAACgB,QAAQ,GAAG,MAAM;IAC1CO,kBAAkB,CAACvB,KAAK,CAACyB,UAAU,GAAG,MAAM;IAC5CF,kBAAkB,CAACvB,KAAK,CAACc,KAAK,GAAG,OAAO;IACxCS,kBAAkB,CAACvB,KAAK,CAAC0B,MAAM,GAAG,SAAS;IAC3CH,kBAAkB,CAACvB,KAAK,CAAC2B,QAAQ,GAAG,OAAO,CAAC,CAAC;;IAE7CJ,kBAAkB,CAACvB,KAAK,CAAC4B,UAAU,GAAG,OAAO;IAC7CL,kBAAkB,CAACM,gBAAgB,CAAC,OAAO,EAAE,YAAY;MACvDC,IAAI,EAAE;IACR,CAAC,CAAC;IACFtC,gBAAgB,CAACuC,WAAW,CAACV,aAAa,CAAC;IAC3C7B,gBAAgB,CAACuC,WAAW,CAACR,kBAAkB,CAAC;IAChD/B,gBAAgB,CAACuC,WAAW,CAACnC,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC,CAAC;IAC1DL,gBAAgB,CAACuC,WAAW,CAACnC,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC,CAAC;IAC1D;;IAEA;IACAN,sBAAsB,CAACoB,eAAe,CAACqB,IAAI,CAACD,WAAW,CAACvC,gBAAgB,CAAC;IACzEC,WAAW,CAACwC,OAAO,CAAC,UAAUC,MAAM,EAAE;MACpCA,MAAM,EACN;MACA1C,gBAAgB,CAAC;IACnB,CAAC,CAAC;IACFC,WAAW,GAAG,EAAE;IAChB;;IAEAF,sBAAsB,CAACmB,MAAM,GAAG,IAAI;EACtC,CAAC;EAEDd,QAAQ,CAACoC,IAAI,CAACD,WAAW,CAACxC,sBAAsB,CAAC;AACnD;AACA;AACA;AACA;;AAGA,SAAS4C,mBAAmB,CAACC,QAAQ,EAAE;EACrC,IAAI5C,gBAAgB,EAAE;IACpB;IACA4C,QAAQ,CAAC5C,gBAAgB,CAAC;IAC1B;EACF;EAEAC,WAAW,CAAC4C,IAAI,CAACD,QAAQ,CAAC;EAE1B,IAAI7C,sBAAsB,EAAE;IAC1B;EACF;EAEAI,eAAe,EAAE;AACnB,CAAC,CAAC;;AAGF,SAASmC,IAAI,GAAG;EACd,IAAI,CAACvC,sBAAsB,EAAE;IAC3B;EACF,CAAC,CAAC;;EAGFK,QAAQ,CAACoC,IAAI,CAACM,WAAW,CAAC/C,sBAAsB,CAAC;EACjDA,sBAAsB,GAAG,IAAI;EAC7BC,gBAAgB,GAAG,IAAI;AACzB;AACA;AACA;AACA;AACA;AACA;;AAGA,SAAS+C,aAAa,CAACC,IAAI,EAAEC,IAAI,EAAE;EACjC,IAAIC,MAAM,GAAGF,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,OAAO;EACrD,IAAIR,IAAI,GAAG,EAAE;EAEb,IAAI,OAAOS,IAAI,KAAK,QAAQ,EAAE;IAC5BT,IAAI,IAAIS,IAAI;EACd,CAAC,MAAM;IACL,IAAIE,IAAI,GAAGF,IAAI,CAACE,IAAI,IAAI,EAAE,CAAC,CAAC;;IAE5B,IAAIC,UAAU,GAAGH,IAAI,CAACG,UAAU,GAAGH,IAAI,CAACG,UAAU,CAACC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAACC,MAAM,CAACL,IAAI,CAACG,UAAU,CAACG,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,CAACD,MAAM,CAACL,IAAI,CAACG,UAAU,EAAE,GAAG,CAAC,GAAG,EAAE,CAACE,MAAM,CAACL,IAAI,CAACG,UAAU,CAAC,GAAG,EAAE;IAClM,IAAII,GAAG,GAAGP,IAAI,CAACO,GAAG;IAClBN,MAAM,IAAI,EAAE,CAACI,MAAM,CAACF,UAAU,IAAID,IAAI,GAAG,MAAM,CAACG,MAAM,CAACF,UAAU,GAAG,EAAE,CAACE,MAAM,CAACF,UAAU,CAAC,CAACE,MAAM,CAACH,IAAI,GAAG,IAAI,CAACG,MAAM,CAACH,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC,GAAGA,IAAI,CAAC,CAACG,MAAM,CAACE,GAAG,GAAG,GAAG,CAACF,MAAM,CAACE,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;IACrLhB,IAAI,IAAIS,IAAI,CAACQ,OAAO,IAAI,EAAE;EAC5B;EAEA,OAAO;IACLP,MAAM,EAAEA,MAAM;IACdV,IAAI,EAAEA;EACR,CAAC;AACH,CAAC,CAAC;;AAEF;AACA;AACA;AACA;;AAGA,SAASkB,IAAI,CAACV,IAAI,EAAEW,QAAQ,EAAE;EAC5BhB,mBAAmB,CAAC,YAAY;IAC9BgB,QAAQ,CAAClB,OAAO,CAAC,UAAUgB,OAAO,EAAE;MAClC,IAAIG,YAAY,GAAGxD,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAChD,IAAIwD,WAAW,GAAGzD,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;MAEhD,IAAIyD,cAAc,GAAGf,aAAa,CAACC,IAAI,EAAES,OAAO,CAAC;QAC7CP,MAAM,GAAGY,cAAc,CAACZ,MAAM;QAC9BV,IAAI,GAAGsB,cAAc,CAACtB,IAAI;MAE9BqB,WAAW,CAAC/B,SAAS,GAAGoB,MAAM;MAC9BW,WAAW,CAACrD,KAAK,CAACc,KAAK,GAAG,GAAG,CAACgC,MAAM,CAAClE,MAAM,CAACG,GAAG,CAAC,CAAC,CAAC;;MAElD,IAAIwE,IAAI,GAAG7E,QAAQ,CAACC,MAAM,CAACqD,IAAI,CAAC,CAAC;MACjC,IAAIwB,eAAe,GAAG5D,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACnD2D,eAAe,CAACC,SAAS,GAAGF,IAAI;MAChCH,YAAY,CAACrB,WAAW,CAACsB,WAAW,CAAC;MACrCD,YAAY,CAACrB,WAAW,CAACnC,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC,CAAC;MACtDuD,YAAY,CAACrB,WAAW,CAACnC,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC,CAAC;MACtDuD,YAAY,CAACrB,WAAW,CAACyB,eAAe,CAAC;MACzCJ,YAAY,CAACrB,WAAW,CAACnC,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC,CAAC;MACtDuD,YAAY,CAACrB,WAAW,CAACnC,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC,CAAC;MACtD;;MAEAL,gBAAgB,CAACuC,WAAW,CAACqB,YAAY,CAAC;IAC5C,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAEA,SAASb,aAAa,EAAEW,IAAI,EAAEpB,IAAI"}, "metadata": {}, "sourceType": "module"}