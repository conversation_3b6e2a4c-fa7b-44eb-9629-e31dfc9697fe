{"ast": null, "code": "import { lazy } from 'react';\nexport const routes = {\n  home: {\n    path: '/',\n    element: /*#__PURE__*/lazy(() => import('pages/Index'))\n  },\n  login: {\n    path: '/login',\n    element: /*#__PURE__*/lazy(() => import('features/auth/Login'))\n  },\n  orders: {\n    path: '/orders',\n    element: /*#__PURE__*/lazy(() => import('features/orders/List')),\n    routes: {\n      new: () => '/orders/new',\n      byStickDate: {\n        path: '/orders/stick-date',\n        element: /*#__PURE__*/lazy(() => import('features/orders/ByStickDate'))\n      },\n      byFlowerDate: {\n        path: '/orders/flower-date',\n        element: /*#__PURE__*/lazy(() => import('features/orders/ByFlowerDate'))\n      },\n      bySpaceDate: {\n        path: '/orders/space-date',\n        element: /*#__PURE__*/lazy(() => import('features/orders/BySpaceDate'))\n      },\n      byPinchDate: {\n        path: '/orders/pinch-date',\n        element: /*#__PURE__*/lazy(() => import('features/orders/ByPinchDate'))\n      },\n      detail: {\n        path: '/orders/:id',\n        element: /*#__PURE__*/lazy(() => import('features/orders/Detail')),\n        to: id => `/orders/${id}`\n      }\n    }\n  },\n  plants: {\n    path: '/plants',\n    element: /*#__PURE__*/lazy(() => import('features/plants/List')),\n    routes: {\n      new: () => '/plants/new',\n      detail: {\n        path: '/plants/:id',\n        element: /*#__PURE__*/lazy(() => import('features/plants/Detail')),\n        to: id => `/plants/${id}`\n      }\n    }\n  },\n  zones: {\n    path: '/zones',\n    element: /*#__PURE__*/lazy(() => import('features/zones/List')),\n    routes: {\n      new: () => '/zones/new',\n      detail: {\n        path: '/zones/:id',\n        element: /*#__PURE__*/lazy(() => import('features/zones/Detail')),\n        to: id => `/zones/${id}`\n      }\n    }\n  },\n  customers: {\n    path: '/customers',\n    element: /*#__PURE__*/lazy(() => import('features/customers/List')),\n    routes: {\n      new: () => '/customers/new',\n      detail: {\n        path: '/customers/:id',\n        element: /*#__PURE__*/lazy(() => import('features/customers/Detail')),\n        to: id => `/customers/${id}`\n      }\n    }\n  },\n  colours: {\n    path: '/colours',\n    element: /*#__PURE__*/lazy(() => import('features/colours/List')),\n    routes: {\n      new: () => '/colours/new',\n      detail: {\n        path: '/colours/:id',\n        element: /*#__PURE__*/lazy(() => import('features/colours/Detail')),\n        to: id => `/colours/${id}`\n      }\n    }\n  },\n  users: {\n    path: '/users',\n    element: /*#__PURE__*/lazy(() => import('features/users/List')),\n    routes: {\n      detail: {\n        path: '/users/:name',\n        element: /*#__PURE__*/lazy(() => import('features/users/Detail')),\n        to: name => `/users/${name}`\n      }\n    }\n  },\n  driverTasks: {\n    list: {\n      path: '/driver-tasks',\n      element: /*#__PURE__*/lazy(() => import('features/driver-tasks/List'))\n    },\n    new: {\n      path: '/driver-tasks/new',\n      element: /*#__PURE__*/lazy(() => import('features/driver-tasks/New'))\n    },\n    detail: {\n      path: '/driver-tasks/:id',\n      to: id => `/driver-tasks/${id}`,\n      element: /*#__PURE__*/lazy(() => import('features/driver-tasks/Detail'))\n    }\n  }\n};", "map": {"version": 3, "names": ["lazy", "routes", "home", "path", "element", "login", "orders", "new", "byStickDate", "byFlowerDate", "bySpaceDate", "byPinchDate", "detail", "to", "id", "plants", "zones", "customers", "colours", "users", "name", "driverTasks", "list"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/app/routes.ts"], "sourcesContent": ["import { lazy } from 'react';\r\n\r\nexport const routes = {\r\n  home: {\r\n    path: '/',\r\n    element: lazy(() => import('pages/Index')),\r\n  },\r\n  login: {\r\n    path: '/login',\r\n    element: lazy(() => import('features/auth/Login')),\r\n  },\r\n  orders: {\r\n    path: '/orders',\r\n    element: lazy(() => import('features/orders/List')),\r\n    routes: {\r\n      new: () => '/orders/new',\r\n      byStickDate: {\r\n        path: '/orders/stick-date',\r\n        element: lazy(() => import('features/orders/ByStickDate')),\r\n      },\r\n      byFlowerDate: {\r\n        path: '/orders/flower-date',\r\n        element: lazy(() => import('features/orders/ByFlowerDate')),\r\n      },\r\n      bySpaceDate: {\r\n        path: '/orders/space-date',\r\n        element: lazy(() => import('features/orders/BySpaceDate')),\r\n      },\r\n      byPinchDate: {\r\n        path: '/orders/pinch-date',\r\n        element: lazy(() => import('features/orders/ByPinchDate')),\r\n      },\r\n      detail: {\r\n        path: '/orders/:id',\r\n        element: lazy(() => import('features/orders/Detail')),\r\n        to: (id: string) => `/orders/${id}`,\r\n      },\r\n    },\r\n  },\r\n  plants: {\r\n    path: '/plants',\r\n    element: lazy(() => import('features/plants/List')),\r\n    routes: {\r\n      new: () => '/plants/new',\r\n      detail: {\r\n        path: '/plants/:id',\r\n        element: lazy(() => import('features/plants/Detail')),\r\n        to: (id: string) => `/plants/${id}`,\r\n      },\r\n    },\r\n  },\r\n  zones: {\r\n    path: '/zones',\r\n    element: lazy(() => import('features/zones/List')),\r\n    routes: {\r\n      new: () => '/zones/new',\r\n      detail: {\r\n        path: '/zones/:id',\r\n        element: lazy(() => import('features/zones/Detail')),\r\n        to: (id: string) => `/zones/${id}`,\r\n      },\r\n    },\r\n  },\r\n  customers: {\r\n    path: '/customers',\r\n    element: lazy(() => import('features/customers/List')),\r\n    routes: {\r\n      new: () => '/customers/new',\r\n      detail: {\r\n        path: '/customers/:id',\r\n        element: lazy(() => import('features/customers/Detail')),\r\n        to: (id: string) => `/customers/${id}`,\r\n      },\r\n    },\r\n  },\r\n  colours: {\r\n    path: '/colours',\r\n    element: lazy(() => import('features/colours/List')),\r\n    routes: {\r\n      new: () => '/colours/new',\r\n      detail: {\r\n        path: '/colours/:id',\r\n        element: lazy(() => import('features/colours/Detail')),\r\n        to: (id: string) => `/colours/${id}`,\r\n      },\r\n    },\r\n  },\r\n  users: {\r\n    path: '/users',\r\n    element: lazy(() => import('features/users/List')),\r\n    routes: {\r\n      detail: {\r\n        path: '/users/:name',\r\n        element: lazy(() => import('features/users/Detail')),\r\n        to: (name: string) => `/users/${name}`,\r\n      },\r\n    },\r\n  },\r\n  driverTasks: {\r\n    list: {\r\n      path: '/driver-tasks',\r\n      element: lazy(() => import('features/driver-tasks/List')),\r\n    },\r\n    new: {\r\n      path: '/driver-tasks/new',\r\n      element: lazy(() => import('features/driver-tasks/New')),\r\n    },\r\n    detail: {\r\n      path: '/driver-tasks/:id',\r\n      to: (id: string) => `/driver-tasks/${id}`,\r\n      element: lazy(() => import('features/driver-tasks/Detail')),\r\n    },\r\n  },\r\n};\r\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,OAAO;AAE5B,OAAO,MAAMC,MAAM,GAAG;EACpBC,IAAI,EAAE;IACJC,IAAI,EAAE,GAAG;IACTC,OAAO,eAAEJ,IAAI,CAAC,MAAM,MAAM,CAAC,aAAa,CAAC;EAC3C,CAAC;EACDK,KAAK,EAAE;IACLF,IAAI,EAAE,QAAQ;IACdC,OAAO,eAAEJ,IAAI,CAAC,MAAM,MAAM,CAAC,qBAAqB,CAAC;EACnD,CAAC;EACDM,MAAM,EAAE;IACNH,IAAI,EAAE,SAAS;IACfC,OAAO,eAAEJ,IAAI,CAAC,MAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;IACnDC,MAAM,EAAE;MACNM,GAAG,EAAE,MAAM,aAAa;MACxBC,WAAW,EAAE;QACXL,IAAI,EAAE,oBAAoB;QAC1BC,OAAO,eAAEJ,IAAI,CAAC,MAAM,MAAM,CAAC,6BAA6B,CAAC;MAC3D,CAAC;MACDS,YAAY,EAAE;QACZN,IAAI,EAAE,qBAAqB;QAC3BC,OAAO,eAAEJ,IAAI,CAAC,MAAM,MAAM,CAAC,8BAA8B,CAAC;MAC5D,CAAC;MACDU,WAAW,EAAE;QACXP,IAAI,EAAE,oBAAoB;QAC1BC,OAAO,eAAEJ,IAAI,CAAC,MAAM,MAAM,CAAC,6BAA6B,CAAC;MAC3D,CAAC;MACDW,WAAW,EAAE;QACXR,IAAI,EAAE,oBAAoB;QAC1BC,OAAO,eAAEJ,IAAI,CAAC,MAAM,MAAM,CAAC,6BAA6B,CAAC;MAC3D,CAAC;MACDY,MAAM,EAAE;QACNT,IAAI,EAAE,aAAa;QACnBC,OAAO,eAAEJ,IAAI,CAAC,MAAM,MAAM,CAAC,wBAAwB,CAAC,CAAC;QACrDa,EAAE,EAAGC,EAAU,IAAM,WAAUA,EAAG;MACpC;IACF;EACF,CAAC;EACDC,MAAM,EAAE;IACNZ,IAAI,EAAE,SAAS;IACfC,OAAO,eAAEJ,IAAI,CAAC,MAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;IACnDC,MAAM,EAAE;MACNM,GAAG,EAAE,MAAM,aAAa;MACxBK,MAAM,EAAE;QACNT,IAAI,EAAE,aAAa;QACnBC,OAAO,eAAEJ,IAAI,CAAC,MAAM,MAAM,CAAC,wBAAwB,CAAC,CAAC;QACrDa,EAAE,EAAGC,EAAU,IAAM,WAAUA,EAAG;MACpC;IACF;EACF,CAAC;EACDE,KAAK,EAAE;IACLb,IAAI,EAAE,QAAQ;IACdC,OAAO,eAAEJ,IAAI,CAAC,MAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC;IAClDC,MAAM,EAAE;MACNM,GAAG,EAAE,MAAM,YAAY;MACvBK,MAAM,EAAE;QACNT,IAAI,EAAE,YAAY;QAClBC,OAAO,eAAEJ,IAAI,CAAC,MAAM,MAAM,CAAC,uBAAuB,CAAC,CAAC;QACpDa,EAAE,EAAGC,EAAU,IAAM,UAASA,EAAG;MACnC;IACF;EACF,CAAC;EACDG,SAAS,EAAE;IACTd,IAAI,EAAE,YAAY;IAClBC,OAAO,eAAEJ,IAAI,CAAC,MAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC;IACtDC,MAAM,EAAE;MACNM,GAAG,EAAE,MAAM,gBAAgB;MAC3BK,MAAM,EAAE;QACNT,IAAI,EAAE,gBAAgB;QACtBC,OAAO,eAAEJ,IAAI,CAAC,MAAM,MAAM,CAAC,2BAA2B,CAAC,CAAC;QACxDa,EAAE,EAAGC,EAAU,IAAM,cAAaA,EAAG;MACvC;IACF;EACF,CAAC;EACDI,OAAO,EAAE;IACPf,IAAI,EAAE,UAAU;IAChBC,OAAO,eAAEJ,IAAI,CAAC,MAAM,MAAM,CAAC,uBAAuB,CAAC,CAAC;IACpDC,MAAM,EAAE;MACNM,GAAG,EAAE,MAAM,cAAc;MACzBK,MAAM,EAAE;QACNT,IAAI,EAAE,cAAc;QACpBC,OAAO,eAAEJ,IAAI,CAAC,MAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC;QACtDa,EAAE,EAAGC,EAAU,IAAM,YAAWA,EAAG;MACrC;IACF;EACF,CAAC;EACDK,KAAK,EAAE;IACLhB,IAAI,EAAE,QAAQ;IACdC,OAAO,eAAEJ,IAAI,CAAC,MAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC;IAClDC,MAAM,EAAE;MACNW,MAAM,EAAE;QACNT,IAAI,EAAE,cAAc;QACpBC,OAAO,eAAEJ,IAAI,CAAC,MAAM,MAAM,CAAC,uBAAuB,CAAC,CAAC;QACpDa,EAAE,EAAGO,IAAY,IAAM,UAASA,IAAK;MACvC;IACF;EACF,CAAC;EACDC,WAAW,EAAE;IACXC,IAAI,EAAE;MACJnB,IAAI,EAAE,eAAe;MACrBC,OAAO,eAAEJ,IAAI,CAAC,MAAM,MAAM,CAAC,4BAA4B,CAAC;IAC1D,CAAC;IACDO,GAAG,EAAE;MACHJ,IAAI,EAAE,mBAAmB;MACzBC,OAAO,eAAEJ,IAAI,CAAC,MAAM,MAAM,CAAC,2BAA2B,CAAC;IACzD,CAAC;IACDY,MAAM,EAAE;MACNT,IAAI,EAAE,mBAAmB;MACzBU,EAAE,EAAGC,EAAU,IAAM,iBAAgBA,EAAG,EAAC;MACzCV,OAAO,eAAEJ,IAAI,CAAC,MAAM,MAAM,CAAC,8BAA8B,CAAC;IAC5D;EACF;AACF,CAAC"}, "metadata": {}, "sourceType": "module"}