{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\GreenRP\\\\Web\\\\ClientApp\\\\src\\\\features\\\\colours\\\\Detail.tsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport function Detail() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"Colour Detail\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 3,\n    columnNumber: 10\n  }, this);\n}\n_c = Detail;\nexport default Detail;\nvar _c;\n$RefreshReg$(_c, \"Detail\");", "map": {"version": 3, "names": ["Detail"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/colours/Detail.tsx"], "sourcesContent": ["\r\nexport function Detail() {\r\n  return <div>Colour Detail</div>;\r\n}\r\n\r\nexport default Detail;"], "mappings": ";;AACA,OAAO,SAASA,MAAM,GAAG;EACvB,oBAAO;IAAA;EAAA;IAAA;IAAA;IAAA;EAAA,QAAwB;AACjC;AAAC,KAFeA,MAAM;AAItB,eAAeA,MAAM;AAAC;AAAA"}, "metadata": {}, "sourceType": "module"}