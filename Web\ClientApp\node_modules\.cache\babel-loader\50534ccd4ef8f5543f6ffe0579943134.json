{"ast": null, "code": "// Public components\nimport { Popper } from './Popper';\nimport { Manager } from './Manager';\nimport { Reference } from './Reference';\nimport { usePopper } from './usePopper';\nexport { Popper, Manager, Reference, usePopper }; // Public types", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Manager", "Reference", "usePopper"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/node_modules/react-popper/lib/esm/index.js"], "sourcesContent": ["// Public components\nimport { Popper } from './Popper';\nimport { Manager } from './Manager';\nimport { Reference } from './Reference';\nimport { usePopper } from './usePopper';\nexport { Popper, Manager, Reference, usePopper }; // Public types"], "mappings": "AAAA;AACA,SAASA,MAAM,QAAQ,UAAU;AACjC,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASH,MAAM,EAAEC,OAAO,EAAEC,SAAS,EAAEC,SAAS,GAAG,CAAC"}, "metadata": {}, "sourceType": "module"}