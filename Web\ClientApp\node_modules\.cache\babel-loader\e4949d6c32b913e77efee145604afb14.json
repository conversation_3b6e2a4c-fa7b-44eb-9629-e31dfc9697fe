{"ast": null, "code": "export function handleFocus(e) {\n  window.setTimeout(() => {\n    if (e.target === document.activeElement) {\n      e.target.select();\n    }\n  }, 100);\n}", "map": {"version": 3, "names": ["handleFocus", "e", "window", "setTimeout", "target", "document", "activeElement", "select"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/utils/focus.ts"], "sourcesContent": ["import React from 'react';\r\n\r\nexport function handleFocus (e: React.FocusEvent<HTMLInputElement>) {\r\n  window.setTimeout(() => {\r\n      if(e.target === document.activeElement) {\r\n        e.target.select();\r\n      }\r\n  }, 100);\r\n}\r\n"], "mappings": "AAEA,OAAO,SAASA,WAAW,CAAEC,CAAqC,EAAE;EAClEC,MAAM,CAACC,UAAU,CAAC,MAAM;IACpB,IAAGF,CAAC,CAACG,MAAM,KAAKC,QAAQ,CAACC,aAAa,EAAE;MACtCL,CAAC,CAACG,MAAM,CAACG,MAAM,EAAE;IACnB;EACJ,CAAC,EAAE,GAAG,CAAC;AACT"}, "metadata": {}, "sourceType": "module"}