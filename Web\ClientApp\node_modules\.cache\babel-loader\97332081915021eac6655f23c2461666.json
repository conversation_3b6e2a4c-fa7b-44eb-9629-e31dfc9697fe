{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\GreenRP\\\\Web\\\\ClientApp\\\\src\\\\features\\\\loading\\\\Loading.tsx\";\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport function Loading() {\n  return /*#__PURE__*/_jsxDEV(\"h1\", {\n    className: \"text-center\",\n    children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n      icon: ['fat', 'spinner'],\n      spin: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n}\n_c = Loading;\nvar _c;\n$RefreshReg$(_c, \"Loading\");", "map": {"version": 3, "names": ["FontAwesomeIcon", "Loading"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/loading/Loading.tsx"], "sourcesContent": ["import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\n\r\nexport function Loading() {\r\n  return (\r\n    <h1 className=\"text-center\">\r\n      <FontAwesomeIcon icon={['fat', 'spinner']} spin />\r\n    </h1>\r\n  )\r\n}\r\n"], "mappings": ";AAAA,SAASA,eAAe,QAAQ,gCAAgC;AAAC;AAEjE,OAAO,SAASC,OAAO,GAAG;EACxB,oBACE;IAAI,SAAS,EAAC,aAAa;IAAA,uBACzB,QAAC,eAAe;MAAC,IAAI,EAAE,CAAC,KAAK,EAAE,SAAS,CAAE;MAAC,IAAI;IAAA;MAAA;MAAA;MAAA;IAAA;EAAG;IAAA;IAAA;IAAA;EAAA,QAC/C;AAET;AAAC,KANeA,OAAO;AAAA;AAAA"}, "metadata": {}, "sourceType": "module"}