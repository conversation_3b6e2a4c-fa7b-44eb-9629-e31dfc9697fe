{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\GreenRP\\\\Web\\\\ClientApp\\\\src\\\\features\\\\driver-tasks\\\\List-Item.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { Link } from 'react-router-dom';\nimport { Button, Input, DropdownItem, DropdownMenu, DropdownToggle, UncontrolledTooltip, UncontrolledDropdown } from 'reactstrap';\nimport moment from 'moment';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { driverTasksApi } from 'api/driver-tasks-service';\nimport { routes } from 'app/routes';\nimport { useAuth } from 'features/auth/use-auth';\nimport { classNames } from 'utils/class-names';\nimport { formatDate } from 'utils/format';\nimport { selectDrivers } from './driver-task-slice';\nimport { setError } from './driver-task-slice';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport function ListItem(_ref) {\n  _s();\n  var _task$assignedTo, _task$assignedTo2, _task$assignedTo3;\n  let {\n    task\n  } = _ref;\n  const dispatch = useDispatch(),\n    drivers = useSelector(selectDrivers),\n    {\n      user,\n      isInRole\n    } = useAuth(),\n    canCreate = isInRole('create:driver-tasks'),\n    isDriver = drivers.some(d => d.name === (user === null || user === void 0 ? void 0 : user.name)),\n    isUnassigned = !task.assignedTo,\n    isAssignedToUser = isDriver && !isUnassigned && ((_task$assignedTo = task.assignedTo) === null || _task$assignedTo === void 0 ? void 0 : _task$assignedTo.name) === (user === null || user === void 0 ? void 0 : user.name),\n    canAssign = canCreate || isDriver && (isUnassigned || isAssignedToUser),\n    canSetStatus = canCreate || isAssignedToUser,\n    today = moment().startOf('day'),\n    dueDate = moment(task.dueDate),\n    isToday = dueDate.isSame(today, 'day'),\n    isOverdue = dueDate.isBefore(today),\n    progressIcon = task.status === 'Not Started' ? 'clock' : task.status === 'In Progress' ? 'person-running' : 'square-check';\n  const handleAssignedToChange = async e => {\n    const name = e.target.value,\n      assignedTo = name ? drivers.find(d => d.name === name) || null : null,\n      updated = {\n        ...task,\n        assignedTo\n      };\n    try {\n      await driverTasksApi.updateDriverTask(updated);\n    } catch (e) {\n      dispatch(setError(e));\n    }\n  };\n  const handleStatusChange = async status => {\n    const updated = {\n      ...task,\n      status\n    };\n    try {\n      await driverTasksApi.updateDriverTask(updated);\n    } catch (e) {\n      dispatch(setError(e));\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"col\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: classNames('card h-100', isOverdue && 'border-danger'),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: classNames('card-title col', isToday && 'fw-bold', task.priority === 'Low' && 'text-muted'),\n            children: [task.priority === 'High' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(UncontrolledTooltip, {\n                target: `driver-task-priority-${task._id}`,\n                children: \"This task is high priority.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                id: `driver-task-priority-${task._id}`,\n                className: \"cursor-pointer\",\n                children: \"\\uD83D\\uDD25\\xA0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true), task.priority === 'Low' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(UncontrolledTooltip, {\n                target: `driver-task-priority-${task._id}`,\n                children: \"This task is low priority.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                id: `driver-task-priority-${task._id}`,\n                className: \"cursor-pointer\",\n                children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                  icon: ['fal', 'arrow-down']\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 109,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true), formatDate(task.dueDate, 'ddd, MMM D')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-auto\",\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: routes.driverTasks.detail.to(task._id),\n              className: \"d-flex align-self-end\",\n              children: \"Edit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body d-flex flex-column text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          className: \"card-subtitle mb-2 text-muted text-capitalize\",\n          children: [!canSetStatus && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(UncontrolledTooltip, {\n              target: `driver-task-status-${task._id}`,\n              children: [\"Task is \", task.status]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              id: `driver-task-status-${task._id}`,\n              color: \"link\",\n              className: \"btn-secondary py-1 mb-1\",\n              children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: ['fat', progressIcon],\n                fixedWidth: true,\n                className: \"cursor-pointer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true), canSetStatus && /*#__PURE__*/_jsxDEV(UncontrolledDropdown, {\n            className: \"d-inline\",\n            children: [/*#__PURE__*/_jsxDEV(DropdownToggle, {\n              color: \"primary\",\n              outline: true,\n              caret: true,\n              className: \"py-1 mb-1\",\n              children: [/*#__PURE__*/_jsxDEV(UncontrolledTooltip, {\n                target: `driver-task-status-${task._id}`,\n                children: [\"Task is \", task.status]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                id: `driver-task-status-${task._id}`,\n                icon: ['fat', progressIcon],\n                fixedWidth: true,\n                className: \"cursor-pointer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(DropdownMenu, {\n              children: [/*#__PURE__*/_jsxDEV(DropdownItem, {\n                onClick: () => handleStatusChange('Not Started'),\n                children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                  icon: ['fat', 'clock'],\n                  fixedWidth: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 21\n                }, this), \"\\xA0 Not Started\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(DropdownItem, {\n                onClick: () => handleStatusChange('In Progress'),\n                children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                  icon: ['fat', 'person-running'],\n                  fixedWidth: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 21\n                }, this), \"\\xA0 In Progress\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(DropdownItem, {\n                onClick: () => handleStatusChange('Complete'),\n                children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                  icon: ['fat', 'square-check'],\n                  fixedWidth: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 21\n                }, this), \"\\xA0 Complete\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 15\n          }, this), \"\\xA0\", !canAssign && /*#__PURE__*/_jsxDEV(Input, {\n            value: ((_task$assignedTo2 = task.assignedTo) === null || _task$assignedTo2 === void 0 ? void 0 : _task$assignedTo2.name) || '',\n            readOnly: true,\n            plaintext: true,\n            className: \"w-auto d-inline-block text-capitalize\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this), canAssign && /*#__PURE__*/_jsxDEV(Input, {\n            value: ((_task$assignedTo3 = task.assignedTo) === null || _task$assignedTo3 === void 0 ? void 0 : _task$assignedTo3.name) || '',\n            onChange: handleAssignedToChange,\n            type: \"select\",\n            className: \"w-auto d-inline-block\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Unassigned\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 17\n            }, this), canCreate && drivers.map(driver => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: driver.name,\n              className: \"text-capitalize\",\n              children: driver.name\n            }, driver.name, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 21\n            }, this)), !canCreate && user && /*#__PURE__*/_jsxDEV(\"option\", {\n              value: user.name,\n              className: \"text-capitalize\",\n              children: user.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"card-text text-start small flex flex-grow-1\",\n          children: task.notes\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col text-end text-truncate\",\n            children: task.fromLocation\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-auto text-center\",\n            children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: ['fat', 'arrow-right']\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col text-start text-truncate\",\n            children: task.toLocation\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-footer\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row small text-muted\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col text-capitalize text-end\",\n            children: [\"Created by \", task.createdBy]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col text-start\",\n            children: `${formatDate(task.createdOn, 'MMM D')} @ ${formatDate(task.createdOn, 'h:mm a')}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 5\n  }, this);\n}\n_s(ListItem, \"ranlblRHoy4C+oGbKxBH70YylW0=\", false, function () {\n  return [useDispatch, useSelector, useAuth];\n});\n_c = ListItem;\nvar _c;\n$RefreshReg$(_c, \"ListItem\");", "map": {"version": 3, "names": ["React", "useDispatch", "useSelector", "Link", "<PERSON><PERSON>", "Input", "DropdownItem", "DropdownMenu", "DropdownToggle", "UncontrolledTooltip", "UncontrolledDropdown", "moment", "FontAwesomeIcon", "driverTasksApi", "routes", "useAuth", "classNames", "formatDate", "selectDrivers", "setError", "ListItem", "task", "dispatch", "drivers", "user", "isInRole", "canCreate", "isDriver", "some", "d", "name", "isUnassigned", "assignedTo", "isAssignedToUser", "canAssign", "canSetStatus", "today", "startOf", "dueDate", "isToday", "isSame", "isOverdue", "isBefore", "progressIcon", "status", "handleAssignedToChange", "e", "target", "value", "find", "updated", "updateDriverTask", "handleStatusChange", "priority", "_id", "driverTasks", "detail", "to", "map", "driver", "notes", "fromLocation", "toLocation", "created<PERSON>y", "createdOn"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/driver-tasks/List-Item.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { Link } from 'react-router-dom';\r\nimport {\r\n  Button,\r\n  Input,\r\n  DropdownItem,\r\n  DropdownMenu,\r\n  DropdownToggle,\r\n  UncontrolledTooltip,\r\n  UncontrolledDropdown,\r\n} from 'reactstrap';\r\nimport moment from 'moment';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { IconProp } from '@fortawesome/fontawesome-svg-core';\r\nimport { driverTasksApi } from 'api/driver-tasks-service';\r\nimport * as models from 'api/models/driver-tasks';\r\nimport { routes } from 'app/routes';\r\nimport { useAuth } from 'features/auth/use-auth';\r\nimport { classNames } from 'utils/class-names';\r\nimport { formatDate } from 'utils/format';\r\nimport { ProblemDetails } from 'utils/problem-details';\r\nimport { selectDrivers } from './driver-task-slice';\r\nimport { setError } from './driver-task-slice';\r\n\r\ninterface ListItemProps {\r\n  task: models.DriverTask;\r\n}\r\n\r\nexport function ListItem({ task }: ListItemProps) {\r\n  const dispatch = useDispatch(),\r\n    drivers = useSelector(selectDrivers),\r\n    { user, isInRole } = useAuth(),\r\n    canCreate = isInRole('create:driver-tasks'),\r\n    isDriver = drivers.some((d) => d.name === user?.name),\r\n    isUnassigned = !task.assignedTo,\r\n    isAssignedToUser =\r\n      isDriver && !isUnassigned && task.assignedTo?.name === user?.name,\r\n    canAssign = canCreate || (isDriver && (isUnassigned || isAssignedToUser)),\r\n    canSetStatus = canCreate || isAssignedToUser,\r\n    today = moment().startOf('day'),\r\n    dueDate = moment(task.dueDate),\r\n    isToday = dueDate.isSame(today, 'day'),\r\n    isOverdue = dueDate.isBefore(today),\r\n    progressIcon: IconProp =\r\n      task.status === 'Not Started'\r\n        ? 'clock'\r\n        : task.status === 'In Progress'\r\n        ? 'person-running'\r\n        : 'square-check';\r\n\r\n  const handleAssignedToChange = async (\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const name = e.target.value,\r\n      assignedTo = name ? drivers.find((d) => d.name === name) || null : null,\r\n      updated = { ...task, assignedTo };\r\n\r\n    try {\r\n      await driverTasksApi.updateDriverTask(updated);\r\n    } catch (e) {\r\n      dispatch(setError(e as ProblemDetails));\r\n    }\r\n  };\r\n\r\n  const handleStatusChange = async (status: models.Status) => {\r\n    const updated = { ...task, status };\r\n\r\n    try {\r\n      await driverTasksApi.updateDriverTask(updated);\r\n    } catch (e) {\r\n      dispatch(setError(e as ProblemDetails));\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"col\">\r\n      <div className={classNames('card h-100', isOverdue && 'border-danger')}>\r\n        <div className=\"card-header\">\r\n          <div className=\"row\">\r\n            <h5\r\n              className={classNames(\r\n                'card-title col',\r\n                isToday && 'fw-bold',\r\n                task.priority === 'Low' && 'text-muted'\r\n              )}>\r\n              {task.priority === 'High' && (\r\n                <>\r\n                  <UncontrolledTooltip\r\n                    target={`driver-task-priority-${task._id}`}>\r\n                    This task is high priority.\r\n                  </UncontrolledTooltip>\r\n                  <span\r\n                    id={`driver-task-priority-${task._id}`}\r\n                    className=\"cursor-pointer\">\r\n                    🔥&nbsp;\r\n                  </span>\r\n                </>\r\n              )}\r\n              {task.priority === 'Low' && (\r\n                <>\r\n                  <UncontrolledTooltip\r\n                    target={`driver-task-priority-${task._id}`}>\r\n                    This task is low priority.\r\n                  </UncontrolledTooltip>\r\n                  <span\r\n                    id={`driver-task-priority-${task._id}`}\r\n                    className=\"cursor-pointer\">\r\n                    <FontAwesomeIcon icon={['fal', 'arrow-down']} />\r\n                  </span>\r\n                </>\r\n              )}\r\n              {formatDate(task.dueDate, 'ddd, MMM D')}\r\n            </h5>\r\n            <div className=\"col-auto\">\r\n              <Link\r\n                to={routes.driverTasks.detail.to(task._id)}\r\n                className=\"d-flex align-self-end\">\r\n                Edit\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"card-body d-flex flex-column text-center\">\r\n          <h6 className=\"card-subtitle mb-2 text-muted text-capitalize\">\r\n            {!canSetStatus && (\r\n              <>\r\n                <UncontrolledTooltip target={`driver-task-status-${task._id}`}>\r\n                  Task is {task.status}\r\n                </UncontrolledTooltip>\r\n                <Button\r\n                  id={`driver-task-status-${task._id}`}\r\n                  color=\"link\"\r\n                  className=\"btn-secondary py-1 mb-1\">\r\n                  <FontAwesomeIcon\r\n                    icon={['fat', progressIcon]}\r\n                    fixedWidth\r\n                    className=\"cursor-pointer\"\r\n                  />\r\n                </Button>\r\n              </>\r\n            )}\r\n            {canSetStatus && (\r\n              <UncontrolledDropdown className=\"d-inline\">\r\n                <DropdownToggle\r\n                  color=\"primary\"\r\n                  outline\r\n                  caret\r\n                  className=\"py-1 mb-1\">\r\n                  <UncontrolledTooltip\r\n                    target={`driver-task-status-${task._id}`}>\r\n                    Task is {task.status}\r\n                  </UncontrolledTooltip>\r\n                  <FontAwesomeIcon\r\n                    id={`driver-task-status-${task._id}`}\r\n                    icon={['fat', progressIcon]}\r\n                    fixedWidth\r\n                    className=\"cursor-pointer\"\r\n                  />\r\n                </DropdownToggle>\r\n                <DropdownMenu>\r\n                  <DropdownItem\r\n                    onClick={() => handleStatusChange('Not Started')}>\r\n                    <FontAwesomeIcon icon={['fat', 'clock']} fixedWidth />\r\n                    &nbsp; Not Started\r\n                  </DropdownItem>\r\n                  <DropdownItem\r\n                    onClick={() => handleStatusChange('In Progress')}>\r\n                    <FontAwesomeIcon\r\n                      icon={['fat', 'person-running']}\r\n                      fixedWidth\r\n                    />\r\n                    &nbsp; In Progress\r\n                  </DropdownItem>\r\n                  <DropdownItem onClick={() => handleStatusChange('Complete')}>\r\n                    <FontAwesomeIcon\r\n                      icon={['fat', 'square-check']}\r\n                      fixedWidth\r\n                    />\r\n                    &nbsp; Complete\r\n                  </DropdownItem>\r\n                </DropdownMenu>\r\n              </UncontrolledDropdown>\r\n            )}\r\n            &nbsp;\r\n            {!canAssign && (\r\n              <Input\r\n                value={task.assignedTo?.name || ''}\r\n                readOnly\r\n                plaintext\r\n                className=\"w-auto d-inline-block text-capitalize\"\r\n              />\r\n            )}\r\n            {canAssign && (\r\n              <Input\r\n                value={task.assignedTo?.name || ''}\r\n                onChange={handleAssignedToChange}\r\n                type=\"select\"\r\n                className=\"w-auto d-inline-block\">\r\n                <option value=\"\">Unassigned</option>\r\n                {canCreate &&\r\n                  drivers.map((driver) => (\r\n                    <option\r\n                      key={driver.name}\r\n                      value={driver.name}\r\n                      className=\"text-capitalize\">\r\n                      {driver.name}\r\n                    </option>\r\n                  ))}\r\n                {!canCreate && user && (\r\n                  <option value={user.name} className=\"text-capitalize\">\r\n                    {user.name}\r\n                  </option>\r\n                )}\r\n              </Input>\r\n            )}\r\n          </h6>\r\n          <p className=\"card-text text-start small flex flex-grow-1\">\r\n            {task.notes}\r\n          </p>\r\n          <div className=\"row\">\r\n            <div className=\"col text-end text-truncate\">\r\n              {task.fromLocation}\r\n            </div>\r\n            <div className=\"col-auto text-center\">\r\n              <FontAwesomeIcon icon={['fat', 'arrow-right']} />\r\n            </div>\r\n            <div className=\"col text-start text-truncate\">\r\n              {task.toLocation}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"card-footer\">\r\n          <div className=\"row small text-muted\">\r\n            <div className=\"col text-capitalize text-end\">\r\n              Created by {task.createdBy}\r\n            </div>\r\n            <div className=\"col text-start\">\r\n              {`${formatDate(task.createdOn, 'MMM D')} @ ${formatDate(\r\n                task.createdOn,\r\n                'h:mm a'\r\n              )}`}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SACEC,MAAM,EACNC,KAAK,EACLC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,mBAAmB,EACnBC,oBAAoB,QACf,YAAY;AACnB,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,eAAe,QAAQ,gCAAgC;AAEhE,SAASC,cAAc,QAAQ,0BAA0B;AAEzD,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,UAAU,QAAQ,cAAc;AAEzC,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,QAAQ,QAAQ,qBAAqB;AAAC;AAAA;AAM/C,OAAO,SAASC,QAAQ,OAA0B;EAAA;EAAA;EAAA,IAAzB;IAAEC;EAAoB,CAAC;EAC9C,MAAMC,QAAQ,GAAGrB,WAAW,EAAE;IAC5BsB,OAAO,GAAGrB,WAAW,CAACgB,aAAa,CAAC;IACpC;MAAEM,IAAI;MAAEC;IAAS,CAAC,GAAGV,OAAO,EAAE;IAC9BW,SAAS,GAAGD,QAAQ,CAAC,qBAAqB,CAAC;IAC3CE,QAAQ,GAAGJ,OAAO,CAACK,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,IAAI,MAAKN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,IAAI,EAAC;IACrDC,YAAY,GAAG,CAACV,IAAI,CAACW,UAAU;IAC/BC,gBAAgB,GACdN,QAAQ,IAAI,CAACI,YAAY,IAAI,qBAAAV,IAAI,CAACW,UAAU,qDAAf,iBAAiBF,IAAI,OAAKN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,IAAI;IACnEI,SAAS,GAAGR,SAAS,IAAKC,QAAQ,KAAKI,YAAY,IAAIE,gBAAgB,CAAE;IACzEE,YAAY,GAAGT,SAAS,IAAIO,gBAAgB;IAC5CG,KAAK,GAAGzB,MAAM,EAAE,CAAC0B,OAAO,CAAC,KAAK,CAAC;IAC/BC,OAAO,GAAG3B,MAAM,CAACU,IAAI,CAACiB,OAAO,CAAC;IAC9BC,OAAO,GAAGD,OAAO,CAACE,MAAM,CAACJ,KAAK,EAAE,KAAK,CAAC;IACtCK,SAAS,GAAGH,OAAO,CAACI,QAAQ,CAACN,KAAK,CAAC;IACnCO,YAAsB,GACpBtB,IAAI,CAACuB,MAAM,KAAK,aAAa,GACzB,OAAO,GACPvB,IAAI,CAACuB,MAAM,KAAK,aAAa,GAC7B,gBAAgB,GAChB,cAAc;EAEtB,MAAMC,sBAAsB,GAAG,MAC7BC,CAAsC,IACnC;IACH,MAAMhB,IAAI,GAAGgB,CAAC,CAACC,MAAM,CAACC,KAAK;MACzBhB,UAAU,GAAGF,IAAI,GAAGP,OAAO,CAAC0B,IAAI,CAAEpB,CAAC,IAAKA,CAAC,CAACC,IAAI,KAAKA,IAAI,CAAC,IAAI,IAAI,GAAG,IAAI;MACvEoB,OAAO,GAAG;QAAE,GAAG7B,IAAI;QAAEW;MAAW,CAAC;IAEnC,IAAI;MACF,MAAMnB,cAAc,CAACsC,gBAAgB,CAACD,OAAO,CAAC;IAChD,CAAC,CAAC,OAAOJ,CAAC,EAAE;MACVxB,QAAQ,CAACH,QAAQ,CAAC2B,CAAC,CAAmB,CAAC;IACzC;EACF,CAAC;EAED,MAAMM,kBAAkB,GAAG,MAAOR,MAAqB,IAAK;IAC1D,MAAMM,OAAO,GAAG;MAAE,GAAG7B,IAAI;MAAEuB;IAAO,CAAC;IAEnC,IAAI;MACF,MAAM/B,cAAc,CAACsC,gBAAgB,CAACD,OAAO,CAAC;IAChD,CAAC,CAAC,OAAOJ,CAAC,EAAE;MACVxB,QAAQ,CAACH,QAAQ,CAAC2B,CAAC,CAAmB,CAAC;IACzC;EACF,CAAC;EAED,oBACE;IAAK,SAAS,EAAC,KAAK;IAAA,uBAClB;MAAK,SAAS,EAAE9B,UAAU,CAAC,YAAY,EAAEyB,SAAS,IAAI,eAAe,CAAE;MAAA,wBACrE;QAAK,SAAS,EAAC,aAAa;QAAA,uBAC1B;UAAK,SAAS,EAAC,KAAK;UAAA,wBAClB;YACE,SAAS,EAAEzB,UAAU,CACnB,gBAAgB,EAChBuB,OAAO,IAAI,SAAS,EACpBlB,IAAI,CAACgC,QAAQ,KAAK,KAAK,IAAI,YAAY,CACvC;YAAA,WACDhC,IAAI,CAACgC,QAAQ,KAAK,MAAM,iBACvB;cAAA,wBACE,QAAC,mBAAmB;gBAClB,MAAM,EAAG,wBAAuBhC,IAAI,CAACiC,GAAI,EAAE;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA,QAEvB,eACtB;gBACE,EAAE,EAAG,wBAAuBjC,IAAI,CAACiC,GAAI,EAAE;gBACvC,SAAS,EAAC,gBAAgB;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA,QAErB;YAAA,gBAEV,EACAjC,IAAI,CAACgC,QAAQ,KAAK,KAAK,iBACtB;cAAA,wBACE,QAAC,mBAAmB;gBAClB,MAAM,EAAG,wBAAuBhC,IAAI,CAACiC,GAAI,EAAE;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA,QAEvB,eACtB;gBACE,EAAE,EAAG,wBAAuBjC,IAAI,CAACiC,GAAI,EAAE;gBACvC,SAAS,EAAC,gBAAgB;gBAAA,uBAC1B,QAAC,eAAe;kBAAC,IAAI,EAAE,CAAC,KAAK,EAAE,YAAY;gBAAE;kBAAA;kBAAA;kBAAA;gBAAA;cAAG;gBAAA;gBAAA;gBAAA;cAAA,QAC3C;YAAA,gBAEV,EACArC,UAAU,CAACI,IAAI,CAACiB,OAAO,EAAE,YAAY,CAAC;UAAA;YAAA;YAAA;YAAA;UAAA,QACpC,eACL;YAAK,SAAS,EAAC,UAAU;YAAA,uBACvB,QAAC,IAAI;cACH,EAAE,EAAExB,MAAM,CAACyC,WAAW,CAACC,MAAM,CAACC,EAAE,CAACpC,IAAI,CAACiC,GAAG,CAAE;cAC3C,SAAS,EAAC,uBAAuB;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA;UAE5B;YAAA;YAAA;YAAA;UAAA,QACH;QAAA;UAAA;UAAA;UAAA;QAAA;MACF;QAAA;QAAA;QAAA;MAAA,QACF,eACN;QAAK,SAAS,EAAC,0CAA0C;QAAA,wBACvD;UAAI,SAAS,EAAC,+CAA+C;UAAA,WAC1D,CAACnB,YAAY,iBACZ;YAAA,wBACE,QAAC,mBAAmB;cAAC,MAAM,EAAG,sBAAqBd,IAAI,CAACiC,GAAI,EAAE;cAAA,uBACnDjC,IAAI,CAACuB,MAAM;YAAA;cAAA;cAAA;cAAA;YAAA,QACA,eACtB,QAAC,MAAM;cACL,EAAE,EAAG,sBAAqBvB,IAAI,CAACiC,GAAI,EAAE;cACrC,KAAK,EAAC,MAAM;cACZ,SAAS,EAAC,yBAAyB;cAAA,uBACnC,QAAC,eAAe;gBACd,IAAI,EAAE,CAAC,KAAK,EAAEX,YAAY,CAAE;gBAC5B,UAAU;gBACV,SAAS,EAAC;cAAgB;gBAAA;gBAAA;gBAAA;cAAA;YAC1B;cAAA;cAAA;cAAA;YAAA,QACK;UAAA,gBAEZ,EACAR,YAAY,iBACX,QAAC,oBAAoB;YAAC,SAAS,EAAC,UAAU;YAAA,wBACxC,QAAC,cAAc;cACb,KAAK,EAAC,SAAS;cACf,OAAO;cACP,KAAK;cACL,SAAS,EAAC,WAAW;cAAA,wBACrB,QAAC,mBAAmB;gBAClB,MAAM,EAAG,sBAAqBd,IAAI,CAACiC,GAAI,EAAE;gBAAA,uBAChCjC,IAAI,CAACuB,MAAM;cAAA;gBAAA;gBAAA;gBAAA;cAAA,QACA,eACtB,QAAC,eAAe;gBACd,EAAE,EAAG,sBAAqBvB,IAAI,CAACiC,GAAI,EAAE;gBACrC,IAAI,EAAE,CAAC,KAAK,EAAEX,YAAY,CAAE;gBAC5B,UAAU;gBACV,SAAS,EAAC;cAAgB;gBAAA;gBAAA;gBAAA;cAAA,QAC1B;YAAA;cAAA;cAAA;cAAA;YAAA,QACa,eACjB,QAAC,YAAY;cAAA,wBACX,QAAC,YAAY;gBACX,OAAO,EAAE,MAAMS,kBAAkB,CAAC,aAAa,CAAE;gBAAA,wBACjD,QAAC,eAAe;kBAAC,IAAI,EAAE,CAAC,KAAK,EAAE,OAAO,CAAE;kBAAC,UAAU;gBAAA;kBAAA;kBAAA;kBAAA;gBAAA,QAAG;cAAA;gBAAA;gBAAA;gBAAA;cAAA,QAEzC,eACf,QAAC,YAAY;gBACX,OAAO,EAAE,MAAMA,kBAAkB,CAAC,aAAa,CAAE;gBAAA,wBACjD,QAAC,eAAe;kBACd,IAAI,EAAE,CAAC,KAAK,EAAE,gBAAgB,CAAE;kBAChC,UAAU;gBAAA;kBAAA;kBAAA;kBAAA;gBAAA,QACV;cAAA;gBAAA;gBAAA;gBAAA;cAAA,QAEW,eACf,QAAC,YAAY;gBAAC,OAAO,EAAE,MAAMA,kBAAkB,CAAC,UAAU,CAAE;gBAAA,wBAC1D,QAAC,eAAe;kBACd,IAAI,EAAE,CAAC,KAAK,EAAE,cAAc,CAAE;kBAC9B,UAAU;gBAAA;kBAAA;kBAAA;kBAAA;gBAAA,QACV;cAAA;gBAAA;gBAAA;gBAAA;cAAA,QAEW;YAAA;cAAA;cAAA;cAAA;YAAA,QACF;UAAA;YAAA;YAAA;YAAA;UAAA,QAElB,UAEA,CAAClB,SAAS,iBACT,QAAC,KAAK;YACJ,KAAK,EAAE,sBAAAb,IAAI,CAACW,UAAU,sDAAf,kBAAiBF,IAAI,KAAI,EAAG;YACnC,QAAQ;YACR,SAAS;YACT,SAAS,EAAC;UAAuC;YAAA;YAAA;YAAA;UAAA,QAEpD,EACAI,SAAS,iBACR,QAAC,KAAK;YACJ,KAAK,EAAE,sBAAAb,IAAI,CAACW,UAAU,sDAAf,kBAAiBF,IAAI,KAAI,EAAG;YACnC,QAAQ,EAAEe,sBAAuB;YACjC,IAAI,EAAC,QAAQ;YACb,SAAS,EAAC,uBAAuB;YAAA,wBACjC;cAAQ,KAAK,EAAC,EAAE;cAAA;YAAA;cAAA;cAAA;cAAA;YAAA,QAAoB,EACnCnB,SAAS,IACRH,OAAO,CAACmC,GAAG,CAAEC,MAAM,iBACjB;cAEE,KAAK,EAAEA,MAAM,CAAC7B,IAAK;cACnB,SAAS,EAAC,iBAAiB;cAAA,UAC1B6B,MAAM,CAAC7B;YAAI,GAHP6B,MAAM,CAAC7B,IAAI;cAAA;cAAA;cAAA;YAAA,QAKnB,CAAC,EACH,CAACJ,SAAS,IAAIF,IAAI,iBACjB;cAAQ,KAAK,EAAEA,IAAI,CAACM,IAAK;cAAC,SAAS,EAAC,iBAAiB;cAAA,UAClDN,IAAI,CAACM;YAAI;cAAA;cAAA;cAAA;YAAA,QAEb;UAAA;YAAA;YAAA;YAAA;UAAA,QAEJ;QAAA;UAAA;UAAA;UAAA;QAAA,QACE,eACL;UAAG,SAAS,EAAC,6CAA6C;UAAA,UACvDT,IAAI,CAACuC;QAAK;UAAA;UAAA;UAAA;QAAA,QACT,eACJ;UAAK,SAAS,EAAC,KAAK;UAAA,wBAClB;YAAK,SAAS,EAAC,4BAA4B;YAAA,UACxCvC,IAAI,CAACwC;UAAY;YAAA;YAAA;YAAA;UAAA,QACd,eACN;YAAK,SAAS,EAAC,sBAAsB;YAAA,uBACnC,QAAC,eAAe;cAAC,IAAI,EAAE,CAAC,KAAK,EAAE,aAAa;YAAE;cAAA;cAAA;cAAA;YAAA;UAAG;YAAA;YAAA;YAAA;UAAA,QAC7C,eACN;YAAK,SAAS,EAAC,8BAA8B;YAAA,UAC1CxC,IAAI,CAACyC;UAAU;YAAA;YAAA;YAAA;UAAA,QACZ;QAAA;UAAA;UAAA;UAAA;QAAA,QACF;MAAA;QAAA;QAAA;QAAA;MAAA,QACF,eACN;QAAK,SAAS,EAAC,aAAa;QAAA,uBAC1B;UAAK,SAAS,EAAC,sBAAsB;UAAA,wBACnC;YAAK,SAAS,EAAC,8BAA8B;YAAA,0BAC/BzC,IAAI,CAAC0C,SAAS;UAAA;YAAA;YAAA;YAAA;UAAA,QACtB,eACN;YAAK,SAAS,EAAC,gBAAgB;YAAA,UAC3B,GAAE9C,UAAU,CAACI,IAAI,CAAC2C,SAAS,EAAE,OAAO,CAAE,MAAK/C,UAAU,CACrDI,IAAI,CAAC2C,SAAS,EACd,QAAQ,CACR;UAAC;YAAA;YAAA;YAAA;UAAA,QACC;QAAA;UAAA;UAAA;UAAA;QAAA;MACF;QAAA;QAAA;QAAA;MAAA,QACF;IAAA;MAAA;MAAA;MAAA;IAAA;EACF;IAAA;IAAA;IAAA;EAAA,QACF;AAEV;AAAC,GA3Ne5C,QAAQ;EAAA,QACLnB,WAAW,EAChBC,WAAW,EACAa,OAAO;AAAA;AAAA,KAHhBK,QAAQ;AAAA;AAAA"}, "metadata": {}, "sourceType": "module"}