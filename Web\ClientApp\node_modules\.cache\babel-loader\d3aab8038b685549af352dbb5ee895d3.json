{"ast": null, "code": "const isDebug = window.location.hostname === 'localhost',\n  remote_server = 'https://7bff221b-c372-4033-bf6c-59d2e859a520-bluemix.cloudant.com',\n  db = isDebug ? 'boekestyn-test' : 'boekestyn-v4',\n  remote_database_name = `${remote_server}/${db}`;\nexport const configuration = {\n  remote_server,\n  remote_database_name,\n  app_database_name: 'greenrpv4',\n  obsolete_database_name: 'greenrpv3'\n};", "map": {"version": 3, "names": ["isDebug", "window", "location", "hostname", "remote_server", "db", "remote_database_name", "configuration", "app_database_name", "obsolete_database_name"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/auth/configuration.ts"], "sourcesContent": ["const isDebug = window.location.hostname === 'localhost',\r\n  remote_server =\r\n    'https://7bff221b-c372-4033-bf6c-59d2e859a520-bluemix.cloudant.com',\r\n  db = isDebug ? 'boekestyn-test' : 'boekestyn-v4',\r\n  remote_database_name = `${remote_server}/${db}`;\r\n\r\nexport const configuration = {\r\n  remote_server,\r\n  remote_database_name,\r\n  app_database_name: 'greenrpv4',\r\n  obsolete_database_name: 'greenrpv3',\r\n};\r\n"], "mappings": "AAAA,MAAMA,OAAO,GAAGC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,WAAW;EACtDC,aAAa,GACX,mEAAmE;EACrEC,EAAE,GAAGL,OAAO,GAAG,gBAAgB,GAAG,cAAc;EAChDM,oBAAoB,GAAI,GAAEF,aAAc,IAAGC,EAAG,EAAC;AAEjD,OAAO,MAAME,aAAa,GAAG;EAC3BH,aAAa;EACbE,oBAAoB;EACpBE,iBAAiB,EAAE,WAAW;EAC9BC,sBAAsB,EAAE;AAC1B,CAAC"}, "metadata": {}, "sourceType": "module"}