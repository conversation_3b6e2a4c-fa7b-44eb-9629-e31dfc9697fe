{"ast": null, "code": "import { ServiceBase } from './service-base';\nclass OrderService extends ServiceBase {\n  getAll() {\n    return this.getAllDocuments('filters/orders');\n  }\n  save(order) {\n    return this.saveDocument(order);\n  }\n  byStickDate(from, to) {\n    return this.query('filters/orders-by-stick-date', from, to);\n  }\n  byFlowerDate(from, to) {\n    return this.query('filters/orders-by-flower-date', from, to);\n  }\n  bySpaceDate(from, to) {\n    return this.query('filters/orders-by-space-date', from, to);\n  }\n  byPinchDate(from, to) {\n    return this.query('filters/orders-by-pinch-date', from, to);\n  }\n}\nexport const orderApi = new OrderService();", "map": {"version": 3, "names": ["ServiceBase", "OrderService", "getAll", "getAllDocuments", "save", "order", "saveDocument", "byStickDate", "from", "to", "query", "byFlowerDate", "bySpaceDate", "byPinchDate", "orderApi"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/api/order-service.ts"], "sourcesContent": ["import * as models from './models/orders';\r\nimport { ServiceBase } from './service-base';\r\n\r\nclass OrderService extends ServiceBase {\r\n  getAll() {\r\n    return this.getAllDocuments<models.Order>('filters/orders');\r\n  }\r\n\r\n  save(order: models.Order) {\r\n    return this.saveDocument<models.Order>(order);\r\n  }\r\n\r\n  byStickDate(from: string, to: string) {\r\n    return this.query<models.Order>('filters/orders-by-stick-date', from, to);\r\n  }\r\n\r\n  byFlowerDate(from: string, to: string) {\r\n    return this.query<models.Order>('filters/orders-by-flower-date', from, to);\r\n  }\r\n\r\n  bySpaceDate(from: string, to: string) {\r\n    return this.query<models.Order>('filters/orders-by-space-date', from, to);\r\n  }\r\n\r\n  byPinchDate(from: string, to: string) {\r\n    return this.query<models.Order>('filters/orders-by-pinch-date', from, to);\r\n  }\r\n}\r\n\r\nexport const orderApi = new OrderService();\r\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,gBAAgB;AAE5C,MAAMC,YAAY,SAASD,WAAW,CAAC;EACrCE,MAAM,GAAG;IACP,OAAO,IAAI,CAACC,eAAe,CAAe,gBAAgB,CAAC;EAC7D;EAEAC,IAAI,CAACC,KAAmB,EAAE;IACxB,OAAO,IAAI,CAACC,YAAY,CAAeD,KAAK,CAAC;EAC/C;EAEAE,WAAW,CAACC,IAAY,EAAEC,EAAU,EAAE;IACpC,OAAO,IAAI,CAACC,KAAK,CAAe,8BAA8B,EAAEF,IAAI,EAAEC,EAAE,CAAC;EAC3E;EAEAE,YAAY,CAACH,IAAY,EAAEC,EAAU,EAAE;IACrC,OAAO,IAAI,CAACC,KAAK,CAAe,+BAA+B,EAAEF,IAAI,EAAEC,EAAE,CAAC;EAC5E;EAEAG,WAAW,CAACJ,IAAY,EAAEC,EAAU,EAAE;IACpC,OAAO,IAAI,CAACC,KAAK,CAAe,8BAA8B,EAAEF,IAAI,EAAEC,EAAE,CAAC;EAC3E;EAEAI,WAAW,CAACL,IAAY,EAAEC,EAAU,EAAE;IACpC,OAAO,IAAI,CAACC,KAAK,CAAe,8BAA8B,EAAEF,IAAI,EAAEC,EAAE,CAAC;EAC3E;AACF;AAEA,OAAO,MAAMK,QAAQ,GAAG,IAAIb,YAAY,EAAE"}, "metadata": {}, "sourceType": "module"}