{"ast": null, "code": "export function classNames() {\n  for (var _len = arguments.length, classes = new Array(_len), _key = 0; _key < _len; _key++) {\n    classes[_key] = arguments[_key];\n  }\n  return classes.filter(Boolean).join(' ');\n}", "map": {"version": 3, "names": ["classNames", "classes", "filter", "Boolean", "join"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/utils/class-names.ts"], "sourcesContent": ["export function classNames(...classes: any[]) {\r\n  return classes.filter(Boolean).join(' ');\r\n}\r\n"], "mappings": "AAAA,OAAO,SAASA,UAAU,GAAoB;EAAA,kCAAhBC,OAAO;IAAPA,OAAO;EAAA;EACnC,OAAOA,OAAO,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;AAC1C"}, "metadata": {}, "sourceType": "module"}