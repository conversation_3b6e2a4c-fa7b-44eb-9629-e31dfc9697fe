{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\GreenRP\\\\Web\\\\ClientApp\\\\src\\\\app\\\\Layout.tsx\";\nimport { Outlet } from 'react-router';\nimport { NavMenu } from './NavMenu';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport function Layout() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(NavMenu, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n}\n_c = Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["Outlet", "NavMenu", "Layout"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/app/Layout.tsx"], "sourcesContent": ["import { Outlet } from 'react-router';\r\nimport { NavMenu } from './NavMenu';\r\n\r\nexport function Layout() {\r\n  return (\r\n    <div>\r\n      <NavMenu />\r\n      <Outlet />\r\n    </div>\r\n  );\r\n}\r\n"], "mappings": ";AAAA,SAASA,MAAM,QAAQ,cAAc;AACrC,SAASC,OAAO,QAAQ,WAAW;AAAC;AAEpC,OAAO,SAASC,MAAM,GAAG;EACvB,oBACE;IAAA,wBACE,QAAC,OAAO;MAAA;MAAA;MAAA;IAAA,QAAG,eACX,QAAC,MAAM;MAAA;MAAA;MAAA;IAAA,QAAG;EAAA;IAAA;IAAA;IAAA;EAAA,QACN;AAEV;AAAC,KAPeA,MAAM;AAAA;AAAA"}, "metadata": {}, "sourceType": "module"}