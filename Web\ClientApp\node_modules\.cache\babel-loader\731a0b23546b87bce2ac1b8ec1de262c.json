{"ast": null, "code": "import { getBatch } from './batch'; // encapsulates the subscription logic for connecting a component to the redux store, as\n// well as nesting subscriptions of descendant components, so that we can ensure the\n// ancestor components re-render before descendants\n\nfunction createListenerCollection() {\n  var batch = getBatch();\n  var first = null;\n  var last = null;\n  return {\n    clear: function clear() {\n      first = null;\n      last = null;\n    },\n    notify: function notify() {\n      batch(function () {\n        var listener = first;\n        while (listener) {\n          listener.callback();\n          listener = listener.next;\n        }\n      });\n    },\n    get: function get() {\n      var listeners = [];\n      var listener = first;\n      while (listener) {\n        listeners.push(listener);\n        listener = listener.next;\n      }\n      return listeners;\n    },\n    subscribe: function subscribe(callback) {\n      var isSubscribed = true;\n      var listener = last = {\n        callback: callback,\n        next: null,\n        prev: last\n      };\n      if (listener.prev) {\n        listener.prev.next = listener;\n      } else {\n        first = listener;\n      }\n      return function unsubscribe() {\n        if (!isSubscribed || first === null) return;\n        isSubscribed = false;\n        if (listener.next) {\n          listener.next.prev = listener.prev;\n        } else {\n          last = listener.prev;\n        }\n        if (listener.prev) {\n          listener.prev.next = listener.next;\n        } else {\n          first = listener.next;\n        }\n      };\n    }\n  };\n}\nvar nullListeners = {\n  notify: function notify() {},\n  get: function get() {\n    return [];\n  }\n};\nexport function createSubscription(store, parentSub) {\n  var unsubscribe;\n  var listeners = nullListeners;\n  function addNestedSub(listener) {\n    trySubscribe();\n    return listeners.subscribe(listener);\n  }\n  function notifyNestedSubs() {\n    listeners.notify();\n  }\n  function handleChangeWrapper() {\n    if (subscription.onStateChange) {\n      subscription.onStateChange();\n    }\n  }\n  function isSubscribed() {\n    return Boolean(unsubscribe);\n  }\n  function trySubscribe() {\n    if (!unsubscribe) {\n      unsubscribe = parentSub ? parentSub.addNestedSub(handleChangeWrapper) : store.subscribe(handleChangeWrapper);\n      listeners = createListenerCollection();\n    }\n  }\n  function tryUnsubscribe() {\n    if (unsubscribe) {\n      unsubscribe();\n      unsubscribe = undefined;\n      listeners.clear();\n      listeners = nullListeners;\n    }\n  }\n  var subscription = {\n    addNestedSub: addNestedSub,\n    notifyNestedSubs: notifyNestedSubs,\n    handleChangeWrapper: handleChangeWrapper,\n    isSubscribed: isSubscribed,\n    trySubscribe: trySubscribe,\n    tryUnsubscribe: tryUnsubscribe,\n    getListeners: function getListeners() {\n      return listeners;\n    }\n  };\n  return subscription;\n}", "map": {"version": 3, "names": ["getBatch", "createListenerCollection", "batch", "first", "last", "clear", "notify", "listener", "callback", "next", "get", "listeners", "push", "subscribe", "isSubscribed", "prev", "unsubscribe", "nullListeners", "createSubscription", "store", "parentSub", "addNestedSub", "trySubscribe", "notifyNestedSubs", "handleChangeWrapper", "subscription", "onStateChange", "Boolean", "tryUnsubscribe", "undefined", "getListeners"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/node_modules/react-redux/es/utils/Subscription.js"], "sourcesContent": ["import { getBatch } from './batch'; // encapsulates the subscription logic for connecting a component to the redux store, as\n// well as nesting subscriptions of descendant components, so that we can ensure the\n// ancestor components re-render before descendants\n\nfunction createListenerCollection() {\n  var batch = getBatch();\n  var first = null;\n  var last = null;\n  return {\n    clear: function clear() {\n      first = null;\n      last = null;\n    },\n    notify: function notify() {\n      batch(function () {\n        var listener = first;\n\n        while (listener) {\n          listener.callback();\n          listener = listener.next;\n        }\n      });\n    },\n    get: function get() {\n      var listeners = [];\n      var listener = first;\n\n      while (listener) {\n        listeners.push(listener);\n        listener = listener.next;\n      }\n\n      return listeners;\n    },\n    subscribe: function subscribe(callback) {\n      var isSubscribed = true;\n      var listener = last = {\n        callback: callback,\n        next: null,\n        prev: last\n      };\n\n      if (listener.prev) {\n        listener.prev.next = listener;\n      } else {\n        first = listener;\n      }\n\n      return function unsubscribe() {\n        if (!isSubscribed || first === null) return;\n        isSubscribed = false;\n\n        if (listener.next) {\n          listener.next.prev = listener.prev;\n        } else {\n          last = listener.prev;\n        }\n\n        if (listener.prev) {\n          listener.prev.next = listener.next;\n        } else {\n          first = listener.next;\n        }\n      };\n    }\n  };\n}\n\nvar nullListeners = {\n  notify: function notify() {},\n  get: function get() {\n    return [];\n  }\n};\nexport function createSubscription(store, parentSub) {\n  var unsubscribe;\n  var listeners = nullListeners;\n\n  function addNestedSub(listener) {\n    trySubscribe();\n    return listeners.subscribe(listener);\n  }\n\n  function notifyNestedSubs() {\n    listeners.notify();\n  }\n\n  function handleChangeWrapper() {\n    if (subscription.onStateChange) {\n      subscription.onStateChange();\n    }\n  }\n\n  function isSubscribed() {\n    return Boolean(unsubscribe);\n  }\n\n  function trySubscribe() {\n    if (!unsubscribe) {\n      unsubscribe = parentSub ? parentSub.addNestedSub(handleChangeWrapper) : store.subscribe(handleChangeWrapper);\n      listeners = createListenerCollection();\n    }\n  }\n\n  function tryUnsubscribe() {\n    if (unsubscribe) {\n      unsubscribe();\n      unsubscribe = undefined;\n      listeners.clear();\n      listeners = nullListeners;\n    }\n  }\n\n  var subscription = {\n    addNestedSub: addNestedSub,\n    notifyNestedSubs: notifyNestedSubs,\n    handleChangeWrapper: handleChangeWrapper,\n    isSubscribed: isSubscribed,\n    trySubscribe: trySubscribe,\n    tryUnsubscribe: tryUnsubscribe,\n    getListeners: function getListeners() {\n      return listeners;\n    }\n  };\n  return subscription;\n}"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,SAAS,CAAC,CAAC;AACpC;AACA;;AAEA,SAASC,wBAAwB,GAAG;EAClC,IAAIC,KAAK,GAAGF,QAAQ,EAAE;EACtB,IAAIG,KAAK,GAAG,IAAI;EAChB,IAAIC,IAAI,GAAG,IAAI;EACf,OAAO;IACLC,KAAK,EAAE,SAASA,KAAK,GAAG;MACtBF,KAAK,GAAG,IAAI;MACZC,IAAI,GAAG,IAAI;IACb,CAAC;IACDE,MAAM,EAAE,SAASA,MAAM,GAAG;MACxBJ,KAAK,CAAC,YAAY;QAChB,IAAIK,QAAQ,GAAGJ,KAAK;QAEpB,OAAOI,QAAQ,EAAE;UACfA,QAAQ,CAACC,QAAQ,EAAE;UACnBD,QAAQ,GAAGA,QAAQ,CAACE,IAAI;QAC1B;MACF,CAAC,CAAC;IACJ,CAAC;IACDC,GAAG,EAAE,SAASA,GAAG,GAAG;MAClB,IAAIC,SAAS,GAAG,EAAE;MAClB,IAAIJ,QAAQ,GAAGJ,KAAK;MAEpB,OAAOI,QAAQ,EAAE;QACfI,SAAS,CAACC,IAAI,CAACL,QAAQ,CAAC;QACxBA,QAAQ,GAAGA,QAAQ,CAACE,IAAI;MAC1B;MAEA,OAAOE,SAAS;IAClB,CAAC;IACDE,SAAS,EAAE,SAASA,SAAS,CAACL,QAAQ,EAAE;MACtC,IAAIM,YAAY,GAAG,IAAI;MACvB,IAAIP,QAAQ,GAAGH,IAAI,GAAG;QACpBI,QAAQ,EAAEA,QAAQ;QAClBC,IAAI,EAAE,IAAI;QACVM,IAAI,EAAEX;MACR,CAAC;MAED,IAAIG,QAAQ,CAACQ,IAAI,EAAE;QACjBR,QAAQ,CAACQ,IAAI,CAACN,IAAI,GAAGF,QAAQ;MAC/B,CAAC,MAAM;QACLJ,KAAK,GAAGI,QAAQ;MAClB;MAEA,OAAO,SAASS,WAAW,GAAG;QAC5B,IAAI,CAACF,YAAY,IAAIX,KAAK,KAAK,IAAI,EAAE;QACrCW,YAAY,GAAG,KAAK;QAEpB,IAAIP,QAAQ,CAACE,IAAI,EAAE;UACjBF,QAAQ,CAACE,IAAI,CAACM,IAAI,GAAGR,QAAQ,CAACQ,IAAI;QACpC,CAAC,MAAM;UACLX,IAAI,GAAGG,QAAQ,CAACQ,IAAI;QACtB;QAEA,IAAIR,QAAQ,CAACQ,IAAI,EAAE;UACjBR,QAAQ,CAACQ,IAAI,CAACN,IAAI,GAAGF,QAAQ,CAACE,IAAI;QACpC,CAAC,MAAM;UACLN,KAAK,GAAGI,QAAQ,CAACE,IAAI;QACvB;MACF,CAAC;IACH;EACF,CAAC;AACH;AAEA,IAAIQ,aAAa,GAAG;EAClBX,MAAM,EAAE,SAASA,MAAM,GAAG,CAAC,CAAC;EAC5BI,GAAG,EAAE,SAASA,GAAG,GAAG;IAClB,OAAO,EAAE;EACX;AACF,CAAC;AACD,OAAO,SAASQ,kBAAkB,CAACC,KAAK,EAAEC,SAAS,EAAE;EACnD,IAAIJ,WAAW;EACf,IAAIL,SAAS,GAAGM,aAAa;EAE7B,SAASI,YAAY,CAACd,QAAQ,EAAE;IAC9Be,YAAY,EAAE;IACd,OAAOX,SAAS,CAACE,SAAS,CAACN,QAAQ,CAAC;EACtC;EAEA,SAASgB,gBAAgB,GAAG;IAC1BZ,SAAS,CAACL,MAAM,EAAE;EACpB;EAEA,SAASkB,mBAAmB,GAAG;IAC7B,IAAIC,YAAY,CAACC,aAAa,EAAE;MAC9BD,YAAY,CAACC,aAAa,EAAE;IAC9B;EACF;EAEA,SAASZ,YAAY,GAAG;IACtB,OAAOa,OAAO,CAACX,WAAW,CAAC;EAC7B;EAEA,SAASM,YAAY,GAAG;IACtB,IAAI,CAACN,WAAW,EAAE;MAChBA,WAAW,GAAGI,SAAS,GAAGA,SAAS,CAACC,YAAY,CAACG,mBAAmB,CAAC,GAAGL,KAAK,CAACN,SAAS,CAACW,mBAAmB,CAAC;MAC5Gb,SAAS,GAAGV,wBAAwB,EAAE;IACxC;EACF;EAEA,SAAS2B,cAAc,GAAG;IACxB,IAAIZ,WAAW,EAAE;MACfA,WAAW,EAAE;MACbA,WAAW,GAAGa,SAAS;MACvBlB,SAAS,CAACN,KAAK,EAAE;MACjBM,SAAS,GAAGM,aAAa;IAC3B;EACF;EAEA,IAAIQ,YAAY,GAAG;IACjBJ,YAAY,EAAEA,YAAY;IAC1BE,gBAAgB,EAAEA,gBAAgB;IAClCC,mBAAmB,EAAEA,mBAAmB;IACxCV,YAAY,EAAEA,YAAY;IAC1BQ,YAAY,EAAEA,YAAY;IAC1BM,cAAc,EAAEA,cAAc;IAC9BE,YAAY,EAAE,SAASA,YAAY,GAAG;MACpC,OAAOnB,SAAS;IAClB;EACF,CAAC;EACD,OAAOc,YAAY;AACrB"}, "metadata": {}, "sourceType": "module"}