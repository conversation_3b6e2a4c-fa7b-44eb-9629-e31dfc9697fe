{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\GreenRP\\\\Web\\\\ClientApp\\\\src\\\\features\\\\users\\\\Detail.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useSelector } from 'react-redux';\nimport { useParams, useNavigate, Navigate } from 'react-router';\nimport { Button, FormGroup, Input, Label, Modal, ModalHeader, ModalBody, ModalFooter } from 'reactstrap';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { selectUsers } from './users-slice';\nimport { routes } from 'app/routes';\nimport { useAuth } from 'features/auth/use-auth';\nimport { Link } from 'react-router-dom';\nimport { authApi } from 'api/auth-service';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nclass Permissions {\n  constructor(roles) {\n    this.roles = roles;\n  }\n  can(role) {\n    return this.roles.indexOf(role) !== -1;\n  }\n  update(role, include) {\n    const roles = include ? this.roles.concat([role]) : this.roles.filter(r => r !== role);\n    return new Permissions(roles);\n  }\n  getRoles() {\n    return this.roles.map(r => r);\n  }\n}\nfunction Detail() {\n  _s();\n  const navigate = useNavigate(),\n    {\n      isInRole,\n      user: credentials\n    } = useAuth(),\n    {\n      name\n    } = useParams(),\n    users = useSelector(selectUsers),\n    [username, setUsername] = useState(''),\n    [email, setEmail] = useState(''),\n    [phone, setPhone] = useState(''),\n    [showPasswordModal, setShowPasswordModal] = useState(false),\n    [password, setPassword] = useState(''),\n    user = users.find(u => u.name === name),\n    canUpdate = isInRole('admin:users'),\n    [permissions, setPermissions] = useState(new Permissions((user === null || user === void 0 ? void 0 : user.roles) || []));\n  useEffect(() => {\n    if (user) {\n      setUsername(user.name);\n      setEmail(user.email || '');\n      setPhone(user.phone || '');\n    }\n  }, [user]);\n  const handleNameChange = e => {\n    setUsername(e.target.value);\n  };\n  const handleEmailChange = e => {\n    setEmail(e.target.value);\n  };\n  const handlePhoneChange = e => {\n    setPhone(e.target.value);\n  };\n  const handlePermissionChanged = (role, e) => {\n    setPermissions(permissions.update(role, e.target.checked));\n  };\n  const handleSaveClick = async () => {\n    if (credentials && user) {\n      const roles = permissions.getRoles(),\n        model = {\n          ...user,\n          name: username,\n          email,\n          phone,\n          roles\n        },\n        response = await authApi.updateUser(credentials, model);\n      if (response) {\n        navigate(routes.users.path);\n      }\n    }\n  };\n  const handleSetPasswordClick = () => {\n    setShowPasswordModal(true);\n  };\n  const handleSetPasswordCancel = () => {\n    setShowPasswordModal(false);\n  };\n  const handlePasswordModalOpened = () => {\n    setPassword('');\n  };\n  const handlePasswordChange = e => {\n    setPassword(e.target.value);\n  };\n  const handleSetPasswordConfirm = async () => {\n    if (credentials && user && password) {\n      const model = {\n          ...user\n        },\n        response = await authApi.updatePassword(credentials, model, password);\n      if (response) {\n        setShowPasswordModal(false);\n        navigate(routes.users.path);\n      }\n    }\n  };\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: routes.users.path\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container d-grid gap-2\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: routes.users.path,\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: ['fat', 'chevron-left']\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this), \"\\xA0 Back to Zones List\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n      children: [\"User \", user.name]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"user-name\",\n          children: \"Name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"user-name\",\n          value: username,\n          onChange: handleNameChange,\n          disabled: !canUpdate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"user-email\",\n          children: \"Email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"user-email\",\n          type: \"email\",\n          value: email,\n          onChange: handleEmailChange,\n          disabled: !canUpdate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"user-phone\",\n          children: \"Phoone\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"user-phone\",\n          value: phone,\n          onChange: handlePhoneChange,\n          disabled: !canUpdate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row mt-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-4 py-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"border-bottom\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: ['fat', 'seedling']\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), \"\\xA0 Plants\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            type: \"checkbox\",\n            id: \"view-plants\",\n            checked: permissions.can('view:plants'),\n            onChange: e => handlePermissionChanged('view:plants', e)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this), \"\\xA0\", /*#__PURE__*/_jsxDEV(Label, {\n            check: true,\n            for: \"view-plants\",\n            children: \"View\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            type: \"checkbox\",\n            id: \"create-plants\",\n            checked: permissions.can('create:plants'),\n            onChange: e => handlePermissionChanged('create:plants', e)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), \"\\xA0\", /*#__PURE__*/_jsxDEV(Label, {\n            check: true,\n            for: \"create-plants\",\n            children: \"Add\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            type: \"checkbox\",\n            id: \"update-plants\",\n            checked: permissions.can('update:plants'),\n            onChange: e => handlePermissionChanged('update:plants', e)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this), \"\\xA0\", /*#__PURE__*/_jsxDEV(Label, {\n            check: true,\n            for: \"update-plants\",\n            children: \"Edit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            type: \"checkbox\",\n            id: \"delete-plants\",\n            checked: permissions.can('delete:plants'),\n            onChange: e => handlePermissionChanged('delete:plants', e)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), \"\\xA0\", /*#__PURE__*/_jsxDEV(Label, {\n            check: true,\n            for: \"delete-plants\",\n            children: \"Delete\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-4 py-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"border-bottom\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: ['fat', 'user-tie']\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this), \"\\xA0 Customers\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            type: \"checkbox\",\n            id: \"view-customers\",\n            checked: permissions.can('view:customers'),\n            onChange: e => handlePermissionChanged('view:customers', e)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this), \"\\xA0\", /*#__PURE__*/_jsxDEV(Label, {\n            check: true,\n            for: \"view-customers\",\n            children: \"View\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            type: \"checkbox\",\n            id: \"create-customers\",\n            checked: permissions.can('create:customers'),\n            onChange: e => handlePermissionChanged('create:customers', e)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this), \"\\xA0\", /*#__PURE__*/_jsxDEV(Label, {\n            check: true,\n            for: \"create-customers\",\n            children: \"Add\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            type: \"checkbox\",\n            id: \"update-customers\",\n            checked: permissions.can('update:customers'),\n            onChange: e => handlePermissionChanged('update:customers', e)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this), \"\\xA0\", /*#__PURE__*/_jsxDEV(Label, {\n            check: true,\n            for: \"update-customers\",\n            children: \"Edit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            type: \"checkbox\",\n            id: \"delete-customers\",\n            checked: permissions.can('delete:customers'),\n            onChange: e => handlePermissionChanged('delete:customers', e)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this), \"\\xA0\", /*#__PURE__*/_jsxDEV(Label, {\n            check: true,\n            for: \"delete-customers\",\n            children: \"Delete\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-4 py-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"border-bottom\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: ['fat', 'map-location-dot']\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this), \"\\xA0 Zones\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            type: \"checkbox\",\n            id: \"view-zones\",\n            checked: permissions.can('view:zones'),\n            onChange: e => handlePermissionChanged('view:zones', e)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this), \"\\xA0\", /*#__PURE__*/_jsxDEV(Label, {\n            check: true,\n            for: \"view-zones\",\n            children: \"View\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            type: \"checkbox\",\n            id: \"create-zones\",\n            checked: permissions.can('create:zones'),\n            onChange: e => handlePermissionChanged('create:zones', e)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this), \"\\xA0\", /*#__PURE__*/_jsxDEV(Label, {\n            check: true,\n            for: \"create-zones\",\n            children: \"Add\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            type: \"checkbox\",\n            id: \"update-zones\",\n            checked: permissions.can('update:zones'),\n            onChange: e => handlePermissionChanged('update:zones', e)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this), \"\\xA0\", /*#__PURE__*/_jsxDEV(Label, {\n            check: true,\n            for: \"update-zones\",\n            children: \"Edit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            type: \"checkbox\",\n            id: \"delete-zones\",\n            checked: permissions.can('delete:zones'),\n            onChange: e => handlePermissionChanged('delete:zones', e)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this), \"\\xA0\", /*#__PURE__*/_jsxDEV(Label, {\n            check: true,\n            for: \"delete-zones\",\n            children: \"Delete\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-4 py-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"border-bottom\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: ['fat', 'file-invoice']\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this), \"\\xA0 Orders\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            type: \"checkbox\",\n            id: \"view-orders\",\n            checked: permissions.can('view:orders'),\n            onChange: e => handlePermissionChanged('view:orders', e)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this), \"\\xA0\", /*#__PURE__*/_jsxDEV(Label, {\n            check: true,\n            for: \"view-orders\",\n            children: \"View\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            type: \"checkbox\",\n            id: \"create-orders\",\n            checked: permissions.can('create:orders'),\n            onChange: e => handlePermissionChanged('create:orders', e)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this), \"\\xA0\", /*#__PURE__*/_jsxDEV(Label, {\n            check: true,\n            for: \"create-orders\",\n            children: \"Add\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            type: \"checkbox\",\n            id: \"update-orders\",\n            checked: permissions.can('update:orders'),\n            onChange: e => handlePermissionChanged('update:orders', e)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this), \"\\xA0\", /*#__PURE__*/_jsxDEV(Label, {\n            check: true,\n            for: \"update-orders\",\n            children: \"Edit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            type: \"checkbox\",\n            id: \"delete-orders\",\n            checked: permissions.can('delete:orders'),\n            onChange: e => handlePermissionChanged('delete:orders', e)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this), \"\\xA0\", /*#__PURE__*/_jsxDEV(Label, {\n            check: true,\n            for: \"delete-orders\",\n            children: \"Delete\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            type: \"checkbox\",\n            id: \"notify-orders\",\n            checked: permissions.can('notify:orders'),\n            onChange: e => handlePermissionChanged('notify:orders', e)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 13\n          }, this), \"\\xA0\", /*#__PURE__*/_jsxDEV(Label, {\n            check: true,\n            for: \"notify-orders\",\n            children: \"Notify on Change\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-4 py-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"border-bottom\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: ['fat', 'user']\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this), \"\\xA0 Users\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            type: \"checkbox\",\n            id: \"administer-users\",\n            checked: permissions.can('admin:users'),\n            onChange: e => handlePermissionChanged('admin:users', e)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this), \"\\xA0\", /*#__PURE__*/_jsxDEV(Label, {\n            check: true,\n            for: \"administer-users\",\n            children: \"Make Changes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 col-md-4 py-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"border-bottom\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: ['fat', 'truck-fast']\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 13\n          }, this), \"\\xA0 Driver Tasks\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            type: \"checkbox\",\n            id: \"is-driver\",\n            checked: permissions.can('driver'),\n            onChange: e => handlePermissionChanged('driver', e)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 13\n          }, this), \"\\xA0\", /*#__PURE__*/_jsxDEV(Label, {\n            check: true,\n            for: \"is-driver\",\n            children: \"User is a Driver\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            type: \"checkbox\",\n            id: \"view-driver-tasks\",\n            checked: permissions.can('view:driver-tasks'),\n            onChange: e => handlePermissionChanged('view:driver-tasks', e)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 13\n          }, this), \"\\xA0\", /*#__PURE__*/_jsxDEV(Label, {\n            check: true,\n            for: \"view-driver-tasks\",\n            children: \"View Driver Tasks\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            type: \"checkbox\",\n            id: \"create-driver-tasks\",\n            checked: permissions.can('create:driver-tasks'),\n            onChange: e => handlePermissionChanged('create:driver-tasks', e)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 13\n          }, this), \"\\xA0\", /*#__PURE__*/_jsxDEV(Label, {\n            check: true,\n            for: \"create-driver-tasks\",\n            children: \"Create Driver Tasks\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 416,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row sticky-bottom bg-white border-top py-2\",\n      children: [canUpdate && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-auto me-auto\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSetPasswordClick,\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: ['fat', 'key']\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 15\n          }, this), \"\\xA0 Set Password\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 463,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col text-end\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          tag: Link,\n          to: routes.users.path,\n          outline: true,\n          size: \"lg\",\n          children: canUpdate ? 'Cancel' : 'Close'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 471,\n          columnNumber: 11\n        }, this), canUpdate && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [\"\\xA0\", /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSaveClick,\n            color: \"success\",\n            size: \"lg\",\n            children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: ['fat', 'save']\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 17\n            }, this), \"\\xA0 Save\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 470,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 461,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      isOpen: showPasswordModal,\n      toggle: handleSetPasswordCancel,\n      onOpened: handlePasswordModalOpened,\n      autoFocus: false,\n      children: [/*#__PURE__*/_jsxDEV(ModalHeader, {\n        toggle: handleSetPasswordCancel,\n        children: \"Set Password\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 490,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ModalBody, {\n        children: /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"password\",\n            children: \"New Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"password\",\n            bsSize: \"lg\",\n            value: password,\n            onChange: handlePasswordChange,\n            autoFocus: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 494,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ModalFooter, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          outline: true,\n          onClick: handleSetPasswordCancel,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 504,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          color: \"primary\",\n          outline: true,\n          onClick: handleSetPasswordConfirm,\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: ['fat', 'key']\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 13\n          }, this), \"\\xA0 Save\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 503,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 485,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 129,\n    columnNumber: 5\n  }, this);\n}\n_s(Detail, \"cmav7fS9e8rVB5XVIwTSWILSEqk=\", false, function () {\n  return [useNavigate, useAuth, useParams, useSelector];\n});\n_c = Detail;\nexport default Detail;\nvar _c;\n$RefreshReg$(_c, \"Detail\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useSelector", "useParams", "useNavigate", "Navigate", "<PERSON><PERSON>", "FormGroup", "Input", "Label", "Modal", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ModalBody", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FontAwesomeIcon", "selectUsers", "routes", "useAuth", "Link", "authApi", "Permissions", "constructor", "roles", "can", "role", "indexOf", "update", "include", "concat", "filter", "r", "getRoles", "map", "Detail", "navigate", "isInRole", "user", "credentials", "name", "users", "username", "setUsername", "email", "setEmail", "phone", "setPhone", "showPasswordModal", "setShowPasswordModal", "password", "setPassword", "find", "u", "canUpdate", "permissions", "setPermissions", "handleNameChange", "e", "target", "value", "handleEmailChange", "handlePhoneChange", "handlePermissionChanged", "checked", "handleSaveClick", "model", "response", "updateUser", "path", "handleSetPasswordClick", "handleSetPasswordCancel", "handlePasswordModalOpened", "handlePasswordChange", "handleSetPasswordConfirm", "updatePassword"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/users/Detail.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { useSelector } from 'react-redux';\r\nimport { useParams, useNavigate, Navigate } from 'react-router';\r\nimport {\r\n  Button,\r\n  FormGroup,\r\n  Input,\r\n  Label,\r\n  Modal,\r\n  ModalHeader,\r\n  ModalBody,\r\n  ModalFooter,\r\n} from 'reactstrap';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { selectUsers } from './users-slice';\r\nimport * as models from 'api/models/auth';\r\nimport { routes } from 'app/routes';\r\nimport { useAuth } from 'features/auth/use-auth';\r\nimport { Link } from 'react-router-dom';\r\nimport { authApi } from 'api/auth-service';\r\n\r\nclass Permissions {\r\n  constructor(private roles: string[]) {}\r\n\r\n  can(role: models.Roles) {\r\n    return this.roles.indexOf(role) !== -1;\r\n  }\r\n\r\n  update(role: models.Roles, include: boolean) {\r\n    const roles = include\r\n      ? this.roles.concat([role])\r\n      : this.roles.filter((r) => r !== role);\r\n    return new Permissions(roles);\r\n  }\r\n\r\n  getRoles() {\r\n    return this.roles.map((r) => r);\r\n  }\r\n}\r\n\r\nfunction Detail() {\r\n  const navigate = useNavigate(),\r\n    { isInRole, user: credentials } = useAuth(),\r\n    { name } = useParams<{ name: string }>(),\r\n    users = useSelector(selectUsers),\r\n    [username, setUsername] = useState(''),\r\n    [email, setEmail] = useState(''),\r\n    [phone, setPhone] = useState(''),\r\n    [showPasswordModal, setShowPasswordModal] = useState(false),\r\n    [password, setPassword] = useState(''),\r\n    user = users.find((u) => u.name === name),\r\n    canUpdate = isInRole('admin:users'),\r\n    [permissions, setPermissions] = useState(\r\n      new Permissions(user?.roles || [])\r\n    );\r\n\r\n  useEffect(() => {\r\n    if (user) {\r\n      setUsername(user.name);\r\n      setEmail(user.email || '');\r\n      setPhone(user.phone || '');\r\n    }\r\n  }, [user]);\r\n\r\n  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    setUsername(e.target.value);\r\n  };\r\n\r\n  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    setEmail(e.target.value);\r\n  };\r\n\r\n  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    setPhone(e.target.value);\r\n  };\r\n\r\n  const handlePermissionChanged = (\r\n    role: models.Roles,\r\n    e: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    setPermissions(permissions.update(role, e.target.checked));\r\n  };\r\n\r\n  const handleSaveClick = async () => {\r\n    if (credentials && user) {\r\n      const roles = permissions.getRoles(),\r\n        model = { ...user, name: username, email, phone, roles },\r\n        response = await authApi.updateUser(credentials, model);\r\n\r\n      if (response) {\r\n        navigate(routes.users.path);\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleSetPasswordClick = () => {\r\n    setShowPasswordModal(true);\r\n  };\r\n\r\n  const handleSetPasswordCancel = () => {\r\n    setShowPasswordModal(false);\r\n  };\r\n\r\n  const handlePasswordModalOpened = () => {\r\n    setPassword('');\r\n  };\r\n\r\n  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    setPassword(e.target.value);\r\n  };\r\n\r\n  const handleSetPasswordConfirm = async () => {\r\n    if (credentials && user && password) {\r\n      const model = { ...user },\r\n        response = await authApi.updatePassword(credentials, model, password);\r\n\r\n      if (response) {\r\n        setShowPasswordModal(false);\r\n        navigate(routes.users.path);\r\n      }\r\n    }\r\n  };\r\n\r\n  if (!user) {\r\n    return <Navigate to={routes.users.path} />;\r\n  }\r\n\r\n  return (\r\n    <div className=\"container d-grid gap-2\">\r\n      <div className=\"row\">\r\n        <div className=\"col\">\r\n          <Link to={routes.users.path}>\r\n            <FontAwesomeIcon icon={['fat', 'chevron-left']} />\r\n            &nbsp; Back to Zones List\r\n          </Link>\r\n        </div>\r\n      </div>\r\n      <h1>User {user.name}</h1>\r\n      <div className=\"row\">\r\n        <div className=\"col-12 col-md-4\">\r\n          <label htmlFor=\"user-name\">Name</label>\r\n          <Input\r\n            id=\"user-name\"\r\n            value={username}\r\n            onChange={handleNameChange}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n        <div className=\"col-12 col-md-4\">\r\n          <label htmlFor=\"user-email\">Email</label>\r\n          <Input\r\n            id=\"user-email\"\r\n            type=\"email\"\r\n            value={email}\r\n            onChange={handleEmailChange}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n        <div className=\"col-12 col-md-4\">\r\n          <label htmlFor=\"user-phone\">Phoone</label>\r\n          <Input\r\n            id=\"user-phone\"\r\n            value={phone}\r\n            onChange={handlePhoneChange}\r\n            disabled={!canUpdate}\r\n          />\r\n        </div>\r\n      </div>\r\n      <div className=\"row mt-4\">\r\n        <div className=\"col-12 col-md-4 py-2\">\r\n          <h2 className=\"border-bottom\">\r\n            <FontAwesomeIcon icon={['fat', 'seedling']} />\r\n            &nbsp; Plants\r\n          </h2>\r\n          <FormGroup>\r\n            <Input\r\n              type=\"checkbox\"\r\n              id=\"view-plants\"\r\n              checked={permissions.can('view:plants')}\r\n              onChange={(e) => handlePermissionChanged('view:plants', e)}\r\n            />\r\n            &nbsp;\r\n            <Label check for=\"view-plants\">\r\n              View\r\n            </Label>\r\n          </FormGroup>\r\n          <FormGroup>\r\n            <Input\r\n              type=\"checkbox\"\r\n              id=\"create-plants\"\r\n              checked={permissions.can('create:plants')}\r\n              onChange={(e) => handlePermissionChanged('create:plants', e)}\r\n            />\r\n            &nbsp;\r\n            <Label check for=\"create-plants\">\r\n              Add\r\n            </Label>\r\n          </FormGroup>\r\n          <FormGroup>\r\n            <Input\r\n              type=\"checkbox\"\r\n              id=\"update-plants\"\r\n              checked={permissions.can('update:plants')}\r\n              onChange={(e) => handlePermissionChanged('update:plants', e)}\r\n            />\r\n            &nbsp;\r\n            <Label check for=\"update-plants\">\r\n              Edit\r\n            </Label>\r\n          </FormGroup>\r\n          <FormGroup>\r\n            <Input\r\n              type=\"checkbox\"\r\n              id=\"delete-plants\"\r\n              checked={permissions.can('delete:plants')}\r\n              onChange={(e) => handlePermissionChanged('delete:plants', e)}\r\n            />\r\n            &nbsp;\r\n            <Label check for=\"delete-plants\">\r\n              Delete\r\n            </Label>\r\n          </FormGroup>\r\n        </div>\r\n        <div className=\"col-12 col-md-4 py-2\">\r\n          <h2 className=\"border-bottom\">\r\n            <FontAwesomeIcon icon={['fat', 'user-tie']} />\r\n            &nbsp; Customers\r\n          </h2>\r\n          <FormGroup>\r\n            <Input\r\n              type=\"checkbox\"\r\n              id=\"view-customers\"\r\n              checked={permissions.can('view:customers')}\r\n              onChange={(e) => handlePermissionChanged('view:customers', e)}\r\n            />\r\n            &nbsp;\r\n            <Label check for=\"view-customers\">\r\n              View\r\n            </Label>\r\n          </FormGroup>\r\n          <FormGroup>\r\n            <Input\r\n              type=\"checkbox\"\r\n              id=\"create-customers\"\r\n              checked={permissions.can('create:customers')}\r\n              onChange={(e) => handlePermissionChanged('create:customers', e)}\r\n            />\r\n            &nbsp;\r\n            <Label check for=\"create-customers\">\r\n              Add\r\n            </Label>\r\n          </FormGroup>\r\n          <FormGroup>\r\n            <Input\r\n              type=\"checkbox\"\r\n              id=\"update-customers\"\r\n              checked={permissions.can('update:customers')}\r\n              onChange={(e) => handlePermissionChanged('update:customers', e)}\r\n            />\r\n            &nbsp;\r\n            <Label check for=\"update-customers\">\r\n              Edit\r\n            </Label>\r\n          </FormGroup>\r\n          <FormGroup>\r\n            <Input\r\n              type=\"checkbox\"\r\n              id=\"delete-customers\"\r\n              checked={permissions.can('delete:customers')}\r\n              onChange={(e) => handlePermissionChanged('delete:customers', e)}\r\n            />\r\n            &nbsp;\r\n            <Label check for=\"delete-customers\">\r\n              Delete\r\n            </Label>\r\n          </FormGroup>\r\n        </div>\r\n        <div className=\"col-12 col-md-4 py-2\">\r\n          <h2 className=\"border-bottom\">\r\n            <FontAwesomeIcon icon={['fat', 'map-location-dot']} />\r\n            &nbsp; Zones\r\n          </h2>\r\n          <FormGroup>\r\n            <Input\r\n              type=\"checkbox\"\r\n              id=\"view-zones\"\r\n              checked={permissions.can('view:zones')}\r\n              onChange={(e) => handlePermissionChanged('view:zones', e)}\r\n            />\r\n            &nbsp;\r\n            <Label check for=\"view-zones\">\r\n              View\r\n            </Label>\r\n          </FormGroup>\r\n          <FormGroup>\r\n            <Input\r\n              type=\"checkbox\"\r\n              id=\"create-zones\"\r\n              checked={permissions.can('create:zones')}\r\n              onChange={(e) => handlePermissionChanged('create:zones', e)}\r\n            />\r\n            &nbsp;\r\n            <Label check for=\"create-zones\">\r\n              Add\r\n            </Label>\r\n          </FormGroup>\r\n          <FormGroup>\r\n            <Input\r\n              type=\"checkbox\"\r\n              id=\"update-zones\"\r\n              checked={permissions.can('update:zones')}\r\n              onChange={(e) => handlePermissionChanged('update:zones', e)}\r\n            />\r\n            &nbsp;\r\n            <Label check for=\"update-zones\">\r\n              Edit\r\n            </Label>\r\n          </FormGroup>\r\n          <FormGroup>\r\n            <Input\r\n              type=\"checkbox\"\r\n              id=\"delete-zones\"\r\n              checked={permissions.can('delete:zones')}\r\n              onChange={(e) => handlePermissionChanged('delete:zones', e)}\r\n            />\r\n            &nbsp;\r\n            <Label check for=\"delete-zones\">\r\n              Delete\r\n            </Label>\r\n          </FormGroup>\r\n        </div>\r\n        <div className=\"col-12 col-md-4 py-2\">\r\n          <h2 className=\"border-bottom\">\r\n            <FontAwesomeIcon icon={['fat', 'file-invoice']} />\r\n            &nbsp; Orders\r\n          </h2>\r\n          <FormGroup>\r\n            <Input\r\n              type=\"checkbox\"\r\n              id=\"view-orders\"\r\n              checked={permissions.can('view:orders')}\r\n              onChange={(e) => handlePermissionChanged('view:orders', e)}\r\n            />\r\n            &nbsp;\r\n            <Label check for=\"view-orders\">\r\n              View\r\n            </Label>\r\n          </FormGroup>\r\n          <FormGroup>\r\n            <Input\r\n              type=\"checkbox\"\r\n              id=\"create-orders\"\r\n              checked={permissions.can('create:orders')}\r\n              onChange={(e) => handlePermissionChanged('create:orders', e)}\r\n            />\r\n            &nbsp;\r\n            <Label check for=\"create-orders\">\r\n              Add\r\n            </Label>\r\n          </FormGroup>\r\n          <FormGroup>\r\n            <Input\r\n              type=\"checkbox\"\r\n              id=\"update-orders\"\r\n              checked={permissions.can('update:orders')}\r\n              onChange={(e) => handlePermissionChanged('update:orders', e)}\r\n            />\r\n            &nbsp;\r\n            <Label check for=\"update-orders\">\r\n              Edit\r\n            </Label>\r\n          </FormGroup>\r\n          <FormGroup>\r\n            <Input\r\n              type=\"checkbox\"\r\n              id=\"delete-orders\"\r\n              checked={permissions.can('delete:orders')}\r\n              onChange={(e) => handlePermissionChanged('delete:orders', e)}\r\n            />\r\n            &nbsp;\r\n            <Label check for=\"delete-orders\">\r\n              Delete\r\n            </Label>\r\n          </FormGroup>\r\n          <FormGroup>\r\n            <Input\r\n              type=\"checkbox\"\r\n              id=\"notify-orders\"\r\n              checked={permissions.can('notify:orders')}\r\n              onChange={(e) => handlePermissionChanged('notify:orders', e)}\r\n            />\r\n            &nbsp;\r\n            <Label check for=\"notify-orders\">\r\n              Notify on Change\r\n            </Label>\r\n          </FormGroup>\r\n        </div>\r\n        <div className=\"col-12 col-md-4 py-2\">\r\n          <h2 className=\"border-bottom\">\r\n            <FontAwesomeIcon icon={['fat', 'user']} />\r\n            &nbsp; Users\r\n          </h2>\r\n          <FormGroup>\r\n            <Input\r\n              type=\"checkbox\"\r\n              id=\"administer-users\"\r\n              checked={permissions.can('admin:users')}\r\n              onChange={(e) => handlePermissionChanged('admin:users', e)}\r\n            />\r\n            &nbsp;\r\n            <Label check for=\"administer-users\">\r\n              Make Changes\r\n            </Label>\r\n          </FormGroup>\r\n        </div>\r\n        <div className=\"col-12 col-md-4 py-2\">\r\n          <h2 className=\"border-bottom\">\r\n            <FontAwesomeIcon icon={['fat', 'truck-fast']} />\r\n            &nbsp; Driver Tasks\r\n          </h2>\r\n          <FormGroup>\r\n            <Input\r\n              type=\"checkbox\"\r\n              id=\"is-driver\"\r\n              checked={permissions.can('driver')}\r\n              onChange={(e) => handlePermissionChanged('driver', e)}\r\n            />\r\n            &nbsp;\r\n            <Label check for=\"is-driver\">\r\n              User is a Driver\r\n            </Label>\r\n          </FormGroup>\r\n          <FormGroup>\r\n            <Input\r\n              type=\"checkbox\"\r\n              id=\"view-driver-tasks\"\r\n              checked={permissions.can('view:driver-tasks')}\r\n              onChange={(e) => handlePermissionChanged('view:driver-tasks', e)}\r\n            />\r\n            &nbsp;\r\n            <Label check for=\"view-driver-tasks\">\r\n              View Driver Tasks\r\n            </Label>\r\n          </FormGroup>\r\n          <FormGroup>\r\n            <Input\r\n              type=\"checkbox\"\r\n              id=\"create-driver-tasks\"\r\n              checked={permissions.can('create:driver-tasks')}\r\n              onChange={(e) =>\r\n                handlePermissionChanged('create:driver-tasks', e)\r\n              }\r\n            />\r\n            &nbsp;\r\n            <Label check for=\"create-driver-tasks\">\r\n              Create Driver Tasks\r\n            </Label>\r\n          </FormGroup>\r\n        </div>\r\n      </div>\r\n      <div className=\"row sticky-bottom bg-white border-top py-2\">\r\n        {canUpdate && (\r\n          <div className=\"col-auto me-auto\">\r\n            <Button onClick={handleSetPasswordClick}>\r\n              <FontAwesomeIcon icon={['fat', 'key']} />\r\n              &nbsp; Set Password\r\n            </Button>\r\n          </div>\r\n        )}\r\n        <div className=\"col text-end\">\r\n          <Button tag={Link} to={routes.users.path} outline size=\"lg\">\r\n            {canUpdate ? 'Cancel' : 'Close'}\r\n          </Button>\r\n          {canUpdate && (\r\n            <>\r\n              &nbsp;\r\n              <Button onClick={handleSaveClick} color=\"success\" size=\"lg\">\r\n                <FontAwesomeIcon icon={['fat', 'save']} />\r\n                &nbsp; Save\r\n              </Button>\r\n            </>\r\n          )}\r\n        </div>\r\n      </div>\r\n      <Modal\r\n        isOpen={showPasswordModal}\r\n        toggle={handleSetPasswordCancel}\r\n        onOpened={handlePasswordModalOpened}\r\n        autoFocus={false}>\r\n        <ModalHeader toggle={handleSetPasswordCancel}>Set Password</ModalHeader>\r\n        <ModalBody>\r\n          <FormGroup>\r\n            <label htmlFor=\"password\">New Password</label>\r\n            <Input\r\n              id=\"password\"\r\n              bsSize=\"lg\"\r\n              value={password}\r\n              onChange={handlePasswordChange}\r\n              autoFocus\r\n            />\r\n          </FormGroup>\r\n        </ModalBody>\r\n        <ModalFooter>\r\n          <Button outline onClick={handleSetPasswordCancel}>\r\n            Cancel\r\n          </Button>\r\n          <Button color=\"primary\" outline onClick={handleSetPasswordConfirm}>\r\n            <FontAwesomeIcon icon={['fat', 'key']} />\r\n            &nbsp; Save\r\n          </Button>\r\n        </ModalFooter>\r\n      </Modal>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Detail;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,SAAS,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,cAAc;AAC/D,SACEC,MAAM,EACNC,SAAS,EACTC,KAAK,EACLC,KAAK,EACLC,KAAK,EACLC,WAAW,EACXC,SAAS,EACTC,WAAW,QACN,YAAY;AACnB,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,WAAW,QAAQ,eAAe;AAE3C,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,OAAO,QAAQ,kBAAkB;AAAC;AAAA;AAE3C,MAAMC,WAAW,CAAC;EAChBC,WAAW,CAASC,KAAe,EAAE;IAAA,KAAjBA,KAAe,GAAfA,KAAe;EAAG;EAEtCC,GAAG,CAACC,IAAkB,EAAE;IACtB,OAAO,IAAI,CAACF,KAAK,CAACG,OAAO,CAACD,IAAI,CAAC,KAAK,CAAC,CAAC;EACxC;EAEAE,MAAM,CAACF,IAAkB,EAAEG,OAAgB,EAAE;IAC3C,MAAML,KAAK,GAAGK,OAAO,GACjB,IAAI,CAACL,KAAK,CAACM,MAAM,CAAC,CAACJ,IAAI,CAAC,CAAC,GACzB,IAAI,CAACF,KAAK,CAACO,MAAM,CAAEC,CAAC,IAAKA,CAAC,KAAKN,IAAI,CAAC;IACxC,OAAO,IAAIJ,WAAW,CAACE,KAAK,CAAC;EAC/B;EAEAS,QAAQ,GAAG;IACT,OAAO,IAAI,CAACT,KAAK,CAACU,GAAG,CAAEF,CAAC,IAAKA,CAAC,CAAC;EACjC;AACF;AAEA,SAASG,MAAM,GAAG;EAAA;EAChB,MAAMC,QAAQ,GAAG9B,WAAW,EAAE;IAC5B;MAAE+B,QAAQ;MAAEC,IAAI,EAAEC;IAAY,CAAC,GAAGpB,OAAO,EAAE;IAC3C;MAAEqB;IAAK,CAAC,GAAGnC,SAAS,EAAoB;IACxCoC,KAAK,GAAGrC,WAAW,CAACa,WAAW,CAAC;IAChC,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;IACtC,CAACyC,KAAK,EAAEC,QAAQ,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;IAChC,CAAC2C,KAAK,EAAEC,QAAQ,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;IAChC,CAAC6C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;IAC3D,CAAC+C,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;IACtCmC,IAAI,GAAGG,KAAK,CAACW,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACb,IAAI,KAAKA,IAAI,CAAC;IACzCc,SAAS,GAAGjB,QAAQ,CAAC,aAAa,CAAC;IACnC,CAACkB,WAAW,EAAEC,cAAc,CAAC,GAAGrD,QAAQ,CACtC,IAAImB,WAAW,CAAC,CAAAgB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEd,KAAK,KAAI,EAAE,CAAC,CACnC;EAEHtB,SAAS,CAAC,MAAM;IACd,IAAIoC,IAAI,EAAE;MACRK,WAAW,CAACL,IAAI,CAACE,IAAI,CAAC;MACtBK,QAAQ,CAACP,IAAI,CAACM,KAAK,IAAI,EAAE,CAAC;MAC1BG,QAAQ,CAACT,IAAI,CAACQ,KAAK,IAAI,EAAE,CAAC;IAC5B;EACF,CAAC,EAAE,CAACR,IAAI,CAAC,CAAC;EAEV,MAAMmB,gBAAgB,GAAIC,CAAsC,IAAK;IACnEf,WAAW,CAACe,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAMC,iBAAiB,GAAIH,CAAsC,IAAK;IACpEb,QAAQ,CAACa,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC1B,CAAC;EAED,MAAME,iBAAiB,GAAIJ,CAAsC,IAAK;IACpEX,QAAQ,CAACW,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC1B,CAAC;EAED,MAAMG,uBAAuB,GAAG,CAC9BrC,IAAkB,EAClBgC,CAAsC,KACnC;IACHF,cAAc,CAACD,WAAW,CAAC3B,MAAM,CAACF,IAAI,EAAEgC,CAAC,CAACC,MAAM,CAACK,OAAO,CAAC,CAAC;EAC5D,CAAC;EAED,MAAMC,eAAe,GAAG,YAAY;IAClC,IAAI1B,WAAW,IAAID,IAAI,EAAE;MACvB,MAAMd,KAAK,GAAG+B,WAAW,CAACtB,QAAQ,EAAE;QAClCiC,KAAK,GAAG;UAAE,GAAG5B,IAAI;UAAEE,IAAI,EAAEE,QAAQ;UAAEE,KAAK;UAAEE,KAAK;UAAEtB;QAAM,CAAC;QACxD2C,QAAQ,GAAG,MAAM9C,OAAO,CAAC+C,UAAU,CAAC7B,WAAW,EAAE2B,KAAK,CAAC;MAEzD,IAAIC,QAAQ,EAAE;QACZ/B,QAAQ,CAAClB,MAAM,CAACuB,KAAK,CAAC4B,IAAI,CAAC;MAC7B;IACF;EACF,CAAC;EAED,MAAMC,sBAAsB,GAAG,MAAM;IACnCrB,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMsB,uBAAuB,GAAG,MAAM;IACpCtB,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAMuB,yBAAyB,GAAG,MAAM;IACtCrB,WAAW,CAAC,EAAE,CAAC;EACjB,CAAC;EAED,MAAMsB,oBAAoB,GAAIf,CAAsC,IAAK;IACvEP,WAAW,CAACO,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAMc,wBAAwB,GAAG,YAAY;IAC3C,IAAInC,WAAW,IAAID,IAAI,IAAIY,QAAQ,EAAE;MACnC,MAAMgB,KAAK,GAAG;UAAE,GAAG5B;QAAK,CAAC;QACvB6B,QAAQ,GAAG,MAAM9C,OAAO,CAACsD,cAAc,CAACpC,WAAW,EAAE2B,KAAK,EAAEhB,QAAQ,CAAC;MAEvE,IAAIiB,QAAQ,EAAE;QACZlB,oBAAoB,CAAC,KAAK,CAAC;QAC3Bb,QAAQ,CAAClB,MAAM,CAACuB,KAAK,CAAC4B,IAAI,CAAC;MAC7B;IACF;EACF,CAAC;EAED,IAAI,CAAC/B,IAAI,EAAE;IACT,oBAAO,QAAC,QAAQ;MAAC,EAAE,EAAEpB,MAAM,CAACuB,KAAK,CAAC4B;IAAK;MAAA;MAAA;MAAA;IAAA,QAAG;EAC5C;EAEA,oBACE;IAAK,SAAS,EAAC,wBAAwB;IAAA,wBACrC;MAAK,SAAS,EAAC,KAAK;MAAA,uBAClB;QAAK,SAAS,EAAC,KAAK;QAAA,uBAClB,QAAC,IAAI;UAAC,EAAE,EAAEnD,MAAM,CAACuB,KAAK,CAAC4B,IAAK;UAAA,wBAC1B,QAAC,eAAe;YAAC,IAAI,EAAE,CAAC,KAAK,EAAE,cAAc;UAAE;YAAA;YAAA;YAAA;UAAA,QAAG;QAAA;UAAA;UAAA;UAAA;QAAA;MAE7C;QAAA;QAAA;QAAA;MAAA;IACH;MAAA;MAAA;MAAA;IAAA,QACF,eACN;MAAA,oBAAU/B,IAAI,CAACE,IAAI;IAAA;MAAA;MAAA;MAAA;IAAA,QAAM,eACzB;MAAK,SAAS,EAAC,KAAK;MAAA,wBAClB;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,WAAW;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAAa,eACvC,QAAC,KAAK;UACJ,EAAE,EAAC,WAAW;UACd,KAAK,EAAEE,QAAS;UAChB,QAAQ,EAAEe,gBAAiB;UAC3B,QAAQ,EAAE,CAACH;QAAU;UAAA;UAAA;UAAA;QAAA,QACrB;MAAA;QAAA;QAAA;QAAA;MAAA,QACE,eACN;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,YAAY;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAAc,eACzC,QAAC,KAAK;UACJ,EAAE,EAAC,YAAY;UACf,IAAI,EAAC,OAAO;UACZ,KAAK,EAAEV,KAAM;UACb,QAAQ,EAAEiB,iBAAkB;UAC5B,QAAQ,EAAE,CAACP;QAAU;UAAA;UAAA;UAAA;QAAA,QACrB;MAAA;QAAA;QAAA;QAAA;MAAA,QACE,eACN;QAAK,SAAS,EAAC,iBAAiB;QAAA,wBAC9B;UAAO,OAAO,EAAC,YAAY;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAAe,eAC1C,QAAC,KAAK;UACJ,EAAE,EAAC,YAAY;UACf,KAAK,EAAER,KAAM;UACb,QAAQ,EAAEgB,iBAAkB;UAC5B,QAAQ,EAAE,CAACR;QAAU;UAAA;UAAA;UAAA;QAAA,QACrB;MAAA;QAAA;QAAA;QAAA;MAAA,QACE;IAAA;MAAA;MAAA;MAAA;IAAA,QACF,eACN;MAAK,SAAS,EAAC,UAAU;MAAA,wBACvB;QAAK,SAAS,EAAC,sBAAsB;QAAA,wBACnC;UAAI,SAAS,EAAC,eAAe;UAAA,wBAC3B,QAAC,eAAe;YAAC,IAAI,EAAE,CAAC,KAAK,EAAE,UAAU;UAAE;YAAA;YAAA;YAAA;UAAA,QAAG;QAAA;UAAA;UAAA;UAAA;QAAA,QAE3C,eACL,QAAC,SAAS;UAAA,wBACR,QAAC,KAAK;YACJ,IAAI,EAAC,UAAU;YACf,EAAE,EAAC,aAAa;YAChB,OAAO,EAAEC,WAAW,CAAC9B,GAAG,CAAC,aAAa,CAAE;YACxC,QAAQ,EAAGiC,CAAC,IAAKK,uBAAuB,CAAC,aAAa,EAAEL,CAAC;UAAE;YAAA;YAAA;YAAA;UAAA,QAC3D,uBAEF,QAAC,KAAK;YAAC,KAAK;YAAC,GAAG,EAAC,aAAa;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAEtB;QAAA;UAAA;UAAA;UAAA;QAAA,QACE,eACZ,QAAC,SAAS;UAAA,wBACR,QAAC,KAAK;YACJ,IAAI,EAAC,UAAU;YACf,EAAE,EAAC,eAAe;YAClB,OAAO,EAAEH,WAAW,CAAC9B,GAAG,CAAC,eAAe,CAAE;YAC1C,QAAQ,EAAGiC,CAAC,IAAKK,uBAAuB,CAAC,eAAe,EAAEL,CAAC;UAAE;YAAA;YAAA;YAAA;UAAA,QAC7D,uBAEF,QAAC,KAAK;YAAC,KAAK;YAAC,GAAG,EAAC,eAAe;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAExB;QAAA;UAAA;UAAA;UAAA;QAAA,QACE,eACZ,QAAC,SAAS;UAAA,wBACR,QAAC,KAAK;YACJ,IAAI,EAAC,UAAU;YACf,EAAE,EAAC,eAAe;YAClB,OAAO,EAAEH,WAAW,CAAC9B,GAAG,CAAC,eAAe,CAAE;YAC1C,QAAQ,EAAGiC,CAAC,IAAKK,uBAAuB,CAAC,eAAe,EAAEL,CAAC;UAAE;YAAA;YAAA;YAAA;UAAA,QAC7D,uBAEF,QAAC,KAAK;YAAC,KAAK;YAAC,GAAG,EAAC,eAAe;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAExB;QAAA;UAAA;UAAA;UAAA;QAAA,QACE,eACZ,QAAC,SAAS;UAAA,wBACR,QAAC,KAAK;YACJ,IAAI,EAAC,UAAU;YACf,EAAE,EAAC,eAAe;YAClB,OAAO,EAAEH,WAAW,CAAC9B,GAAG,CAAC,eAAe,CAAE;YAC1C,QAAQ,EAAGiC,CAAC,IAAKK,uBAAuB,CAAC,eAAe,EAAEL,CAAC;UAAE;YAAA;YAAA;YAAA;UAAA,QAC7D,uBAEF,QAAC,KAAK;YAAC,KAAK;YAAC,GAAG,EAAC,eAAe;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAExB;QAAA;UAAA;UAAA;UAAA;QAAA,QACE;MAAA;QAAA;QAAA;QAAA;MAAA,QACR,eACN;QAAK,SAAS,EAAC,sBAAsB;QAAA,wBACnC;UAAI,SAAS,EAAC,eAAe;UAAA,wBAC3B,QAAC,eAAe;YAAC,IAAI,EAAE,CAAC,KAAK,EAAE,UAAU;UAAE;YAAA;YAAA;YAAA;UAAA,QAAG;QAAA;UAAA;UAAA;UAAA;QAAA,QAE3C,eACL,QAAC,SAAS;UAAA,wBACR,QAAC,KAAK;YACJ,IAAI,EAAC,UAAU;YACf,EAAE,EAAC,gBAAgB;YACnB,OAAO,EAAEH,WAAW,CAAC9B,GAAG,CAAC,gBAAgB,CAAE;YAC3C,QAAQ,EAAGiC,CAAC,IAAKK,uBAAuB,CAAC,gBAAgB,EAAEL,CAAC;UAAE;YAAA;YAAA;YAAA;UAAA,QAC9D,uBAEF,QAAC,KAAK;YAAC,KAAK;YAAC,GAAG,EAAC,gBAAgB;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAEzB;QAAA;UAAA;UAAA;UAAA;QAAA,QACE,eACZ,QAAC,SAAS;UAAA,wBACR,QAAC,KAAK;YACJ,IAAI,EAAC,UAAU;YACf,EAAE,EAAC,kBAAkB;YACrB,OAAO,EAAEH,WAAW,CAAC9B,GAAG,CAAC,kBAAkB,CAAE;YAC7C,QAAQ,EAAGiC,CAAC,IAAKK,uBAAuB,CAAC,kBAAkB,EAAEL,CAAC;UAAE;YAAA;YAAA;YAAA;UAAA,QAChE,uBAEF,QAAC,KAAK;YAAC,KAAK;YAAC,GAAG,EAAC,kBAAkB;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAE3B;QAAA;UAAA;UAAA;UAAA;QAAA,QACE,eACZ,QAAC,SAAS;UAAA,wBACR,QAAC,KAAK;YACJ,IAAI,EAAC,UAAU;YACf,EAAE,EAAC,kBAAkB;YACrB,OAAO,EAAEH,WAAW,CAAC9B,GAAG,CAAC,kBAAkB,CAAE;YAC7C,QAAQ,EAAGiC,CAAC,IAAKK,uBAAuB,CAAC,kBAAkB,EAAEL,CAAC;UAAE;YAAA;YAAA;YAAA;UAAA,QAChE,uBAEF,QAAC,KAAK;YAAC,KAAK;YAAC,GAAG,EAAC,kBAAkB;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAE3B;QAAA;UAAA;UAAA;UAAA;QAAA,QACE,eACZ,QAAC,SAAS;UAAA,wBACR,QAAC,KAAK;YACJ,IAAI,EAAC,UAAU;YACf,EAAE,EAAC,kBAAkB;YACrB,OAAO,EAAEH,WAAW,CAAC9B,GAAG,CAAC,kBAAkB,CAAE;YAC7C,QAAQ,EAAGiC,CAAC,IAAKK,uBAAuB,CAAC,kBAAkB,EAAEL,CAAC;UAAE;YAAA;YAAA;YAAA;UAAA,QAChE,uBAEF,QAAC,KAAK;YAAC,KAAK;YAAC,GAAG,EAAC,kBAAkB;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAE3B;QAAA;UAAA;UAAA;UAAA;QAAA,QACE;MAAA;QAAA;QAAA;QAAA;MAAA,QACR,eACN;QAAK,SAAS,EAAC,sBAAsB;QAAA,wBACnC;UAAI,SAAS,EAAC,eAAe;UAAA,wBAC3B,QAAC,eAAe;YAAC,IAAI,EAAE,CAAC,KAAK,EAAE,kBAAkB;UAAE;YAAA;YAAA;YAAA;UAAA,QAAG;QAAA;UAAA;UAAA;UAAA;QAAA,QAEnD,eACL,QAAC,SAAS;UAAA,wBACR,QAAC,KAAK;YACJ,IAAI,EAAC,UAAU;YACf,EAAE,EAAC,YAAY;YACf,OAAO,EAAEH,WAAW,CAAC9B,GAAG,CAAC,YAAY,CAAE;YACvC,QAAQ,EAAGiC,CAAC,IAAKK,uBAAuB,CAAC,YAAY,EAAEL,CAAC;UAAE;YAAA;YAAA;YAAA;UAAA,QAC1D,uBAEF,QAAC,KAAK;YAAC,KAAK;YAAC,GAAG,EAAC,YAAY;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAErB;QAAA;UAAA;UAAA;UAAA;QAAA,QACE,eACZ,QAAC,SAAS;UAAA,wBACR,QAAC,KAAK;YACJ,IAAI,EAAC,UAAU;YACf,EAAE,EAAC,cAAc;YACjB,OAAO,EAAEH,WAAW,CAAC9B,GAAG,CAAC,cAAc,CAAE;YACzC,QAAQ,EAAGiC,CAAC,IAAKK,uBAAuB,CAAC,cAAc,EAAEL,CAAC;UAAE;YAAA;YAAA;YAAA;UAAA,QAC5D,uBAEF,QAAC,KAAK;YAAC,KAAK;YAAC,GAAG,EAAC,cAAc;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAEvB;QAAA;UAAA;UAAA;UAAA;QAAA,QACE,eACZ,QAAC,SAAS;UAAA,wBACR,QAAC,KAAK;YACJ,IAAI,EAAC,UAAU;YACf,EAAE,EAAC,cAAc;YACjB,OAAO,EAAEH,WAAW,CAAC9B,GAAG,CAAC,cAAc,CAAE;YACzC,QAAQ,EAAGiC,CAAC,IAAKK,uBAAuB,CAAC,cAAc,EAAEL,CAAC;UAAE;YAAA;YAAA;YAAA;UAAA,QAC5D,uBAEF,QAAC,KAAK;YAAC,KAAK;YAAC,GAAG,EAAC,cAAc;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAEvB;QAAA;UAAA;UAAA;UAAA;QAAA,QACE,eACZ,QAAC,SAAS;UAAA,wBACR,QAAC,KAAK;YACJ,IAAI,EAAC,UAAU;YACf,EAAE,EAAC,cAAc;YACjB,OAAO,EAAEH,WAAW,CAAC9B,GAAG,CAAC,cAAc,CAAE;YACzC,QAAQ,EAAGiC,CAAC,IAAKK,uBAAuB,CAAC,cAAc,EAAEL,CAAC;UAAE;YAAA;YAAA;YAAA;UAAA,QAC5D,uBAEF,QAAC,KAAK;YAAC,KAAK;YAAC,GAAG,EAAC,cAAc;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAEvB;QAAA;UAAA;UAAA;UAAA;QAAA,QACE;MAAA;QAAA;QAAA;QAAA;MAAA,QACR,eACN;QAAK,SAAS,EAAC,sBAAsB;QAAA,wBACnC;UAAI,SAAS,EAAC,eAAe;UAAA,wBAC3B,QAAC,eAAe;YAAC,IAAI,EAAE,CAAC,KAAK,EAAE,cAAc;UAAE;YAAA;YAAA;YAAA;UAAA,QAAG;QAAA;UAAA;UAAA;UAAA;QAAA,QAE/C,eACL,QAAC,SAAS;UAAA,wBACR,QAAC,KAAK;YACJ,IAAI,EAAC,UAAU;YACf,EAAE,EAAC,aAAa;YAChB,OAAO,EAAEH,WAAW,CAAC9B,GAAG,CAAC,aAAa,CAAE;YACxC,QAAQ,EAAGiC,CAAC,IAAKK,uBAAuB,CAAC,aAAa,EAAEL,CAAC;UAAE;YAAA;YAAA;YAAA;UAAA,QAC3D,uBAEF,QAAC,KAAK;YAAC,KAAK;YAAC,GAAG,EAAC,aAAa;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAEtB;QAAA;UAAA;UAAA;UAAA;QAAA,QACE,eACZ,QAAC,SAAS;UAAA,wBACR,QAAC,KAAK;YACJ,IAAI,EAAC,UAAU;YACf,EAAE,EAAC,eAAe;YAClB,OAAO,EAAEH,WAAW,CAAC9B,GAAG,CAAC,eAAe,CAAE;YAC1C,QAAQ,EAAGiC,CAAC,IAAKK,uBAAuB,CAAC,eAAe,EAAEL,CAAC;UAAE;YAAA;YAAA;YAAA;UAAA,QAC7D,uBAEF,QAAC,KAAK;YAAC,KAAK;YAAC,GAAG,EAAC,eAAe;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAExB;QAAA;UAAA;UAAA;UAAA;QAAA,QACE,eACZ,QAAC,SAAS;UAAA,wBACR,QAAC,KAAK;YACJ,IAAI,EAAC,UAAU;YACf,EAAE,EAAC,eAAe;YAClB,OAAO,EAAEH,WAAW,CAAC9B,GAAG,CAAC,eAAe,CAAE;YAC1C,QAAQ,EAAGiC,CAAC,IAAKK,uBAAuB,CAAC,eAAe,EAAEL,CAAC;UAAE;YAAA;YAAA;YAAA;UAAA,QAC7D,uBAEF,QAAC,KAAK;YAAC,KAAK;YAAC,GAAG,EAAC,eAAe;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAExB;QAAA;UAAA;UAAA;UAAA;QAAA,QACE,eACZ,QAAC,SAAS;UAAA,wBACR,QAAC,KAAK;YACJ,IAAI,EAAC,UAAU;YACf,EAAE,EAAC,eAAe;YAClB,OAAO,EAAEH,WAAW,CAAC9B,GAAG,CAAC,eAAe,CAAE;YAC1C,QAAQ,EAAGiC,CAAC,IAAKK,uBAAuB,CAAC,eAAe,EAAEL,CAAC;UAAE;YAAA;YAAA;YAAA;UAAA,QAC7D,uBAEF,QAAC,KAAK;YAAC,KAAK;YAAC,GAAG,EAAC,eAAe;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAExB;QAAA;UAAA;UAAA;UAAA;QAAA,QACE,eACZ,QAAC,SAAS;UAAA,wBACR,QAAC,KAAK;YACJ,IAAI,EAAC,UAAU;YACf,EAAE,EAAC,eAAe;YAClB,OAAO,EAAEH,WAAW,CAAC9B,GAAG,CAAC,eAAe,CAAE;YAC1C,QAAQ,EAAGiC,CAAC,IAAKK,uBAAuB,CAAC,eAAe,EAAEL,CAAC;UAAE;YAAA;YAAA;YAAA;UAAA,QAC7D,uBAEF,QAAC,KAAK;YAAC,KAAK;YAAC,GAAG,EAAC,eAAe;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAExB;QAAA;UAAA;UAAA;UAAA;QAAA,QACE;MAAA;QAAA;QAAA;QAAA;MAAA,QACR,eACN;QAAK,SAAS,EAAC,sBAAsB;QAAA,wBACnC;UAAI,SAAS,EAAC,eAAe;UAAA,wBAC3B,QAAC,eAAe;YAAC,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM;UAAE;YAAA;YAAA;YAAA;UAAA,QAAG;QAAA;UAAA;UAAA;UAAA;QAAA,QAEvC,eACL,QAAC,SAAS;UAAA,wBACR,QAAC,KAAK;YACJ,IAAI,EAAC,UAAU;YACf,EAAE,EAAC,kBAAkB;YACrB,OAAO,EAAEH,WAAW,CAAC9B,GAAG,CAAC,aAAa,CAAE;YACxC,QAAQ,EAAGiC,CAAC,IAAKK,uBAAuB,CAAC,aAAa,EAAEL,CAAC;UAAE;YAAA;YAAA;YAAA;UAAA,QAC3D,uBAEF,QAAC,KAAK;YAAC,KAAK;YAAC,GAAG,EAAC,kBAAkB;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAE3B;QAAA;UAAA;UAAA;UAAA;QAAA,QACE;MAAA;QAAA;QAAA;QAAA;MAAA,QACR,eACN;QAAK,SAAS,EAAC,sBAAsB;QAAA,wBACnC;UAAI,SAAS,EAAC,eAAe;UAAA,wBAC3B,QAAC,eAAe;YAAC,IAAI,EAAE,CAAC,KAAK,EAAE,YAAY;UAAE;YAAA;YAAA;YAAA;UAAA,QAAG;QAAA;UAAA;UAAA;UAAA;QAAA,QAE7C,eACL,QAAC,SAAS;UAAA,wBACR,QAAC,KAAK;YACJ,IAAI,EAAC,UAAU;YACf,EAAE,EAAC,WAAW;YACd,OAAO,EAAEH,WAAW,CAAC9B,GAAG,CAAC,QAAQ,CAAE;YACnC,QAAQ,EAAGiC,CAAC,IAAKK,uBAAuB,CAAC,QAAQ,EAAEL,CAAC;UAAE;YAAA;YAAA;YAAA;UAAA,QACtD,uBAEF,QAAC,KAAK;YAAC,KAAK;YAAC,GAAG,EAAC,WAAW;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAEpB;QAAA;UAAA;UAAA;UAAA;QAAA,QACE,eACZ,QAAC,SAAS;UAAA,wBACR,QAAC,KAAK;YACJ,IAAI,EAAC,UAAU;YACf,EAAE,EAAC,mBAAmB;YACtB,OAAO,EAAEH,WAAW,CAAC9B,GAAG,CAAC,mBAAmB,CAAE;YAC9C,QAAQ,EAAGiC,CAAC,IAAKK,uBAAuB,CAAC,mBAAmB,EAAEL,CAAC;UAAE;YAAA;YAAA;YAAA;UAAA,QACjE,uBAEF,QAAC,KAAK;YAAC,KAAK;YAAC,GAAG,EAAC,mBAAmB;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAE5B;QAAA;UAAA;UAAA;UAAA;QAAA,QACE,eACZ,QAAC,SAAS;UAAA,wBACR,QAAC,KAAK;YACJ,IAAI,EAAC,UAAU;YACf,EAAE,EAAC,qBAAqB;YACxB,OAAO,EAAEH,WAAW,CAAC9B,GAAG,CAAC,qBAAqB,CAAE;YAChD,QAAQ,EAAGiC,CAAC,IACVK,uBAAuB,CAAC,qBAAqB,EAAEL,CAAC;UACjD;YAAA;YAAA;YAAA;UAAA,QACD,uBAEF,QAAC,KAAK;YAAC,KAAK;YAAC,GAAG,EAAC,qBAAqB;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAE9B;QAAA;UAAA;UAAA;UAAA;QAAA,QACE;MAAA;QAAA;QAAA;QAAA;MAAA,QACR;IAAA;MAAA;MAAA;MAAA;IAAA,QACF,eACN;MAAK,SAAS,EAAC,4CAA4C;MAAA,WACxDJ,SAAS,iBACR;QAAK,SAAS,EAAC,kBAAkB;QAAA,uBAC/B,QAAC,MAAM;UAAC,OAAO,EAAEgB,sBAAuB;UAAA,wBACtC,QAAC,eAAe;YAAC,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK;UAAE;YAAA;YAAA;YAAA;UAAA,QAAG;QAAA;UAAA;UAAA;UAAA;QAAA;MAElC;QAAA;QAAA;QAAA;MAAA,QAEZ,eACD;QAAK,SAAS,EAAC,cAAc;QAAA,wBAC3B,QAAC,MAAM;UAAC,GAAG,EAAElD,IAAK;UAAC,EAAE,EAAEF,MAAM,CAACuB,KAAK,CAAC4B,IAAK;UAAC,OAAO;UAAC,IAAI,EAAC,IAAI;UAAA,UACxDf,SAAS,GAAG,QAAQ,GAAG;QAAO;UAAA;UAAA;UAAA;QAAA,QACxB,EACRA,SAAS,iBACR;UAAA,gCAEE,QAAC,MAAM;YAAC,OAAO,EAAEW,eAAgB;YAAC,KAAK,EAAC,SAAS;YAAC,IAAI,EAAC,IAAI;YAAA,wBACzD,QAAC,eAAe;cAAC,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM;YAAE;cAAA;cAAA;cAAA;YAAA,QAAG;UAAA;YAAA;YAAA;YAAA;UAAA,QAEnC;QAAA,gBAEZ;MAAA;QAAA;QAAA;QAAA;MAAA,QACG;IAAA;MAAA;MAAA;MAAA;IAAA,QACF,eACN,QAAC,KAAK;MACJ,MAAM,EAAEjB,iBAAkB;MAC1B,MAAM,EAAEuB,uBAAwB;MAChC,QAAQ,EAAEC,yBAA0B;MACpC,SAAS,EAAE,KAAM;MAAA,wBACjB,QAAC,WAAW;QAAC,MAAM,EAAED,uBAAwB;QAAA;MAAA;QAAA;QAAA;QAAA;MAAA,QAA2B,eACxE,QAAC,SAAS;QAAA,uBACR,QAAC,SAAS;UAAA,wBACR;YAAO,OAAO,EAAC,UAAU;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAqB,eAC9C,QAAC,KAAK;YACJ,EAAE,EAAC,UAAU;YACb,MAAM,EAAC,IAAI;YACX,KAAK,EAAErB,QAAS;YAChB,QAAQ,EAAEuB,oBAAqB;YAC/B,SAAS;UAAA;YAAA;YAAA;YAAA;UAAA,QACT;QAAA;UAAA;UAAA;UAAA;QAAA;MACQ;QAAA;QAAA;QAAA;MAAA,QACF,eACZ,QAAC,WAAW;QAAA,wBACV,QAAC,MAAM;UAAC,OAAO;UAAC,OAAO,EAAEF,uBAAwB;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAExC,eACT,QAAC,MAAM;UAAC,KAAK,EAAC,SAAS;UAAC,OAAO;UAAC,OAAO,EAAEG,wBAAyB;UAAA,wBAChE,QAAC,eAAe;YAAC,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK;UAAE;YAAA;YAAA;YAAA;UAAA,QAAG;QAAA;UAAA;UAAA;UAAA;QAAA,QAElC;MAAA;QAAA;QAAA;QAAA;MAAA,QACG;IAAA;MAAA;MAAA;MAAA;IAAA,QACR;EAAA;IAAA;IAAA;IAAA;EAAA,QACJ;AAEV;AAAC,GA1dQvC,MAAM;EAAA,QACI7B,WAAW,EACQa,OAAO,EAC9Bd,SAAS,EACZD,WAAW;AAAA;AAAA,KAJd+B,MAAM;AA4df,eAAeA,MAAM;AAAC;AAAA"}, "metadata": {}, "sourceType": "module"}