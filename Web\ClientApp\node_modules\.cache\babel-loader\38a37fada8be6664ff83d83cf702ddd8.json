{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\GreenRP\\\\Web\\\\ClientApp\\\\src\\\\features\\\\driver-tasks\\\\List.tsx\",\n  _s = $RefreshSig$();\nimport { useCallback, useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { Link } from 'react-router-dom';\nimport { Button } from 'reactstrap';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { events, EventTypes } from 'app/events';\nimport { routes } from 'app/routes';\nimport { useAuth } from 'features/auth/use-auth';\nimport { Error } from 'features/error/Error';\nimport { Loading } from 'features/loading/Loading';\nimport { getDriverTasks, selectError, selectIsLoading, selectTaskDates, clearError, getDrivers } from './driver-task-slice';\nimport { ListDate } from './List-Date';\nimport { ListFilters } from './ListFilters';\nimport { classNames } from 'utils/class-names';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport function List() {\n  _s();\n  const dispatch = useDispatch(),\n    {\n      isInRole,\n      user\n    } = useAuth(),\n    [showFilterMobile, setShowFilterMobile] = useState(false),\n    error = useSelector(selectError),\n    isLoading = useSelector(selectIsLoading),\n    taskDates = useSelector(selectTaskDates),\n    canCreateDriverTasks = isInRole('create:driver-tasks');\n  const refreshTasks = useCallback(() => {\n    dispatch(getDriverTasks());\n  }, [dispatch]);\n  useEffect(() => {\n    if (user) {\n      dispatch(getDrivers(user));\n    }\n  }, [dispatch, user]);\n  useEffect(() => {\n    events.on(EventTypes.driverTasksUpdated, refreshTasks);\n    refreshTasks();\n    return function cleanup() {\n      events.off(EventTypes.driverTasksUpdated);\n    };\n  }, [refreshTasks]);\n  const handleClearErrorClick = () => {\n    dispatch(clearError());\n  };\n  const handleToggleFilter = () => {\n    setShowFilterMobile(!showFilterMobile);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container-fluid\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white sticky-top-navbar mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto row mt-2 py-2 border-bottom shadow\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row my-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"col\",\n            children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: ['fat', 'truck-fast']\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this), \"\\xA0 Driver Tasks\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this), canCreateDriverTasks && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-auto\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              tag: Link,\n              to: routes.driverTasks.new.path,\n              outline: true,\n              color: \"success\",\n              className: \"d-block d-md-inline-block\",\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: ['fat', 'plus']\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"d-none d-md-inline\",\n                children: \"\\xA0 New Driver Task\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row d-none d-md-flex\",\n          children: /*#__PURE__*/_jsxDEV(ListFilters, {\n            show: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row d-md-none\",\n          children: /*#__PURE__*/_jsxDEV(ListFilters, {\n            show: showFilterMobile\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: classNames('row d-md-none', showFilterMobile ? 'mt-4' : 'mt-2'),\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              color: \"secondary\",\n              size: \"sm\",\n              outline: true,\n              block: true,\n              onClick: handleToggleFilter,\n              tabIndex: -1,\n              children: [`${showFilterMobile ? 'Hide' : 'Show'} filters`, \"\\xA0\", /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: ['fat', showFilterMobile ? 'chevron-up' : 'chevron-down']\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this), !!error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container row mx-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col\",\n        children: /*#__PURE__*/_jsxDEV(Error, {\n          error: error,\n          clearError: handleClearErrorClick\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 9\n    }, this), !!isLoading && /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 23\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row\",\n        children: taskDates.map(date => /*#__PURE__*/_jsxDEV(ListDate, {\n          date: date\n        }, date, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 5\n  }, this);\n}\n_s(List, \"G4SxdhoqZULKidmF43UBlWyqlfc=\", false, function () {\n  return [useDispatch, useAuth, useSelector, useSelector, useSelector];\n});\n_c = List;\nexport default List;\nvar _c;\n$RefreshReg$(_c, \"List\");", "map": {"version": 3, "names": ["useCallback", "useEffect", "useState", "useDispatch", "useSelector", "Link", "<PERSON><PERSON>", "FontAwesomeIcon", "events", "EventTypes", "routes", "useAuth", "Error", "Loading", "getDriverTasks", "selectError", "selectIsLoading", "selectTaskDates", "clearError", "getDrivers", "ListDate", "ListFilters", "classNames", "List", "dispatch", "isInRole", "user", "showFilterMobile", "setShowFilterMobile", "error", "isLoading", "taskDates", "canCreateDriverTasks", "refreshTasks", "on", "driverTasksUpdated", "cleanup", "off", "handleClearErrorClick", "handleToggleFilter", "driverTasks", "new", "path", "map", "date"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/driver-tasks/List.tsx"], "sourcesContent": ["import { useCallback, useEffect, useState } from 'react';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { Link } from 'react-router-dom';\r\nimport { Button } from 'reactstrap';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { events, EventTypes } from 'app/events';\r\nimport { routes } from 'app/routes';\r\nimport { useAuth } from 'features/auth/use-auth';\r\nimport { Error } from 'features/error/Error';\r\nimport { Loading } from 'features/loading/Loading';\r\nimport {\r\n  getDriverTasks,\r\n  selectError,\r\n  selectIsLoading,\r\n  selectTaskDates,\r\n  clearError,\r\n  getDrivers,\r\n} from './driver-task-slice';\r\nimport { ListDate } from './List-Date';\r\nimport { ListFilters } from './ListFilters';\r\nimport { classNames } from 'utils/class-names';\r\n\r\nexport function List() {\r\n  const dispatch = useDispatch(),\r\n    { isInRole, user } = useAuth(),\r\n    [showFilterMobile, setShowFilterMobile] = useState(false),\r\n    error = useSelector(selectError),\r\n    isLoading = useSelector(selectIsLoading),\r\n    taskDates = useSelector(selectTaskDates),\r\n    canCreateDriverTasks = isInRole('create:driver-tasks');\r\n\r\n  const refreshTasks = useCallback(() => {\r\n    dispatch(getDriverTasks());\r\n  }, [dispatch]);\r\n\r\n  useEffect(() => {\r\n    if (user) {\r\n      dispatch(getDrivers(user));\r\n    }\r\n  }, [dispatch, user]);\r\n\r\n  useEffect(() => {\r\n    events.on(EventTypes.driverTasksUpdated, refreshTasks);\r\n\r\n    refreshTasks();\r\n\r\n    return function cleanup() {\r\n      events.off(EventTypes.driverTasksUpdated);\r\n    };\r\n  }, [refreshTasks]);\r\n\r\n  const handleClearErrorClick = () => {\r\n    dispatch(clearError());\r\n  };\r\n\r\n  const handleToggleFilter = () => {\r\n    setShowFilterMobile(!showFilterMobile);\r\n  };\r\n\r\n  return (\r\n    <div className=\"container-fluid\">\r\n      <div className=\"bg-white sticky-top-navbar mb-4\">\r\n        <div className=\"container mx-auto row mt-2 py-2 border-bottom shadow\">\r\n          <div className=\"row my-2\">\r\n            <h1 className=\"col\">\r\n              <FontAwesomeIcon icon={['fat', 'truck-fast']} />\r\n              &nbsp; Driver Tasks\r\n            </h1>\r\n            {canCreateDriverTasks && (\r\n              <div className=\"col-auto\">\r\n                <Button\r\n                  tag={Link}\r\n                  to={routes.driverTasks.new.path}\r\n                  outline\r\n                  color=\"success\"\r\n                  className=\"d-block d-md-inline-block\">\r\n                  <FontAwesomeIcon icon={['fat', 'plus']} />\r\n                  <span className=\"d-none d-md-inline\">\r\n                    &nbsp; New Driver Task\r\n                  </span>\r\n                </Button>\r\n              </div>\r\n            )}\r\n          </div>\r\n          <div className=\"row d-none d-md-flex\">\r\n            <ListFilters show={true} />\r\n          </div>\r\n          <div className=\"row d-md-none\">\r\n            <ListFilters show={showFilterMobile} />\r\n          </div>\r\n          <div\r\n            className={classNames(\r\n              'row d-md-none',\r\n              showFilterMobile ? 'mt-4' : 'mt-2'\r\n            )}>\r\n            <div className=\"col-12\">\r\n              <Button\r\n                color=\"secondary\"\r\n                size=\"sm\"\r\n                outline\r\n                block\r\n                onClick={handleToggleFilter}\r\n                tabIndex={-1}>\r\n                {`${showFilterMobile ? 'Hide' : 'Show'} filters`}\r\n                &nbsp;\r\n                <FontAwesomeIcon\r\n                  icon={[\r\n                    'fat',\r\n                    showFilterMobile ? 'chevron-up' : 'chevron-down',\r\n                  ]}\r\n                />\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      {!!error && (\r\n        <div className=\"container row mx-auto\">\r\n          <div className=\"col\">\r\n            <Error error={error} clearError={handleClearErrorClick} />\r\n          </div>\r\n        </div>\r\n      )}\r\n      {!!isLoading && <Loading />}\r\n      <div className=\"container mx-auto mb-4\">\r\n        <div className=\"row\">\r\n          {taskDates.map((date) => (\r\n            <ListDate key={date} date={date} />\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default List;\r\n"], "mappings": ";;AAAA,SAASA,WAAW,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AACxD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,MAAM,EAAEC,UAAU,QAAQ,YAAY;AAC/C,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,KAAK,QAAQ,sBAAsB;AAC5C,SAASC,OAAO,QAAQ,0BAA0B;AAClD,SACEC,cAAc,EACdC,WAAW,EACXC,eAAe,EACfC,eAAe,EACfC,UAAU,EACVC,UAAU,QACL,qBAAqB;AAC5B,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,UAAU,QAAQ,mBAAmB;AAAC;AAE/C,OAAO,SAASC,IAAI,GAAG;EAAA;EACrB,MAAMC,QAAQ,GAAGrB,WAAW,EAAE;IAC5B;MAAEsB,QAAQ;MAAEC;IAAK,CAAC,GAAGf,OAAO,EAAE;IAC9B,CAACgB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;IACzD2B,KAAK,GAAGzB,WAAW,CAACW,WAAW,CAAC;IAChCe,SAAS,GAAG1B,WAAW,CAACY,eAAe,CAAC;IACxCe,SAAS,GAAG3B,WAAW,CAACa,eAAe,CAAC;IACxCe,oBAAoB,GAAGP,QAAQ,CAAC,qBAAqB,CAAC;EAExD,MAAMQ,YAAY,GAAGjC,WAAW,CAAC,MAAM;IACrCwB,QAAQ,CAACV,cAAc,EAAE,CAAC;EAC5B,CAAC,EAAE,CAACU,QAAQ,CAAC,CAAC;EAEdvB,SAAS,CAAC,MAAM;IACd,IAAIyB,IAAI,EAAE;MACRF,QAAQ,CAACL,UAAU,CAACO,IAAI,CAAC,CAAC;IAC5B;EACF,CAAC,EAAE,CAACF,QAAQ,EAAEE,IAAI,CAAC,CAAC;EAEpBzB,SAAS,CAAC,MAAM;IACdO,MAAM,CAAC0B,EAAE,CAACzB,UAAU,CAAC0B,kBAAkB,EAAEF,YAAY,CAAC;IAEtDA,YAAY,EAAE;IAEd,OAAO,SAASG,OAAO,GAAG;MACxB5B,MAAM,CAAC6B,GAAG,CAAC5B,UAAU,CAAC0B,kBAAkB,CAAC;IAC3C,CAAC;EACH,CAAC,EAAE,CAACF,YAAY,CAAC,CAAC;EAElB,MAAMK,qBAAqB,GAAG,MAAM;IAClCd,QAAQ,CAACN,UAAU,EAAE,CAAC;EACxB,CAAC;EAED,MAAMqB,kBAAkB,GAAG,MAAM;IAC/BX,mBAAmB,CAAC,CAACD,gBAAgB,CAAC;EACxC,CAAC;EAED,oBACE;IAAK,SAAS,EAAC,iBAAiB;IAAA,wBAC9B;MAAK,SAAS,EAAC,iCAAiC;MAAA,uBAC9C;QAAK,SAAS,EAAC,sDAAsD;QAAA,wBACnE;UAAK,SAAS,EAAC,UAAU;UAAA,wBACvB;YAAI,SAAS,EAAC,KAAK;YAAA,wBACjB,QAAC,eAAe;cAAC,IAAI,EAAE,CAAC,KAAK,EAAE,YAAY;YAAE;cAAA;cAAA;cAAA;YAAA,QAAG;UAAA;YAAA;YAAA;YAAA;UAAA,QAE7C,EACJK,oBAAoB,iBACnB;YAAK,SAAS,EAAC,UAAU;YAAA,uBACvB,QAAC,MAAM;cACL,GAAG,EAAE3B,IAAK;cACV,EAAE,EAAEK,MAAM,CAAC8B,WAAW,CAACC,GAAG,CAACC,IAAK;cAChC,OAAO;cACP,KAAK,EAAC,SAAS;cACf,SAAS,EAAC,2BAA2B;cAAA,wBACrC,QAAC,eAAe;gBAAC,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM;cAAE;gBAAA;gBAAA;gBAAA;cAAA,QAAG,eAC1C;gBAAM,SAAS,EAAC,oBAAoB;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA,QAE7B;YAAA;cAAA;cAAA;cAAA;YAAA;UACA;YAAA;YAAA;YAAA;UAAA,QAEZ;QAAA;UAAA;UAAA;UAAA;QAAA,QACG,eACN;UAAK,SAAS,EAAC,sBAAsB;UAAA,uBACnC,QAAC,WAAW;YAAC,IAAI,EAAE;UAAK;YAAA;YAAA;YAAA;UAAA;QAAG;UAAA;UAAA;UAAA;QAAA,QACvB,eACN;UAAK,SAAS,EAAC,eAAe;UAAA,uBAC5B,QAAC,WAAW;YAAC,IAAI,EAAEf;UAAiB;YAAA;YAAA;YAAA;UAAA;QAAG;UAAA;UAAA;UAAA;QAAA,QACnC,eACN;UACE,SAAS,EAAEL,UAAU,CACnB,eAAe,EACfK,gBAAgB,GAAG,MAAM,GAAG,MAAM,CAClC;UAAA,uBACF;YAAK,SAAS,EAAC,QAAQ;YAAA,uBACrB,QAAC,MAAM;cACL,KAAK,EAAC,WAAW;cACjB,IAAI,EAAC,IAAI;cACT,OAAO;cACP,KAAK;cACL,OAAO,EAAEY,kBAAmB;cAC5B,QAAQ,EAAE,CAAC,CAAE;cAAA,WACX,GAAEZ,gBAAgB,GAAG,MAAM,GAAG,MAAO,UAAS,uBAEhD,QAAC,eAAe;gBACd,IAAI,EAAE,CACJ,KAAK,EACLA,gBAAgB,GAAG,YAAY,GAAG,cAAc;cAChD;gBAAA;gBAAA;gBAAA;cAAA,QACF;YAAA;cAAA;cAAA;cAAA;YAAA;UACK;YAAA;YAAA;YAAA;UAAA;QACL;UAAA;UAAA;UAAA;QAAA,QACF;MAAA;QAAA;QAAA;QAAA;MAAA;IACF;MAAA;MAAA;MAAA;IAAA,QACF,EACL,CAAC,CAACE,KAAK,iBACN;MAAK,SAAS,EAAC,uBAAuB;MAAA,uBACpC;QAAK,SAAS,EAAC,KAAK;QAAA,uBAClB,QAAC,KAAK;UAAC,KAAK,EAAEA,KAAM;UAAC,UAAU,EAAES;QAAsB;UAAA;UAAA;UAAA;QAAA;MAAG;QAAA;QAAA;QAAA;MAAA;IACtD;MAAA;MAAA;MAAA;IAAA,QAET,EACA,CAAC,CAACR,SAAS,iBAAI,QAAC,OAAO;MAAA;MAAA;MAAA;IAAA,QAAG,eAC3B;MAAK,SAAS,EAAC,wBAAwB;MAAA,uBACrC;QAAK,SAAS,EAAC,KAAK;QAAA,UACjBC,SAAS,CAACY,GAAG,CAAEC,IAAI,iBAClB,QAAC,QAAQ;UAAY,IAAI,EAAEA;QAAK,GAAjBA,IAAI;UAAA;UAAA;UAAA;QAAA,QACpB;MAAC;QAAA;QAAA;QAAA;MAAA;IACE;MAAA;MAAA;MAAA;IAAA,QACF;EAAA;IAAA;IAAA;IAAA;EAAA,QACF;AAEV;AAAC,GA/GerB,IAAI;EAAA,QACDpB,WAAW,EACLQ,OAAO,EAEpBP,WAAW,EACPA,WAAW,EACXA,WAAW;AAAA;AAAA,KANXmB,IAAI;AAiHpB,eAAeA,IAAI;AAAC;AAAA"}, "metadata": {}, "sourceType": "module"}