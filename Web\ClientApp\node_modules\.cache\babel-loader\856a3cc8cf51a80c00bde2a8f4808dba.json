{"ast": null, "code": "import { createAction, createAsyncThunk, createSlice } from '@reduxjs/toolkit';\nimport { createColour } from 'api/models/colour';\nimport { colourApi } from 'api/colour-service';\nconst initialState = {\n  isLoading: false,\n  colour: createColour(),\n  error: null\n};\nexport const saveColour = createAsyncThunk('colour-detail/save-colour', async (_, _ref) => {\n  let {\n    rejectWithValue,\n    getState\n  } = _ref;\n  try {\n    const colour = getState().colourDetail.colour,\n      doc = {\n        ...colour\n      };\n    const updated = await colourApi.save(doc);\n    return updated;\n  } catch (e) {\n    return rejectWithValue(e);\n  }\n});\nexport const deleteColour = createAsyncThunk('colour-detail/delete-colour', async (_, _ref2) => {\n  let {\n    rejectWithValue,\n    getState\n  } = _ref2;\n  try {\n    const colour = getState().colourDetail.colour,\n      doc = {\n        ...colour\n      };\n    const updated = await colourApi.delete(doc);\n    return updated;\n  } catch (e) {\n    return rejectWithValue(e);\n  }\n});\nconst savePending = createAction(saveColour.pending.type),\n  saveFulfilled = createAction(saveColour.fulfilled.type),\n  saveRejected = createAction(saveColour.rejected.type),\n  deletePending = createAction(deleteColour.pending.type),\n  deleteFulfilled = createAction(deleteColour.fulfilled.type),\n  deleteRejected = createAction(deleteColour.rejected.type);\nexport const colourDetailSlice = createSlice({\n  name: 'colour-detail',\n  initialState,\n  reducers: {\n    setColour(state, action) {\n      state.colour = action.payload;\n    }\n  },\n  extraReducers: builder => builder.addCase(savePending, state => {\n    state.isLoading = true;\n    state.error = null;\n  }).addCase(saveFulfilled, (state, action) => {\n    state.isLoading = false;\n    if (action.payload) {\n      state.colour = action.payload;\n    }\n  }).addCase(saveRejected, (state, action) => {\n    state.isLoading = false;\n    state.error = action.payload;\n  }).addCase(deletePending, state => {\n    state.isLoading = true;\n    state.error = null;\n  }).addCase(deleteFulfilled, (state, action) => {\n    state.isLoading = false;\n    state.colour = createColour();\n  }).addCase(deleteRejected, (state, action) => {\n    state.isLoading = false;\n    state.error = action.payload;\n  })\n});\nexport const {\n  setColour\n} = colourDetailSlice.actions;\nexport const selectColour = state => state.colourDetail.colour;\nexport const selectIsLoading = state => state.colourDetail.isLoading;\nexport const selectError = state => state.colourDetail.error;\nexport default colourDetailSlice.reducer;", "map": {"version": 3, "names": ["createAction", "createAsyncThunk", "createSlice", "createColour", "colourApi", "initialState", "isLoading", "colour", "error", "saveColour", "_", "rejectWithValue", "getState", "colourDetail", "doc", "updated", "save", "e", "deleteColour", "delete", "savePending", "pending", "type", "saveFulfilled", "fulfilled", "saveRejected", "rejected", "deletePending", "deleteFulfilled", "deleteRejected", "colourDetailSlice", "name", "reducers", "setColour", "state", "action", "payload", "extraReducers", "builder", "addCase", "actions", "selectColour", "selectIsLoading", "selectError", "reducer"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/colours/detail-slice.ts"], "sourcesContent": ["\r\nimport { AsyncThunk, createAction, createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';\r\nimport { createColour, Colour } from 'api/models/colour';\r\nimport { colourApi } from 'api/colour-service';\r\nimport { RootState } from 'app/store';\r\nimport { ProblemDetails } from 'utils/problem-details';\r\n\r\ninterface ColourDetailState {\r\n  isLoading: boolean;\r\n  colour: Colour;\r\n  error: ProblemDetails | null;\r\n}\r\n\r\nconst initialState: ColourDetailState = {\r\n  isLoading: false,\r\n  colour: createColour(),\r\n  error: null\r\n};\r\n\r\nexport const saveColour: AsyncThunk<Colour, void, {state: RootState}> = createAsyncThunk(\r\n  'colour-detail/save-colour',\r\n  async (_, {rejectWithValue, getState}) => {\r\n    try {\r\n      const colour = (getState() as RootState).colourDetail.colour,\r\n        doc = {...colour};\r\n      \r\n      const updated = await colourApi.save(doc);\r\n      return updated;\r\n    } catch(e) {\r\n      return rejectWithValue(e as ProblemDetails);\r\n    }\r\n  }\r\n);\r\n\r\nexport const deleteColour: AsyncThunk<void, void, {state: RootState}> = createAsyncThunk(\r\n  'colour-detail/delete-colour',\r\n  async (_, {rejectWithValue, getState}) => {\r\n    try {\r\n      const colour = (getState() as RootState).colourDetail.colour,\r\n        doc = {...colour};\r\n      \r\n      const updated = await colourApi.delete(doc);\r\n      return updated;\r\n    } catch(e) {\r\n      return rejectWithValue(e as ProblemDetails);\r\n    }\r\n  }\r\n);\r\n\r\nconst savePending = createAction(saveColour.pending.type),\r\n  saveFulfilled = createAction<Colour | undefined>(saveColour.fulfilled.type),\r\n  saveRejected = createAction<ProblemDetails>(saveColour.rejected.type),\r\n  deletePending = createAction(deleteColour.pending.type),\r\n  deleteFulfilled = createAction(deleteColour.fulfilled.type),\r\n  deleteRejected = createAction<ProblemDetails>(deleteColour.rejected.type);\r\n\r\nexport const colourDetailSlice = createSlice({\r\n  name: 'colour-detail',\r\n  initialState,\r\n  reducers: {\r\n    setColour(state, action: PayloadAction<Colour>) {\r\n      state.colour = action.payload;\r\n    }\r\n  },\r\n  extraReducers: builder =>\r\n    builder\r\n      .addCase(savePending, state => {\r\n        state.isLoading = true;\r\n        state.error = null;\r\n      })\r\n      .addCase(saveFulfilled, (state, action) => {\r\n        state.isLoading = false;\r\n        if(action.payload) {\r\n          state.colour = action.payload;\r\n        }\r\n      })\r\n      .addCase(saveRejected, (state, action) => {\r\n        state.isLoading = false;\r\n        state.error = action.payload;\r\n      })\r\n      .addCase(deletePending, state => {\r\n        state.isLoading = true;\r\n        state.error = null;\r\n      })\r\n      .addCase(deleteFulfilled, (state, action) => {\r\n        state.isLoading = false;\r\n        state.colour = createColour();\r\n      })\r\n      .addCase(deleteRejected, (state, action) => {\r\n        state.isLoading = false;\r\n        state.error = action.payload;\r\n      })\r\n});\r\n\r\nexport const { setColour } = colourDetailSlice.actions;\r\n\r\nexport const selectColour = (state: RootState) => state.colourDetail.colour;\r\nexport const selectIsLoading = (state: RootState) => state.colourDetail.isLoading;\r\nexport const selectError = (state: RootState) => state.colourDetail.error;\r\n\r\nexport default colourDetailSlice.reducer;\r\n\r\n"], "mappings": "AACA,SAAqBA,YAAY,EAAEC,gBAAgB,EAAEC,WAAW,QAAuB,kBAAkB;AACzG,SAASC,YAAY,QAAgB,mBAAmB;AACxD,SAASC,SAAS,QAAQ,oBAAoB;AAU9C,MAAMC,YAA+B,GAAG;EACtCC,SAAS,EAAE,KAAK;EAChBC,MAAM,EAAEJ,YAAY,EAAE;EACtBK,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,UAAwD,GAAGR,gBAAgB,CACtF,2BAA2B,EAC3B,OAAOS,CAAC,WAAkC;EAAA,IAAhC;IAACC,eAAe;IAAEC;EAAQ,CAAC;EACnC,IAAI;IACF,MAAML,MAAM,GAAIK,QAAQ,EAAE,CAAeC,YAAY,CAACN,MAAM;MAC1DO,GAAG,GAAG;QAAC,GAAGP;MAAM,CAAC;IAEnB,MAAMQ,OAAO,GAAG,MAAMX,SAAS,CAACY,IAAI,CAACF,GAAG,CAAC;IACzC,OAAOC,OAAO;EAChB,CAAC,CAAC,OAAME,CAAC,EAAE;IACT,OAAON,eAAe,CAACM,CAAC,CAAmB;EAC7C;AACF,CAAC,CACF;AAED,OAAO,MAAMC,YAAwD,GAAGjB,gBAAgB,CACtF,6BAA6B,EAC7B,OAAOS,CAAC,YAAkC;EAAA,IAAhC;IAACC,eAAe;IAAEC;EAAQ,CAAC;EACnC,IAAI;IACF,MAAML,MAAM,GAAIK,QAAQ,EAAE,CAAeC,YAAY,CAACN,MAAM;MAC1DO,GAAG,GAAG;QAAC,GAAGP;MAAM,CAAC;IAEnB,MAAMQ,OAAO,GAAG,MAAMX,SAAS,CAACe,MAAM,CAACL,GAAG,CAAC;IAC3C,OAAOC,OAAO;EAChB,CAAC,CAAC,OAAME,CAAC,EAAE;IACT,OAAON,eAAe,CAACM,CAAC,CAAmB;EAC7C;AACF,CAAC,CACF;AAED,MAAMG,WAAW,GAAGpB,YAAY,CAACS,UAAU,CAACY,OAAO,CAACC,IAAI,CAAC;EACvDC,aAAa,GAAGvB,YAAY,CAAqBS,UAAU,CAACe,SAAS,CAACF,IAAI,CAAC;EAC3EG,YAAY,GAAGzB,YAAY,CAAiBS,UAAU,CAACiB,QAAQ,CAACJ,IAAI,CAAC;EACrEK,aAAa,GAAG3B,YAAY,CAACkB,YAAY,CAACG,OAAO,CAACC,IAAI,CAAC;EACvDM,eAAe,GAAG5B,YAAY,CAACkB,YAAY,CAACM,SAAS,CAACF,IAAI,CAAC;EAC3DO,cAAc,GAAG7B,YAAY,CAAiBkB,YAAY,CAACQ,QAAQ,CAACJ,IAAI,CAAC;AAE3E,OAAO,MAAMQ,iBAAiB,GAAG5B,WAAW,CAAC;EAC3C6B,IAAI,EAAE,eAAe;EACrB1B,YAAY;EACZ2B,QAAQ,EAAE;IACRC,SAAS,CAACC,KAAK,EAAEC,MAA6B,EAAE;MAC9CD,KAAK,CAAC3B,MAAM,GAAG4B,MAAM,CAACC,OAAO;IAC/B;EACF,CAAC;EACDC,aAAa,EAAEC,OAAO,IACpBA,OAAO,CACJC,OAAO,CAACnB,WAAW,EAAEc,KAAK,IAAI;IAC7BA,KAAK,CAAC5B,SAAS,GAAG,IAAI;IACtB4B,KAAK,CAAC1B,KAAK,GAAG,IAAI;EACpB,CAAC,CAAC,CACD+B,OAAO,CAAChB,aAAa,EAAE,CAACW,KAAK,EAAEC,MAAM,KAAK;IACzCD,KAAK,CAAC5B,SAAS,GAAG,KAAK;IACvB,IAAG6B,MAAM,CAACC,OAAO,EAAE;MACjBF,KAAK,CAAC3B,MAAM,GAAG4B,MAAM,CAACC,OAAO;IAC/B;EACF,CAAC,CAAC,CACDG,OAAO,CAACd,YAAY,EAAE,CAACS,KAAK,EAAEC,MAAM,KAAK;IACxCD,KAAK,CAAC5B,SAAS,GAAG,KAAK;IACvB4B,KAAK,CAAC1B,KAAK,GAAG2B,MAAM,CAACC,OAAO;EAC9B,CAAC,CAAC,CACDG,OAAO,CAACZ,aAAa,EAAEO,KAAK,IAAI;IAC/BA,KAAK,CAAC5B,SAAS,GAAG,IAAI;IACtB4B,KAAK,CAAC1B,KAAK,GAAG,IAAI;EACpB,CAAC,CAAC,CACD+B,OAAO,CAACX,eAAe,EAAE,CAACM,KAAK,EAAEC,MAAM,KAAK;IAC3CD,KAAK,CAAC5B,SAAS,GAAG,KAAK;IACvB4B,KAAK,CAAC3B,MAAM,GAAGJ,YAAY,EAAE;EAC/B,CAAC,CAAC,CACDoC,OAAO,CAACV,cAAc,EAAE,CAACK,KAAK,EAAEC,MAAM,KAAK;IAC1CD,KAAK,CAAC5B,SAAS,GAAG,KAAK;IACvB4B,KAAK,CAAC1B,KAAK,GAAG2B,MAAM,CAACC,OAAO;EAC9B,CAAC;AACP,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEH;AAAU,CAAC,GAAGH,iBAAiB,CAACU,OAAO;AAEtD,OAAO,MAAMC,YAAY,GAAIP,KAAgB,IAAKA,KAAK,CAACrB,YAAY,CAACN,MAAM;AAC3E,OAAO,MAAMmC,eAAe,GAAIR,KAAgB,IAAKA,KAAK,CAACrB,YAAY,CAACP,SAAS;AACjF,OAAO,MAAMqC,WAAW,GAAIT,KAAgB,IAAKA,KAAK,CAACrB,YAAY,CAACL,KAAK;AAEzE,eAAesB,iBAAiB,CAACc,OAAO"}, "metadata": {}, "sourceType": "module"}