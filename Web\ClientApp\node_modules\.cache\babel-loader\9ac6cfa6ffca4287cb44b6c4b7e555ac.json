{"ast": null, "code": "import { useContext } from 'react';\nimport { ReactReduxContext } from '../components/Context';\nimport { useReduxContext as useDefaultReduxContext } from './useReduxContext';\n/**\r\n * Hook factory, which creates a `useStore` hook bound to a given context.\r\n *\r\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\r\n * @returns {Function} A `useStore` hook bound to the specified context.\r\n */\n\nexport function createStoreHook(context) {\n  if (context === void 0) {\n    context = ReactReduxContext;\n  }\n  var useReduxContext = context === ReactReduxContext ? useDefaultReduxContext : function () {\n    return useContext(context);\n  };\n  return function useStore() {\n    var _useReduxContext = useReduxContext(),\n      store = _useReduxContext.store;\n    return store;\n  };\n}\n/**\r\n * A hook to access the redux store.\r\n *\r\n * @returns {any} the redux store\r\n *\r\n * @example\r\n *\r\n * import React from 'react'\r\n * import { useStore } from 'react-redux'\r\n *\r\n * export const ExampleComponent = () => {\r\n *   const store = useStore()\r\n *   return <div>{store.getState()}</div>\r\n * }\r\n */\n\nexport var useStore = /*#__PURE__*/createStoreHook();", "map": {"version": 3, "names": ["useContext", "ReactReduxContext", "useReduxContext", "useDefaultReduxContext", "createStoreHook", "context", "useStore", "_useReduxContext", "store"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/node_modules/react-redux/es/hooks/useStore.js"], "sourcesContent": ["import { useContext } from 'react';\nimport { ReactReduxContext } from '../components/Context';\nimport { useReduxContext as useDefaultReduxContext } from './useReduxContext';\n/**\r\n * Hook factory, which creates a `useStore` hook bound to a given context.\r\n *\r\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\r\n * @returns {Function} A `useStore` hook bound to the specified context.\r\n */\n\nexport function createStoreHook(context) {\n  if (context === void 0) {\n    context = ReactReduxContext;\n  }\n\n  var useReduxContext = context === ReactReduxContext ? useDefaultReduxContext : function () {\n    return useContext(context);\n  };\n  return function useStore() {\n    var _useReduxContext = useReduxContext(),\n        store = _useReduxContext.store;\n\n    return store;\n  };\n}\n/**\r\n * A hook to access the redux store.\r\n *\r\n * @returns {any} the redux store\r\n *\r\n * @example\r\n *\r\n * import React from 'react'\r\n * import { useStore } from 'react-redux'\r\n *\r\n * export const ExampleComponent = () => {\r\n *   const store = useStore()\r\n *   return <div>{store.getState()}</div>\r\n * }\r\n */\n\nexport var useStore = /*#__PURE__*/createStoreHook();"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,eAAe,IAAIC,sBAAsB,QAAQ,mBAAmB;AAC7E;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,eAAe,CAACC,OAAO,EAAE;EACvC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAGJ,iBAAiB;EAC7B;EAEA,IAAIC,eAAe,GAAGG,OAAO,KAAKJ,iBAAiB,GAAGE,sBAAsB,GAAG,YAAY;IACzF,OAAOH,UAAU,CAACK,OAAO,CAAC;EAC5B,CAAC;EACD,OAAO,SAASC,QAAQ,GAAG;IACzB,IAAIC,gBAAgB,GAAGL,eAAe,EAAE;MACpCM,KAAK,GAAGD,gBAAgB,CAACC,KAAK;IAElC,OAAOA,KAAK;EACd,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,IAAIF,QAAQ,GAAG,aAAaF,eAAe,EAAE"}, "metadata": {}, "sourceType": "module"}