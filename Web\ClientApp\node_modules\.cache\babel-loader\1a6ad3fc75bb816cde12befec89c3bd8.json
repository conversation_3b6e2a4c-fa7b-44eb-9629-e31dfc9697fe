{"ast": null, "code": "import logger from \"../modules/logger/index.js\";\nvar name = \"webpack-dev-server\"; // default level is set on the client side, so it does not need\n// to be set by the CLI or API\n\nvar defaultLevel = \"info\"; // options new options, merge with old options\n\n/**\n * @param {false | true | \"none\" | \"error\" | \"warn\" | \"info\" | \"log\" | \"verbose\"} level\n * @returns {void}\n */\n\nfunction setLogLevel(level) {\n  logger.configureDefaultLogger({\n    level: level\n  });\n}\nsetLogLevel(defaultLevel);\nvar log = logger.getLogger(name);\nexport { log, setLogLevel };", "map": {"version": 3, "names": ["logger", "name", "defaultLevel", "setLogLevel", "level", "configure<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "log", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/node_modules/webpack-dev-server/client/utils/log.js"], "sourcesContent": ["import logger from \"../modules/logger/index.js\";\nvar name = \"webpack-dev-server\"; // default level is set on the client side, so it does not need\n// to be set by the CLI or API\n\nvar defaultLevel = \"info\"; // options new options, merge with old options\n\n/**\n * @param {false | true | \"none\" | \"error\" | \"warn\" | \"info\" | \"log\" | \"verbose\"} level\n * @returns {void}\n */\n\nfunction setLogLevel(level) {\n  logger.configureDefaultLogger({\n    level: level\n  });\n}\n\nsetLogLevel(defaultLevel);\nvar log = logger.getLogger(name);\nexport { log, setLogLevel };"], "mappings": "AAAA,OAAOA,MAAM,MAAM,4BAA4B;AAC/C,IAAIC,IAAI,GAAG,oBAAoB,CAAC,CAAC;AACjC;;AAEA,IAAIC,YAAY,GAAG,MAAM,CAAC,CAAC;;AAE3B;AACA;AACA;AACA;;AAEA,SAASC,WAAW,CAACC,KAAK,EAAE;EAC1BJ,MAAM,CAACK,sBAAsB,CAAC;IAC5BD,KAAK,EAAEA;EACT,CAAC,CAAC;AACJ;AAEAD,WAAW,CAACD,YAAY,CAAC;AACzB,IAAII,GAAG,GAAGN,MAAM,CAACO,SAAS,CAACN,IAAI,CAAC;AAChC,SAASK,GAAG,EAAEH,WAAW"}, "metadata": {}, "sourceType": "module"}