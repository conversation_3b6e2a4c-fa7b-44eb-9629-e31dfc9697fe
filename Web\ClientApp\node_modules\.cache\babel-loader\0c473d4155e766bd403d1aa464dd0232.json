{"ast": null, "code": "function n(n) {\n  for (var r = arguments.length, t = Array(r > 1 ? r - 1 : 0), e = 1; e < r; e++) t[e - 1] = arguments[e];\n  if (\"production\" !== process.env.NODE_ENV) {\n    var i = Y[n],\n      o = i ? \"function\" == typeof i ? i.apply(null, t) : i : \"unknown error nr: \" + n;\n    throw Error(\"[Immer] \" + o);\n  }\n  throw Error(\"[Immer] minified error nr: \" + n + (t.length ? \" \" + t.map(function (n) {\n    return \"'\" + n + \"'\";\n  }).join(\",\") : \"\") + \". Find the full error at: https://bit.ly/3cXEKWf\");\n}\nfunction r(n) {\n  return !!n && !!n[Q];\n}\nfunction t(n) {\n  var r;\n  return !!n && (function (n) {\n    if (!n || \"object\" != typeof n) return !1;\n    var r = Object.getPrototypeOf(n);\n    if (null === r) return !0;\n    var t = Object.hasOwnProperty.call(r, \"constructor\") && r.constructor;\n    return t === Object || \"function\" == typeof t && Function.toString.call(t) === Z;\n  }(n) || Array.isArray(n) || !!n[L] || !!(null === (r = n.constructor) || void 0 === r ? void 0 : r[L]) || s(n) || v(n));\n}\nfunction e(t) {\n  return r(t) || n(23, t), t[Q].t;\n}\nfunction i(n, r, t) {\n  void 0 === t && (t = !1), 0 === o(n) ? (t ? Object.keys : nn)(n).forEach(function (e) {\n    t && \"symbol\" == typeof e || r(e, n[e], n);\n  }) : n.forEach(function (t, e) {\n    return r(e, t, n);\n  });\n}\nfunction o(n) {\n  var r = n[Q];\n  return r ? r.i > 3 ? r.i - 4 : r.i : Array.isArray(n) ? 1 : s(n) ? 2 : v(n) ? 3 : 0;\n}\nfunction u(n, r) {\n  return 2 === o(n) ? n.has(r) : Object.prototype.hasOwnProperty.call(n, r);\n}\nfunction a(n, r) {\n  return 2 === o(n) ? n.get(r) : n[r];\n}\nfunction f(n, r, t) {\n  var e = o(n);\n  2 === e ? n.set(r, t) : 3 === e ? (n.delete(r), n.add(t)) : n[r] = t;\n}\nfunction c(n, r) {\n  return n === r ? 0 !== n || 1 / n == 1 / r : n != n && r != r;\n}\nfunction s(n) {\n  return X && n instanceof Map;\n}\nfunction v(n) {\n  return q && n instanceof Set;\n}\nfunction p(n) {\n  return n.o || n.t;\n}\nfunction l(n) {\n  if (Array.isArray(n)) return Array.prototype.slice.call(n);\n  var r = rn(n);\n  delete r[Q];\n  for (var t = nn(r), e = 0; e < t.length; e++) {\n    var i = t[e],\n      o = r[i];\n    !1 === o.writable && (o.writable = !0, o.configurable = !0), (o.get || o.set) && (r[i] = {\n      configurable: !0,\n      writable: !0,\n      enumerable: o.enumerable,\n      value: n[i]\n    });\n  }\n  return Object.create(Object.getPrototypeOf(n), r);\n}\nfunction d(n, e) {\n  return void 0 === e && (e = !1), y(n) || r(n) || !t(n) ? n : (o(n) > 1 && (n.set = n.add = n.clear = n.delete = h), Object.freeze(n), e && i(n, function (n, r) {\n    return d(r, !0);\n  }, !0), n);\n}\nfunction h() {\n  n(2);\n}\nfunction y(n) {\n  return null == n || \"object\" != typeof n || Object.isFrozen(n);\n}\nfunction b(r) {\n  var t = tn[r];\n  return t || n(18, r), t;\n}\nfunction m(n, r) {\n  tn[n] || (tn[n] = r);\n}\nfunction _() {\n  return \"production\" === process.env.NODE_ENV || U || n(0), U;\n}\nfunction j(n, r) {\n  r && (b(\"Patches\"), n.u = [], n.s = [], n.v = r);\n}\nfunction O(n) {\n  g(n), n.p.forEach(S), n.p = null;\n}\nfunction g(n) {\n  n === U && (U = n.l);\n}\nfunction w(n) {\n  return U = {\n    p: [],\n    l: U,\n    h: n,\n    m: !0,\n    _: 0\n  };\n}\nfunction S(n) {\n  var r = n[Q];\n  0 === r.i || 1 === r.i ? r.j() : r.O = !0;\n}\nfunction P(r, e) {\n  e._ = e.p.length;\n  var i = e.p[0],\n    o = void 0 !== r && r !== i;\n  return e.h.g || b(\"ES5\").S(e, r, o), o ? (i[Q].P && (O(e), n(4)), t(r) && (r = M(e, r), e.l || x(e, r)), e.u && b(\"Patches\").M(i[Q].t, r, e.u, e.s)) : r = M(e, i, []), O(e), e.u && e.v(e.u, e.s), r !== H ? r : void 0;\n}\nfunction M(n, r, t) {\n  if (y(r)) return r;\n  var e = r[Q];\n  if (!e) return i(r, function (i, o) {\n    return A(n, e, r, i, o, t);\n  }, !0), r;\n  if (e.A !== n) return r;\n  if (!e.P) return x(n, e.t, !0), e.t;\n  if (!e.I) {\n    e.I = !0, e.A._--;\n    var o = 4 === e.i || 5 === e.i ? e.o = l(e.k) : e.o;\n    i(3 === e.i ? new Set(o) : o, function (r, i) {\n      return A(n, e, o, r, i, t);\n    }), x(n, o, !1), t && n.u && b(\"Patches\").R(e, t, n.u, n.s);\n  }\n  return e.o;\n}\nfunction A(e, i, o, a, c, s) {\n  if (\"production\" !== process.env.NODE_ENV && c === o && n(5), r(c)) {\n    var v = M(e, c, s && i && 3 !== i.i && !u(i.D, a) ? s.concat(a) : void 0);\n    if (f(o, a, v), !r(v)) return;\n    e.m = !1;\n  }\n  if (t(c) && !y(c)) {\n    if (!e.h.F && e._ < 1) return;\n    M(e, c), i && i.A.l || x(e, c);\n  }\n}\nfunction x(n, r, t) {\n  void 0 === t && (t = !1), n.h.F && n.m && d(r, t);\n}\nfunction z(n, r) {\n  var t = n[Q];\n  return (t ? p(t) : n)[r];\n}\nfunction I(n, r) {\n  if (r in n) for (var t = Object.getPrototypeOf(n); t;) {\n    var e = Object.getOwnPropertyDescriptor(t, r);\n    if (e) return e;\n    t = Object.getPrototypeOf(t);\n  }\n}\nfunction k(n) {\n  n.P || (n.P = !0, n.l && k(n.l));\n}\nfunction E(n) {\n  n.o || (n.o = l(n.t));\n}\nfunction R(n, r, t) {\n  var e = s(r) ? b(\"MapSet\").N(r, t) : v(r) ? b(\"MapSet\").T(r, t) : n.g ? function (n, r) {\n    var t = Array.isArray(n),\n      e = {\n        i: t ? 1 : 0,\n        A: r ? r.A : _(),\n        P: !1,\n        I: !1,\n        D: {},\n        l: r,\n        t: n,\n        k: null,\n        o: null,\n        j: null,\n        C: !1\n      },\n      i = e,\n      o = en;\n    t && (i = [e], o = on);\n    var u = Proxy.revocable(i, o),\n      a = u.revoke,\n      f = u.proxy;\n    return e.k = f, e.j = a, f;\n  }(r, t) : b(\"ES5\").J(r, t);\n  return (t ? t.A : _()).p.push(e), e;\n}\nfunction D(e) {\n  return r(e) || n(22, e), function n(r) {\n    if (!t(r)) return r;\n    var e,\n      u = r[Q],\n      c = o(r);\n    if (u) {\n      if (!u.P && (u.i < 4 || !b(\"ES5\").K(u))) return u.t;\n      u.I = !0, e = F(r, c), u.I = !1;\n    } else e = F(r, c);\n    return i(e, function (r, t) {\n      u && a(u.t, r) === t || f(e, r, n(t));\n    }), 3 === c ? new Set(e) : e;\n  }(e);\n}\nfunction F(n, r) {\n  switch (r) {\n    case 2:\n      return new Map(n);\n    case 3:\n      return Array.from(n);\n  }\n  return l(n);\n}\nfunction N() {\n  function t(n, r) {\n    var t = s[n];\n    return t ? t.enumerable = r : s[n] = t = {\n      configurable: !0,\n      enumerable: r,\n      get: function () {\n        var r = this[Q];\n        return \"production\" !== process.env.NODE_ENV && f(r), en.get(r, n);\n      },\n      set: function (r) {\n        var t = this[Q];\n        \"production\" !== process.env.NODE_ENV && f(t), en.set(t, n, r);\n      }\n    }, t;\n  }\n  function e(n) {\n    for (var r = n.length - 1; r >= 0; r--) {\n      var t = n[r][Q];\n      if (!t.P) switch (t.i) {\n        case 5:\n          a(t) && k(t);\n          break;\n        case 4:\n          o(t) && k(t);\n      }\n    }\n  }\n  function o(n) {\n    for (var r = n.t, t = n.k, e = nn(t), i = e.length - 1; i >= 0; i--) {\n      var o = e[i];\n      if (o !== Q) {\n        var a = r[o];\n        if (void 0 === a && !u(r, o)) return !0;\n        var f = t[o],\n          s = f && f[Q];\n        if (s ? s.t !== a : !c(f, a)) return !0;\n      }\n    }\n    var v = !!r[Q];\n    return e.length !== nn(r).length + (v ? 0 : 1);\n  }\n  function a(n) {\n    var r = n.k;\n    if (r.length !== n.t.length) return !0;\n    var t = Object.getOwnPropertyDescriptor(r, r.length - 1);\n    if (t && !t.get) return !0;\n    for (var e = 0; e < r.length; e++) if (!r.hasOwnProperty(e)) return !0;\n    return !1;\n  }\n  function f(r) {\n    r.O && n(3, JSON.stringify(p(r)));\n  }\n  var s = {};\n  m(\"ES5\", {\n    J: function (n, r) {\n      var e = Array.isArray(n),\n        i = function (n, r) {\n          if (n) {\n            for (var e = Array(r.length), i = 0; i < r.length; i++) Object.defineProperty(e, \"\" + i, t(i, !0));\n            return e;\n          }\n          var o = rn(r);\n          delete o[Q];\n          for (var u = nn(o), a = 0; a < u.length; a++) {\n            var f = u[a];\n            o[f] = t(f, n || !!o[f].enumerable);\n          }\n          return Object.create(Object.getPrototypeOf(r), o);\n        }(e, n),\n        o = {\n          i: e ? 5 : 4,\n          A: r ? r.A : _(),\n          P: !1,\n          I: !1,\n          D: {},\n          l: r,\n          t: n,\n          k: i,\n          o: null,\n          O: !1,\n          C: !1\n        };\n      return Object.defineProperty(i, Q, {\n        value: o,\n        writable: !0\n      }), i;\n    },\n    S: function (n, t, o) {\n      o ? r(t) && t[Q].A === n && e(n.p) : (n.u && function n(r) {\n        if (r && \"object\" == typeof r) {\n          var t = r[Q];\n          if (t) {\n            var e = t.t,\n              o = t.k,\n              f = t.D,\n              c = t.i;\n            if (4 === c) i(o, function (r) {\n              r !== Q && (void 0 !== e[r] || u(e, r) ? f[r] || n(o[r]) : (f[r] = !0, k(t)));\n            }), i(e, function (n) {\n              void 0 !== o[n] || u(o, n) || (f[n] = !1, k(t));\n            });else if (5 === c) {\n              if (a(t) && (k(t), f.length = !0), o.length < e.length) for (var s = o.length; s < e.length; s++) f[s] = !1;else for (var v = e.length; v < o.length; v++) f[v] = !0;\n              for (var p = Math.min(o.length, e.length), l = 0; l < p; l++) o.hasOwnProperty(l) || (f[l] = !0), void 0 === f[l] && n(o[l]);\n            }\n          }\n        }\n      }(n.p[0]), e(n.p));\n    },\n    K: function (n) {\n      return 4 === n.i ? o(n) : a(n);\n    }\n  });\n}\nfunction T() {\n  function e(n) {\n    if (!t(n)) return n;\n    if (Array.isArray(n)) return n.map(e);\n    if (s(n)) return new Map(Array.from(n.entries()).map(function (n) {\n      return [n[0], e(n[1])];\n    }));\n    if (v(n)) return new Set(Array.from(n).map(e));\n    var r = Object.create(Object.getPrototypeOf(n));\n    for (var i in n) r[i] = e(n[i]);\n    return u(n, L) && (r[L] = n[L]), r;\n  }\n  function f(n) {\n    return r(n) ? e(n) : n;\n  }\n  var c = \"add\";\n  m(\"Patches\", {\n    $: function (r, t) {\n      return t.forEach(function (t) {\n        for (var i = t.path, u = t.op, f = r, s = 0; s < i.length - 1; s++) {\n          var v = o(f),\n            p = \"\" + i[s];\n          0 !== v && 1 !== v || \"__proto__\" !== p && \"constructor\" !== p || n(24), \"function\" == typeof f && \"prototype\" === p && n(24), \"object\" != typeof (f = a(f, p)) && n(15, i.join(\"/\"));\n        }\n        var l = o(f),\n          d = e(t.value),\n          h = i[i.length - 1];\n        switch (u) {\n          case \"replace\":\n            switch (l) {\n              case 2:\n                return f.set(h, d);\n              case 3:\n                n(16);\n              default:\n                return f[h] = d;\n            }\n          case c:\n            switch (l) {\n              case 1:\n                return \"-\" === h ? f.push(d) : f.splice(h, 0, d);\n              case 2:\n                return f.set(h, d);\n              case 3:\n                return f.add(d);\n              default:\n                return f[h] = d;\n            }\n          case \"remove\":\n            switch (l) {\n              case 1:\n                return f.splice(h, 1);\n              case 2:\n                return f.delete(h);\n              case 3:\n                return f.delete(t.value);\n              default:\n                return delete f[h];\n            }\n          default:\n            n(17, u);\n        }\n      }), r;\n    },\n    R: function (n, r, t, e) {\n      switch (n.i) {\n        case 0:\n        case 4:\n        case 2:\n          return function (n, r, t, e) {\n            var o = n.t,\n              s = n.o;\n            i(n.D, function (n, i) {\n              var v = a(o, n),\n                p = a(s, n),\n                l = i ? u(o, n) ? \"replace\" : c : \"remove\";\n              if (v !== p || \"replace\" !== l) {\n                var d = r.concat(n);\n                t.push(\"remove\" === l ? {\n                  op: l,\n                  path: d\n                } : {\n                  op: l,\n                  path: d,\n                  value: p\n                }), e.push(l === c ? {\n                  op: \"remove\",\n                  path: d\n                } : \"remove\" === l ? {\n                  op: c,\n                  path: d,\n                  value: f(v)\n                } : {\n                  op: \"replace\",\n                  path: d,\n                  value: f(v)\n                });\n              }\n            });\n          }(n, r, t, e);\n        case 5:\n        case 1:\n          return function (n, r, t, e) {\n            var i = n.t,\n              o = n.D,\n              u = n.o;\n            if (u.length < i.length) {\n              var a = [u, i];\n              i = a[0], u = a[1];\n              var s = [e, t];\n              t = s[0], e = s[1];\n            }\n            for (var v = 0; v < i.length; v++) if (o[v] && u[v] !== i[v]) {\n              var p = r.concat([v]);\n              t.push({\n                op: \"replace\",\n                path: p,\n                value: f(u[v])\n              }), e.push({\n                op: \"replace\",\n                path: p,\n                value: f(i[v])\n              });\n            }\n            for (var l = i.length; l < u.length; l++) {\n              var d = r.concat([l]);\n              t.push({\n                op: c,\n                path: d,\n                value: f(u[l])\n              });\n            }\n            i.length < u.length && e.push({\n              op: \"replace\",\n              path: r.concat([\"length\"]),\n              value: i.length\n            });\n          }(n, r, t, e);\n        case 3:\n          return function (n, r, t, e) {\n            var i = n.t,\n              o = n.o,\n              u = 0;\n            i.forEach(function (n) {\n              if (!o.has(n)) {\n                var i = r.concat([u]);\n                t.push({\n                  op: \"remove\",\n                  path: i,\n                  value: n\n                }), e.unshift({\n                  op: c,\n                  path: i,\n                  value: n\n                });\n              }\n              u++;\n            }), u = 0, o.forEach(function (n) {\n              if (!i.has(n)) {\n                var o = r.concat([u]);\n                t.push({\n                  op: c,\n                  path: o,\n                  value: n\n                }), e.unshift({\n                  op: \"remove\",\n                  path: o,\n                  value: n\n                });\n              }\n              u++;\n            });\n          }(n, r, t, e);\n      }\n    },\n    M: function (n, r, t, e) {\n      t.push({\n        op: \"replace\",\n        path: [],\n        value: r === H ? void 0 : r\n      }), e.push({\n        op: \"replace\",\n        path: [],\n        value: n\n      });\n    }\n  });\n}\nfunction C() {\n  function r(n, r) {\n    function t() {\n      this.constructor = n;\n    }\n    a(n, r), n.prototype = (t.prototype = r.prototype, new t());\n  }\n  function e(n) {\n    n.o || (n.D = new Map(), n.o = new Map(n.t));\n  }\n  function o(n) {\n    n.o || (n.o = new Set(), n.t.forEach(function (r) {\n      if (t(r)) {\n        var e = R(n.A.h, r, n);\n        n.p.set(r, e), n.o.add(e);\n      } else n.o.add(r);\n    }));\n  }\n  function u(r) {\n    r.O && n(3, JSON.stringify(p(r)));\n  }\n  var a = function (n, r) {\n      return (a = Object.setPrototypeOf || {\n        __proto__: []\n      } instanceof Array && function (n, r) {\n        n.__proto__ = r;\n      } || function (n, r) {\n        for (var t in r) r.hasOwnProperty(t) && (n[t] = r[t]);\n      })(n, r);\n    },\n    f = function () {\n      function n(n, r) {\n        return this[Q] = {\n          i: 2,\n          l: r,\n          A: r ? r.A : _(),\n          P: !1,\n          I: !1,\n          o: void 0,\n          D: void 0,\n          t: n,\n          k: this,\n          C: !1,\n          O: !1\n        }, this;\n      }\n      r(n, Map);\n      var o = n.prototype;\n      return Object.defineProperty(o, \"size\", {\n        get: function () {\n          return p(this[Q]).size;\n        }\n      }), o.has = function (n) {\n        return p(this[Q]).has(n);\n      }, o.set = function (n, r) {\n        var t = this[Q];\n        return u(t), p(t).has(n) && p(t).get(n) === r || (e(t), k(t), t.D.set(n, !0), t.o.set(n, r), t.D.set(n, !0)), this;\n      }, o.delete = function (n) {\n        if (!this.has(n)) return !1;\n        var r = this[Q];\n        return u(r), e(r), k(r), r.t.has(n) ? r.D.set(n, !1) : r.D.delete(n), r.o.delete(n), !0;\n      }, o.clear = function () {\n        var n = this[Q];\n        u(n), p(n).size && (e(n), k(n), n.D = new Map(), i(n.t, function (r) {\n          n.D.set(r, !1);\n        }), n.o.clear());\n      }, o.forEach = function (n, r) {\n        var t = this;\n        p(this[Q]).forEach(function (e, i) {\n          n.call(r, t.get(i), i, t);\n        });\n      }, o.get = function (n) {\n        var r = this[Q];\n        u(r);\n        var i = p(r).get(n);\n        if (r.I || !t(i)) return i;\n        if (i !== r.t.get(n)) return i;\n        var o = R(r.A.h, i, r);\n        return e(r), r.o.set(n, o), o;\n      }, o.keys = function () {\n        return p(this[Q]).keys();\n      }, o.values = function () {\n        var n,\n          r = this,\n          t = this.keys();\n        return (n = {})[V] = function () {\n          return r.values();\n        }, n.next = function () {\n          var n = t.next();\n          return n.done ? n : {\n            done: !1,\n            value: r.get(n.value)\n          };\n        }, n;\n      }, o.entries = function () {\n        var n,\n          r = this,\n          t = this.keys();\n        return (n = {})[V] = function () {\n          return r.entries();\n        }, n.next = function () {\n          var n = t.next();\n          if (n.done) return n;\n          var e = r.get(n.value);\n          return {\n            done: !1,\n            value: [n.value, e]\n          };\n        }, n;\n      }, o[V] = function () {\n        return this.entries();\n      }, n;\n    }(),\n    c = function () {\n      function n(n, r) {\n        return this[Q] = {\n          i: 3,\n          l: r,\n          A: r ? r.A : _(),\n          P: !1,\n          I: !1,\n          o: void 0,\n          t: n,\n          k: this,\n          p: new Map(),\n          O: !1,\n          C: !1\n        }, this;\n      }\n      r(n, Set);\n      var t = n.prototype;\n      return Object.defineProperty(t, \"size\", {\n        get: function () {\n          return p(this[Q]).size;\n        }\n      }), t.has = function (n) {\n        var r = this[Q];\n        return u(r), r.o ? !!r.o.has(n) || !(!r.p.has(n) || !r.o.has(r.p.get(n))) : r.t.has(n);\n      }, t.add = function (n) {\n        var r = this[Q];\n        return u(r), this.has(n) || (o(r), k(r), r.o.add(n)), this;\n      }, t.delete = function (n) {\n        if (!this.has(n)) return !1;\n        var r = this[Q];\n        return u(r), o(r), k(r), r.o.delete(n) || !!r.p.has(n) && r.o.delete(r.p.get(n));\n      }, t.clear = function () {\n        var n = this[Q];\n        u(n), p(n).size && (o(n), k(n), n.o.clear());\n      }, t.values = function () {\n        var n = this[Q];\n        return u(n), o(n), n.o.values();\n      }, t.entries = function () {\n        var n = this[Q];\n        return u(n), o(n), n.o.entries();\n      }, t.keys = function () {\n        return this.values();\n      }, t[V] = function () {\n        return this.values();\n      }, t.forEach = function (n, r) {\n        for (var t = this.values(), e = t.next(); !e.done;) n.call(r, e.value, e.value, this), e = t.next();\n      }, n;\n    }();\n  m(\"MapSet\", {\n    N: function (n, r) {\n      return new f(n, r);\n    },\n    T: function (n, r) {\n      return new c(n, r);\n    }\n  });\n}\nfunction J() {\n  N(), C(), T();\n}\nfunction K(n) {\n  return n;\n}\nfunction $(n) {\n  return n;\n}\nvar G,\n  U,\n  W = \"undefined\" != typeof Symbol && \"symbol\" == typeof Symbol(\"x\"),\n  X = \"undefined\" != typeof Map,\n  q = \"undefined\" != typeof Set,\n  B = \"undefined\" != typeof Proxy && void 0 !== Proxy.revocable && \"undefined\" != typeof Reflect,\n  H = W ? Symbol.for(\"immer-nothing\") : ((G = {})[\"immer-nothing\"] = !0, G),\n  L = W ? Symbol.for(\"immer-draftable\") : \"__$immer_draftable\",\n  Q = W ? Symbol.for(\"immer-state\") : \"__$immer_state\",\n  V = \"undefined\" != typeof Symbol && Symbol.iterator || \"@@iterator\",\n  Y = {\n    0: \"Illegal state\",\n    1: \"Immer drafts cannot have computed properties\",\n    2: \"This object has been frozen and should not be mutated\",\n    3: function (n) {\n      return \"Cannot use a proxy that has been revoked. Did you pass an object from inside an immer function to an async process? \" + n;\n    },\n    4: \"An immer producer returned a new value *and* modified its draft. Either return a new value *or* modify the draft.\",\n    5: \"Immer forbids circular references\",\n    6: \"The first or second argument to `produce` must be a function\",\n    7: \"The third argument to `produce` must be a function or undefined\",\n    8: \"First argument to `createDraft` must be a plain object, an array, or an immerable object\",\n    9: \"First argument to `finishDraft` must be a draft returned by `createDraft`\",\n    10: \"The given draft is already finalized\",\n    11: \"Object.defineProperty() cannot be used on an Immer draft\",\n    12: \"Object.setPrototypeOf() cannot be used on an Immer draft\",\n    13: \"Immer only supports deleting array indices\",\n    14: \"Immer only supports setting array indices and the 'length' property\",\n    15: function (n) {\n      return \"Cannot apply patch, path doesn't resolve: \" + n;\n    },\n    16: 'Sets cannot have \"replace\" patches.',\n    17: function (n) {\n      return \"Unsupported patch operation: \" + n;\n    },\n    18: function (n) {\n      return \"The plugin for '\" + n + \"' has not been loaded into Immer. To enable the plugin, import and call `enable\" + n + \"()` when initializing your application.\";\n    },\n    20: \"Cannot use proxies if Proxy, Proxy.revocable or Reflect are not available\",\n    21: function (n) {\n      return \"produce can only be called on things that are draftable: plain objects, arrays, Map, Set or classes that are marked with '[immerable]: true'. Got '\" + n + \"'\";\n    },\n    22: function (n) {\n      return \"'current' expects a draft, got: \" + n;\n    },\n    23: function (n) {\n      return \"'original' expects a draft, got: \" + n;\n    },\n    24: \"Patching reserved attributes like __proto__, prototype and constructor is not allowed\"\n  },\n  Z = \"\" + Object.prototype.constructor,\n  nn = \"undefined\" != typeof Reflect && Reflect.ownKeys ? Reflect.ownKeys : void 0 !== Object.getOwnPropertySymbols ? function (n) {\n    return Object.getOwnPropertyNames(n).concat(Object.getOwnPropertySymbols(n));\n  } : Object.getOwnPropertyNames,\n  rn = Object.getOwnPropertyDescriptors || function (n) {\n    var r = {};\n    return nn(n).forEach(function (t) {\n      r[t] = Object.getOwnPropertyDescriptor(n, t);\n    }), r;\n  },\n  tn = {},\n  en = {\n    get: function (n, r) {\n      if (r === Q) return n;\n      var e = p(n);\n      if (!u(e, r)) return function (n, r, t) {\n        var e,\n          i = I(r, t);\n        return i ? \"value\" in i ? i.value : null === (e = i.get) || void 0 === e ? void 0 : e.call(n.k) : void 0;\n      }(n, e, r);\n      var i = e[r];\n      return n.I || !t(i) ? i : i === z(n.t, r) ? (E(n), n.o[r] = R(n.A.h, i, n)) : i;\n    },\n    has: function (n, r) {\n      return r in p(n);\n    },\n    ownKeys: function (n) {\n      return Reflect.ownKeys(p(n));\n    },\n    set: function (n, r, t) {\n      var e = I(p(n), r);\n      if (null == e ? void 0 : e.set) return e.set.call(n.k, t), !0;\n      if (!n.P) {\n        var i = z(p(n), r),\n          o = null == i ? void 0 : i[Q];\n        if (o && o.t === t) return n.o[r] = t, n.D[r] = !1, !0;\n        if (c(t, i) && (void 0 !== t || u(n.t, r))) return !0;\n        E(n), k(n);\n      }\n      return n.o[r] === t && \"number\" != typeof t && (void 0 !== t || r in n.o) || (n.o[r] = t, n.D[r] = !0, !0);\n    },\n    deleteProperty: function (n, r) {\n      return void 0 !== z(n.t, r) || r in n.t ? (n.D[r] = !1, E(n), k(n)) : delete n.D[r], n.o && delete n.o[r], !0;\n    },\n    getOwnPropertyDescriptor: function (n, r) {\n      var t = p(n),\n        e = Reflect.getOwnPropertyDescriptor(t, r);\n      return e ? {\n        writable: !0,\n        configurable: 1 !== n.i || \"length\" !== r,\n        enumerable: e.enumerable,\n        value: t[r]\n      } : e;\n    },\n    defineProperty: function () {\n      n(11);\n    },\n    getPrototypeOf: function (n) {\n      return Object.getPrototypeOf(n.t);\n    },\n    setPrototypeOf: function () {\n      n(12);\n    }\n  },\n  on = {};\ni(en, function (n, r) {\n  on[n] = function () {\n    return arguments[0] = arguments[0][0], r.apply(this, arguments);\n  };\n}), on.deleteProperty = function (r, t) {\n  return \"production\" !== process.env.NODE_ENV && isNaN(parseInt(t)) && n(13), on.set.call(this, r, t, void 0);\n}, on.set = function (r, t, e) {\n  return \"production\" !== process.env.NODE_ENV && \"length\" !== t && isNaN(parseInt(t)) && n(14), en.set.call(this, r[0], t, e, r[0]);\n};\nvar un = function () {\n    function e(r) {\n      var e = this;\n      this.g = B, this.F = !0, this.produce = function (r, i, o) {\n        if (\"function\" == typeof r && \"function\" != typeof i) {\n          var u = i;\n          i = r;\n          var a = e;\n          return function (n) {\n            var r = this;\n            void 0 === n && (n = u);\n            for (var t = arguments.length, e = Array(t > 1 ? t - 1 : 0), o = 1; o < t; o++) e[o - 1] = arguments[o];\n            return a.produce(n, function (n) {\n              var t;\n              return (t = i).call.apply(t, [r, n].concat(e));\n            });\n          };\n        }\n        var f;\n        if (\"function\" != typeof i && n(6), void 0 !== o && \"function\" != typeof o && n(7), t(r)) {\n          var c = w(e),\n            s = R(e, r, void 0),\n            v = !0;\n          try {\n            f = i(s), v = !1;\n          } finally {\n            v ? O(c) : g(c);\n          }\n          return \"undefined\" != typeof Promise && f instanceof Promise ? f.then(function (n) {\n            return j(c, o), P(n, c);\n          }, function (n) {\n            throw O(c), n;\n          }) : (j(c, o), P(f, c));\n        }\n        if (!r || \"object\" != typeof r) {\n          if (void 0 === (f = i(r)) && (f = r), f === H && (f = void 0), e.F && d(f, !0), o) {\n            var p = [],\n              l = [];\n            b(\"Patches\").M(r, f, p, l), o(p, l);\n          }\n          return f;\n        }\n        n(21, r);\n      }, this.produceWithPatches = function (n, r) {\n        if (\"function\" == typeof n) return function (r) {\n          for (var t = arguments.length, i = Array(t > 1 ? t - 1 : 0), o = 1; o < t; o++) i[o - 1] = arguments[o];\n          return e.produceWithPatches(r, function (r) {\n            return n.apply(void 0, [r].concat(i));\n          });\n        };\n        var t,\n          i,\n          o = e.produce(n, r, function (n, r) {\n            t = n, i = r;\n          });\n        return \"undefined\" != typeof Promise && o instanceof Promise ? o.then(function (n) {\n          return [n, t, i];\n        }) : [o, t, i];\n      }, \"boolean\" == typeof (null == r ? void 0 : r.useProxies) && this.setUseProxies(r.useProxies), \"boolean\" == typeof (null == r ? void 0 : r.autoFreeze) && this.setAutoFreeze(r.autoFreeze);\n    }\n    var i = e.prototype;\n    return i.createDraft = function (e) {\n      t(e) || n(8), r(e) && (e = D(e));\n      var i = w(this),\n        o = R(this, e, void 0);\n      return o[Q].C = !0, g(i), o;\n    }, i.finishDraft = function (r, t) {\n      var e = r && r[Q];\n      \"production\" !== process.env.NODE_ENV && (e && e.C || n(9), e.I && n(10));\n      var i = e.A;\n      return j(i, t), P(void 0, i);\n    }, i.setAutoFreeze = function (n) {\n      this.F = n;\n    }, i.setUseProxies = function (r) {\n      r && !B && n(20), this.g = r;\n    }, i.applyPatches = function (n, t) {\n      var e;\n      for (e = t.length - 1; e >= 0; e--) {\n        var i = t[e];\n        if (0 === i.path.length && \"replace\" === i.op) {\n          n = i.value;\n          break;\n        }\n      }\n      e > -1 && (t = t.slice(e + 1));\n      var o = b(\"Patches\").$;\n      return r(n) ? o(n, t) : this.produce(n, function (n) {\n        return o(n, t);\n      });\n    }, e;\n  }(),\n  an = new un(),\n  fn = an.produce,\n  cn = an.produceWithPatches.bind(an),\n  sn = an.setAutoFreeze.bind(an),\n  vn = an.setUseProxies.bind(an),\n  pn = an.applyPatches.bind(an),\n  ln = an.createDraft.bind(an),\n  dn = an.finishDraft.bind(an);\nexport default fn;\nexport { un as Immer, pn as applyPatches, K as castDraft, $ as castImmutable, ln as createDraft, D as current, J as enableAllPlugins, N as enableES5, C as enableMapSet, T as enablePatches, dn as finishDraft, d as freeze, L as immerable, r as isDraft, t as isDraftable, H as nothing, e as original, fn as produce, cn as produceWithPatches, sn as setAutoFreeze, vn as setUseProxies };", "map": {"version": 3, "mappings": "SA4CgBA,EAAIC;EAAAA,+BAA+BC;EAAAA,2CACrC;IAAA,IACNC,IAAIC,EAAOH;MACXI,IAAOF,IAEG,qBAANA,IACPA,EAAEG,MAAM,MAAMJ,KACdC,IAHA,uBAAuBF;IAAAA,MAIhBM,mBAAiBF;EAAAA;EAAAA,MAElBE,sCACqBN,KAC7BC,EAAKM,SAAS,MAAMN,EAAKO,IAAI;IAAAC,aAASA;EAAAA,GAAMC,KAAK,OAAO;AAAA;AAAA,SCvC3CC,EAAQC;EAAAA,SACdA,OAAWA,EAAMC;AAAAA;AAAAA,SAKXC,EAAYF;EAAAA;EAAAA,SACtBA,gBAawBA;IAAAA,KACxBA,KAA0B,mBAAVA,GAAoB,QAAO;IAAA,IAC1CG,IAAQC,OAAOC,eAAeL;IAAAA,IACtB,SAAVG,WACI;IAAA,IAEFG,IACLF,OAAOG,eAAeC,KAAKL,GAAO,kBAAkBA,EAAMM;IAAAA,OAEvDH,MAASF,UAGG,qBAARE,KACPI,SAASC,SAASH,KAAKF,OAAUM;EAAAA,CAxBjCC,CAAcb,MACdc,MAAMC,QAAQf,QACZA,EAAMgB,uBACNhB,EAAMS,wCAANQ,EAAoBD,OACtBE,EAAMlB,MACNmB,EAAMnB;AAAAA;AAAAA,SA0BQoB,EAASpB;EAAAA,OACnBD,EAAQC,MAAQb,EAAI,IAAIa,IACtBA,EAAMC,GAAaoB;AAAAA;AA8B3B,SAAgBC,EAAKC,GAAUC,GAAWC;EAAAA,sBAAiB,UACtDC,EAAYH,MACbE,IAAiBrB,OAAOuB,OAAOC,IAASL,GAAKM,QAAQ;IACjDJ,KAAiC,mBAARK,KAAkBN,EAAKM,GAAKP,EAAIO,IAAMP;EAAAA,KAGrEA,EAAIM,QAAQ,UAACE,GAAYC;IAAAA,OAAeR,EAAKQ,GAAOD,GAAOR;EAAAA;AAAAA;AAAAA,SAK7CG,EAAYO;EAAAA,IAErBC,IAAgCD,EAAMhC;EAAAA,OACrCiC,IACJA,EAAMC,IAAQ,IACbD,EAAMC,IAAQ,IACbD,EAAMC,IACRrB,MAAMC,QAAQkB,SAEdf,EAAMe,SAENd,EAAMc;AAAAA;AAAAA,SAMMG,EAAIH,GAAYI;EAAAA,aACxBX,EAAYO,KAChBA,EAAMG,IAAIC,KACVjC,OAAOkC,UAAU/B,eAAeC,KAAKyB,GAAOI;AAAAA;AAAAA,SAIhCE,EAAIN,GAA2BI;EAAAA,aAEvCX,EAAYO,KAA0BA,EAAMM,IAAIF,KAAQJ,EAAMI;AAAAA;AAItE,SAAgBG,EAAIP,GAAYQ,GAA6BzC;EAAAA,IACtD0C,IAAIhB,EAAYO;EAAAA,MAClBS,IAAoBT,EAAMO,IAAIC,GAAgBzC,WACzC0C,KACRT,EAAMU,OAAOF,IACbR,EAAMW,IAAI5C,MACJiC,EAAMQ,KAAkBzC;AAAAA;AAAAA,SAIhB6C,EAAGC,GAAQC;EAAAA,OAEtBD,MAAMC,IACI,MAAND,KAAW,IAAIA,KAAM,IAAIC,IAEzBD,KAAMA,KAAKC,KAAMA;AAAAA;AAAAA,SAKV7B,EAAM8B;EAAAA,OACdC,KAAUD,aAAkBE;AAAAA;AAAAA,SAIpB/B,EAAM6B;EAAAA,OACdG,KAAUH,aAAkBI;AAAAA;AAAAA,SAGpBC,EAAOnB;EAAAA,OACfA,EAAMoB,KAASpB,EAAMb;AAAAA;AAAAA,SAIbkC,EAAYC;EAAAA,IACvB1C,MAAMC,QAAQyC,IAAO,OAAO1C,MAAMwB,UAAUmB,MAAMjD,KAAKgD;EAAAA,IACrDE,IAAcC,GAA0BH;EAAAA,OACvCE,EAAYzD;EAAAA,SACf0B,IAAOC,GAAQ8B,IACVE,IAAI,GAAGA,IAAIjC,EAAKhC,QAAQiE,KAAK;IAAA,IAC/B9B,IAAWH,EAAKiC;MAChBC,IAAOH,EAAY5B;IAAAA,CACH,MAAlB+B,EAAKC,aACRD,EAAKC,YAAW,GAChBD,EAAKE,gBAAe,KAKjBF,EAAKtB,OAAOsB,EAAKrB,SACpBkB,EAAY5B,KAAO;MAClBiC,eAAc;MACdD,WAAU;MACVE,YAAYH,EAAKG;MACjBhE,OAAOwD,EAAK1B;IAAAA;EAAAA;EAAAA,OAGR1B,OAAO6D,OAAO7D,OAAOC,eAAemD,IAAOE;AAAAA;AAAAA,SAWnCQ,EAAU3C,GAAU4C;EAAAA,6BAAgB,IAC/CC,EAAS7C,MAAQxB,EAAQwB,OAASrB,EAAYqB,KAAaA,KAC3DG,EAAYH,KAAO,MACtBA,EAAIiB,MAAMjB,EAAIqB,MAAMrB,EAAI8C,QAAQ9C,EAAIoB,SAAS2B,IAE9ClE,OAAO8D,OAAO3C,IACV4C,KAAM7C,EAAKC,GAAK,UAACO,GAAK9B;IAAAA,OAAUkE,EAAOlE,IAAO;EAAA,IAAO,IAClDuB;AAAAA;AAGR,SAAS+C;EACRnF,EAAI;AAAA;AAAA,SAGWiF,EAAS7C;EAAAA,OACb,QAAPA,KAA8B,mBAARA,KAEnBnB,OAAOgE,SAAS7C;AAAAA;AAAAA,SCzKRgD,EACfC;EAAAA,IAEMC,IAASC,GAAQF;EAAAA,OAClBC,KACJtF,EAAI,IAAIqF,IAGFC;AAAAA;AAAAA,SAGQE,EACfH,GACAI;EAEKF,GAAQF,OAAYE,GAAQF,KAAaI;AAAAA;AClC/C,SAAgBC;EAAAA,wBACXC,wBAAYC,KAAc5F,EAAI,IAC3B4F;AAAAA;AAAAA,SAkBQC,EACfC,GACAC;EAEIA,MACHX,EAAU,YACVU,EAAME,IAAW,IACjBF,EAAMG,IAAkB,IACxBH,EAAMI,IAAiBH;AAAAA;AAAAA,SAITI,EAAYL;EAC3BM,EAAWN,IACXA,EAAMO,EAAQ3D,QAAQ4D,IAEtBR,EAAMO,IAAU;AAAA;AAAA,SAGDD,EAAWN;EACtBA,MAAUF,MACbA,IAAeE,EAAMS;AAAAA;AAAAA,SAIPC,EAAWC;EAAAA,OAClBb,IArCD;IACNS,GAAS;IACTE,GAmCkCX;IAlClCc,GAkCgDD;IA/BhDE,IAAgB;IAChBC,GAAoB;EAAA;AAAA;AAiCtB,SAASN,EAAYO;EAAAA,IACd9D,IAAoB8D,EAAM/F;EAAAA,MAE/BiC,EAAMC,WACND,EAAMC,IAEND,EAAM+D,MACF/D,EAAMgE,KAAW;AAAA;AAAA,SC9DPC,EAAcC,GAAanB;EAC1CA,EAAMc,IAAqBd,EAAMO,EAAQ7F;EAAAA,IACnC0G,IAAYpB,EAAMO,EAAS;IAC3Bc,SAAwBC,MAAXH,KAAwBA,MAAWC;EAAAA,OACjDpB,EAAMY,EAAOW,KACjBjC,EAAU,OAAOkC,EAAiBxB,GAAOmB,GAAQE,IAC9CA,KACCD,EAAUpG,GAAayG,MAC1BpB,EAAYL,IACZ9F,EAAI,KAEDe,EAAYkG,OAEfA,IAASO,EAAS1B,GAAOmB,IACpBnB,EAAMS,KAASkB,EAAY3B,GAAOmB,KAEpCnB,EAAME,KACTZ,EAAU,WAAWsC,EACpBR,EAAUpG,GAAaoB,GACvB+E,GACAnB,EAAME,GACNF,EAAMG,MAKRgB,IAASO,EAAS1B,GAAOoB,GAAW,KAErCf,EAAYL,IACRA,EAAME,KACTF,EAAMI,EAAgBJ,EAAME,GAAUF,EAAMG,IAEtCgB,MAAWU,IAAUV,SAASG;AAAAA;AAGtC,SAASI,EAASI,GAAuB/G,GAAYgH;EAAAA,IAEhD5C,EAASpE,IAAQ,OAAOA;EAAAA,IAEtBkC,IAAoBlC,EAAMC;EAAAA,KAE3BiC,UACJZ,EACCtB,GACA,UAAC8B,GAAKmF;IAAAA,OACLC,EAAiBH,GAAW7E,GAAOlC,GAAO8B,GAAKmF,GAAYD;EAAAA,IAC5D,IAEMhH;EAAAA,IAGJkC,EAAMiF,MAAWJ,GAAW,OAAO/G;EAAAA,KAElCkC,EAAMwE,UACVE,EAAYG,GAAW7E,EAAMb,IAAO,IAC7Ba,EAAMb;EAAAA,KAGTa,EAAMkF,GAAY;IACtBlF,EAAMkF,KAAa,GACnBlF,EAAMiF,EAAOpB;IAAAA,IACPK,UAELlE,EAAMC,WAAiCD,EAAMC,IACzCD,EAAMoB,IAAQC,EAAYrB,EAAMmF,KACjCnF,EAAMoB;IAKVhC,QACCY,EAAMC,IAA0B,IAAIiB,IAAIgD,KAAUA,GAClD,UAACtE,GAAKmF;MAAAA,OACLC,EAAiBH,GAAW7E,GAAOkE,GAAQtE,GAAKmF,GAAYD;IAAAA,IAG9DJ,EAAYG,GAAWX,IAAQ,IAE3BY,KAAQD,EAAU5B,KACrBZ,EAAU,WAAW+C,EACpBpF,GACA8E,GACAD,EAAU5B,GACV4B,EAAU3B;EAAAA;EAAAA,OAINlD,EAAMoB;AAAAA;AAGd,SAAS4D,EACRH,GACAQ,GACAC,GACAnF,GACA4E,GACAQ;EAAAA,qBAEI3C,wBAAWmC,MAAeO,KAAcrI,EAAI,IAC5CY,EAAQkH,IAAa;IAAA,IASlBS,IAAMf,EAASI,GAAWE,GAP/BQ,KACAF,WACAA,EAAapF,MACZC,EAAKmF,EAA8CI,GAAYtF,KAC7DoF,EAAUG,OAAOvF,UACjBkE;IAAAA,IAGJ/D,EAAIgF,GAAcnF,GAAMqF,KAGpB3H,EAAQ2H,IAEL;IADNX,EAAUjB,KAAiB;EAAA;EAAA,IAIzB5F,EAAY+G,OAAgB7C,EAAS6C,IAAa;IAAA,KAChDF,EAAUlB,EAAOgC,KAAed,EAAUhB,IAAqB;IAQpEY,EAASI,GAAWE,IAEfM,KAAgBA,EAAYJ,EAAOzB,KACvCkB,EAAYG,GAAWE;EAAAA;AAAAA;AAI1B,SAASL,EAAY3B,GAAmBjF,GAAYmE;EAAAA,sBAAO,IACtDc,EAAMY,EAAOgC,KAAe5C,EAAMa,KACrC5B,EAAOlE,GAAOmE;AAAAA;AC8EhB,SAAS2D,EAAK9B,GAAgB3D;EAAAA,IACvBH,IAAQ8D,EAAM/F;EAAAA,QACLiC,IAAQmB,EAAOnB,KAAS8D,GACzB3D;AAAAA;AAcf,SAAS0F,EACRC,GACA3F;EAAAA,IAGMA,KAAQ2F,YACV7H,IAAQC,OAAOC,eAAe2H,IAC3B7H,IAAO;IAAA,IACP0D,IAAOzD,OAAO6H,yBAAyB9H,GAAOkC;IAAAA,IAChDwB,GAAM,OAAOA;IACjB1D,IAAQC,OAAOC,eAAeF;EAAAA;AAAAA;AAAAA,SAKhB+H,EAAYhG;EACtBA,EAAMwE,MACVxE,EAAMwE,KAAY,GACdxE,EAAMwD,KACTwC,EAAYhG,EAAMwD;AAAAA;AAAAA,SAKLyC,EAAYjG;EACtBA,EAAMoB,MACVpB,EAAMoB,IAAQC,EAAYrB,EAAMb;AAAAA;ACnDlC,SAAgB+G,EACfxC,GACA5F,GACAqI;EAAAA,IAGMrC,IAAiB9E,EAAMlB,KAC1BuE,EAAU,UAAU+D,EAAUtI,GAAOqI,KACrClH,EAAMnB,KACNuE,EAAU,UAAUgE,EAAUvI,GAAOqI,KACrCzC,EAAMY,cD1LThD,GACA6E;IAAAA,IAEMtH,IAAUD,MAAMC,QAAQyC;MACxBtB,IAAoB;QACzBC,GAAOpB,QAAkC;QAEzCoG,GAAQkB,IAASA,EAAOlB,IAAStC;QAEjC6B,IAAW;QAEXU,IAAY;QAEZO,GAAW;QAEXjC,GAAS2C;QAEThH,GAAOmC;QAEP6D,GAAQ;QAER/D,GAAO;QAEP2C,GAAS;QACTuC,IAAW;MAAA;MASRxF,IAAYd;MACZuG,IAA2CC;IAC3C3H,MACHiC,IAAS,CAACd,IACVuG,IAAQE;IAAAA,QAGeC,MAAMC,UAAU7F,GAAQyF;MAAzCK;MAAQC;IAAAA,OACf7G,EAAMmF,IAAS0B,GACf7G,EAAM+D,IAAU6C,GACTC;EAAAA,CCgJJC,CAAiBhJ,GAAOqI,KACxB9D,EAAU,OAAO0E,EAAgBjJ,GAAOqI;EAAAA,QAE7BA,IAASA,EAAOlB,IAAStC,KACjCW,EAAQ0D,KAAKlD,IACZA;AAAAA;AAAAA,SCjOQmD,EAAQnJ;EAAAA,OAClBD,EAAQC,MAAQb,EAAI,IAAIa,IAI9B,SAASoJ,EAAYpJ;IAAAA,KACfE,EAAYF,IAAQ,OAAOA;IAAAA,IAE5BqJ;MADEnH,IAAgClC,EAAMC;MAEtCqJ,IAAW5H,EAAY1B;IAAAA,IACzBkC,GAAO;MAAA,KAERA,EAAMwE,MACNxE,EAAMC,IAAQ,MAAMoC,EAAU,OAAOgF,EAAYrH,KAElD,OAAOA,EAAMb;MAEda,EAAMkF,KAAa,GACnBiC,IAAOG,EAAWxJ,GAAOsJ,IACzBpH,EAAMkF,KAAa;IAAA,OAEnBiC,IAAOG,EAAWxJ,GAAOsJ;IAAAA,OAG1BhI,EAAK+H,GAAM,UAACvH,GAAKmF;MACZ/E,KAASK,EAAIL,EAAMb,GAAOS,OAASmF,KACvCzE,EAAI6G,GAAMvH,GAAKsH,EAAYnC;IAAAA,UAGrBqC,IAA4B,IAAIlG,IAAIiG,KAAQA;EAAAA,CA3B5CD,CAAYpJ;AAAAA;AA8BpB,SAASwJ,EAAWxJ,GAAYsJ;EAAAA,QAEvBA;IAAAA;MAAAA,OAEC,IAAIpG,IAAIlD;IAAAA;MAAAA,OAGRc,MAAM2I,KAAKzJ;EAAAA;EAAAA,OAEbuD,EAAYvD;AAAAA;AAAAA,SClCJ0J;EAAAA,SA8ENC,EACRtH,GACA2B;IAAAA,IAEIH,IAAOH,EAAYrB;IAAAA,OACnBwB,IACHA,EAAKG,aAAaA,IAElBN,EAAYrB,KAAQwB,IAAO;MAC1BE,eAAc;MACdC;MACAzB;QAAAA,IACOL,IAAQ0H,KAAK3J;QAAAA,gDACN4J,EAAgB3H,IAEtBwG,GAAYnG,IAAIL,GAAOG;MAAAA;MAE/BG,eAAexC;QAAAA,IACRkC,IAAQ0H,KAAK3J;QAAAA,yCACN4J,EAAgB3H,IAE7BwG,GAAYlG,IAAIN,GAAOG,GAAMrC;MAAAA;IAAAA,GAIzB6D;EAAAA;EAAAA,SAICiG,EAAiBC;IAAAA,KAKpB,IAAInG,IAAImG,EAAOpK,SAAS,GAAGiE,KAAK,GAAGA,KAAK;MAAA,IACtC1B,IAAkB6H,EAAOnG,GAAG3D;MAAAA,KAC7BiC,EAAMwE,WACFxE,EAAMC;QAAAA;UAER6H,EAAgB9H,MAAQgG,EAAYhG;UAAAA;QAAAA;UAGpC+H,EAAiB/H,MAAQgG,EAAYhG;MAAAA;IAAAA;EAAAA;EAAAA,SA6DrC+H,EAAiB/H;IAAAA,SAClBb,IAAiBa,EAAjBb,GAAOgG,IAAUnF,EAAVmF,GAIR1F,IAAOC,GAAQyF,IACZzD,IAAIjC,EAAKhC,SAAS,GAAGiE,KAAK,GAAGA,KAAK;MAAA,IACpC9B,IAAWH,EAAKiC;MAAAA,IAClB9B,MAAQ7B;QAAAA,IACNiK,IAAY7I,EAAMS;QAAAA,SAENyE,MAAd2D,MAA4B9H,EAAIf,GAAOS,YACnC;QAAA,IAKD9B,IAAQqH,EAAOvF;UACfI,IAAoBlC,KAASA,EAAMC;QAAAA,IACrCiC,IAAQA,EAAMb,MAAU6I,KAAarH,EAAG7C,GAAOkK,YAC3C;MAAA;IAAA;IAAA,IAOJC,MAAgB9I,EAAMpB;IAAAA,OACrB0B,EAAKhC,WAAWiC,GAAQP,GAAO1B,UAAUwK,IAAc,IAAI;EAAA;EAAA,SAG1DH,EAAgB9H;IAAAA,IACjBmF,IAAUnF,EAAVmF;IAAAA,IACHA,EAAO1H,WAAWuC,EAAMb,EAAM1B,QAAQ,QAAO;IAAA,IAS3CyK,IAAahK,OAAO6H,yBACzBZ,GACAA,EAAO1H,SAAS;IAAA,IAGbyK,MAAeA,EAAW7H,KAAK,QAAO;IAAA,KAErC,IAAIqB,IAAI,GAAGA,IAAIyD,EAAO1H,QAAQiE,UAC7ByD,EAAO9G,eAAeqD,IAAI,QAAO;IAAA,QAGhC;EAAA;EAAA,SASCiG,EAAgB3H;IACpBA,EAAMgE,KAAU/G,EAAI,GAAGkL,KAAKC,UAAUjH,EAAOnB;EAAAA;EAAAA,IAxK5CwB,IAAoD;EA2K1DiB,EAAW,OAAO;IACjBsE,aA5MAzF,GACA6E;MAAAA,IAEMtH,IAAUD,MAAMC,QAAQyC;QACxBwC,cA1BiBjF,GAAkByC;UAAAA,IACrCzC,GAAS;YAAA,SACNiF,IAAYlF,MAAM0C,EAAK7D,SACpBiE,IAAI,GAAGA,IAAIJ,EAAK7D,QAAQiE,KAChCxD,OAAOmK,eAAevE,GAAO,KAAKpC,GAAG+F,EAAc/F,IAAG;YAAA,OAChDoC;UAAAA;UAAAA,IAEDtC,IAAcC,GAA0BH;UAAAA,OACvCE,EAAYzD;UAAAA,SACb0B,IAAOC,GAAQ8B,IACZE,IAAI,GAAGA,IAAIjC,EAAKhC,QAAQiE,KAAK;YAAA,IAC/B9B,IAAWH,EAAKiC;YACtBF,EAAY5B,KAAO6H,EAClB7H,GACAf,OAAa2C,EAAY5B,GAAKkC;UAAAA;UAAAA,OAGzB5D,OAAO6D,OAAO7D,OAAOC,eAAemD,IAAOE;QAAAA,CASrC8G,CAAezJ,GAASyC;QAEhCtB,IAAwC;UAC7CC,GAAOpB,QAAgC;UACvCoG,GAAQkB,IAASA,EAAOlB,IAAStC;UACjC6B,IAAW;UACXU,IAAY;UACZO,GAAW;UACXjC,GAAS2C;UAEThH,GAAOmC;UAEP6D,GAAQrB;UACR1C,GAAO;UACP4C,IAAU;UACVsC,IAAW;QAAA;MAAA,OAGZpI,OAAOmK,eAAevE,GAAO/F,GAAa;QACzCD,OAAOkC;QAEP4B,WAAU;MAAA,IAEJkC;IAAAA;IAkLPS,aAvPAxB,GACAmB,GACAE;MAEKA,IASJvG,EAAQqG,MACPA,EAAOnG,GAA0BkH,MAAWlC,KAE7C6E,EAAiB7E,EAAMO,MAXnBP,EAAME,cAwHHsF,EAAuBC;QAAAA,IAC1BA,KAA4B,mBAAXA;UAAAA,IAChBxI,IAA8BwI,EAAOzK;UAAAA,IACtCiC;YAAAA,IACEb,IAAmCa,EAAnCb;cAAOgG,IAA4BnF,EAA5BmF;cAAQM,IAAoBzF,EAApByF;cAAWxF,IAASD,EAATC;YAAAA,UAC7BA,GAKHb,EAAK+F,GAAQ;cACPvF,MAAgB7B,WAEOsG,MAAvBlF,EAAcS,MAAuBM,EAAIf,GAAOS,KAGzC6F,EAAU7F,MAErB2I,EAAuBpD,EAAOvF,OAJ9B6F,EAAU7F,MAAO,GACjBoG,EAAYhG;YAAAA,IAOdZ,EAAKD,GAAO;cAAAS,KAESyE,MAAhBc,EAAOvF,MAAuBM,EAAIiF,GAAQvF,OAC7C6F,EAAU7F,MAAO,GACjBoG,EAAYhG;YAAAA,QAGR,UAAIC,GAA8B;cAAA,IACpC6H,EAAgB9H,OACnBgG,EAAYhG,IACZyF,EAAUhI,UAAS,IAGhB0H,EAAO1H,SAAS0B,EAAM1B,aACpB,IAAIiE,IAAIyD,EAAO1H,QAAQiE,IAAIvC,EAAM1B,QAAQiE,KAAK+D,EAAU/D,MAAK,YAE7D,IAAIA,IAAIvC,EAAM1B,QAAQiE,IAAIyD,EAAO1H,QAAQiE,KAAK+D,EAAU/D,MAAK;cAAA,SAI7D+G,IAAMC,KAAKD,IAAItD,EAAO1H,QAAQ0B,EAAM1B,SAEjCiE,IAAI,GAAGA,IAAI+G,GAAK/G,KAEnByD,EAAO9G,eAAeqD,OAC1B+D,EAAU/D,MAAK,SAEK2C,MAAjBoB,EAAU/D,MAAkB6G,EAAuBpD,EAAOzD;YAAAA;UAAAA;QAAAA;MAAAA,CAxK9D6G,CAAuBxF,EAAMO,EAAS,KAGvCsE,EAAiB7E,EAAMO;IAAAA;IA+OxB+D,aAboBrH;MAAAA,aACbA,EAAMC,IACV8H,EAAiB/H,KACjB8H,EAAgB9H;IAAAA;EAAAA;AAAAA;AAAAA,SC9OL2I;EAAAA,SAyPNC,EAAoBvJ;IAAAA,KACvBrB,EAAYqB,IAAM,OAAOA;IAAAA,IAC1BT,MAAMC,QAAQQ,IAAM,OAAOA,EAAI3B,IAAIkL;IAAAA,IACnC5J,EAAMK,IACT,OAAO,IAAI2B,IACVpC,MAAM2I,KAAKlI,EAAIwJ,WAAWnL,IAAI;MAAA,OAAY,OAAIkL;IAAAA;IAAAA,IAE5C3J,EAAMI,IAAM,OAAO,IAAI6B,IAAItC,MAAM2I,KAAKlI,GAAK3B,IAAIkL;IAAAA,IAC7CE,IAAS5K,OAAO6D,OAAO7D,OAAOC,eAAekB;IAAAA,KAC9C,IAAMO,KAAOP,GAAKyJ,EAAOlJ,KAAOgJ,EAAoBvJ,EAAIO;IAAAA,OACzDM,EAAIb,GAAK0J,OAAYD,EAAOC,KAAa1J,EAAI0J,KAC1CD;EAAAA;EAAAA,SAGCE,EAA2B3J;IAAAA,OAC/BxB,EAAQwB,KACJuJ,EAAoBvJ,KACdA;EAAAA;EAAAA,IAxQT4J,IAAM;EA2QZxG,EAAW,WAAW;IACrByG,aA9FyBpF,GAAUqF;MAAAA,OACnCA,EAAQxJ,QAAQ;QAAAyJ,SACRtE,IAAYsE,EAAZtE,MAAMuE,IAAMD,EAANC,IAET/H,IAAYwC,GACPpC,IAAI,GAAGA,IAAIoD,EAAKrH,SAAS,GAAGiE,KAAK;UAAA,IACnC4H,IAAa9J,EAAY8B;YACzBiI,IAAI,KAAKzE,EAAKpD;UAAAA,MAGlB4H,WAAkCA,KAC5B,gBAANC,KAA2B,kBAANA,KAEtBtM,EAAI,KACe,qBAATqE,KAA6B,gBAANiI,KAAmBtM,EAAI,KAErC,oBADpBqE,IAAOjB,EAAIiB,GAAMiI,OACatM,EAAI,IAAI6H,EAAKlH,KAAK;QAAA;QAAA,IAG3C4L,IAAOhK,EAAY8B;UACnBxD,IAAQ8K,EAAoBQ,EAAMtL;UAClC8B,IAAMkF,EAAKA,EAAKrH,SAAS;QAAA,QACvB4L;UAAAA,KArMM;YAAA,QAuMJG;cAAAA;gBAAAA,OAEClI,EAAKhB,IAAIV,GAAK9B;cAAAA;gBAGrBb,EAAI;cAAA;gBAAA,OAMIqE,EAAK1B,KAAO9B;YAAAA;UAAAA,KAElBmL;YAAAA,QACIO;cAAAA;gBAAAA,OAES,QAAR5J,IACJ0B,EAAK0F,KAAKlJ,KACVwD,EAAKmI,OAAO7J,GAAY,GAAG9B;cAAAA;gBAAAA,OAEvBwD,EAAKhB,IAAIV,GAAK9B;cAAAA;gBAAAA,OAEdwD,EAAKZ,IAAI5C;cAAAA;gBAAAA,OAERwD,EAAK1B,KAAO9B;YAAAA;UAAAA,KA7NX;YAAA,QAgOH0L;cAAAA;gBAAAA,OAEClI,EAAKmI,OAAO7J,GAAY;cAAA;gBAAA,OAExB0B,EAAKb,OAAOb;cAAAA;gBAAAA,OAEZ0B,EAAKb,OAAO2I,EAAMtL;cAAAA;gBAAAA,cAEXwD,EAAK1B;YAAAA;UAAAA;YAGrB3C,EAAI,IAAIoM;QAAAA;MAAAA,IAIJvF;IAAAA;IA6BPsB,aAzQApF,GACA0J,GACAP,GACAQ;MAAAA,QAEQ3J,EAAMC;QAAAA;QAAAA;QAAAA;UAAAA,iBAgFdD,GACA0J,GACAP,GACAQ;YAAAA,IAEOxK,IAAgBa,EAAhBb;cAAOiC,IAASpB,EAAToB;YACdhC,EAAKY,EAAMyF,GAAY,UAAC7F,GAAKgK;cAAAA,IACtBC,IAAYxJ,EAAIlB,GAAOS;gBACvB9B,IAAQuC,EAAIe,GAAQxB;gBACpByJ,IAAMO,IAAyB1J,EAAIf,GAAOS,KAnGlC,YAmGmDqJ,IAjGpD;cAAA,IAkGTY,MAAc/L,KApGJ,cAoGauL;gBAAAA,IACrBvE,IAAO4E,EAAShE,OAAO9F;gBAC7BuJ,EAAQnC,KApGK,aAoGAqC,IAAgB;kBAACA;kBAAIvE;gBAAAA,IAAQ;kBAACuE;kBAAIvE;kBAAMhH;gBAAAA,IACrD6L,EAAe3C,KACdqC,MAAOJ,IACJ;kBAACI,IAvGQ;kBAuGIvE;gBAAAA,IAvGJ,aAwGTuE,IACA;kBAACA,IAAIJ;kBAAKnE;kBAAMhH,OAAOkL,EAAwBa;gBAAAA,IAC/C;kBAACR,IA5GS;kBA4GIvE;kBAAMhH,OAAOkL,EAAwBa;gBAAAA;cAAAA;YAAAA;UAAAA,CA9F/CC,CACN9J,GACA0J,GACAP,GACAQ;QAAAA;QAAAA;UAAAA,iBAgBH3J,GACA0J,GACAP,GACAQ;YAAAA,IAEKxK,IAAoBa,EAApBb;cAAOsG,IAAazF,EAAbyF;cACRrE,IAAQpB,EAAMoB;YAAAA,IAGdA,EAAM3D,SAAS0B,EAAM1B,QAAQ;cAAA,QAEd,CAAC2D,GAAOjC;cAAxBA,UAAOiC;cAAAA,QACoB,CAACuI,GAAgBR;cAA5CA,UAASQ;YAAAA;YAAAA,KAIP,IAAIjI,IAAI,GAAGA,IAAIvC,EAAM1B,QAAQiE,SAC7B+D,EAAU/D,MAAMN,EAAMM,OAAOvC,EAAMuC,IAAI;cAAA,IACpCoD,IAAO4E,EAAShE,OAAO,CAAChE;cAC9ByH,EAAQnC,KAAK;gBACZqC,IAtDY;gBAuDZvE;gBAGAhH,OAAOkL,EAAwB5H,EAAMM;cAAAA,IAEtCiI,EAAe3C,KAAK;gBACnBqC,IA7DY;gBA8DZvE;gBACAhH,OAAOkL,EAAwB7J,EAAMuC;cAAAA;YAAAA;YAAAA,KAMnC,IAAIA,IAAIvC,EAAM1B,QAAQiE,IAAIN,EAAM3D,QAAQiE,KAAK;cAAA,IAC3CoD,IAAO4E,EAAShE,OAAO,CAAChE;cAC9ByH,EAAQnC,KAAK;gBACZqC,IAAIJ;gBACJnE;gBAGAhH,OAAOkL,EAAwB5H,EAAMM;cAAAA;YAAAA;YAGnCvC,EAAM1B,SAAS2D,EAAM3D,UACxBkM,EAAe3C,KAAK;cACnBqC,IAjFa;cAkFbvE,MAAM4E,EAAShE,OAAO,CAAC;cACvB5H,OAAOqB,EAAM1B;YAAAA;UAAAA,CA7DNsM,CAAqB/J,GAAO0J,GAAUP,GAASQ;QAAAA;UAAAA,iBA4FxD3J,GACA0J,GACAP,GACAQ;YAAAA,IAEKxK,IAAgBa,EAAhBb;cAAOiC,IAASpB,EAAToB;cAERM,IAAI;YACRvC,EAAMQ,QAAQ,UAAC7B;cAAAA,KACTsD,EAAOlB,IAAIpC,IAAQ;gBAAA,IACjBgH,IAAO4E,EAAShE,OAAO,CAAChE;gBAC9ByH,EAAQnC,KAAK;kBACZqC,IA5HW;kBA6HXvE;kBACAhH;gBAAAA,IAED6L,EAAeK,QAAQ;kBACtBX,IAAIJ;kBACJnE;kBACAhH;gBAAAA;cAAAA;cAGF4D;YAAAA,IAEDA,IAAI,GACJN,EAAOzB,QAAQ,UAAC7B;cAAAA,KACVqB,EAAMe,IAAIpC,IAAQ;gBAAA,IAChBgH,IAAO4E,EAAShE,OAAO,CAAChE;gBAC9ByH,EAAQnC,KAAK;kBACZqC,IAAIJ;kBACJnE;kBACAhH;gBAAAA,IAED6L,EAAeK,QAAQ;kBACtBX,IAlJW;kBAmJXvE;kBACAhH;gBAAAA;cAAAA;cAGF4D;YAAAA;UAAAA,CAjIQuI,CACLjK,GACD0J,GACAP,GACAQ;MAAAA;IAAAA;IAmPHhF,aAjHAqD,GACAkC,GACAf,GACAQ;MAEAR,EAAQnC,KAAK;QACZqC,IApKc;QAqKdvE,MAAM;QACNhH,OAAOoM,MAAgBtF,SAAUP,IAAY6F;MAAAA,IAE9CP,EAAe3C,KAAK;QACnBqC,IAzKc;QA0KdvE,MAAM;QACNhH,OAAOkK;MAAAA;IAAAA;EAAAA;AAAAA;ACrMV,SAmBgBmC;EAAAA,SAgBNC,EAAUC,GAAQC;IAAAA,SAEjBC;MAAAA,KACHhM,cAAc8L;IAAAA;IAFpBG,EAAcH,GAAGC,IAIjBD,EAAEjK,aAECmK,EAAGnK,YAAYkK,EAAElK,WAAY,IAAImK;EAAAA;EAAAA,SA8J5BE,EAAezK;IAClBA,EAAMoB,MACVpB,EAAMyF,IAAY,IAAIzE,OACtBhB,EAAMoB,IAAQ,IAAIJ,IAAIhB,EAAMb;EAAAA;EAAAA,SA0HrBuL,EAAe1K;IAClBA,EAAMoB,MAEVpB,EAAMoB,IAAQ,IAAIF,OAClBlB,EAAMb,EAAMQ,QAAQ;MAAA7B,IACfE,EAAYF,IAAQ;QAAA,IACjBgG,IAAQoC,EAAYlG,EAAMiF,EAAOtB,GAAQ7F,GAAOkC;QACtDA,EAAMsD,EAAQhD,IAAIxC,GAAOgG,IACzB9D,EAAMoB,EAAOV,IAAIoD;MAAAA,OAEjB9D,EAAMoB,EAAOV,IAAI5C;IAAAA;EAAAA;EAAAA,SAMZ6J,EAAgB3H;IACpBA,EAAMgE,KAAU/G,EAAI,GAAGkL,KAAKC,UAAUjH,EAAOnB;EAAAA;EAAAA,IAjU9CwK,IAAgB,UAASH,GAAQC;MAAAA,QACpCE,IACCtM,OAAOyM,kBACN;QAACC,WAAW;MAAA,aAAehM,SAC3B,UAASyL,GAAGC;QACXD,EAAEO,YAAYN;MAAAA,KAEhB,UAASD,GAAGC;QAAAA,KACN,IAAIf,KAAKe,GAAOA,EAAEjM,eAAekL,OAAIc,EAAEd,KAAKe,EAAEf;MAAAA,GAEhCc,GAAGC;IAAAA;IAcnBO,IAAY;MAAA,SAGRA,EAAoB/J,GAAgBqF;QAAAA,YACvCpI,KAAe;UACnBkC;UACAuD,GAAS2C;UACTlB,GAAQkB,IAASA,EAAOlB,IAAStC;UACjC6B,IAAW;UACXU,IAAY;UACZ9D,QAAOiD;UACPoB,QAAWpB;UACXlF,GAAO2B;UACPqE,GAAQuC;UACRpB,IAAW;UACXtC,IAAU;QAAA,GAEJ0D;MAAAA;MAhBR0C,EAAUS,GAmJR7J;MAAAA,IAjIIuI,IAAIsB,EAASzK;MAAAA,OAEnBlC,OAAOmK,eAAekB,GAAG,QAAQ;QAChClJ,KAAK;UAAA,OACGc,EAAOuG,KAAK3J,IAAc+M;QAAAA;MAAAA,IAMnCvB,EAAErJ,MAAM,UAASN;QAAAA,OACTuB,EAAOuG,KAAK3J,IAAcmC,IAAIN;MAAAA,GAGtC2J,EAAEjJ,MAAM,UAASV,GAAU9B;QAAAA,IACpBkC,IAAkB0H,KAAK3J;QAAAA,OAC7B4J,EAAgB3H,IACXmB,EAAOnB,GAAOE,IAAIN,MAAQuB,EAAOnB,GAAOK,IAAIT,OAAS9B,MACzD2M,EAAezK,IACfgG,EAAYhG,IACZA,EAAMyF,EAAWnF,IAAIV,IAAK,IAC1BI,EAAMoB,EAAOd,IAAIV,GAAK9B,IACtBkC,EAAMyF,EAAWnF,IAAIV,IAAK,KAEpB8H;MAAAA,GAGR6B,EAAE9I,SAAS,UAASb;QAAAA,KACd8H,KAAKxH,IAAIN,YACN;QAAA,IAGFI,IAAkB0H,KAAK3J;QAAAA,OAC7B4J,EAAgB3H,IAChByK,EAAezK,IACfgG,EAAYhG,IACRA,EAAMb,EAAMe,IAAIN,KACnBI,EAAMyF,EAAWnF,IAAIV,IAAK,KAE1BI,EAAMyF,EAAWhF,OAAOb,IAEzBI,EAAMoB,EAAOX,OAAOb,KACb;MAAA,GAGR2J,EAAEpH,QAAQ;QAAA,IACHnC,IAAkB0H,KAAK3J;QAC7B4J,EAAgB3H,IACZmB,EAAOnB,GAAO8K,SACjBL,EAAezK,IACfgG,EAAYhG,IACZA,EAAMyF,IAAY,IAAIzE,OACtB5B,EAAKY,EAAMb,GAAO;UACjBa,EAAMyF,EAAWnF,IAAIV,IAAK;QAAA,IAE3BI,EAAMoB,EAAOe;MAAAA,GAIfoH,EAAE5J,UAAU,UACXoL,GACAC;QAAAA;QAGA7J,EADwBuG,KAAK3J,IACf4B,QAAQ,UAACsL,GAAarL;UACnCmL,EAAGzM,KAAK0M,GAASE,EAAK7K,IAAIT,IAAMA,GAAKsL;QAAAA;MAAAA,GAIvC3B,EAAElJ,MAAM,UAAST;QAAAA,IACVI,IAAkB0H,KAAK3J;QAC7B4J,EAAgB3H;QAAAA,IACVlC,IAAQqD,EAAOnB,GAAOK,IAAIT;QAAAA,IAC5BI,EAAMkF,MAAelH,EAAYF,WAC7BA;QAAAA,IAEJA,MAAUkC,EAAMb,EAAMkB,IAAIT,WACtB9B;QAAAA,IAGFgG,IAAQoC,EAAYlG,EAAMiF,EAAOtB,GAAQ7F,GAAOkC;QAAAA,OACtDyK,EAAezK,IACfA,EAAMoB,EAAOd,IAAIV,GAAKkE,IACfA;MAAAA,GAGRyF,EAAE9J,OAAO;QAAA,OACD0B,EAAOuG,KAAK3J,IAAc0B;MAAAA,GAGlC8J,EAAE4B,SAAS;QAAA;UAAAC;UACJC,IAAW3D,KAAKjI;QAAAA,gBAEpB6L,KAAiB;UAAA,OAAMC,EAAKJ;QAAAA,KAC7BK,OAAM;UAAA,IACCJ,IAAIC,EAASG;UAAAA,OAEfJ,EAAEK,OAAaL,IAEZ;YACNK,OAAM;YACN3N,OAHayN,EAAKlL,IAAI+K,EAAEtN;UAAAA;QAAAA;MAAAA,GAS5ByL,EAAEV,UAAU;QAAA;UAAAuC;UACLC,IAAW3D,KAAKjI;QAAAA,gBAEpB6L,KAAiB;UAAA,OAAMI,EAAK7C;QAAAA,KAC7B2C,OAAM;UAAA,IACCJ,IAAIC,EAASG;UAAAA,IAEfJ,EAAEK,MAAM,OAAOL;UAAAA,IACbtN,IAAQ4N,EAAKrL,IAAI+K,EAAEtN;UAAAA,OAClB;YACN2N,OAAM;YACN3N,OAAO,CAACsN,EAAEtN,OAAOA;UAAAA;QAAAA;MAAAA,GAMrByL,EAAE+B,KAAkB;QAAA,OACZ5D,KAAKmB;MAAAA,GAGNgC;IAAAA,CAnJU;IAkKZc,IAAY;MAAA,SAGRA,EAAoB7K,GAAgBqF;QAAAA,YACvCpI,KAAe;UACnBkC;UACAuD,GAAS2C;UACTlB,GAAQkB,IAASA,EAAOlB,IAAStC;UACjC6B,IAAW;UACXU,IAAY;UACZ9D,QAAOiD;UACPlF,GAAO2B;UACPqE,GAAQuC;UACRpE,GAAS,IAAItC;UACbgD,IAAU;UACVsC,IAAW;QAAA,GAELoB;MAAAA;MAhBR0C,EAAUuB,GA8GRzK;MAAAA,IA5FIqI,IAAIoC,EAASvL;MAAAA,OAEnBlC,OAAOmK,eAAekB,GAAG,QAAQ;QAChClJ,KAAK;UAAA,OACGc,EAAOuG,KAAK3J,IAAc+M;QAAAA;MAAAA,IAKnCvB,EAAErJ,MAAM,UAASpC;QAAAA,IACVkC,IAAkB0H,KAAK3J;QAAAA,OAC7B4J,EAAgB3H,IAEXA,EAAMoB,MAGPpB,EAAMoB,EAAMlB,IAAIpC,SAChBkC,EAAMsD,EAAQpD,IAAIpC,OAAUkC,EAAMoB,EAAMlB,IAAIF,EAAMsD,EAAQjD,IAAIvC,OAH1DkC,EAAMb,EAAMe,IAAIpC;MAAAA,GAQzByL,EAAE7I,MAAM,UAAS5C;QAAAA,IACVkC,IAAkB0H,KAAK3J;QAAAA,OAC7B4J,EAAgB3H,IACX0H,KAAKxH,IAAIpC,OACb4M,EAAe1K,IACfgG,EAAYhG,IACZA,EAAMoB,EAAOV,IAAI5C,KAEX4J;MAAAA,GAGR6B,EAAE9I,SAAS,UAAS3C;QAAAA,KACd4J,KAAKxH,IAAIpC,YACN;QAAA,IAGFkC,IAAkB0H,KAAK3J;QAAAA,OAC7B4J,EAAgB3H,IAChB0K,EAAe1K,IACfgG,EAAYhG,IAEXA,EAAMoB,EAAOX,OAAO3C,QACnBkC,EAAMsD,EAAQpD,IAAIpC,MAChBkC,EAAMoB,EAAOX,OAAOT,EAAMsD,EAAQjD,IAAIvC;MAAAA,GAK3CyL,EAAEpH,QAAQ;QAAA,IACHnC,IAAkB0H,KAAK3J;QAC7B4J,EAAgB3H,IACZmB,EAAOnB,GAAO8K,SACjBJ,EAAe1K,IACfgG,EAAYhG,IACZA,EAAMoB,EAAOe;MAAAA,GAIfoH,EAAE4B,SAAS;QAAA,IACJnL,IAAkB0H,KAAK3J;QAAAA,OAC7B4J,EAAgB3H,IAChB0K,EAAe1K,IACRA,EAAMoB,EAAO+J;MAAAA,GAGrB5B,EAAEV,UAAU;QAAA,IACL7I,IAAkB0H,KAAK3J;QAAAA,OAC7B4J,EAAgB3H,IAChB0K,EAAe1K,IACRA,EAAMoB,EAAOyH;MAAAA,GAGrBU,EAAE9J,OAAO;QAAA,OACDiI,KAAKyD;MAAAA,GAGb5B,EAAE+B,KAAkB;QAAA,OACZ5D,KAAKyD;MAAAA,GAGb5B,EAAE5J,UAAU,UAAiBoL,GAASC;QAAAA,SAC/BK,IAAW3D,KAAKyD,UAClBjH,IAASmH,EAASG,SACdtH,EAAOuH,OACdV,EAAGzM,KAAK0M,GAAS9G,EAAOpG,OAAOoG,EAAOpG,OAAO4J,OAC7CxD,IAASmH,EAASG;MAAAA,GAIbG;IAAAA,CA9GU;EA0IlBlJ,EAAW,UAAU;IAAC2D,aAtJetF,GAAWqF;MAAAA,OAExC,IAAI0E,EAAS/J,GAAQqF;IAAAA;IAoJIE,aAzBIvF,GAAWqF;MAAAA,OAExC,IAAIwF,EAAS7K,GAAQqF;IAAAA;EAAAA;AAAAA;AAAAA,SC/TdyF;EACfpE,KACA2C,KACAxB;AAAAA;AAAAA,SC2FekD,EAAa/N;EAAAA,OACrBA;AAAAA;AAAAA,SAQQgO,EAAiBhO;EAAAA,OACzBA;AAAAA;AAAAA;ETnFJ+E;EUpBEkJ,IACa,sBAAXC,UAAiD,mBAAhBA,OAAO;EACnCjL,IAAwB,sBAARC;EAChBC,IAAwB,sBAARC;EAChB+K,IACK,sBAAVvF,cACoB,MAApBA,MAAMC,aACM,sBAAZuF;EAKKtH,IAAmBmH,IAC7BC,OAAOG,IAAI,6BACR,oBAAkB;EAUXrN,IAA2BiN,IACrCC,OAAOG,IAAI,qBACV;EAESpO,IAA6BgO,IACvCC,OAAOG,IAAI,iBACV;EAGSb,IACM,sBAAVU,UAAyBA,OAAOX,YAAc;EbvCjDhO,IAAS;IAAA,GACX;IAAA,GACA;IAAA,GACA;IAAA,aACD+O;MAAAA,OAEA,yHACAA;IAAAA;IAAAA,GAGC;IAAA,GACA;IAAA,GACA;IAAA,GACA;IAAA,GACA;IAAA,GACA;IAAA,IACC;IAAA,IACA;IAAA,IACA;IAAA,IACA;IAAA,IACA;IAAA,cACDtH;MAAAA,OACK,+CAA+CA;IAAAA;IAAAA,IAEnD;IAAA,cACDuE;MAAAA,OACK,kCAAkCA;IAAAA;IAAAA,cAEvC9G;MAAAA,4BACwBA,wFAAyFA;IAAAA;IAAAA,IAEhH;IAAA,cACDxC;MAAAA,+JAC2JA;IAAAA;IAAAA,cAE3JA;MAAAA,4CACwCA;IAAAA;IAAAA,cAExCA;MAAAA,6CACyCA;IAAAA;IAAAA,IAExC;EAAA;ECNCrB,IAAmBR,YAAOkC,UAAU7B;EA4B7BmB,KACO,sBAAZwM,WAA2BA,QAAQxM,UACvCwM,QAAQxM,eACgC,MAAjCxB,OAAOmO,wBACd;IAAAhN,OACAnB,OAAOoO,oBAAoBjN,GAAKqG,OAC/BxH,OAAOmO,sBAAsBhN;EAAAA,IAEHnB,OAAOoO;EAEzB7K,KACZvD,OAAOuD,6BACP,UAAmCX;IAAAA,IAE5B0E,IAAW;IAAA,OACjB9F,GAAQoB,GAAQnB,QAAQ;MACvB6F,EAAI5F,KAAO1B,OAAO6H,yBAAyBjF,GAAQlB;IAAAA,IAE7C4F;EAAAA;ECnEHhD,KA4BF;EGyDSgE,KAAwC;IACpDnG,eAAIL,GAAOG;MAAAA,IACNA,MAASpC,GAAa,OAAOiC;MAAAA,IAE3B8F,IAAS3E,EAAOnB;MAAAA,KACjBE,EAAI4F,GAAQ3F,WAwInB,UAA2BH,GAAmB8F,GAAa3F;QAAAA;UACpDwB,IAAOkE,EAAuBC,GAAQ3F;QAAAA,OACrCwB,IACJ,WAAWA,IACVA,EAAK7D,sBAGL6D,EAAKtB,gCAALkM,EAAUjO,KAAK0B,EAAMmF,UACtBd;MAAAA,CA9IMmI,CAAkBxM,GAAO8F,GAAQ3F;MAAAA,IAEnCrC,IAAQgI,EAAO3F;MAAAA,OACjBH,EAAMkF,MAAelH,EAAYF,KAC7BA,IAIJA,MAAU8H,EAAK5F,EAAMb,GAAOgB,MAC/B8F,EAAYjG,IACJA,EAAMoB,EAAOjB,KAAe+F,EACnClG,EAAMiF,EAAOtB,GACb7F,GACAkC,MAGKlC;IAAAA;IAERoC,eAAIF,GAAOG;MAAAA,OACHA,KAAQgB,EAAOnB;IAAAA;IAEvBN,mBAAQM;MAAAA,OACAkM,QAAQxM,QAAQyB,EAAOnB;IAAAA;IAE/BM,eACCN,GACAG,GACArC;MAAAA,IAEM6D,IAAOkE,EAAuB1E,EAAOnB,IAAQG;MAAAA,IAC/CwB,uBAAMrB,YAGTqB,EAAKrB,IAAIhC,KAAK0B,EAAMmF,GAAQrH,KACrB;MAAA,KAEHkC,EAAMwE,GAAW;QAAA,IAGfyC,IAAUrB,EAAKzE,EAAOnB,IAAQG;UAE9BsM,IAAiCxF,uBAAUlJ;QAAAA,IAC7C0O,KAAgBA,EAAatN,MAAUrB,UAC1CkC,EAAMoB,EAAOjB,KAAQrC,GACrBkC,EAAMyF,EAAUtF,MAAQ,IACjB;QAAA,IAEJQ,EAAG7C,GAAOmJ,YAAuB5C,MAAVvG,KAAuBoC,EAAIF,EAAMb,GAAOgB,KAClE,QAAO;QACR8F,EAAYjG,IACZgG,EAAYhG;MAAAA;MAAAA,OAIZA,EAAMoB,EAAOjB,OAAUrC,KAEN,mBAAVA,WAEIuG,MAAVvG,KAAuBqC,KAAQH,EAAMoB,OAKvCpB,EAAMoB,EAAOjB,KAAQrC,GACrBkC,EAAMyF,EAAUtF,MAAQ,IACjB;IAAA;IAERuM,0BAAe1M,GAAOG;MAAAA,YAEWkE,MAA5BuB,EAAK5F,EAAMb,GAAOgB,MAAuBA,KAAQH,EAAMb,KAC1Da,EAAMyF,EAAUtF,MAAQ,GACxB8F,EAAYjG,IACZgG,EAAYhG,aAGLA,EAAMyF,EAAUtF,IAGpBH,EAAMoB,YAAcpB,EAAMoB,EAAMjB,KAC7B;IAAA;IAIR4F,oCAAyB/F,GAAOG;MAAAA,IACzBwM,IAAQxL,EAAOnB;QACf2B,IAAOuK,QAAQnG,yBAAyB4G,GAAOxM;MAAAA,OAChDwB,IACE;QACNC,WAAU;QACVC,oBAAc7B,EAAMC,KAA2C,aAATE;QACtD2B,YAAYH,EAAKG;QACjBhE,OAAO6O,EAAMxM;MAAAA,IALIwB;IAAAA;IAQnB0G;MACCpL,EAAI;IAAA;IAELkB,0BAAe6B;MAAAA,OACP9B,OAAOC,eAAe6B,EAAMb;IAAAA;IAEpCwL;MACC1N,EAAI;IAAA;EAAA;EAQAwJ,KAA8C;AACpDrH,EAAKoH,IAAa,UAAC5G,GAAKgN;EAEvBnG,GAAW7G,KAAO;IAAA,OACjBiN,UAAU,KAAKA,UAAU,GAAG,IACrBD,EAAGrP,MAAMmK,MAAMmF;EAAAA;AAAAA,IAGxBpG,GAAWiG,iBAAiB,UAAS1M,GAAOG;EAAAA,wBACvCyC,wBAAWkK,MAAMC,SAAS5M,OAAelD,EAAI,KAE1CwJ,GAAWnG,IAAKhC,KAAKoJ,MAAM1H,GAAOG,QAAMkE;AAAAA,GAEhDoC,GAAWnG,MAAM,UAASN,GAAOG,GAAMrC;EAAAA,wBAClC8E,wBAAoB,aAATzC,KAAqB2M,MAAMC,SAAS5M,OAAelD,EAAI,KAC/DuJ,GAAYlG,IAAKhC,KAAKoJ,MAAM1H,EAAM,IAAIG,GAAMrC,GAAOkC,EAAM;AAAA;AAAA,ICpMpDgN,KAAb;IAAA,WAKaC;MAAAA;MAAAA,SAJWhB,aAEA,kBA4BH,UAAC3K,GAAW4L,GAAclK;QAAAA,IAEzB,qBAAT1B,KAAyC,qBAAX4L,GAAuB;UAAA,IACzDC,IAAcD;UACpBA,IAAS5L;UAAAA,IAEH8L,IAAOlC;UAAAA,OACN,UAEN5J;YAAAA;YAAAA,qBAAO6L;YAAAA,+BACJhQ;YAAAA,OAEIiQ,EAAKC,QAAQ/L,GAAM,UAACwC;cAAAA;cAAAA,YAAmBoJ,GAAO5O,eAAKiN,GAAMzH,UAAU3G;YAAAA;UAAAA;QAAAA;QAAAA,IAQxE+G;QAAAA,IAJkB,qBAAXgJ,KAAuBjQ,EAAI,SAChBoH,MAAlBrB,KAAwD,qBAAlBA,KACzC/F,EAAI,IAKDe,EAAYsD,IAAO;UAAA,IAChByB,IAAQU,EAAWyH;YACnBrE,IAAQX,EAAYgF,GAAM5J,QAAM+C;YAClCiJ,KAAW;UAAA;YAEdpJ,IAASgJ,EAAOrG,IAChByG,KAAW;UAAA;YAGPA,IAAUlK,EAAYL,KACrBM,EAAWN;UAAAA;UAAAA,OAEM,sBAAZwK,WAA2BrJ,aAAkBqJ,UAChDrJ,EAAOsJ,KACb;YAAAtJ,OACCpB,EAAkBC,GAAOC,IAClBiB,EAAcC,GAAQnB;UAAAA,GAE9B;YAAA7F,MACCkG,EAAYL,IACN7F;UAAAA,MAIT4F,EAAkBC,GAAOC,IAClBiB,EAAcC,GAAQnB;QAAAA;QACvB,KAAKzB,KAAwB,mBAATA,GAAmB;UAAA,SAE9B+C,OADfH,IAASgJ,EAAO5L,QACU4C,IAAS5C,IAC/B4C,MAAWU,MAASV,SAASG,IAC7B6G,EAAKvF,KAAa3D,EAAOkC,IAAQ,IACjClB,GAAe;YAAA,IACZuG,IAAa;cACbkE,IAAc;YACpBpL,EAAU,WAAWsC,EAA4BrD,GAAM4C,GAAQqF,GAAGkE,IAClEzK,EAAcuG,GAAGkE;UAAAA;UAAAA,OAEXvJ;QAAAA;QACDjH,EAAI,IAAIqE;MAAAA,6BAG0B,UACzCA,GACA4L;QAAAA,IAGoB,qBAAT5L,UACH,UAACtB;UAAAA,+BAAe7C;UAAAA,OACtB+N,EAAKwC,mBAAmB1N,GAAO,UAAC8D;YAAAA,OAAexC,iBAAKwC,UAAU3G;UAAAA;QAAAA;QAAAA,IAG5DgM;UAAkBQ;UAChBzF,IAASgH,EAAKmC,QAAQ/L,GAAM4L,GAAQ,UAAC3D,GAAYkE;YACtDtE,IAAUI,GACVI,IAAiB8D;UAAAA;QAAAA,OAGK,sBAAZF,WAA2BrJ,aAAkBqJ,UAChDrJ,EAAOsJ,KAAK;UAAAG,OAAa,CAACA,GAAWxE,GAAUQ;QAAAA,KAEhD,CAACzF,GAAQiF,GAAUQ;MAAAA,GA5GQ,qBAAvBsD,uBAAQW,eAClBlG,KAAKmG,cAAcZ,EAAQW,aACM,qBAAvBX,uBAAQa,eAClBpG,KAAKqG,cAAcd,EAAQa;IAAAA;IAAAA;IAAAA,SA4G7BE,wBAAiC1M;MAC3BtD,EAAYsD,MAAOrE,EAAI,IACxBY,EAAQyD,OAAOA,IAAO2F,EAAQ3F;MAAAA,IAC5ByB,IAAQU,EAAWiE;QACnBb,IAAQX,EAAYwB,MAAMpG,QAAM+C;MAAAA,OACtCwC,EAAM9I,GAAauI,KAAY,GAC/BjD,EAAWN,IACJ8D;IAAAA,KAGRoH,wBACCnK,GACAd;MAAAA,IAEMhD,IAAoB8D,KAAUA,EAAc/F;MAAAA,0CAE5CiC,KAAUA,EAAMsG,KAAWrJ,EAAI,IAChC+C,EAAMkF,KAAYjI,EAAI;MAAA,IAEZ8F,IAAS/C,EAAjBiF;MAAAA,OACPnC,EAAkBC,GAAOC,IAClBiB,OAAcI,GAAWtB;IAAAA,KAQjCgL,0BAAcjQ;MAAAA,KACR6H,IAAc7H;IAAAA,KASpB+P,0BAAc/P;MACTA,MAAUmO,KACbhP,EAAI,UAEAqH,IAAcxG;IAAAA,KAGpBoQ,yBAAkC5M,GAAS6H;MAAAA,IAGtCzH;MAAAA,KACCA,IAAIyH,EAAQ1L,SAAS,GAAGiE,KAAK,GAAGA,KAAK;QAAA,IACnC0H,IAAQD,EAAQzH;QAAAA,IACI,MAAtB0H,EAAMtE,KAAKrH,UAA6B,cAAb2L,EAAMC,IAAkB;UACtD/H,IAAO8H,EAAMtL;UAAAA;QAAAA;MAAAA;MAMX4D,KAAK,MACRyH,IAAUA,EAAQ5H,MAAMG,IAAI;MAAA,IAGvByM,IAAmB9L,EAAU,WAAW6G;MAAAA,OAC1CrL,EAAQyD,KAEJ6M,EAAiB7M,GAAM6H,KAGxBzB,KAAK2F,QAAQ/L,GAAM,UAACwC;QAAAA,OAC1BqK,EAAiBrK,GAAOqF;MAAAA;IAAAA;EAAAA,CA3L3B;EMZMzF,KAAQ,IAAIsJ;EAqBLK,KAAoB3J,GAAM2J;EAO1BK,KAA0ChK,GAAMgK,mBAAmBU,KAC/E1K;EAQYqK,KAAgBrK,GAAMqK,cAAcK,KAAK1K;EAQzCmK,KAAgBnK,GAAMmK,cAAcO,KAAK1K;EAOzCwK,KAAexK,GAAMwK,aAAaE,KAAK1K;EAMvCsK,KAActK,GAAMsK,YAAYI,KAAK1K;EAUrCuK,KAAcvK,GAAMuK,YAAYG,KAAK1K;AAAAA;AAAAA", "names": ["die", "error", "args", "e", "errors", "msg", "apply", "Error", "length", "map", "s", "join", "isDraft", "value", "DRAFT_STATE", "isDraftable", "proto", "Object", "getPrototypeOf", "Ctor", "hasOwnProperty", "call", "constructor", "Function", "toString", "objectCtorString", "isPlainObject", "Array", "isArray", "DRAFTABLE", "_value$constructor", "isMap", "isSet", "original", "base_", "each", "obj", "iter", "enumerableOnly", "getArchtype", "keys", "ownKeys", "for<PERSON>ach", "key", "entry", "index", "thing", "state", "type_", "has", "prop", "prototype", "get", "set", "propOrOldValue", "t", "delete", "add", "is", "x", "y", "target", "hasMap", "Map", "hasSet", "Set", "latest", "copy_", "shallowCopy", "base", "slice", "descriptors", "getOwnPropertyDescriptors", "i", "desc", "writable", "configurable", "enumerable", "create", "freeze", "deep", "isFrozen", "clear", "dontMutateFrozenCollections", "getPlugin", "pluginKey", "plugin", "plugins", "loadPlugin", "implementation", "getCurrentScope", "process", "currentScope", "usePatchesInScope", "scope", "patchListener", "patches_", "inversePatches_", "patchListener_", "revokeScope", "leaveScope", "drafts_", "revokeDraft", "parent_", "enterScope", "immer", "immer_", "canAutoFreeze_", "unfinalizedDrafts_", "draft", "revoke_", "revoked_", "processResult", "result", "baseDraft", "isReplaced", "undefined", "useProxies_", "willFinalizeES5_", "modified_", "finalize", "<PERSON><PERSON><PERSON><PERSON>", "generateReplacementPatches_", "NOTHING", "rootScope", "path", "childValue", "finalizeProperty", "scope_", "finalized_", "draft_", "generatePatches_", "parentState", "targetObject", "rootPath", "res", "assigned_", "concat", "autoFreeze_", "peek", "getDescriptorFromProto", "source", "getOwnPropertyDescriptor", "<PERSON><PERSON><PERSON><PERSON>", "prepareCopy", "createProxy", "parent", "proxyMap_", "proxySet_", "isManual_", "traps", "objectTraps", "arrayTraps", "Proxy", "revocable", "revoke", "proxy", "createProxyProxy", "createES5Proxy_", "push", "current", "currentImpl", "copy", "archType", "hasChanges_", "copyHelper", "from", "enableES5", "proxyProperty", "this", "assertUnrevoked", "mark<PERSON><PERSON>esSweep", "drafts", "hasArrayChanges", "hasObjectChanges", "baseValue", "baseIsDraft", "descriptor", "JSON", "stringify", "defineProperty", "createES5Draft", "mark<PERSON>hangesRecursively", "object", "min", "Math", "enablePatches", "deepClonePatchValue", "entries", "cloned", "immerable", "clonePatchValueIfNeeded", "ADD", "applyPatches_", "patches", "patch", "op", "parentType", "p", "type", "splice", "basePath", "inversePatches", "assignedValue", "origValue", "generatePatchesFromAssigned", "generateArrayPatches", "unshift", "generateSetPatches", "replacement", "enableMapSet", "__extends", "d", "b", "__", "extendStatics", "prepareMapCopy", "prepareSetCopy", "setPrototypeOf", "__proto__", "DraftMap", "size", "cb", "thisArg", "_value", "_this", "values", "r", "iterator", "iteratorSymbol", "_this2", "next", "done", "_this3", "DraftSet", "enableAllPlugins", "castDraft", "castImmutable", "hasSymbol", "Symbol", "hasProxies", "Reflect", "for", "data", "getOwnPropertySymbols", "getOwnPropertyNames", "_desc$get", "readPropFromProto", "currentState", "deleteProperty", "owner", "fn", "arguments", "isNaN", "parseInt", "Immer", "config", "recipe", "defaultBase", "self", "produce", "<PERSON><PERSON><PERSON><PERSON>", "Promise", "then", "ip", "produceWithPatches", "nextState", "useProxies", "setUseProxies", "autoFreeze", "setAutoFreeze", "createDraft", "finishDraft", "applyPatches", "applyPatchesImpl", "bind"], "sources": ["C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\immer\\src\\utils\\errors.ts", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\immer\\src\\utils\\common.ts", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\immer\\src\\utils\\plugins.ts", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\immer\\src\\core\\scope.ts", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\immer\\src\\core\\finalize.ts", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\immer\\src\\core\\proxy.ts", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\immer\\src\\core\\immerClass.ts", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\immer\\src\\core\\current.ts", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\immer\\src\\plugins\\es5.ts", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\immer\\src\\plugins\\patches.ts", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\immer\\src\\plugins\\mapset.ts", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\immer\\src\\plugins\\all.ts", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\immer\\src\\immer.ts", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\node_modules\\immer\\src\\utils\\env.ts"], "sourcesContent": ["const errors = {\n\t0: \"Illegal state\",\n\t1: \"Immer drafts cannot have computed properties\",\n\t2: \"This object has been frozen and should not be mutated\",\n\t3(data: any) {\n\t\treturn (\n\t\t\t\"Cannot use a proxy that has been revoked. Did you pass an object from inside an immer function to an async process? \" +\n\t\t\tdata\n\t\t)\n\t},\n\t4: \"An immer producer returned a new value *and* modified its draft. Either return a new value *or* modify the draft.\",\n\t5: \"Immer forbids circular references\",\n\t6: \"The first or second argument to `produce` must be a function\",\n\t7: \"The third argument to `produce` must be a function or undefined\",\n\t8: \"First argument to `createDraft` must be a plain object, an array, or an immerable object\",\n\t9: \"First argument to `finishDraft` must be a draft returned by `createDraft`\",\n\t10: \"The given draft is already finalized\",\n\t11: \"Object.defineProperty() cannot be used on an Immer draft\",\n\t12: \"Object.setPrototypeOf() cannot be used on an Immer draft\",\n\t13: \"Immer only supports deleting array indices\",\n\t14: \"Immer only supports setting array indices and the 'length' property\",\n\t15(path: string) {\n\t\treturn \"Cannot apply patch, path doesn't resolve: \" + path\n\t},\n\t16: 'Sets cannot have \"replace\" patches.',\n\t17(op: string) {\n\t\treturn \"Unsupported patch operation: \" + op\n\t},\n\t18(plugin: string) {\n\t\treturn `The plugin for '${plugin}' has not been loaded into Immer. To enable the plugin, import and call \\`enable${plugin}()\\` when initializing your application.`\n\t},\n\t20: \"Cannot use proxies if Proxy, Proxy.revocable or Reflect are not available\",\n\t21(thing: string) {\n\t\treturn `produce can only be called on things that are draftable: plain objects, arrays, Map, Set or classes that are marked with '[immerable]: true'. Got '${thing}'`\n\t},\n\t22(thing: string) {\n\t\treturn `'current' expects a draft, got: ${thing}`\n\t},\n\t23(thing: string) {\n\t\treturn `'original' expects a draft, got: ${thing}`\n\t},\n\t24: \"Patching reserved attributes like __proto__, prototype and constructor is not allowed\"\n} as const\n\nexport function die(error: keyof typeof errors, ...args: any[]): never {\n\tif (__DEV__) {\n\t\tconst e = errors[error]\n\t\tconst msg = !e\n\t\t\t? \"unknown error nr: \" + error\n\t\t\t: typeof e === \"function\"\n\t\t\t? e.apply(null, args as any)\n\t\t\t: e\n\t\tthrow new Error(`[Immer] ${msg}`)\n\t}\n\tthrow new Error(\n\t\t`[Immer] minified error nr: ${error}${\n\t\t\targs.length ? \" \" + args.map(s => `'${s}'`).join(\",\") : \"\"\n\t\t}. Find the full error at: https://bit.ly/3cXEKWf`\n\t)\n}\n", "import {\n\tDRAFT_STATE,\n\tDRAF<PERSON><PERSON><PERSON>,\n\thasSet,\n\tObjectish,\n\tDrafted,\n\tAnyObject,\n\tAnyMap,\n\tAnySet,\n\tImmerState,\n\thasMap,\n\tArchtype,\n\tdie\n} from \"../internal\"\n\n/** Returns true if the given value is an Immer draft */\n/*#__PURE__*/\nexport function isDraft(value: any): boolean {\n\treturn !!value && !!value[DRAFT_STATE]\n}\n\n/** Returns true if the given value can be drafted by Immer */\n/*#__PURE__*/\nexport function isDraftable(value: any): boolean {\n\tif (!value) return false\n\treturn (\n\t\tisPlainObject(value) ||\n\t\tArray.isArray(value) ||\n\t\t!!value[DRAFTABLE] ||\n\t\t!!value.constructor?.[DRAFTABLE] ||\n\t\tisMap(value) ||\n\t\tisSet(value)\n\t)\n}\n\nconst objectCtorString = Object.prototype.constructor.toString()\n/*#__PURE__*/\nexport function isPlainObject(value: any): boolean {\n\tif (!value || typeof value !== \"object\") return false\n\tconst proto = Object.getPrototypeOf(value)\n\tif (proto === null) {\n\t\treturn true\n\t}\n\tconst Ctor =\n\t\tObject.hasOwnProperty.call(proto, \"constructor\") && proto.constructor\n\n\tif (Ctor === Object) return true\n\n\treturn (\n\t\ttypeof Ctor == \"function\" &&\n\t\tFunction.toString.call(Ctor) === objectCtorString\n\t)\n}\n\n/** Get the underlying object that is represented by the given draft */\n/*#__PURE__*/\nexport function original<T>(value: T): T | undefined\nexport function original(value: Drafted<any>): any {\n\tif (!isDraft(value)) die(23, value)\n\treturn value[DRAFT_STATE].base_\n}\n\n/*#__PURE__*/\nexport const ownKeys: (target: AnyObject) => PropertyKey[] =\n\ttypeof Reflect !== \"undefined\" && Reflect.ownKeys\n\t\t? Reflect.ownKeys\n\t\t: typeof Object.getOwnPropertySymbols !== \"undefined\"\n\t\t? obj =>\n\t\t\t\tObject.getOwnPropertyNames(obj).concat(\n\t\t\t\t\tObject.getOwnPropertySymbols(obj) as any\n\t\t\t\t)\n\t\t: /* istanbul ignore next */ Object.getOwnPropertyNames\n\nexport const getOwnPropertyDescriptors =\n\tObject.getOwnPropertyDescriptors ||\n\tfunction getOwnPropertyDescriptors(target: any) {\n\t\t// Polyfill needed for Hermes and IE, see https://github.com/facebook/hermes/issues/274\n\t\tconst res: any = {}\n\t\townKeys(target).forEach(key => {\n\t\t\tres[key] = Object.getOwnPropertyDescriptor(target, key)\n\t\t})\n\t\treturn res\n\t}\n\nexport function each<T extends Objectish>(\n\tobj: T,\n\titer: (key: string | number, value: any, source: T) => void,\n\tenumerableOnly?: boolean\n): void\nexport function each(obj: any, iter: any, enumerableOnly = false) {\n\tif (getArchtype(obj) === Archtype.Object) {\n\t\t;(enumerableOnly ? Object.keys : ownKeys)(obj).forEach(key => {\n\t\t\tif (!enumerableOnly || typeof key !== \"symbol\") iter(key, obj[key], obj)\n\t\t})\n\t} else {\n\t\tobj.forEach((entry: any, index: any) => iter(index, entry, obj))\n\t}\n}\n\n/*#__PURE__*/\nexport function getArchtype(thing: any): Archtype {\n\t/* istanbul ignore next */\n\tconst state: undefined | ImmerState = thing[DRAFT_STATE]\n\treturn state\n\t\t? state.type_ > 3\n\t\t\t? state.type_ - 4 // cause Object and Array map back from 4 and 5\n\t\t\t: (state.type_ as any) // others are the same\n\t\t: Array.isArray(thing)\n\t\t? Archtype.Array\n\t\t: isMap(thing)\n\t\t? Archtype.Map\n\t\t: isSet(thing)\n\t\t? Archtype.Set\n\t\t: Archtype.Object\n}\n\n/*#__PURE__*/\nexport function has(thing: any, prop: PropertyKey): boolean {\n\treturn getArchtype(thing) === Archtype.Map\n\t\t? thing.has(prop)\n\t\t: Object.prototype.hasOwnProperty.call(thing, prop)\n}\n\n/*#__PURE__*/\nexport function get(thing: AnyMap | AnyObject, prop: PropertyKey): any {\n\t// @ts-ignore\n\treturn getArchtype(thing) === Archtype.Map ? thing.get(prop) : thing[prop]\n}\n\n/*#__PURE__*/\nexport function set(thing: any, propOrOldValue: PropertyKey, value: any) {\n\tconst t = getArchtype(thing)\n\tif (t === Archtype.Map) thing.set(propOrOldValue, value)\n\telse if (t === Archtype.Set) {\n\t\tthing.delete(propOrOldValue)\n\t\tthing.add(value)\n\t} else thing[propOrOldValue] = value\n}\n\n/*#__PURE__*/\nexport function is(x: any, y: any): boolean {\n\t// From: https://github.com/facebook/fbjs/blob/c69904a511b900266935168223063dd8772dfc40/packages/fbjs/src/core/shallowEqual.js\n\tif (x === y) {\n\t\treturn x !== 0 || 1 / x === 1 / y\n\t} else {\n\t\treturn x !== x && y !== y\n\t}\n}\n\n/*#__PURE__*/\nexport function isMap(target: any): target is AnyMap {\n\treturn hasMap && target instanceof Map\n}\n\n/*#__PURE__*/\nexport function isSet(target: any): target is AnySet {\n\treturn hasSet && target instanceof Set\n}\n/*#__PURE__*/\nexport function latest(state: ImmerState): any {\n\treturn state.copy_ || state.base_\n}\n\n/*#__PURE__*/\nexport function shallowCopy(base: any) {\n\tif (Array.isArray(base)) return Array.prototype.slice.call(base)\n\tconst descriptors = getOwnPropertyDescriptors(base)\n\tdelete descriptors[DRAFT_STATE as any]\n\tlet keys = ownKeys(descriptors)\n\tfor (let i = 0; i < keys.length; i++) {\n\t\tconst key: any = keys[i]\n\t\tconst desc = descriptors[key]\n\t\tif (desc.writable === false) {\n\t\t\tdesc.writable = true\n\t\t\tdesc.configurable = true\n\t\t}\n\t\t// like object.assign, we will read any _own_, get/set accessors. This helps in dealing\n\t\t// with libraries that trap values, like mobx or vue\n\t\t// unlike object.assign, non-enumerables will be copied as well\n\t\tif (desc.get || desc.set)\n\t\t\tdescriptors[key] = {\n\t\t\t\tconfigurable: true,\n\t\t\t\twritable: true, // could live with !!desc.set as well here...\n\t\t\t\tenumerable: desc.enumerable,\n\t\t\t\tvalue: base[key]\n\t\t\t}\n\t}\n\treturn Object.create(Object.getPrototypeOf(base), descriptors)\n}\n\n/**\n * Freezes draftable objects. Returns the original object.\n * By default freezes shallowly, but if the second argument is `true` it will freeze recursively.\n *\n * @param obj\n * @param deep\n */\nexport function freeze<T>(obj: T, deep?: boolean): T\nexport function freeze<T>(obj: any, deep: boolean = false): T {\n\tif (isFrozen(obj) || isDraft(obj) || !isDraftable(obj)) return obj\n\tif (getArchtype(obj) > 1 /* Map or Set */) {\n\t\tobj.set = obj.add = obj.clear = obj.delete = dontMutateFrozenCollections as any\n\t}\n\tObject.freeze(obj)\n\tif (deep) each(obj, (key, value) => freeze(value, true), true)\n\treturn obj\n}\n\nfunction dontMutateFrozenCollections() {\n\tdie(2)\n}\n\nexport function isFrozen(obj: any): boolean {\n\tif (obj == null || typeof obj !== \"object\") return true\n\t// See #600, IE dies on non-objects in Object.isFrozen\n\treturn Object.isFrozen(obj)\n}\n", "import {\n\tImmerState,\n\t<PERSON>,\n\t<PERSON>mmer<PERSON>cope,\n\tDrafted,\n\tAnyObject,\n\tImmerBaseState,\n\tAnyMap,\n\tAnySet,\n\tProxyType,\n\tdie\n} from \"../internal\"\n\n/** Plugin utilities */\nconst plugins: {\n\tPatches?: {\n\t\tgeneratePatches_(\n\t\t\tstate: ImmerState,\n\t\t\tbasePath: PatchPath,\n\t\t\tpatches: Patch[],\n\t\t\tinversePatches: Patch[]\n\t\t): void\n\t\tgenerateReplacementPatches_(\n\t\t\tbase: any,\n\t\t\treplacement: any,\n\t\t\tpatches: Patch[],\n\t\t\tinversePatches: Patch[]\n\t\t): void\n\t\tapplyPatches_<T>(draft: T, patches: Patch[]): T\n\t}\n\tES5?: {\n\t\twillFinalizeES5_(scope: ImmerScope, result: any, isReplaced: boolean): void\n\t\tcreateES5Proxy_<T>(\n\t\t\tbase: T,\n\t\t\tparent?: ImmerState\n\t\t): Drafted<T, ES5ObjectState | ES5ArrayState>\n\t\thasChanges_(state: ES5ArrayState | ES5ObjectState): boolean\n\t}\n\tMapSet?: {\n\t\tproxyMap_<T extends AnyMap>(target: T, parent?: ImmerState): T\n\t\tproxySet_<T extends AnySet>(target: T, parent?: ImmerState): T\n\t}\n} = {}\n\ntype Plugins = typeof plugins\n\nexport function getPlugin<K extends keyof Plugins>(\n\tpluginKey: K\n): Exclude<Plugins[K], undefined> {\n\tconst plugin = plugins[pluginKey]\n\tif (!plugin) {\n\t\tdie(18, pluginKey)\n\t}\n\t// @ts-ignore\n\treturn plugin\n}\n\nexport function loadPlugin<K extends keyof Plugins>(\n\tpluginKey: K,\n\timplementation: Plugins[K]\n): void {\n\tif (!plugins[pluginKey]) plugins[pluginKey] = implementation\n}\n\n/** ES5 Plugin */\n\ninterface ES5BaseState extends ImmerBaseState {\n\tassigned_: {[key: string]: any}\n\tparent_?: ImmerState\n\trevoked_: boolean\n}\n\nexport interface ES5ObjectState extends ES5BaseState {\n\ttype_: ProxyType.ES5Object\n\tdraft_: Drafted<AnyObject, ES5ObjectState>\n\tbase_: AnyObject\n\tcopy_: AnyObject | null\n}\n\nexport interface ES5ArrayState extends ES5BaseState {\n\ttype_: ProxyType.ES5Array\n\tdraft_: Drafted<AnyObject, ES5ArrayState>\n\tbase_: any\n\tcopy_: any\n}\n\n/** Map / Set plugin */\n\nexport interface MapState extends ImmerBaseState {\n\ttype_: ProxyType.Map\n\tcopy_: AnyMap | undefined\n\tassigned_: Map<any, boolean> | undefined\n\tbase_: AnyMap\n\trevoked_: boolean\n\tdraft_: Drafted<AnyMap, MapState>\n}\n\nexport interface SetState extends ImmerBaseState {\n\ttype_: ProxyType.Set\n\tcopy_: AnySet | undefined\n\tbase_: AnySet\n\tdrafts_: Map<any, Drafted> // maps the original value to the draft value in the new set\n\trevoked_: boolean\n\tdraft_: Drafted<AnySet, SetState>\n}\n\n/** Patches plugin */\n\nexport type PatchPath = (string | number)[]\n", "import {\n\t<PERSON>,\n\tPatchListener,\n\tDrafted,\n\tImmer,\n\tDRAFT_STATE,\n\tImmerState,\n\tProxyType,\n\tgetPlugin\n} from \"../internal\"\nimport {die} from \"../utils/errors\"\n\n/** Each scope represents a `produce` call. */\n\nexport interface ImmerScope {\n\tpatches_?: Patch[]\n\tinversePatches_?: Patch[]\n\tcanAutoFreeze_: boolean\n\tdrafts_: any[]\n\tparent_?: ImmerScope\n\tpatchListener_?: PatchListener\n\timmer_: Immer\n\tunfinalizedDrafts_: number\n}\n\nlet currentScope: ImmerScope | undefined\n\nexport function getCurrentScope() {\n\tif (__DEV__ && !currentScope) die(0)\n\treturn currentScope!\n}\n\nfunction createScope(\n\tparent_: ImmerScope | undefined,\n\timmer_: Immer\n): ImmerScope {\n\treturn {\n\t\tdrafts_: [],\n\t\tparent_,\n\t\timmer_,\n\t\t// Whenever the modified draft contains a draft from another scope, we\n\t\t// need to prevent auto-freezing so the unowned draft can be finalized.\n\t\tcanAutoFreeze_: true,\n\t\tunfinalizedDrafts_: 0\n\t}\n}\n\nexport function usePatchesInScope(\n\tscope: ImmerScope,\n\tpatchListener?: PatchListener\n) {\n\tif (patchListener) {\n\t\tgetPlugin(\"Patches\") // assert we have the plugin\n\t\tscope.patches_ = []\n\t\tscope.inversePatches_ = []\n\t\tscope.patchListener_ = patchListener\n\t}\n}\n\nexport function revokeScope(scope: ImmerScope) {\n\tleaveScope(scope)\n\tscope.drafts_.forEach(revokeDraft)\n\t// @ts-ignore\n\tscope.drafts_ = null\n}\n\nexport function leaveScope(scope: ImmerScope) {\n\tif (scope === currentScope) {\n\t\tcurrentScope = scope.parent_\n\t}\n}\n\nexport function enterScope(immer: Immer) {\n\treturn (currentScope = createScope(currentScope, immer))\n}\n\nfunction revokeDraft(draft: Drafted) {\n\tconst state: ImmerState = draft[DRAFT_STATE]\n\tif (\n\t\tstate.type_ === ProxyType.ProxyObject ||\n\t\tstate.type_ === ProxyType.ProxyArray\n\t)\n\t\tstate.revoke_()\n\telse state.revoked_ = true\n}\n", "import {\n\tImmerScope,\n\tDRAFT_STATE,\n\tisDraftable,\n\tNOTHING,\n\tPatchPath,\n\teach,\n\thas,\n\tfreeze,\n\tImmerState,\n\tisDraft,\n\tSetState,\n\tset,\n\tProxyType,\n\tgetPlugin,\n\tdie,\n\trevokeScope,\n\tisFrozen,\n\tshallowCopy\n} from \"../internal\"\n\nexport function processResult(result: any, scope: ImmerScope) {\n\tscope.unfinalizedDrafts_ = scope.drafts_.length\n\tconst baseDraft = scope.drafts_![0]\n\tconst isReplaced = result !== undefined && result !== baseDraft\n\tif (!scope.immer_.useProxies_)\n\t\tgetPlugin(\"ES5\").willFinalizeES5_(scope, result, isReplaced)\n\tif (isReplaced) {\n\t\tif (baseDraft[DRAFT_STATE].modified_) {\n\t\t\trevokeScope(scope)\n\t\t\tdie(4)\n\t\t}\n\t\tif (isDraftable(result)) {\n\t\t\t// Finalize the result in case it contains (or is) a subset of the draft.\n\t\t\tresult = finalize(scope, result)\n\t\t\tif (!scope.parent_) maybeFreeze(scope, result)\n\t\t}\n\t\tif (scope.patches_) {\n\t\t\tgetPlugin(\"Patches\").generateReplacementPatches_(\n\t\t\t\tbaseDraft[DRAFT_STATE].base_,\n\t\t\t\tresult,\n\t\t\t\tscope.patches_,\n\t\t\t\tscope.inversePatches_!\n\t\t\t)\n\t\t}\n\t} else {\n\t\t// Finalize the base draft.\n\t\tresult = finalize(scope, baseDraft, [])\n\t}\n\trevokeScope(scope)\n\tif (scope.patches_) {\n\t\tscope.patchListener_!(scope.patches_, scope.inversePatches_!)\n\t}\n\treturn result !== NOTHING ? result : undefined\n}\n\nfunction finalize(rootScope: ImmerScope, value: any, path?: PatchPath) {\n\t// Don't recurse in tho recursive data structures\n\tif (isFrozen(value)) return value\n\n\tconst state: ImmerState = value[DRAFT_STATE]\n\t// A plain object, might need freezing, might contain drafts\n\tif (!state) {\n\t\teach(\n\t\t\tvalue,\n\t\t\t(key, childValue) =>\n\t\t\t\tfinalizeProperty(rootScope, state, value, key, childValue, path),\n\t\t\ttrue // See #590, don't recurse into non-enumerable of non drafted objects\n\t\t)\n\t\treturn value\n\t}\n\t// Never finalize drafts owned by another scope.\n\tif (state.scope_ !== rootScope) return value\n\t// Unmodified draft, return the (frozen) original\n\tif (!state.modified_) {\n\t\tmaybeFreeze(rootScope, state.base_, true)\n\t\treturn state.base_\n\t}\n\t// Not finalized yet, let's do that now\n\tif (!state.finalized_) {\n\t\tstate.finalized_ = true\n\t\tstate.scope_.unfinalizedDrafts_--\n\t\tconst result =\n\t\t\t// For ES5, create a good copy from the draft first, with added keys and without deleted keys.\n\t\t\tstate.type_ === ProxyType.ES5Object || state.type_ === ProxyType.ES5Array\n\t\t\t\t? (state.copy_ = shallowCopy(state.draft_))\n\t\t\t\t: state.copy_\n\t\t// Finalize all children of the copy\n\t\t// For sets we clone before iterating, otherwise we can get in endless loop due to modifying during iteration, see #628\n\t\t// Although the original test case doesn't seem valid anyway, so if this in the way we can turn the next line\n\t\t// back to each(result, ....)\n\t\teach(\n\t\t\tstate.type_ === ProxyType.Set ? new Set(result) : result,\n\t\t\t(key, childValue) =>\n\t\t\t\tfinalizeProperty(rootScope, state, result, key, childValue, path)\n\t\t)\n\t\t// everything inside is frozen, we can freeze here\n\t\tmaybeFreeze(rootScope, result, false)\n\t\t// first time finalizing, let's create those patches\n\t\tif (path && rootScope.patches_) {\n\t\t\tgetPlugin(\"Patches\").generatePatches_(\n\t\t\t\tstate,\n\t\t\t\tpath,\n\t\t\t\trootScope.patches_,\n\t\t\t\trootScope.inversePatches_!\n\t\t\t)\n\t\t}\n\t}\n\treturn state.copy_\n}\n\nfunction finalizeProperty(\n\trootScope: ImmerScope,\n\tparentState: undefined | ImmerState,\n\ttargetObject: any,\n\tprop: string | number,\n\tchildValue: any,\n\trootPath?: PatchPath\n) {\n\tif (__DEV__ && childValue === targetObject) die(5)\n\tif (isDraft(childValue)) {\n\t\tconst path =\n\t\t\trootPath &&\n\t\t\tparentState &&\n\t\t\tparentState!.type_ !== ProxyType.Set && // Set objects are atomic since they have no keys.\n\t\t\t!has((parentState as Exclude<ImmerState, SetState>).assigned_!, prop) // Skip deep patches for assigned keys.\n\t\t\t\t? rootPath!.concat(prop)\n\t\t\t\t: undefined\n\t\t// Drafts owned by `scope` are finalized here.\n\t\tconst res = finalize(rootScope, childValue, path)\n\t\tset(targetObject, prop, res)\n\t\t// Drafts from another scope must prevented to be frozen\n\t\t// if we got a draft back from finalize, we're in a nested produce and shouldn't freeze\n\t\tif (isDraft(res)) {\n\t\t\trootScope.canAutoFreeze_ = false\n\t\t} else return\n\t}\n\t// Search new objects for unfinalized drafts. Frozen objects should never contain drafts.\n\tif (isDraftable(childValue) && !isFrozen(childValue)) {\n\t\tif (!rootScope.immer_.autoFreeze_ && rootScope.unfinalizedDrafts_ < 1) {\n\t\t\t// optimization: if an object is not a draft, and we don't have to\n\t\t\t// deepfreeze everything, and we are sure that no drafts are left in the remaining object\n\t\t\t// cause we saw and finalized all drafts already; we can stop visiting the rest of the tree.\n\t\t\t// This benefits especially adding large data tree's without further processing.\n\t\t\t// See add-data.js perf test\n\t\t\treturn\n\t\t}\n\t\tfinalize(rootScope, childValue)\n\t\t// immer deep freezes plain objects, so if there is no parent state, we freeze as well\n\t\tif (!parentState || !parentState.scope_.parent_)\n\t\t\tmaybeFreeze(rootScope, childValue)\n\t}\n}\n\nfunction maybeFreeze(scope: ImmerScope, value: any, deep = false) {\n\tif (scope.immer_.autoFreeze_ && scope.canAutoFreeze_) {\n\t\tfreeze(value, deep)\n\t}\n}\n", "import {\n\teach,\n\thas,\n\tis,\n\tisDraftable,\n\tshallowCopy,\n\tlatest,\n\tImmerBaseState,\n\tImmerState,\n\tDrafted,\n\tAnyObject,\n\tAnyArray,\n\tObjectish,\n\tgetCurrentScope,\n\tDRAFT_STATE,\n\tdie,\n\tcreateProxy,\n\tProxyType\n} from \"../internal\"\n\ninterface ProxyBaseState extends ImmerBaseState {\n\tassigned_: {\n\t\t[property: string]: boolean\n\t}\n\tparent_?: ImmerState\n\trevoke_(): void\n}\n\nexport interface ProxyObjectState extends ProxyBaseState {\n\ttype_: ProxyType.ProxyObject\n\tbase_: any\n\tcopy_: any\n\tdraft_: Drafted<AnyObject, ProxyObjectState>\n}\n\nexport interface ProxyArrayState extends ProxyBaseState {\n\ttype_: ProxyType.ProxyArray\n\tbase_: AnyArray\n\tcopy_: AnyArray | null\n\tdraft_: Drafted<AnyArray, ProxyArrayState>\n}\n\ntype ProxyState = ProxyObjectState | ProxyArrayState\n\n/**\n * Returns a new draft of the `base` object.\n *\n * The second argument is the parent draft-state (used internally).\n */\nexport function createProxyProxy<T extends Objectish>(\n\tbase: T,\n\tparent?: ImmerState\n): Drafted<T, ProxyState> {\n\tconst isArray = Array.isArray(base)\n\tconst state: ProxyState = {\n\t\ttype_: isArray ? ProxyType.ProxyArray : (ProxyType.ProxyObject as any),\n\t\t// Track which produce call this is associated with.\n\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t// True for both shallow and deep changes.\n\t\tmodified_: false,\n\t\t// Used during finalization.\n\t\tfinalized_: false,\n\t\t// Track which properties have been assigned (true) or deleted (false).\n\t\tassigned_: {},\n\t\t// The parent draft state.\n\t\tparent_: parent,\n\t\t// The base state.\n\t\tbase_: base,\n\t\t// The base proxy.\n\t\tdraft_: null as any, // set below\n\t\t// The base copy with any updated values.\n\t\tcopy_: null,\n\t\t// Called by the `produce` function.\n\t\trevoke_: null as any,\n\t\tisManual_: false\n\t}\n\n\t// the traps must target something, a bit like the 'real' base.\n\t// but also, we need to be able to determine from the target what the relevant state is\n\t// (to avoid creating traps per instance to capture the state in closure,\n\t// and to avoid creating weird hidden properties as well)\n\t// So the trick is to use 'state' as the actual 'target'! (and make sure we intercept everything)\n\t// Note that in the case of an array, we put the state in an array to have better Reflect defaults ootb\n\tlet target: T = state as any\n\tlet traps: ProxyHandler<object | Array<any>> = objectTraps\n\tif (isArray) {\n\t\ttarget = [state] as any\n\t\ttraps = arrayTraps\n\t}\n\n\tconst {revoke, proxy} = Proxy.revocable(target, traps)\n\tstate.draft_ = proxy as any\n\tstate.revoke_ = revoke\n\treturn proxy as any\n}\n\n/**\n * Object drafts\n */\nexport const objectTraps: ProxyHandler<ProxyState> = {\n\tget(state, prop) {\n\t\tif (prop === DRAFT_STATE) return state\n\n\t\tconst source = latest(state)\n\t\tif (!has(source, prop)) {\n\t\t\t// non-existing or non-own property...\n\t\t\treturn readPropFromProto(state, source, prop)\n\t\t}\n\t\tconst value = source[prop]\n\t\tif (state.finalized_ || !isDraftable(value)) {\n\t\t\treturn value\n\t\t}\n\t\t// Check for existing draft in modified state.\n\t\t// Assigned values are never drafted. This catches any drafts we created, too.\n\t\tif (value === peek(state.base_, prop)) {\n\t\t\tprepareCopy(state)\n\t\t\treturn (state.copy_![prop as any] = createProxy(\n\t\t\t\tstate.scope_.immer_,\n\t\t\t\tvalue,\n\t\t\t\tstate\n\t\t\t))\n\t\t}\n\t\treturn value\n\t},\n\thas(state, prop) {\n\t\treturn prop in latest(state)\n\t},\n\townKeys(state) {\n\t\treturn Reflect.ownKeys(latest(state))\n\t},\n\tset(\n\t\tstate: ProxyObjectState,\n\t\tprop: string /* strictly not, but helps TS */,\n\t\tvalue\n\t) {\n\t\tconst desc = getDescriptorFromProto(latest(state), prop)\n\t\tif (desc?.set) {\n\t\t\t// special case: if this write is captured by a setter, we have\n\t\t\t// to trigger it with the correct context\n\t\t\tdesc.set.call(state.draft_, value)\n\t\t\treturn true\n\t\t}\n\t\tif (!state.modified_) {\n\t\t\t// the last check is because we need to be able to distinguish setting a non-existing to undefined (which is a change)\n\t\t\t// from setting an existing property with value undefined to undefined (which is not a change)\n\t\t\tconst current = peek(latest(state), prop)\n\t\t\t// special case, if we assigning the original value to a draft, we can ignore the assignment\n\t\t\tconst currentState: ProxyObjectState = current?.[DRAFT_STATE]\n\t\t\tif (currentState && currentState.base_ === value) {\n\t\t\t\tstate.copy_![prop] = value\n\t\t\t\tstate.assigned_[prop] = false\n\t\t\t\treturn true\n\t\t\t}\n\t\t\tif (is(value, current) && (value !== undefined || has(state.base_, prop)))\n\t\t\t\treturn true\n\t\t\tprepareCopy(state)\n\t\t\tmarkChanged(state)\n\t\t}\n\n\t\tif (\n\t\t\tstate.copy_![prop] === value &&\n\t\t\t// special case: NaN\n\t\t\ttypeof value !== \"number\" &&\n\t\t\t// special case: handle new props with value 'undefined'\n\t\t\t(value !== undefined || prop in state.copy_)\n\t\t)\n\t\t\treturn true\n\n\t\t// @ts-ignore\n\t\tstate.copy_![prop] = value\n\t\tstate.assigned_[prop] = true\n\t\treturn true\n\t},\n\tdeleteProperty(state, prop: string) {\n\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\tif (peek(state.base_, prop) !== undefined || prop in state.base_) {\n\t\t\tstate.assigned_[prop] = false\n\t\t\tprepareCopy(state)\n\t\t\tmarkChanged(state)\n\t\t} else {\n\t\t\t// if an originally not assigned property was deleted\n\t\t\tdelete state.assigned_[prop]\n\t\t}\n\t\t// @ts-ignore\n\t\tif (state.copy_) delete state.copy_[prop]\n\t\treturn true\n\t},\n\t// Note: We never coerce `desc.value` into an Immer draft, because we can't make\n\t// the same guarantee in ES5 mode.\n\tgetOwnPropertyDescriptor(state, prop) {\n\t\tconst owner = latest(state)\n\t\tconst desc = Reflect.getOwnPropertyDescriptor(owner, prop)\n\t\tif (!desc) return desc\n\t\treturn {\n\t\t\twritable: true,\n\t\t\tconfigurable: state.type_ !== ProxyType.ProxyArray || prop !== \"length\",\n\t\t\tenumerable: desc.enumerable,\n\t\t\tvalue: owner[prop]\n\t\t}\n\t},\n\tdefineProperty() {\n\t\tdie(11)\n\t},\n\tgetPrototypeOf(state) {\n\t\treturn Object.getPrototypeOf(state.base_)\n\t},\n\tsetPrototypeOf() {\n\t\tdie(12)\n\t}\n}\n\n/**\n * Array drafts\n */\n\nconst arrayTraps: ProxyHandler<[ProxyArrayState]> = {}\neach(objectTraps, (key, fn) => {\n\t// @ts-ignore\n\tarrayTraps[key] = function() {\n\t\targuments[0] = arguments[0][0]\n\t\treturn fn.apply(this, arguments)\n\t}\n})\narrayTraps.deleteProperty = function(state, prop) {\n\tif (__DEV__ && isNaN(parseInt(prop as any))) die(13)\n\t// @ts-ignore\n\treturn arrayTraps.set!.call(this, state, prop, undefined)\n}\narrayTraps.set = function(state, prop, value) {\n\tif (__DEV__ && prop !== \"length\" && isNaN(parseInt(prop as any))) die(14)\n\treturn objectTraps.set!.call(this, state[0], prop, value, state[0])\n}\n\n// Access a property without creating an Immer draft.\nfunction peek(draft: Drafted, prop: PropertyKey) {\n\tconst state = draft[DRAFT_STATE]\n\tconst source = state ? latest(state) : draft\n\treturn source[prop]\n}\n\nfunction readPropFromProto(state: ImmerState, source: any, prop: PropertyKey) {\n\tconst desc = getDescriptorFromProto(source, prop)\n\treturn desc\n\t\t? `value` in desc\n\t\t\t? desc.value\n\t\t\t: // This is a very special case, if the prop is a getter defined by the\n\t\t\t  // prototype, we should invoke it with the draft as context!\n\t\t\t  desc.get?.call(state.draft_)\n\t\t: undefined\n}\n\nfunction getDescriptorFromProto(\n\tsource: any,\n\tprop: PropertyKey\n): PropertyDescriptor | undefined {\n\t// 'in' checks proto!\n\tif (!(prop in source)) return undefined\n\tlet proto = Object.getPrototypeOf(source)\n\twhile (proto) {\n\t\tconst desc = Object.getOwnPropertyDescriptor(proto, prop)\n\t\tif (desc) return desc\n\t\tproto = Object.getPrototypeOf(proto)\n\t}\n\treturn undefined\n}\n\nexport function markChanged(state: ImmerState) {\n\tif (!state.modified_) {\n\t\tstate.modified_ = true\n\t\tif (state.parent_) {\n\t\t\tmarkChanged(state.parent_)\n\t\t}\n\t}\n}\n\nexport function prepareCopy(state: {base_: any; copy_: any}) {\n\tif (!state.copy_) {\n\t\tstate.copy_ = shallowCopy(state.base_)\n\t}\n}\n", "import {\n\tIProduceWithPatches,\n\tIProduce,\n\tImmerState,\n\tDrafted,\n\tisDraftable,\n\tprocessR<PERSON>ult,\n\tPatch,\n\tObjectish,\n\tDRAFT_STATE,\n\tDraft,\n\tPatchListener,\n\tisDraft,\n\tisMap,\n\tisSet,\n\tcreateProxyProxy,\n\tgetPlugin,\n\tdie,\n\thasProxies,\n\tenterScope,\n\trevokeScope,\n\tleaveScope,\n\tusePatchesInScope,\n\tgetCurrentScope,\n\tNOTHING,\n\tfreeze,\n\tcurrent\n} from \"../internal\"\n\ninterface ProducersFns {\n\tproduce: IProduce\n\tproduceWithPatches: IProduceWithPatches\n}\n\nexport class Immer implements ProducersFns {\n\tuseProxies_: boolean = hasProxies\n\n\tautoFreeze_: boolean = true\n\n\tconstructor(config?: {useProxies?: boolean; autoFreeze?: boolean}) {\n\t\tif (typeof config?.useProxies === \"boolean\")\n\t\t\tthis.setUseProxies(config!.useProxies)\n\t\tif (typeof config?.autoFreeze === \"boolean\")\n\t\t\tthis.setAutoFreeze(config!.autoFreeze)\n\t}\n\n\t/**\n\t * The `produce` function takes a value and a \"recipe function\" (whose\n\t * return value often depends on the base state). The recipe function is\n\t * free to mutate its first argument however it wants. All mutations are\n\t * only ever applied to a __copy__ of the base state.\n\t *\n\t * Pass only a function to create a \"curried producer\" which relieves you\n\t * from passing the recipe function every time.\n\t *\n\t * Only plain objects and arrays are made mutable. All other objects are\n\t * considered uncopyable.\n\t *\n\t * Note: This function is __bound__ to its `Immer` instance.\n\t *\n\t * @param {any} base - the initial state\n\t * @param {Function} producer - function that receives a proxy of the base state as first argument and which can be freely modified\n\t * @param {Function} patchListener - optional function that will be called with all the patches produced here\n\t * @returns {any} a new state, or the initial state if nothing was modified\n\t */\n\tproduce: IProduce = (base: any, recipe?: any, patchListener?: any) => {\n\t\t// curried invocation\n\t\tif (typeof base === \"function\" && typeof recipe !== \"function\") {\n\t\t\tconst defaultBase = recipe\n\t\t\trecipe = base\n\n\t\t\tconst self = this\n\t\t\treturn function curriedProduce(\n\t\t\t\tthis: any,\n\t\t\t\tbase = defaultBase,\n\t\t\t\t...args: any[]\n\t\t\t) {\n\t\t\t\treturn self.produce(base, (draft: Drafted) => recipe.call(this, draft, ...args)) // prettier-ignore\n\t\t\t}\n\t\t}\n\n\t\tif (typeof recipe !== \"function\") die(6)\n\t\tif (patchListener !== undefined && typeof patchListener !== \"function\")\n\t\t\tdie(7)\n\n\t\tlet result\n\n\t\t// Only plain objects, arrays, and \"immerable classes\" are drafted.\n\t\tif (isDraftable(base)) {\n\t\t\tconst scope = enterScope(this)\n\t\t\tconst proxy = createProxy(this, base, undefined)\n\t\t\tlet hasError = true\n\t\t\ttry {\n\t\t\t\tresult = recipe(proxy)\n\t\t\t\thasError = false\n\t\t\t} finally {\n\t\t\t\t// finally instead of catch + rethrow better preserves original stack\n\t\t\t\tif (hasError) revokeScope(scope)\n\t\t\t\telse leaveScope(scope)\n\t\t\t}\n\t\t\tif (typeof Promise !== \"undefined\" && result instanceof Promise) {\n\t\t\t\treturn result.then(\n\t\t\t\t\tresult => {\n\t\t\t\t\t\tusePatchesInScope(scope, patchListener)\n\t\t\t\t\t\treturn processResult(result, scope)\n\t\t\t\t\t},\n\t\t\t\t\terror => {\n\t\t\t\t\t\trevokeScope(scope)\n\t\t\t\t\t\tthrow error\n\t\t\t\t\t}\n\t\t\t\t)\n\t\t\t}\n\t\t\tusePatchesInScope(scope, patchListener)\n\t\t\treturn processResult(result, scope)\n\t\t} else if (!base || typeof base !== \"object\") {\n\t\t\tresult = recipe(base)\n\t\t\tif (result === undefined) result = base\n\t\t\tif (result === NOTHING) result = undefined\n\t\t\tif (this.autoFreeze_) freeze(result, true)\n\t\t\tif (patchListener) {\n\t\t\t\tconst p: Patch[] = []\n\t\t\t\tconst ip: Patch[] = []\n\t\t\t\tgetPlugin(\"Patches\").generateReplacementPatches_(base, result, p, ip)\n\t\t\t\tpatchListener(p, ip)\n\t\t\t}\n\t\t\treturn result\n\t\t} else die(21, base)\n\t}\n\n\tproduceWithPatches: IProduceWithPatches = (\n\t\tbase: any,\n\t\trecipe?: any,\n\t): any => {\n\t\t// curried invocation\n\t\tif (typeof base === \"function\") {\n\t\t\treturn (state: any, ...args: any[]) =>\n\t\t\t\tthis.produceWithPatches(state, (draft: any) => base(draft, ...args))\n\t\t}\n\n\t\tlet patches: Patch[], inversePatches: Patch[]\n\t\tconst result = this.produce(base, recipe, (p: Patch[], ip: Patch[]) => {\n\t\t\tpatches = p\n\t\t\tinversePatches = ip\n\t\t})\n\n\t\tif (typeof Promise !== \"undefined\" && result instanceof Promise) {\n\t\t\treturn result.then(nextState => [nextState, patches!, inversePatches!])\n\t\t}\n\t\treturn [result, patches!, inversePatches!]\n\t}\n\n\tcreateDraft<T extends Objectish>(base: T): Draft<T> {\n\t\tif (!isDraftable(base)) die(8)\n\t\tif (isDraft(base)) base = current(base)\n\t\tconst scope = enterScope(this)\n\t\tconst proxy = createProxy(this, base, undefined)\n\t\tproxy[DRAFT_STATE].isManual_ = true\n\t\tleaveScope(scope)\n\t\treturn proxy as any\n\t}\n\n\tfinishDraft<D extends Draft<any>>(\n\t\tdraft: D,\n\t\tpatchListener?: PatchListener\n\t): D extends Draft<infer T> ? T : never {\n\t\tconst state: ImmerState = draft && (draft as any)[DRAFT_STATE]\n\t\tif (__DEV__) {\n\t\t\tif (!state || !state.isManual_) die(9)\n\t\t\tif (state.finalized_) die(10)\n\t\t}\n\t\tconst {scope_: scope} = state\n\t\tusePatchesInScope(scope, patchListener)\n\t\treturn processResult(undefined, scope)\n\t}\n\n\t/**\n\t * Pass true to automatically freeze all copies created by Immer.\n\t *\n\t * By default, auto-freezing is enabled.\n\t */\n\tsetAutoFreeze(value: boolean) {\n\t\tthis.autoFreeze_ = value\n\t}\n\n\t/**\n\t * Pass true to use the ES2015 `Proxy` class when creating drafts, which is\n\t * always faster than using ES5 proxies.\n\t *\n\t * By default, feature detection is used, so calling this is rarely necessary.\n\t */\n\tsetUseProxies(value: boolean) {\n\t\tif (value && !hasProxies) {\n\t\t\tdie(20)\n\t\t}\n\t\tthis.useProxies_ = value\n\t}\n\n\tapplyPatches<T extends Objectish>(base: T, patches: Patch[]): T {\n\t\t// If a patch replaces the entire state, take that replacement as base\n\t\t// before applying patches\n\t\tlet i: number\n\t\tfor (i = patches.length - 1; i >= 0; i--) {\n\t\t\tconst patch = patches[i]\n\t\t\tif (patch.path.length === 0 && patch.op === \"replace\") {\n\t\t\t\tbase = patch.value\n\t\t\t\tbreak\n\t\t\t}\n\t\t}\n\t\t// If there was a patch that replaced the entire state, start from the\n\t\t// patch after that.\n\t\tif (i > -1) {\n\t\t\tpatches = patches.slice(i + 1)\n\t\t}\n\n\t\tconst applyPatchesImpl = getPlugin(\"Patches\").applyPatches_\n\t\tif (isDraft(base)) {\n\t\t\t// N.B: never hits if some patch a replacement, patches are never drafts\n\t\t\treturn applyPatchesImpl(base, patches)\n\t\t}\n\t\t// Otherwise, produce a copy of the base state.\n\t\treturn this.produce(base, (draft: Drafted) =>\n\t\t\tapplyPatchesImpl(draft, patches)\n\t\t)\n\t}\n}\n\nexport function createProxy<T extends Objectish>(\n\timmer: Immer,\n\tvalue: T,\n\tparent?: ImmerState\n): Drafted<T, ImmerState> {\n\t// precondition: createProxy should be guarded by isDraftable, so we know we can safely draft\n\tconst draft: Drafted = isMap(value)\n\t\t? getPlugin(\"MapSet\").proxyMap_(value, parent)\n\t\t: isSet(value)\n\t\t? getPlugin(\"MapSet\").proxySet_(value, parent)\n\t\t: immer.useProxies_\n\t\t? createProxyProxy(value, parent)\n\t\t: getPlugin(\"ES5\").createES5Proxy_(value, parent)\n\n\tconst scope = parent ? parent.scope_ : getCurrentScope()\n\tscope.drafts_.push(draft)\n\treturn draft\n}\n", "import {\n\tdie,\n\tisDraft,\n\tshallowCopy,\n\teach,\n\tDRAFT_STATE,\n\tget,\n\tset,\n\tImmerState,\n\tisDraftable,\n\tArchtype,\n\tgetArchtype,\n\tgetPlugin\n} from \"../internal\"\n\n/** Takes a snapshot of the current state of a draft and finalizes it (but without freezing). This is a great utility to print the current state during debugging (no Proxies in the way). The output of current can also be safely leaked outside the producer. */\nexport function current<T>(value: T): T\nexport function current(value: any): any {\n\tif (!isDraft(value)) die(22, value)\n\treturn currentImpl(value)\n}\n\nfunction currentImpl(value: any): any {\n\tif (!isDraftable(value)) return value\n\tconst state: ImmerState | undefined = value[DRAFT_STATE]\n\tlet copy: any\n\tconst archType = getArchtype(value)\n\tif (state) {\n\t\tif (\n\t\t\t!state.modified_ &&\n\t\t\t(state.type_ < 4 || !getPlugin(\"ES5\").hasChanges_(state as any))\n\t\t)\n\t\t\treturn state.base_\n\t\t// Optimization: avoid generating new drafts during copying\n\t\tstate.finalized_ = true\n\t\tcopy = copyHelper(value, archType)\n\t\tstate.finalized_ = false\n\t} else {\n\t\tcopy = copyHelper(value, archType)\n\t}\n\n\teach(copy, (key, childValue) => {\n\t\tif (state && get(state.base_, key) === childValue) return // no need to copy or search in something that didn't change\n\t\tset(copy, key, currentImpl(childValue))\n\t})\n\t// In the future, we might consider freezing here, based on the current settings\n\treturn archType === Archtype.Set ? new Set(copy) : copy\n}\n\nfunction copyHelper(value: any, archType: number): any {\n\t// creates a shallow copy, even if it is a map or set\n\tswitch (archType) {\n\t\tcase Archtype.Map:\n\t\t\treturn new Map(value)\n\t\tcase Archtype.Set:\n\t\t\t// Set will be cloned as array temporarily, so that we can replace individual items\n\t\t\treturn Array.from(value)\n\t}\n\treturn shallowCopy(value)\n}\n", "import {\n\tImmerState,\n\tDrafted,\n\tES5ArrayState,\n\tES5ObjectState,\n\teach,\n\thas,\n\tisDraft,\n\tlatest,\n\tDRAFT_STATE,\n\tis,\n\tloadPlugin,\n\tImmerScope,\n\tProxyType,\n\tgetCurrentScope,\n\tdie,\n\tmarkChanged,\n\tobjectTraps,\n\townKeys,\n\tgetOwnPropertyDescriptors\n} from \"../internal\"\n\ntype ES5State = ES5ArrayState | ES5ObjectState\n\nexport function enableES5() {\n\tfunction willFinalizeES5_(\n\t\tscope: ImmerScope,\n\t\tresult: any,\n\t\tisReplaced: boolean\n\t) {\n\t\tif (!isReplaced) {\n\t\t\tif (scope.patches_) {\n\t\t\t\tmarkChangesRecursively(scope.drafts_![0])\n\t\t\t}\n\t\t\t// This is faster when we don't care about which attributes changed.\n\t\t\tmarkChangesSweep(scope.drafts_)\n\t\t}\n\t\t// When a child draft is returned, look for changes.\n\t\telse if (\n\t\t\tisDraft(result) &&\n\t\t\t(result[DRAFT_STATE] as ES5State).scope_ === scope\n\t\t) {\n\t\t\tmarkChangesSweep(scope.drafts_)\n\t\t}\n\t}\n\n\tfunction createES5Draft(isArray: boolean, base: any) {\n\t\tif (isArray) {\n\t\t\tconst draft = new Array(base.length)\n\t\t\tfor (let i = 0; i < base.length; i++)\n\t\t\t\tObject.defineProperty(draft, \"\" + i, proxyProperty(i, true))\n\t\t\treturn draft\n\t\t} else {\n\t\t\tconst descriptors = getOwnPropertyDescriptors(base)\n\t\t\tdelete descriptors[DRAFT_STATE as any]\n\t\t\tconst keys = ownKeys(descriptors)\n\t\t\tfor (let i = 0; i < keys.length; i++) {\n\t\t\t\tconst key: any = keys[i]\n\t\t\t\tdescriptors[key] = proxyProperty(\n\t\t\t\t\tkey,\n\t\t\t\t\tisArray || !!descriptors[key].enumerable\n\t\t\t\t)\n\t\t\t}\n\t\t\treturn Object.create(Object.getPrototypeOf(base), descriptors)\n\t\t}\n\t}\n\n\tfunction createES5Proxy_<T>(\n\t\tbase: T,\n\t\tparent?: ImmerState\n\t): Drafted<T, ES5ObjectState | ES5ArrayState> {\n\t\tconst isArray = Array.isArray(base)\n\t\tconst draft = createES5Draft(isArray, base)\n\n\t\tconst state: ES5ObjectState | ES5ArrayState = {\n\t\t\ttype_: isArray ? ProxyType.ES5Array : (ProxyType.ES5Object as any),\n\t\t\tscope_: parent ? parent.scope_ : getCurrentScope(),\n\t\t\tmodified_: false,\n\t\t\tfinalized_: false,\n\t\t\tassigned_: {},\n\t\t\tparent_: parent,\n\t\t\t// base is the object we are drafting\n\t\t\tbase_: base,\n\t\t\t// draft is the draft object itself, that traps all reads and reads from either the base (if unmodified) or copy (if modified)\n\t\t\tdraft_: draft,\n\t\t\tcopy_: null,\n\t\t\trevoked_: false,\n\t\t\tisManual_: false\n\t\t}\n\n\t\tObject.defineProperty(draft, DRAFT_STATE, {\n\t\t\tvalue: state,\n\t\t\t// enumerable: false <- the default\n\t\t\twritable: true\n\t\t})\n\t\treturn draft\n\t}\n\n\t// property descriptors are recycled to make sure we don't create a get and set closure per property,\n\t// but share them all instead\n\tconst descriptors: {[prop: string]: PropertyDescriptor} = {}\n\n\tfunction proxyProperty(\n\t\tprop: string | number,\n\t\tenumerable: boolean\n\t): PropertyDescriptor {\n\t\tlet desc = descriptors[prop]\n\t\tif (desc) {\n\t\t\tdesc.enumerable = enumerable\n\t\t} else {\n\t\t\tdescriptors[prop] = desc = {\n\t\t\t\tconfigurable: true,\n\t\t\t\tenumerable,\n\t\t\t\tget(this: any) {\n\t\t\t\t\tconst state = this[DRAFT_STATE]\n\t\t\t\t\tif (__DEV__) assertUnrevoked(state)\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\treturn objectTraps.get(state, prop)\n\t\t\t\t},\n\t\t\t\tset(this: any, value) {\n\t\t\t\t\tconst state = this[DRAFT_STATE]\n\t\t\t\t\tif (__DEV__) assertUnrevoked(state)\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\tobjectTraps.set(state, prop, value)\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn desc\n\t}\n\n\t// This looks expensive, but only proxies are visited, and only objects without known changes are scanned.\n\tfunction markChangesSweep(drafts: Drafted<any, ImmerState>[]) {\n\t\t// The natural order of drafts in the `scope` array is based on when they\n\t\t// were accessed. By processing drafts in reverse natural order, we have a\n\t\t// better chance of processing leaf nodes first. When a leaf node is known to\n\t\t// have changed, we can avoid any traversal of its ancestor nodes.\n\t\tfor (let i = drafts.length - 1; i >= 0; i--) {\n\t\t\tconst state: ES5State = drafts[i][DRAFT_STATE]\n\t\t\tif (!state.modified_) {\n\t\t\t\tswitch (state.type_) {\n\t\t\t\t\tcase ProxyType.ES5Array:\n\t\t\t\t\t\tif (hasArrayChanges(state)) markChanged(state)\n\t\t\t\t\t\tbreak\n\t\t\t\t\tcase ProxyType.ES5Object:\n\t\t\t\t\t\tif (hasObjectChanges(state)) markChanged(state)\n\t\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction markChangesRecursively(object: any) {\n\t\tif (!object || typeof object !== \"object\") return\n\t\tconst state: ES5State | undefined = object[DRAFT_STATE]\n\t\tif (!state) return\n\t\tconst {base_, draft_, assigned_, type_} = state\n\t\tif (type_ === ProxyType.ES5Object) {\n\t\t\t// Look for added keys.\n\t\t\t// probably there is a faster way to detect changes, as sweep + recurse seems to do some\n\t\t\t// unnecessary work.\n\t\t\t// also: probably we can store the information we detect here, to speed up tree finalization!\n\t\t\teach(draft_, key => {\n\t\t\t\tif ((key as any) === DRAFT_STATE) return\n\t\t\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\t\t\tif ((base_ as any)[key] === undefined && !has(base_, key)) {\n\t\t\t\t\tassigned_[key] = true\n\t\t\t\t\tmarkChanged(state)\n\t\t\t\t} else if (!assigned_[key]) {\n\t\t\t\t\t// Only untouched properties trigger recursion.\n\t\t\t\t\tmarkChangesRecursively(draft_[key])\n\t\t\t\t}\n\t\t\t})\n\t\t\t// Look for removed keys.\n\t\t\teach(base_, key => {\n\t\t\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\t\t\tif (draft_[key] === undefined && !has(draft_, key)) {\n\t\t\t\t\tassigned_[key] = false\n\t\t\t\t\tmarkChanged(state)\n\t\t\t\t}\n\t\t\t})\n\t\t} else if (type_ === ProxyType.ES5Array) {\n\t\t\tif (hasArrayChanges(state as ES5ArrayState)) {\n\t\t\t\tmarkChanged(state)\n\t\t\t\tassigned_.length = true\n\t\t\t}\n\n\t\t\tif (draft_.length < base_.length) {\n\t\t\t\tfor (let i = draft_.length; i < base_.length; i++) assigned_[i] = false\n\t\t\t} else {\n\t\t\t\tfor (let i = base_.length; i < draft_.length; i++) assigned_[i] = true\n\t\t\t}\n\n\t\t\t// Minimum count is enough, the other parts has been processed.\n\t\t\tconst min = Math.min(draft_.length, base_.length)\n\n\t\t\tfor (let i = 0; i < min; i++) {\n\t\t\t\t// Only untouched indices trigger recursion.\n\t\t\t\tif (!draft_.hasOwnProperty(i)) {\n\t\t\t\t\tassigned_[i] = true\n\t\t\t\t}\n\t\t\t\tif (assigned_[i] === undefined) markChangesRecursively(draft_[i])\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction hasObjectChanges(state: ES5ObjectState) {\n\t\tconst {base_, draft_} = state\n\n\t\t// Search for added keys and changed keys. Start at the back, because\n\t\t// non-numeric keys are ordered by time of definition on the object.\n\t\tconst keys = ownKeys(draft_)\n\t\tfor (let i = keys.length - 1; i >= 0; i--) {\n\t\t\tconst key: any = keys[i]\n\t\t\tif (key === DRAFT_STATE) continue\n\t\t\tconst baseValue = base_[key]\n\t\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\t\tif (baseValue === undefined && !has(base_, key)) {\n\t\t\t\treturn true\n\t\t\t}\n\t\t\t// Once a base key is deleted, future changes go undetected, because its\n\t\t\t// descriptor is erased. This branch detects any missed changes.\n\t\t\telse {\n\t\t\t\tconst value = draft_[key]\n\t\t\t\tconst state: ImmerState = value && value[DRAFT_STATE]\n\t\t\t\tif (state ? state.base_ !== baseValue : !is(value, baseValue)) {\n\t\t\t\t\treturn true\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// At this point, no keys were added or changed.\n\t\t// Compare key count to determine if keys were deleted.\n\t\tconst baseIsDraft = !!base_[DRAFT_STATE as any]\n\t\treturn keys.length !== ownKeys(base_).length + (baseIsDraft ? 0 : 1) // + 1 to correct for DRAFT_STATE\n\t}\n\n\tfunction hasArrayChanges(state: ES5ArrayState) {\n\t\tconst {draft_} = state\n\t\tif (draft_.length !== state.base_.length) return true\n\t\t// See #116\n\t\t// If we first shorten the length, our array interceptors will be removed.\n\t\t// If after that new items are added, result in the same original length,\n\t\t// those last items will have no intercepting property.\n\t\t// So if there is no own descriptor on the last position, we know that items were removed and added\n\t\t// N.B.: splice, unshift, etc only shift values around, but not prop descriptors, so we only have to check\n\t\t// the last one\n\t\t// last descriptor can be not a trap, if the array was extended\n\t\tconst descriptor = Object.getOwnPropertyDescriptor(\n\t\t\tdraft_,\n\t\t\tdraft_.length - 1\n\t\t)\n\t\t// descriptor can be null, but only for newly created sparse arrays, eg. new Array(10)\n\t\tif (descriptor && !descriptor.get) return true\n\t\t// if we miss a property, it has been deleted, so array probobaly changed\n\t\tfor (let i = 0; i < draft_.length; i++) {\n\t\t\tif (!draft_.hasOwnProperty(i)) return true\n\t\t}\n\t\t// For all other cases, we don't have to compare, as they would have been picked up by the index setters\n\t\treturn false\n\t}\n\n\tfunction hasChanges_(state: ES5State) {\n\t\treturn state.type_ === ProxyType.ES5Object\n\t\t\t? hasObjectChanges(state)\n\t\t\t: hasArrayChanges(state)\n\t}\n\n\tfunction assertUnrevoked(state: any /*ES5State | MapState | SetState*/) {\n\t\tif (state.revoked_) die(3, JSON.stringify(latest(state)))\n\t}\n\n\tloadPlugin(\"ES5\", {\n\t\tcreateES5Proxy_,\n\t\twillFinalizeES5_,\n\t\thasChanges_\n\t})\n}\n", "import {immerable} from \"../immer\"\nimport {\n\tImmerState,\n\tPatch,\n\tSetState,\n\tES5ArrayState,\n\tProxyArrayState,\n\tMapState,\n\tES5ObjectState,\n\tProxyObjectState,\n\tPatchPath,\n\tget,\n\teach,\n\thas,\n\tgetArchtype,\n\tisSet,\n\tisMap,\n\tloadPlugin,\n\tProxyType,\n\tArchtype,\n\tdie,\n\tisDraft,\n\tisDraftable,\n\tNOTHING\n} from \"../internal\"\n\nexport function enablePatches() {\n\tconst REPLACE = \"replace\"\n\tconst ADD = \"add\"\n\tconst REMOVE = \"remove\"\n\n\tfunction generatePatches_(\n\t\tstate: ImmerState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t): void {\n\t\tswitch (state.type_) {\n\t\t\tcase ProxyType.ProxyObject:\n\t\t\tcase ProxyType.ES5Object:\n\t\t\tcase ProxyType.Map:\n\t\t\t\treturn generatePatchesFromAssigned(\n\t\t\t\t\tstate,\n\t\t\t\t\tbasePath,\n\t\t\t\t\tpatches,\n\t\t\t\t\tinversePatches\n\t\t\t\t)\n\t\t\tcase ProxyType.ES5Array:\n\t\t\tcase ProxyType.ProxyArray:\n\t\t\t\treturn generateArrayPatches(state, basePath, patches, inversePatches)\n\t\t\tcase ProxyType.Set:\n\t\t\t\treturn generateSetPatches(\n\t\t\t\t\t(state as any) as SetState,\n\t\t\t\t\tbasePath,\n\t\t\t\t\tpatches,\n\t\t\t\t\tinversePatches\n\t\t\t\t)\n\t\t}\n\t}\n\n\tfunction generateArrayPatches(\n\t\tstate: ES5ArrayState | ProxyArrayState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tlet {base_, assigned_} = state\n\t\tlet copy_ = state.copy_!\n\n\t\t// Reduce complexity by ensuring `base` is never longer.\n\t\tif (copy_.length < base_.length) {\n\t\t\t// @ts-ignore\n\t\t\t;[base_, copy_] = [copy_, base_]\n\t\t\t;[patches, inversePatches] = [inversePatches, patches]\n\t\t}\n\n\t\t// Process replaced indices.\n\t\tfor (let i = 0; i < base_.length; i++) {\n\t\t\tif (assigned_[i] && copy_[i] !== base_[i]) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: REPLACE,\n\t\t\t\t\tpath,\n\t\t\t\t\t// Need to maybe clone it, as it can in fact be the original value\n\t\t\t\t\t// due to the base/copy inversion at the start of this function\n\t\t\t\t\tvalue: clonePatchValueIfNeeded(copy_[i])\n\t\t\t\t})\n\t\t\t\tinversePatches.push({\n\t\t\t\t\top: REPLACE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue: clonePatchValueIfNeeded(base_[i])\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\n\t\t// Process added indices.\n\t\tfor (let i = base_.length; i < copy_.length; i++) {\n\t\t\tconst path = basePath.concat([i])\n\t\t\tpatches.push({\n\t\t\t\top: ADD,\n\t\t\t\tpath,\n\t\t\t\t// Need to maybe clone it, as it can in fact be the original value\n\t\t\t\t// due to the base/copy inversion at the start of this function\n\t\t\t\tvalue: clonePatchValueIfNeeded(copy_[i])\n\t\t\t})\n\t\t}\n\t\tif (base_.length < copy_.length) {\n\t\t\tinversePatches.push({\n\t\t\t\top: REPLACE,\n\t\t\t\tpath: basePath.concat([\"length\"]),\n\t\t\t\tvalue: base_.length\n\t\t\t})\n\t\t}\n\t}\n\n\t// This is used for both Map objects and normal objects.\n\tfunction generatePatchesFromAssigned(\n\t\tstate: MapState | ES5ObjectState | ProxyObjectState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tconst {base_, copy_} = state\n\t\teach(state.assigned_!, (key, assignedValue) => {\n\t\t\tconst origValue = get(base_, key)\n\t\t\tconst value = get(copy_!, key)\n\t\t\tconst op = !assignedValue ? REMOVE : has(base_, key) ? REPLACE : ADD\n\t\t\tif (origValue === value && op === REPLACE) return\n\t\t\tconst path = basePath.concat(key as any)\n\t\t\tpatches.push(op === REMOVE ? {op, path} : {op, path, value})\n\t\t\tinversePatches.push(\n\t\t\t\top === ADD\n\t\t\t\t\t? {op: REMOVE, path}\n\t\t\t\t\t: op === REMOVE\n\t\t\t\t\t? {op: ADD, path, value: clonePatchValueIfNeeded(origValue)}\n\t\t\t\t\t: {op: REPLACE, path, value: clonePatchValueIfNeeded(origValue)}\n\t\t\t)\n\t\t})\n\t}\n\n\tfunction generateSetPatches(\n\t\tstate: SetState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tlet {base_, copy_} = state\n\n\t\tlet i = 0\n\t\tbase_.forEach((value: any) => {\n\t\t\tif (!copy_!.has(value)) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: REMOVE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t\tinversePatches.unshift({\n\t\t\t\t\top: ADD,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t}\n\t\t\ti++\n\t\t})\n\t\ti = 0\n\t\tcopy_!.forEach((value: any) => {\n\t\t\tif (!base_.has(value)) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: ADD,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t\tinversePatches.unshift({\n\t\t\t\t\top: REMOVE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t}\n\t\t\ti++\n\t\t})\n\t}\n\n\tfunction generateReplacementPatches_(\n\t\tbaseValue: any,\n\t\treplacement: any,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t): void {\n\t\tpatches.push({\n\t\t\top: REPLACE,\n\t\t\tpath: [],\n\t\t\tvalue: replacement === NOTHING ? undefined : replacement\n\t\t})\n\t\tinversePatches.push({\n\t\t\top: REPLACE,\n\t\t\tpath: [],\n\t\t\tvalue: baseValue\n\t\t})\n\t}\n\n\tfunction applyPatches_<T>(draft: T, patches: Patch[]): T {\n\t\tpatches.forEach(patch => {\n\t\t\tconst {path, op} = patch\n\n\t\t\tlet base: any = draft\n\t\t\tfor (let i = 0; i < path.length - 1; i++) {\n\t\t\t\tconst parentType = getArchtype(base)\n\t\t\t\tconst p = \"\" + path[i]\n\t\t\t\t// See #738, avoid prototype pollution\n\t\t\t\tif (\n\t\t\t\t\t(parentType === Archtype.Object || parentType === Archtype.Array) &&\n\t\t\t\t\t(p === \"__proto__\" || p === \"constructor\")\n\t\t\t\t)\n\t\t\t\t\tdie(24)\n\t\t\t\tif (typeof base === \"function\" && p === \"prototype\") die(24)\n\t\t\t\tbase = get(base, p)\n\t\t\t\tif (typeof base !== \"object\") die(15, path.join(\"/\"))\n\t\t\t}\n\n\t\t\tconst type = getArchtype(base)\n\t\t\tconst value = deepClonePatchValue(patch.value) // used to clone patch to ensure original patch is not modified, see #411\n\t\t\tconst key = path[path.length - 1]\n\t\t\tswitch (op) {\n\t\t\t\tcase REPLACE:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase Archtype.Map:\n\t\t\t\t\t\t\treturn base.set(key, value)\n\t\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\t\tcase Archtype.Set:\n\t\t\t\t\t\t\tdie(16)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t// if value is an object, then it's assigned by reference\n\t\t\t\t\t\t\t// in the following add or remove ops, the value field inside the patch will also be modifyed\n\t\t\t\t\t\t\t// so we use value from the cloned patch\n\t\t\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t\t\treturn (base[key] = value)\n\t\t\t\t\t}\n\t\t\t\tcase ADD:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase Archtype.Array:\n\t\t\t\t\t\t\treturn key === \"-\"\n\t\t\t\t\t\t\t\t? base.push(value)\n\t\t\t\t\t\t\t\t: base.splice(key as any, 0, value)\n\t\t\t\t\t\tcase Archtype.Map:\n\t\t\t\t\t\t\treturn base.set(key, value)\n\t\t\t\t\t\tcase Archtype.Set:\n\t\t\t\t\t\t\treturn base.add(value)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\treturn (base[key] = value)\n\t\t\t\t\t}\n\t\t\t\tcase REMOVE:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase Archtype.Array:\n\t\t\t\t\t\t\treturn base.splice(key as any, 1)\n\t\t\t\t\t\tcase Archtype.Map:\n\t\t\t\t\t\t\treturn base.delete(key)\n\t\t\t\t\t\tcase Archtype.Set:\n\t\t\t\t\t\t\treturn base.delete(patch.value)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\treturn delete base[key]\n\t\t\t\t\t}\n\t\t\t\tdefault:\n\t\t\t\t\tdie(17, op)\n\t\t\t}\n\t\t})\n\n\t\treturn draft\n\t}\n\n\t// optimize: this is quite a performance hit, can we detect intelligently when it is needed?\n\t// E.g. auto-draft when new objects from outside are assigned and modified?\n\t// (See failing test when deepClone just returns obj)\n\tfunction deepClonePatchValue<T>(obj: T): T\n\tfunction deepClonePatchValue(obj: any) {\n\t\tif (!isDraftable(obj)) return obj\n\t\tif (Array.isArray(obj)) return obj.map(deepClonePatchValue)\n\t\tif (isMap(obj))\n\t\t\treturn new Map(\n\t\t\t\tArray.from(obj.entries()).map(([k, v]) => [k, deepClonePatchValue(v)])\n\t\t\t)\n\t\tif (isSet(obj)) return new Set(Array.from(obj).map(deepClonePatchValue))\n\t\tconst cloned = Object.create(Object.getPrototypeOf(obj))\n\t\tfor (const key in obj) cloned[key] = deepClonePatchValue(obj[key])\n\t\tif (has(obj, immerable)) cloned[immerable] = obj[immerable]\n\t\treturn cloned\n\t}\n\n\tfunction clonePatchValueIfNeeded<T>(obj: T): T {\n\t\tif (isDraft(obj)) {\n\t\t\treturn deepClonePatchValue(obj)\n\t\t} else return obj\n\t}\n\n\tloadPlugin(\"Patches\", {\n\t\tapplyPatches_,\n\t\tgeneratePatches_,\n\t\tgenerateReplacementPatches_\n\t})\n}\n", "// types only!\nimport {\n\tImmerState,\n\tAnyMap,\n\tAnySet,\n\tMapState,\n\tSetState,\n\tDRAFT_STATE,\n\tgetCurrentScope,\n\tlatest,\n\titeratorSymbol,\n\tisDraftable,\n\tcreateProxy,\n\tloadPlugin,\n\tmarkChanged,\n\tProxyType,\n\tdie,\n\teach\n} from \"../internal\"\n\nexport function enableMapSet() {\n\t/* istanbul ignore next */\n\tvar extendStatics = function(d: any, b: any): any {\n\t\textendStatics =\n\t\t\tObject.setPrototypeOf ||\n\t\t\t({__proto__: []} instanceof Array &&\n\t\t\t\tfunction(d, b) {\n\t\t\t\t\td.__proto__ = b\n\t\t\t\t}) ||\n\t\t\tfunction(d, b) {\n\t\t\t\tfor (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]\n\t\t\t}\n\t\treturn extendStatics(d, b)\n\t}\n\n\t// Ugly hack to resolve #502 and inherit built in Map / Set\n\tfunction __extends(d: any, b: any): any {\n\t\textendStatics(d, b)\n\t\tfunction __(this: any): any {\n\t\t\tthis.constructor = d\n\t\t}\n\t\td.prototype =\n\t\t\t// @ts-ignore\n\t\t\t((__.prototype = b.prototype), new __())\n\t}\n\n\tconst DraftMap = (function(_super) {\n\t\t__extends(DraftMap, _super)\n\t\t// Create class manually, cause #502\n\t\tfunction DraftMap(this: any, target: AnyMap, parent?: ImmerState): any {\n\t\t\tthis[DRAFT_STATE] = {\n\t\t\t\ttype_: ProxyType.Map,\n\t\t\t\tparent_: parent,\n\t\t\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t\t\tmodified_: false,\n\t\t\t\tfinalized_: false,\n\t\t\t\tcopy_: undefined,\n\t\t\t\tassigned_: undefined,\n\t\t\t\tbase_: target,\n\t\t\t\tdraft_: this as any,\n\t\t\t\tisManual_: false,\n\t\t\t\trevoked_: false\n\t\t\t} as MapState\n\t\t\treturn this\n\t\t}\n\t\tconst p = DraftMap.prototype\n\n\t\tObject.defineProperty(p, \"size\", {\n\t\t\tget: function() {\n\t\t\t\treturn latest(this[DRAFT_STATE]).size\n\t\t\t}\n\t\t\t// enumerable: false,\n\t\t\t// configurable: true\n\t\t})\n\n\t\tp.has = function(key: any): boolean {\n\t\t\treturn latest(this[DRAFT_STATE]).has(key)\n\t\t}\n\n\t\tp.set = function(key: any, value: any) {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (!latest(state).has(key) || latest(state).get(key) !== value) {\n\t\t\t\tprepareMapCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.assigned_!.set(key, true)\n\t\t\t\tstate.copy_!.set(key, value)\n\t\t\t\tstate.assigned_!.set(key, true)\n\t\t\t}\n\t\t\treturn this\n\t\t}\n\n\t\tp.delete = function(key: any): boolean {\n\t\t\tif (!this.has(key)) {\n\t\t\t\treturn false\n\t\t\t}\n\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareMapCopy(state)\n\t\t\tmarkChanged(state)\n\t\t\tif (state.base_.has(key)) {\n\t\t\t\tstate.assigned_!.set(key, false)\n\t\t\t} else {\n\t\t\t\tstate.assigned_!.delete(key)\n\t\t\t}\n\t\t\tstate.copy_!.delete(key)\n\t\t\treturn true\n\t\t}\n\n\t\tp.clear = function() {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (latest(state).size) {\n\t\t\t\tprepareMapCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.assigned_ = new Map()\n\t\t\t\teach(state.base_, key => {\n\t\t\t\t\tstate.assigned_!.set(key, false)\n\t\t\t\t})\n\t\t\t\tstate.copy_!.clear()\n\t\t\t}\n\t\t}\n\n\t\tp.forEach = function(\n\t\t\tcb: (value: any, key: any, self: any) => void,\n\t\t\tthisArg?: any\n\t\t) {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tlatest(state).forEach((_value: any, key: any, _map: any) => {\n\t\t\t\tcb.call(thisArg, this.get(key), key, this)\n\t\t\t})\n\t\t}\n\n\t\tp.get = function(key: any): any {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tconst value = latest(state).get(key)\n\t\t\tif (state.finalized_ || !isDraftable(value)) {\n\t\t\t\treturn value\n\t\t\t}\n\t\t\tif (value !== state.base_.get(key)) {\n\t\t\t\treturn value // either already drafted or reassigned\n\t\t\t}\n\t\t\t// despite what it looks, this creates a draft only once, see above condition\n\t\t\tconst draft = createProxy(state.scope_.immer_, value, state)\n\t\t\tprepareMapCopy(state)\n\t\t\tstate.copy_!.set(key, draft)\n\t\t\treturn draft\n\t\t}\n\n\t\tp.keys = function(): IterableIterator<any> {\n\t\t\treturn latest(this[DRAFT_STATE]).keys()\n\t\t}\n\n\t\tp.values = function(): IterableIterator<any> {\n\t\t\tconst iterator = this.keys()\n\t\t\treturn {\n\t\t\t\t[iteratorSymbol]: () => this.values(),\n\t\t\t\tnext: () => {\n\t\t\t\t\tconst r = iterator.next()\n\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\tif (r.done) return r\n\t\t\t\t\tconst value = this.get(r.value)\n\t\t\t\t\treturn {\n\t\t\t\t\t\tdone: false,\n\t\t\t\t\t\tvalue\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} as any\n\t\t}\n\n\t\tp.entries = function(): IterableIterator<[any, any]> {\n\t\t\tconst iterator = this.keys()\n\t\t\treturn {\n\t\t\t\t[iteratorSymbol]: () => this.entries(),\n\t\t\t\tnext: () => {\n\t\t\t\t\tconst r = iterator.next()\n\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\tif (r.done) return r\n\t\t\t\t\tconst value = this.get(r.value)\n\t\t\t\t\treturn {\n\t\t\t\t\t\tdone: false,\n\t\t\t\t\t\tvalue: [r.value, value]\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} as any\n\t\t}\n\n\t\tp[iteratorSymbol] = function() {\n\t\t\treturn this.entries()\n\t\t}\n\n\t\treturn DraftMap\n\t})(Map)\n\n\tfunction proxyMap_<T extends AnyMap>(target: T, parent?: ImmerState): T {\n\t\t// @ts-ignore\n\t\treturn new DraftMap(target, parent)\n\t}\n\n\tfunction prepareMapCopy(state: MapState) {\n\t\tif (!state.copy_) {\n\t\t\tstate.assigned_ = new Map()\n\t\t\tstate.copy_ = new Map(state.base_)\n\t\t}\n\t}\n\n\tconst DraftSet = (function(_super) {\n\t\t__extends(DraftSet, _super)\n\t\t// Create class manually, cause #502\n\t\tfunction DraftSet(this: any, target: AnySet, parent?: ImmerState) {\n\t\t\tthis[DRAFT_STATE] = {\n\t\t\t\ttype_: ProxyType.Set,\n\t\t\t\tparent_: parent,\n\t\t\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t\t\tmodified_: false,\n\t\t\t\tfinalized_: false,\n\t\t\t\tcopy_: undefined,\n\t\t\t\tbase_: target,\n\t\t\t\tdraft_: this,\n\t\t\t\tdrafts_: new Map(),\n\t\t\t\trevoked_: false,\n\t\t\t\tisManual_: false\n\t\t\t} as SetState\n\t\t\treturn this\n\t\t}\n\t\tconst p = DraftSet.prototype\n\n\t\tObject.defineProperty(p, \"size\", {\n\t\t\tget: function() {\n\t\t\t\treturn latest(this[DRAFT_STATE]).size\n\t\t\t}\n\t\t\t// enumerable: true,\n\t\t})\n\n\t\tp.has = function(value: any): boolean {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\t// bit of trickery here, to be able to recognize both the value, and the draft of its value\n\t\t\tif (!state.copy_) {\n\t\t\t\treturn state.base_.has(value)\n\t\t\t}\n\t\t\tif (state.copy_.has(value)) return true\n\t\t\tif (state.drafts_.has(value) && state.copy_.has(state.drafts_.get(value)))\n\t\t\t\treturn true\n\t\t\treturn false\n\t\t}\n\n\t\tp.add = function(value: any): any {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (!this.has(value)) {\n\t\t\t\tprepareSetCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.copy_!.add(value)\n\t\t\t}\n\t\t\treturn this\n\t\t}\n\n\t\tp.delete = function(value: any): any {\n\t\t\tif (!this.has(value)) {\n\t\t\t\treturn false\n\t\t\t}\n\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\tmarkChanged(state)\n\t\t\treturn (\n\t\t\t\tstate.copy_!.delete(value) ||\n\t\t\t\t(state.drafts_.has(value)\n\t\t\t\t\t? state.copy_!.delete(state.drafts_.get(value))\n\t\t\t\t\t: /* istanbul ignore next */ false)\n\t\t\t)\n\t\t}\n\n\t\tp.clear = function() {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (latest(state).size) {\n\t\t\t\tprepareSetCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.copy_!.clear()\n\t\t\t}\n\t\t}\n\n\t\tp.values = function(): IterableIterator<any> {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\treturn state.copy_!.values()\n\t\t}\n\n\t\tp.entries = function entries(): IterableIterator<[any, any]> {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\treturn state.copy_!.entries()\n\t\t}\n\n\t\tp.keys = function(): IterableIterator<any> {\n\t\t\treturn this.values()\n\t\t}\n\n\t\tp[iteratorSymbol] = function() {\n\t\t\treturn this.values()\n\t\t}\n\n\t\tp.forEach = function forEach(cb: any, thisArg?: any) {\n\t\t\tconst iterator = this.values()\n\t\t\tlet result = iterator.next()\n\t\t\twhile (!result.done) {\n\t\t\t\tcb.call(thisArg, result.value, result.value, this)\n\t\t\t\tresult = iterator.next()\n\t\t\t}\n\t\t}\n\n\t\treturn DraftSet\n\t})(Set)\n\n\tfunction proxySet_<T extends AnySet>(target: T, parent?: ImmerState): T {\n\t\t// @ts-ignore\n\t\treturn new DraftSet(target, parent)\n\t}\n\n\tfunction prepareSetCopy(state: SetState) {\n\t\tif (!state.copy_) {\n\t\t\t// create drafts for all entries to preserve insertion order\n\t\t\tstate.copy_ = new Set()\n\t\t\tstate.base_.forEach(value => {\n\t\t\t\tif (isDraftable(value)) {\n\t\t\t\t\tconst draft = createProxy(state.scope_.immer_, value, state)\n\t\t\t\t\tstate.drafts_.set(value, draft)\n\t\t\t\t\tstate.copy_!.add(draft)\n\t\t\t\t} else {\n\t\t\t\t\tstate.copy_!.add(value)\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\t}\n\n\tfunction assertUnrevoked(state: any /*ES5State | MapState | SetState*/) {\n\t\tif (state.revoked_) die(3, JSON.stringify(latest(state)))\n\t}\n\n\tloadPlugin(\"MapSet\", {proxyMap_, proxySet_})\n}\n", "import {enableES5} from \"./es5\"\nimport {enableMapSet} from \"./mapset\"\nimport {enablePatches} from \"./patches\"\n\nexport function enableAllPlugins() {\n\tenableES5()\n\tenableMapSet()\n\tenablePatches()\n}\n", "import {\n\tIProduce,\n\tIProduceWithPatches,\n\tImmer,\n\tDraft,\n\tImmutable\n} from \"./internal\"\n\nexport {\n\tDraft,\n\tImmutable,\n\tPatch,\n\tPatchListener,\n\toriginal,\n\tcurrent,\n\tisDraft,\n\tisDraftable,\n\tNOTHING as nothing,\n\tDRAFTABLE as immerable,\n\tfreeze\n} from \"./internal\"\n\nconst immer = new Immer()\n\n/**\n * The `produce` function takes a value and a \"recipe function\" (whose\n * return value often depends on the base state). The recipe function is\n * free to mutate its first argument however it wants. All mutations are\n * only ever applied to a __copy__ of the base state.\n *\n * Pass only a function to create a \"curried producer\" which relieves you\n * from passing the recipe function every time.\n *\n * Only plain objects and arrays are made mutable. All other objects are\n * considered uncopyable.\n *\n * Note: This function is __bound__ to its `Immer` instance.\n *\n * @param {any} base - the initial state\n * @param {Function} producer - function that receives a proxy of the base state as first argument and which can be freely modified\n * @param {Function} patchListener - optional function that will be called with all the patches produced here\n * @returns {any} a new state, or the initial state if nothing was modified\n */\nexport const produce: IProduce = immer.produce\nexport default produce\n\n/**\n * Like `produce`, but `produceWithPatches` always returns a tuple\n * [nextState, patches, inversePatches] (instead of just the next state)\n */\nexport const produceWithPatches: IProduceWithPatches = immer.produceWithPatches.bind(\n\timmer\n)\n\n/**\n * Pass true to automatically freeze all copies created by Immer.\n *\n * Always freeze by default, even in production mode\n */\nexport const setAutoFreeze = immer.setAutoFreeze.bind(immer)\n\n/**\n * Pass true to use the ES2015 `Proxy` class when creating drafts, which is\n * always faster than using ES5 proxies.\n *\n * By default, feature detection is used, so calling this is rarely necessary.\n */\nexport const setUseProxies = immer.setUseProxies.bind(immer)\n\n/**\n * Apply an array of Immer patches to the first argument.\n *\n * This function is a producer, which means copy-on-write is in effect.\n */\nexport const applyPatches = immer.applyPatches.bind(immer)\n\n/**\n * Create an Immer draft from the given base state, which may be a draft itself.\n * The draft can be modified until you finalize it with the `finishDraft` function.\n */\nexport const createDraft = immer.createDraft.bind(immer)\n\n/**\n * Finalize an Immer draft from a `createDraft` call, returning the base state\n * (if no changes were made) or a modified copy. The draft must *not* be\n * mutated afterwards.\n *\n * Pass a function as the 2nd argument to generate Immer patches based on the\n * changes that were made.\n */\nexport const finishDraft = immer.finishDraft.bind(immer)\n\n/**\n * This function is actually a no-op, but can be used to cast an immutable type\n * to an draft type and make TypeScript happy\n *\n * @param value\n */\nexport function castDraft<T>(value: T): Draft<T> {\n\treturn value as any\n}\n\n/**\n * This function is actually a no-op, but can be used to cast a mutable type\n * to an immutable type and make TypeScript happy\n * @param value\n */\nexport function castImmutable<T>(value: T): Immutable<T> {\n\treturn value as any\n}\n\nexport {Immer}\n\nexport {enableES5} from \"./plugins/es5\"\nexport {enablePatches} from \"./plugins/patches\"\nexport {enableMapSet} from \"./plugins/mapset\"\nexport {enableAllPlugins} from \"./plugins/all\"\n", "// Should be no imports here!\n\n// Some things that should be evaluated before all else...\n\n// We only want to know if non-polyfilled symbols are available\nconst hasSymbol =\n\ttypeof Symbol !== \"undefined\" && typeof Symbol(\"x\") === \"symbol\"\nexport const hasMap = typeof Map !== \"undefined\"\nexport const hasSet = typeof Set !== \"undefined\"\nexport const hasProxies =\n\ttypeof Proxy !== \"undefined\" &&\n\ttypeof Proxy.revocable !== \"undefined\" &&\n\ttypeof Reflect !== \"undefined\"\n\n/**\n * The sentinel value returned by producers to replace the draft with undefined.\n */\nexport const NOTHING: Nothing = hasSymbol\n\t? Symbol.for(\"immer-nothing\")\n\t: ({[\"immer-nothing\"]: true} as any)\n\n/**\n * To let Immer treat your class instances as plain immutable objects\n * (albeit with a custom prototype), you must define either an instance property\n * or a static property on each of your custom classes.\n *\n * Otherwise, your class instance will never be drafted, which means it won't be\n * safe to mutate in a produce callback.\n */\nexport const DRAFTABLE: unique symbol = hasSymbol\n\t? Symbol.for(\"immer-draftable\")\n\t: (\"__$immer_draftable\" as any)\n\nexport const DRAFT_STATE: unique symbol = hasSymbol\n\t? Symbol.for(\"immer-state\")\n\t: (\"__$immer_state\" as any)\n\n// Even a polyfilled Symbol might provide Symbol.iterator\nexport const iteratorSymbol: typeof Symbol.iterator =\n\t(typeof Symbol != \"undefined\" && Symbol.iterator) || (\"@@iterator\" as any)\n\n/** Use a class type for `nothing` so its type is unique */\nexport class Nothing {\n\t// This lets us do `Exclude<T, Nothing>`\n\t// @ts-ignore\n\tprivate _!: unique symbol\n}\n"]}, "metadata": {}, "sourceType": "module"}