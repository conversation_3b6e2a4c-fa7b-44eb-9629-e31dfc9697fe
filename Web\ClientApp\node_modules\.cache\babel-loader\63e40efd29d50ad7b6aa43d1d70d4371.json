{"ast": null, "code": "import { useEffect, useLayoutEffect } from 'react'; // <PERSON><PERSON> currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser. We need useLayoutEffect to ensure the store\n// subscription callback always has the selector from the latest render commit\n// available, otherwise a store update may happen between render and the effect,\n// which may cause missed updates; we also must ensure the store subscription\n// is created synchronously, otherwise a store update may occur before the\n// subscription is created and an inconsistent state may be observed\n\nexport var useIsomorphicLayoutEffect = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined' ? useLayoutEffect : useEffect;", "map": {"version": 3, "names": ["useEffect", "useLayoutEffect", "useIsomorphicLayoutEffect", "window", "document", "createElement"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/node_modules/react-redux/es/utils/useIsomorphicLayoutEffect.js"], "sourcesContent": ["import { useEffect, useLayoutEffect } from 'react'; // <PERSON><PERSON> currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser. We need useLayoutEffect to ensure the store\n// subscription callback always has the selector from the latest render commit\n// available, otherwise a store update may happen between render and the effect,\n// which may cause missed updates; we also must ensure the store subscription\n// is created synchronously, otherwise a store update may occur before the\n// subscription is created and an inconsistent state may be observed\n\nexport var useIsomorphicLayoutEffect = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined' ? useLayoutEffect : useEffect;"], "mappings": "AAAA,SAASA,SAAS,EAAEC,eAAe,QAAQ,OAAO,CAAC,CAAC;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,IAAIC,yBAAyB,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,WAAW,IAAI,OAAOD,MAAM,CAACC,QAAQ,CAACC,aAAa,KAAK,WAAW,GAAGJ,eAAe,GAAGD,SAAS"}, "metadata": {}, "sourceType": "module"}