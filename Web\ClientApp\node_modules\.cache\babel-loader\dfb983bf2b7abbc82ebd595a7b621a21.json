{"ast": null, "code": "import { ServiceBase } from './service-base';\nclass PlantService extends ServiceBase {\n  getAll() {\n    return this.getAllDocuments('filters/plants');\n  }\n  async save(plant) {\n    return this.saveDocument(plant);\n  }\n}\nexport const plantApi = new PlantService();", "map": {"version": 3, "names": ["ServiceBase", "PlantService", "getAll", "getAllDocuments", "save", "plant", "saveDocument", "plantApi"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/api/plant-service.ts"], "sourcesContent": ["import * as models from './models/plants';\r\nimport { ServiceBase } from './service-base';\r\n\r\nclass PlantService extends ServiceBase {\r\n  getAll() {\r\n    return this.getAllDocuments<models.Plant>('filters/plants');\r\n  }\r\n\r\n  async save(plant: models.Plant) {\r\n    return this.saveDocument<models.Plant>(plant);\r\n  }\r\n}\r\n\r\nexport const plantApi = new PlantService();\r\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,gBAAgB;AAE5C,MAAMC,YAAY,SAASD,WAAW,CAAC;EACrCE,MAAM,GAAG;IACP,OAAO,IAAI,CAACC,eAAe,CAAe,gBAAgB,CAAC;EAC7D;EAEA,MAAMC,IAAI,CAACC,KAAmB,EAAE;IAC9B,OAAO,IAAI,CAACC,YAAY,CAAeD,KAAK,CAAC;EAC/C;AACF;AAEA,OAAO,MAAME,QAAQ,GAAG,IAAIN,YAAY,EAAE"}, "metadata": {}, "sourceType": "module"}