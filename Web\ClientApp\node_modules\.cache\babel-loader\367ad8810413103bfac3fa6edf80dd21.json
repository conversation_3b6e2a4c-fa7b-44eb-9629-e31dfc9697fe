{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\GreenRP\\\\Web\\\\ClientApp\\\\src\\\\features\\\\plants\\\\List.tsx\",\n  _s = $RefreshSig$();\nimport { useSelector } from 'react-redux';\nimport { Link } from 'react-router-dom';\nimport { Button } from 'reactstrap';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { routes } from 'app/routes';\nimport { useAuth } from 'features/auth/use-auth';\nimport { selectPlants } from './plants-slice';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport function List() {\n  _s();\n  const plants = useSelector(selectPlants),\n    {\n      isInRole\n    } = useAuth(),\n    canCreate = isInRole('create:plants');\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container d-grid gap-2\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row sticky-top-navbar bg-white mt-2 py-2 border-bottom shadow\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"col pt-2\",\n        children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n          icon: ['fat', 'seedling']\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), \"\\xA0 Plants List\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), canCreate && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-auto pt-3\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          tag: Link,\n          to: routes.plants.routes.new(),\n          outline: true,\n          color: \"success\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: ['fat', 'plus']\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 15\n          }, this), \"\\xA0 New Plant\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n      className: \"table\",\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          className: \"sticky-top bg-white\",\n          style: {\n            top: '140px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"\\xA0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Abbreviation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"text-center\",\n            children: \"Cuttings/Pot\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"text-center\",\n            children: \"Pots/Case\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"text-center\",\n            children: \"Lights Out?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"text-center\",\n            children: \"Pinching?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        children: plants.map(plant => /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: routes.plants.routes.detail.to(plant._id),\n              children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: ['fat', 'edit']\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 49,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: plant.abbreviation\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: plant.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"text-center\",\n            children: plant.cuttingsPerPot\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"text-center\",\n            children: plant.potsPerCase\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"text-center\",\n            children: plant.hasLightsOut && /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: ['fat', 'check-square']\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"text-center\",\n            children: plant.hasPinching && /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: ['fat', 'check-square']\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 15\n          }, this)]\n        }, plant._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n}\n_s(List, \"67AHeRXQdmHLwQI+MeiZFek7qts=\", false, function () {\n  return [useSelector, useAuth];\n});\n_c = List;\nexport default List;\nvar _c;\n$RefreshReg$(_c, \"List\");", "map": {"version": 3, "names": ["useSelector", "Link", "<PERSON><PERSON>", "FontAwesomeIcon", "routes", "useAuth", "selectPlants", "List", "plants", "isInRole", "canCreate", "new", "top", "map", "plant", "detail", "to", "_id", "abbreviation", "name", "cuttingsPerPot", "potsPerCase", "hasLightsOut", "hasPinching"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/plants/List.tsx"], "sourcesContent": ["import { useSelector } from 'react-redux';\r\nimport { Link } from 'react-router-dom';\r\nimport { Button } from 'reactstrap';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { routes } from 'app/routes';\r\nimport { useAuth } from 'features/auth/use-auth';\r\nimport { selectPlants } from './plants-slice';\r\n\r\nexport function List() {\r\n  const plants = useSelector(selectPlants),\r\n    {isInRole} = useAuth(),\r\n    canCreate = isInRole('create:plants');\r\n\r\n  return (\r\n    <div className=\"container d-grid gap-2\">\r\n      <div className=\"row sticky-top-navbar bg-white mt-2 py-2 border-bottom shadow\">\r\n        <h1 className=\"col pt-2\">\r\n          <FontAwesomeIcon icon={['fat', 'seedling']} />\r\n          &nbsp;\r\n          Plants List\r\n        </h1>\r\n        {canCreate &&\r\n          <div className=\"col-auto pt-3\">\r\n            <Button tag={Link} to={routes.plants.routes.new()} outline color=\"success\">\r\n              <FontAwesomeIcon icon={['fat', 'plus']} />\r\n              &nbsp;\r\n              New Plant\r\n            </Button>\r\n          </div>\r\n        }\r\n      </div>\r\n      <table className=\"table\">\r\n        <thead>\r\n          <tr className=\"sticky-top bg-white\" style={{top: '140px'}}>\r\n            <th>&nbsp;</th>\r\n            <th>Abbreviation</th>\r\n            <th>Name</th>\r\n            <th className=\"text-center\">Cuttings/Pot</th>\r\n            <th className=\"text-center\">Pots/Case</th>\r\n            <th className=\"text-center\">Lights Out?</th>\r\n            <th className=\"text-center\">Pinching?</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          {plants.map(plant =>\r\n            <tr key={plant._id}>\r\n              <td>\r\n                <Link to={routes.plants.routes.detail.to(plant._id)}>\r\n                  <FontAwesomeIcon icon={['fat', 'edit']} />\r\n                </Link>\r\n              </td>\r\n              <td>{plant.abbreviation}</td>\r\n              <td>{plant.name}</td>\r\n              <td className=\"text-center\">{plant.cuttingsPerPot}</td>\r\n              <td className=\"text-center\">{plant.potsPerCase}</td>\r\n              <td className=\"text-center\">\r\n                {plant.hasLightsOut &&\r\n                  <FontAwesomeIcon icon={['fat', 'check-square']} />\r\n                }\r\n              </td>\r\n              <td className=\"text-center\">\r\n                {plant.hasPinching &&\r\n                  <FontAwesomeIcon icon={['fat', 'check-square']} />\r\n                }\r\n              </td>\r\n            </tr>\r\n          )}\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default List;\r\n"], "mappings": ";;AAAA,SAASA,WAAW,QAAQ,aAAa;AACzC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,YAAY,QAAQ,gBAAgB;AAAC;AAE9C,OAAO,SAASC,IAAI,GAAG;EAAA;EACrB,MAAMC,MAAM,GAAGR,WAAW,CAACM,YAAY,CAAC;IACtC;MAACG;IAAQ,CAAC,GAAGJ,OAAO,EAAE;IACtBK,SAAS,GAAGD,QAAQ,CAAC,eAAe,CAAC;EAEvC,oBACE;IAAK,SAAS,EAAC,wBAAwB;IAAA,wBACrC;MAAK,SAAS,EAAC,+DAA+D;MAAA,wBAC5E;QAAI,SAAS,EAAC,UAAU;QAAA,wBACtB,QAAC,eAAe;UAAC,IAAI,EAAE,CAAC,KAAK,EAAE,UAAU;QAAE;UAAA;UAAA;UAAA;QAAA,QAAG;MAAA;QAAA;QAAA;QAAA;MAAA,QAG3C,EACJC,SAAS,iBACR;QAAK,SAAS,EAAC,eAAe;QAAA,uBAC5B,QAAC,MAAM;UAAC,GAAG,EAAET,IAAK;UAAC,EAAE,EAAEG,MAAM,CAACI,MAAM,CAACJ,MAAM,CAACO,GAAG,EAAG;UAAC,OAAO;UAAC,KAAK,EAAC,SAAS;UAAA,wBACxE,QAAC,eAAe;YAAC,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM;UAAE;YAAA;YAAA;YAAA;UAAA,QAAG;QAAA;UAAA;UAAA;UAAA;QAAA;MAGnC;QAAA;QAAA;QAAA;MAAA,QACL;IAAA;MAAA;MAAA;MAAA;IAAA,QAEJ,eACN;MAAO,SAAS,EAAC,OAAO;MAAA,wBACtB;QAAA,uBACE;UAAI,SAAS,EAAC,qBAAqB;UAAC,KAAK,EAAE;YAACC,GAAG,EAAE;UAAO,CAAE;UAAA,wBACxD;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAe,eACf;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAqB,eACrB;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAa,eACb;YAAI,SAAS,EAAC,aAAa;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAkB,eAC7C;YAAI,SAAS,EAAC,aAAa;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAe,eAC1C;YAAI,SAAS,EAAC,aAAa;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAiB,eAC5C;YAAI,SAAS,EAAC,aAAa;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAe;QAAA;UAAA;UAAA;UAAA;QAAA;MACvC;QAAA;QAAA;QAAA;MAAA,QACC,eACR;QAAA,UACGJ,MAAM,CAACK,GAAG,CAACC,KAAK,iBACf;UAAA,wBACE;YAAA,uBACE,QAAC,IAAI;cAAC,EAAE,EAAEV,MAAM,CAACI,MAAM,CAACJ,MAAM,CAACW,MAAM,CAACC,EAAE,CAACF,KAAK,CAACG,GAAG,CAAE;cAAA,uBAClD,QAAC,eAAe;gBAAC,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM;cAAE;gBAAA;gBAAA;gBAAA;cAAA;YAAG;cAAA;cAAA;cAAA;YAAA;UACrC;YAAA;YAAA;YAAA;UAAA,QACJ,eACL;YAAA,UAAKH,KAAK,CAACI;UAAY;YAAA;YAAA;YAAA;UAAA,QAAM,eAC7B;YAAA,UAAKJ,KAAK,CAACK;UAAI;YAAA;YAAA;YAAA;UAAA,QAAM,eACrB;YAAI,SAAS,EAAC,aAAa;YAAA,UAAEL,KAAK,CAACM;UAAc;YAAA;YAAA;YAAA;UAAA,QAAM,eACvD;YAAI,SAAS,EAAC,aAAa;YAAA,UAAEN,KAAK,CAACO;UAAW;YAAA;YAAA;YAAA;UAAA,QAAM,eACpD;YAAI,SAAS,EAAC,aAAa;YAAA,UACxBP,KAAK,CAACQ,YAAY,iBACjB,QAAC,eAAe;cAAC,IAAI,EAAE,CAAC,KAAK,EAAE,cAAc;YAAE;cAAA;cAAA;cAAA;YAAA;UAAG;YAAA;YAAA;YAAA;UAAA,QAEjD,eACL;YAAI,SAAS,EAAC,aAAa;YAAA,UACxBR,KAAK,CAACS,WAAW,iBAChB,QAAC,eAAe;cAAC,IAAI,EAAE,CAAC,KAAK,EAAE,cAAc;YAAE;cAAA;cAAA;cAAA;YAAA;UAAG;YAAA;YAAA;YAAA;UAAA,QAEjD;QAAA,GAnBET,KAAK,CAACG,GAAG;UAAA;UAAA;UAAA;QAAA,QAoBb;MACN;QAAA;QAAA;QAAA;MAAA,QACK;IAAA;MAAA;MAAA;MAAA;IAAA,QACF;EAAA;IAAA;IAAA;IAAA;EAAA,QACJ;AAEV;AAAC,GA/DeV,IAAI;EAAA,QACHP,WAAW,EACXK,OAAO;AAAA;AAAA,KAFRE,IAAI;AAiEpB,eAAeA,IAAI;AAAC;AAAA"}, "metadata": {}, "sourceType": "module"}