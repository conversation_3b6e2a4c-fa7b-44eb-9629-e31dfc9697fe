{"ast": null, "code": "import FormData from 'form-data';\nexport default FormData;", "map": {"version": 3, "names": ["FormData"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/node_modules/axios/lib/env/classes/FormData.js"], "sourcesContent": ["import FormData from 'form-data';\nexport default FormData;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,WAAW;AAChC,eAAeA,QAAQ"}, "metadata": {}, "sourceType": "module"}