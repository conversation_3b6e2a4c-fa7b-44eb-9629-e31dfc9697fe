{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\nconst initialState = {\n  colours: []\n};\nexport const coloursSlice = createSlice({\n  name: 'colours',\n  initialState,\n  reducers: {\n    setColours(state, action) {\n      state.colours = action.payload;\n    }\n  }\n});\nexport const {\n  setColours\n} = coloursSlice.actions;\nexport const selectColours = state => state.colours.colours;\nexport default coloursSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "initialState", "colours", "coloursSlice", "name", "reducers", "setColours", "state", "action", "payload", "actions", "selectColours", "reducer"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/colours/colours-slice.ts"], "sourcesContent": ["import { Colour } from \"api/models/colour\";\r\nimport { createSlice, PayloadAction } from '@reduxjs/toolkit';\r\nimport { RootState } from 'app/store';\r\n\r\nexport interface ColourState {\r\n  colours: Colour[];\r\n}\r\n\r\nconst initialState: ColourState = {\r\n  colours: []\r\n};\r\n\r\nexport const coloursSlice = createSlice({\r\n  name: 'colours',\r\n  initialState,\r\n  reducers: {\r\n    setColours(state, action: PayloadAction<Colour[]>) {\r\n      state.colours = action.payload;\r\n    }\r\n  }\r\n});\r\n\r\nexport const { setColours } = coloursSlice.actions;\r\n\r\nexport const selectColours = (state: RootState) => state.colours.colours;\r\n\r\nexport default coloursSlice.reducer;\r\n"], "mappings": "AACA,SAASA,WAAW,QAAuB,kBAAkB;AAO7D,MAAMC,YAAyB,GAAG;EAChCC,OAAO,EAAE;AACX,CAAC;AAED,OAAO,MAAMC,YAAY,GAAGH,WAAW,CAAC;EACtCI,IAAI,EAAE,SAAS;EACfH,YAAY;EACZI,QAAQ,EAAE;IACRC,UAAU,CAACC,KAAK,EAAEC,MAA+B,EAAE;MACjDD,KAAK,CAACL,OAAO,GAAGM,MAAM,CAACC,OAAO;IAChC;EACF;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEH;AAAW,CAAC,GAAGH,YAAY,CAACO,OAAO;AAElD,OAAO,MAAMC,aAAa,GAAIJ,KAAgB,IAAKA,KAAK,CAACL,OAAO,CAACA,OAAO;AAExE,eAAeC,YAAY,CAACS,OAAO"}, "metadata": {}, "sourceType": "module"}