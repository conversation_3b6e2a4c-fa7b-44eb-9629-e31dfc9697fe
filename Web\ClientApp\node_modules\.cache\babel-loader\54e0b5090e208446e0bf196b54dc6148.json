{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\GreenRP\\\\Web\\\\ClientApp\\\\src\\\\features\\\\orders\\\\OrderRow.tsx\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport { useSelector } from 'react-redux';\nimport { Link } from 'react-router-dom';\nimport { routes } from 'app/routes';\nimport { formatNumber, formatDate } from 'utils/format';\nimport { Button, Collapse } from 'reactstrap';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { selectZones } from './orders-slice';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport function OrderRow(props) {\n  _s();\n  var _zones$find, _order$varieties, _order$varieties2, _order$varieties3, _order$fullSpaceZone2;\n  const {\n      order,\n      showTightTables,\n      showSpacedTables,\n      showPinchDate\n    } = props,\n    zones = useSelector(selectZones),\n    [showVarieties, setShowVarieties] = useState(false),\n    isOffsite = ((_zones$find = zones.find(z => {\n      var _order$fullSpaceZone;\n      return z._id === ((_order$fullSpaceZone = order.fullSpaceZone) === null || _order$fullSpaceZone === void 0 ? void 0 : _order$fullSpaceZone._id);\n    })) === null || _zones$find === void 0 ? void 0 : _zones$find.isOffsite) || false;\n  const handleShowVarietiesClick = () => {\n    setShowVarieties(!showVarieties);\n  };\n  return /*#__PURE__*/_jsxDEV(\"tr\", {\n    style: {\n      backgroundColor: order.plant.colour || ''\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"bg-transparent\",\n      children: /*#__PURE__*/_jsxDEV(Link, {\n        to: routes.orders.routes.detail.to(order._id),\n        children: order.orderNumber\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"mx-0 text-end bg-transparent\",\n      children: !!((_order$varieties = order.varieties) !== null && _order$varieties !== void 0 && _order$varieties.length) && /*#__PURE__*/_jsxDEV(Button, {\n        outline: true,\n        size: \"sm\",\n        onClick: handleShowVarietiesClick,\n        children: [showVarieties && /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n          icon: ['fat', 'minus']\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 31\n        }, this), !showVarieties && /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n          icon: ['fat', 'plus']\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 32\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"bg-transparent\",\n      children: [order.plant.size, \" \", order.plant.crop, !!((_order$varieties2 = order.varieties) !== null && _order$varieties2 !== void 0 && _order$varieties2.length) && /*#__PURE__*/_jsxDEV(Collapse, {\n        tag: 'ul',\n        isOpen: showVarieties,\n        className: \"list-unstyled ms-3 fst-italic\",\n        children: order.varieties.map(variety => /*#__PURE__*/_jsxDEV(\"li\", {\n          children: variety.name\n        }, variety.name, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 11\n      }, this), !!order.notes && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"fw-bold fst-italic mb-0\",\n        children: order.notes\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"text-center text-nowrap bg-transparent\",\n      children: [formatNumber(order.pots), order.cases && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\xA0/\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 13\n        }, this), formatNumber(order.cases)]\n      }, void 0, true), !!((_order$varieties3 = order.varieties) !== null && _order$varieties3 !== void 0 && _order$varieties3.length) && /*#__PURE__*/_jsxDEV(Collapse, {\n        tag: 'ul',\n        isOpen: showVarieties,\n        className: \"list-unstyled ms-3 fst-italic\",\n        children: order.varieties.map(variety => /*#__PURE__*/_jsxDEV(\"li\", {\n          className: \"text-nowrap\",\n          children: [formatNumber(variety.pots), \"\\xA0/\\xA0\", formatNumber(variety.cases)]\n        }, variety.name, true, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this), showTightTables && /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"text-center bg-transparent\",\n      children: formatNumber(order.tableCountTight)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 9\n    }, this), showSpacedTables && /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"text-center bg-transparent\",\n      children: isOffsite ? '-' : formatNumber(order.tableCountSpaced)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"text-center bg-transparent\",\n      children: order.stickZone.name\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"text-center bg-transparent\",\n      children: (_order$fullSpaceZone2 = order.fullSpaceZone) === null || _order$fullSpaceZone2 === void 0 ? void 0 : _order$fullSpaceZone2.name\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"text-center text-nowrap bg-transparent\",\n      children: formatDate(order.stickDate)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"text-center text-nowrap bg-transparent\",\n      children: formatDate(order.fullSpaceDate)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this), showPinchDate && /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"text-center text-nowrap bg-transparent\",\n      children: formatDate(order.pinchDate)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n      className: \"text-center text-nowrap bg-transparent\",\n      children: isOffsite ? '-' : formatDate(order.flowerDate)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this)]\n  }, order._id, true, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this);\n}\n_s(OrderRow, \"R1zEcxSClQHCfK/sTF+VGa/KfhQ=\", false, function () {\n  return [useSelector];\n});\n_c = OrderRow;\nvar _c;\n$RefreshReg$(_c, \"OrderRow\");", "map": {"version": 3, "names": ["useState", "useSelector", "Link", "routes", "formatNumber", "formatDate", "<PERSON><PERSON>", "Collapse", "FontAwesomeIcon", "selectZones", "OrderRow", "props", "order", "showTightTables", "showSpacedTables", "showPinchDate", "zones", "showVarieties", "setShowVarieties", "isOffsite", "find", "z", "_id", "fullSpaceZone", "handleShowVarietiesClick", "backgroundColor", "plant", "colour", "orders", "detail", "to", "orderNumber", "varieties", "length", "size", "crop", "map", "variety", "name", "notes", "pots", "cases", "tableCountTight", "tableCountSpaced", "stickZone", "stickDate", "fullSpaceDate", "pinchDate", "flowerDate"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/orders/OrderRow.tsx"], "sourcesContent": ["import { useState } from 'react';\r\nimport { useSelector } from 'react-redux';\r\nimport { Link } from 'react-router-dom';\r\nimport { Order } from 'api/models/orders';\r\nimport { routes } from 'app/routes';\r\nimport { formatNumber, formatDate } from 'utils/format';\r\nimport { Button, Collapse } from 'reactstrap';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { selectZones } from './orders-slice';\r\n\r\ninterface OrderRowProps {\r\n  order: Order;\r\n  showTightTables?: boolean;\r\n  showSpacedTables?: boolean;\r\n  showPinchDate?: boolean;\r\n}\r\n\r\nexport function OrderRow(props: OrderRowProps) {\r\n  const { order, showTightTables, showSpacedTables, showPinchDate } = props,\r\n    zones = useSelector(selectZones),\r\n    [showVarieties, setShowVarieties] = useState(false),\r\n    isOffsite =\r\n      zones.find((z) => z._id === order.fullSpaceZone?._id)?.isOffsite || false;\r\n\r\n  const handleShowVarietiesClick = () => {\r\n    setShowVarieties(!showVarieties);\r\n  };\r\n\r\n  return (\r\n    <tr key={order._id} style={{ backgroundColor: order.plant.colour || '' }}>\r\n      <td className=\"bg-transparent\">\r\n        <Link to={routes.orders.routes.detail.to(order._id)}>\r\n          {order.orderNumber}\r\n        </Link>\r\n      </td>\r\n      <td className=\"mx-0 text-end bg-transparent\">\r\n        {!!order.varieties?.length && (\r\n          <Button outline size=\"sm\" onClick={handleShowVarietiesClick}>\r\n            {showVarieties && <FontAwesomeIcon icon={['fat', 'minus']} />}\r\n            {!showVarieties && <FontAwesomeIcon icon={['fat', 'plus']} />}\r\n          </Button>\r\n        )}\r\n      </td>\r\n      <td className=\"bg-transparent\">\r\n        {order.plant.size} {order.plant.crop}\r\n        {!!order.varieties?.length && (\r\n          <Collapse\r\n            tag={'ul'}\r\n            isOpen={showVarieties}\r\n            className=\"list-unstyled ms-3 fst-italic\">\r\n            {order.varieties.map((variety) => (\r\n              <li key={variety.name}>{variety.name}</li>\r\n            ))}\r\n          </Collapse>\r\n        )}\r\n        {!!order.notes && (\r\n          <p className=\"fw-bold fst-italic mb-0\">{order.notes}</p>\r\n        )}\r\n      </td>\r\n      <td className=\"text-center text-nowrap bg-transparent\">\r\n        {formatNumber(order.pots)}\r\n        {order.cases && (\r\n          <>\r\n            <span>&nbsp;/&nbsp;</span>\r\n            {formatNumber(order.cases)}\r\n          </>\r\n        )}\r\n        {!!order.varieties?.length && (\r\n          <Collapse\r\n            tag={'ul'}\r\n            isOpen={showVarieties}\r\n            className=\"list-unstyled ms-3 fst-italic\">\r\n            {order.varieties.map((variety) => (\r\n              <li key={variety.name} className=\"text-nowrap\">\r\n                {formatNumber(variety.pots)}&nbsp;/&nbsp;\r\n                {formatNumber(variety.cases)}\r\n              </li>\r\n            ))}\r\n          </Collapse>\r\n        )}\r\n      </td>\r\n      {showTightTables && (\r\n        <td className=\"text-center bg-transparent\">\r\n          {formatNumber(order.tableCountTight)}\r\n        </td>\r\n      )}\r\n      {showSpacedTables && (\r\n        <td className=\"text-center bg-transparent\">\r\n          {isOffsite ? '-' : formatNumber(order.tableCountSpaced)}\r\n        </td>\r\n      )}\r\n      <td className=\"text-center bg-transparent\">{order.stickZone.name}</td>\r\n      <td className=\"text-center bg-transparent\">\r\n        {order.fullSpaceZone?.name}\r\n      </td>\r\n      <td className=\"text-center text-nowrap bg-transparent\">\r\n        {formatDate(order.stickDate)}\r\n      </td>\r\n      <td className=\"text-center text-nowrap bg-transparent\">\r\n        {formatDate(order.fullSpaceDate)}\r\n      </td>\r\n      {showPinchDate && (\r\n        <td className=\"text-center text-nowrap bg-transparent\">\r\n          {formatDate(order.pinchDate)}\r\n        </td>\r\n      )}\r\n      <td className=\"text-center text-nowrap bg-transparent\">\r\n        {isOffsite ? '-' : formatDate(order.flowerDate)}\r\n      </td>\r\n    </tr>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,IAAI,QAAQ,kBAAkB;AAEvC,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,YAAY,EAAEC,UAAU,QAAQ,cAAc;AACvD,SAASC,MAAM,EAAEC,QAAQ,QAAQ,YAAY;AAC7C,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,WAAW,QAAQ,gBAAgB;AAAC;AAAA;AAS7C,OAAO,SAASC,QAAQ,CAACC,KAAoB,EAAE;EAAA;EAAA;EAC7C,MAAM;MAAEC,KAAK;MAAEC,eAAe;MAAEC,gBAAgB;MAAEC;IAAc,CAAC,GAAGJ,KAAK;IACvEK,KAAK,GAAGf,WAAW,CAACQ,WAAW,CAAC;IAChC,CAACQ,aAAa,EAAEC,gBAAgB,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;IACnDmB,SAAS,GACP,gBAAAH,KAAK,CAACI,IAAI,CAAEC,CAAC;MAAA;MAAA,OAAKA,CAAC,CAACC,GAAG,8BAAKV,KAAK,CAACW,aAAa,yDAAnB,qBAAqBD,GAAG;IAAA,EAAC,gDAArD,YAAuDH,SAAS,KAAI,KAAK;EAE7E,MAAMK,wBAAwB,GAAG,MAAM;IACrCN,gBAAgB,CAAC,CAACD,aAAa,CAAC;EAClC,CAAC;EAED,oBACE;IAAoB,KAAK,EAAE;MAAEQ,eAAe,EAAEb,KAAK,CAACc,KAAK,CAACC,MAAM,IAAI;IAAG,CAAE;IAAA,wBACvE;MAAI,SAAS,EAAC,gBAAgB;MAAA,uBAC5B,QAAC,IAAI;QAAC,EAAE,EAAExB,MAAM,CAACyB,MAAM,CAACzB,MAAM,CAAC0B,MAAM,CAACC,EAAE,CAAClB,KAAK,CAACU,GAAG,CAAE;QAAA,UACjDV,KAAK,CAACmB;MAAW;QAAA;QAAA;QAAA;MAAA;IACb;MAAA;MAAA;MAAA;IAAA,QACJ,eACL;MAAI,SAAS,EAAC,8BAA8B;MAAA,UACzC,CAAC,sBAACnB,KAAK,CAACoB,SAAS,6CAAf,iBAAiBC,MAAM,kBACxB,QAAC,MAAM;QAAC,OAAO;QAAC,IAAI,EAAC,IAAI;QAAC,OAAO,EAAET,wBAAyB;QAAA,WACzDP,aAAa,iBAAI,QAAC,eAAe;UAAC,IAAI,EAAE,CAAC,KAAK,EAAE,OAAO;QAAE;UAAA;UAAA;UAAA;QAAA,QAAG,EAC5D,CAACA,aAAa,iBAAI,QAAC,eAAe;UAAC,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM;QAAE;UAAA;UAAA;UAAA;QAAA,QAAG;MAAA;QAAA;QAAA;QAAA;MAAA;IAEhE;MAAA;MAAA;MAAA;IAAA,QACE,eACL;MAAI,SAAS,EAAC,gBAAgB;MAAA,WAC3BL,KAAK,CAACc,KAAK,CAACQ,IAAI,OAAGtB,KAAK,CAACc,KAAK,CAACS,IAAI,EACnC,CAAC,uBAACvB,KAAK,CAACoB,SAAS,8CAAf,kBAAiBC,MAAM,kBACxB,QAAC,QAAQ;QACP,GAAG,EAAE,IAAK;QACV,MAAM,EAAEhB,aAAc;QACtB,SAAS,EAAC,+BAA+B;QAAA,UACxCL,KAAK,CAACoB,SAAS,CAACI,GAAG,CAAEC,OAAO,iBAC3B;UAAA,UAAwBA,OAAO,CAACC;QAAI,GAA3BD,OAAO,CAACC,IAAI;UAAA;UAAA;UAAA;QAAA,QACtB;MAAC;QAAA;QAAA;QAAA;MAAA,QAEL,EACA,CAAC,CAAC1B,KAAK,CAAC2B,KAAK,iBACZ;QAAG,SAAS,EAAC,yBAAyB;QAAA,UAAE3B,KAAK,CAAC2B;MAAK;QAAA;QAAA;QAAA;MAAA,QACpD;IAAA;MAAA;MAAA;MAAA;IAAA,QACE,eACL;MAAI,SAAS,EAAC,wCAAwC;MAAA,WACnDnC,YAAY,CAACQ,KAAK,CAAC4B,IAAI,CAAC,EACxB5B,KAAK,CAAC6B,KAAK,iBACV;QAAA,wBACE;UAAA;QAAA;UAAA;UAAA;UAAA;QAAA,QAA0B,EACzBrC,YAAY,CAACQ,KAAK,CAAC6B,KAAK,CAAC;MAAA,gBAE7B,EACA,CAAC,uBAAC7B,KAAK,CAACoB,SAAS,8CAAf,kBAAiBC,MAAM,kBACxB,QAAC,QAAQ;QACP,GAAG,EAAE,IAAK;QACV,MAAM,EAAEhB,aAAc;QACtB,SAAS,EAAC,+BAA+B;QAAA,UACxCL,KAAK,CAACoB,SAAS,CAACI,GAAG,CAAEC,OAAO,iBAC3B;UAAuB,SAAS,EAAC,aAAa;UAAA,WAC3CjC,YAAY,CAACiC,OAAO,CAACG,IAAI,CAAC,eAC1BpC,YAAY,CAACiC,OAAO,CAACI,KAAK,CAAC;QAAA,GAFrBJ,OAAO,CAACC,IAAI;UAAA;UAAA;UAAA;QAAA,QAItB;MAAC;QAAA;QAAA;QAAA;MAAA,QAEL;IAAA;MAAA;MAAA;MAAA;IAAA,QACE,EACJzB,eAAe,iBACd;MAAI,SAAS,EAAC,4BAA4B;MAAA,UACvCT,YAAY,CAACQ,KAAK,CAAC8B,eAAe;IAAC;MAAA;MAAA;MAAA;IAAA,QAEvC,EACA5B,gBAAgB,iBACf;MAAI,SAAS,EAAC,4BAA4B;MAAA,UACvCK,SAAS,GAAG,GAAG,GAAGf,YAAY,CAACQ,KAAK,CAAC+B,gBAAgB;IAAC;MAAA;MAAA;MAAA;IAAA,QAE1D,eACD;MAAI,SAAS,EAAC,4BAA4B;MAAA,UAAE/B,KAAK,CAACgC,SAAS,CAACN;IAAI;MAAA;MAAA;MAAA;IAAA,QAAM,eACtE;MAAI,SAAS,EAAC,4BAA4B;MAAA,mCACvC1B,KAAK,CAACW,aAAa,0DAAnB,sBAAqBe;IAAI;MAAA;MAAA;MAAA;IAAA,QACvB,eACL;MAAI,SAAS,EAAC,wCAAwC;MAAA,UACnDjC,UAAU,CAACO,KAAK,CAACiC,SAAS;IAAC;MAAA;MAAA;MAAA;IAAA,QACzB,eACL;MAAI,SAAS,EAAC,wCAAwC;MAAA,UACnDxC,UAAU,CAACO,KAAK,CAACkC,aAAa;IAAC;MAAA;MAAA;MAAA;IAAA,QAC7B,EACJ/B,aAAa,iBACZ;MAAI,SAAS,EAAC,wCAAwC;MAAA,UACnDV,UAAU,CAACO,KAAK,CAACmC,SAAS;IAAC;MAAA;MAAA;MAAA;IAAA,QAE/B,eACD;MAAI,SAAS,EAAC,wCAAwC;MAAA,UACnD5B,SAAS,GAAG,GAAG,GAAGd,UAAU,CAACO,KAAK,CAACoC,UAAU;IAAC;MAAA;MAAA;MAAA;IAAA,QAC5C;EAAA,GA/EEpC,KAAK,CAACU,GAAG;IAAA;IAAA;IAAA;EAAA,QAgFb;AAET;AAAC,GA9FeZ,QAAQ;EAAA,QAEZT,WAAW;AAAA;AAAA,KAFPS,QAAQ;AAAA;AAAA"}, "metadata": {}, "sourceType": "module"}