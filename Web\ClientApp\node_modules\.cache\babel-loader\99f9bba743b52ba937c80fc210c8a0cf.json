{"ast": null, "code": "import { getBasicAuth } from './auth-service';\nimport { axios } from 'boot/axios';\nimport { getUserInfo } from 'features/auth/auth-provider';\nconst type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n  responseType = 'blob';\nclass ReportsService {\n  async orderSummary(start, end, plant) {\n    const config = getConfig(),\n      response = await axios.get(`/reports/orders/summary?start=${start}&end=${end}&plant=${plant || ''}`, config),\n      {\n        headers,\n        data\n      } = response,\n      blob = new Blob([data], {\n        type\n      }),\n      filename = getFilename(headers) || 'Orders.xlsx';\n    downloadFile(blob, filename);\n  }\n  async ordersByStickDate(start, end, plant) {\n    const config = getConfig(),\n      response = await axios.get(`/reports/orders/by-stick-date?start=${start}&end=${end}&plant=${plant || ''}`, config),\n      {\n        headers,\n        data\n      } = response,\n      blob = new Blob([data], {\n        type\n      }),\n      filename = getFilename(headers) || 'ByStickDate.xlsx';\n    downloadFile(blob, filename);\n  }\n  async ordersByPinchDate(start, end, plant) {\n    const config = getConfig(),\n      response = await axios.get(`/reports/orders/by-pinch-date?start=${start}&end=${end}&plant=${plant || ''}`, config),\n      {\n        headers,\n        data\n      } = response,\n      blob = new Blob([data], {\n        type\n      }),\n      filename = getFilename(headers) || 'ByPinchDate.xlsx';\n    downloadFile(blob, filename);\n  }\n  async ordersByFlowerDate(start, end, plant) {\n    const config = getConfig(),\n      response = await axios.get(`/reports/orders/by-flower-date?start=${start}&end=${end}&plant=${plant || ''}`, config),\n      {\n        headers,\n        data\n      } = response,\n      blob = new Blob([data], {\n        type\n      }),\n      filename = getFilename(headers) || 'ByFlowerDate.xlsx';\n    downloadFile(blob, filename);\n  }\n  async ordersBySpaceDate(start, end, plant) {\n    const config = getConfig(),\n      response = await axios.get(`/reports/orders/by-space-date?start=${start}&end=${end}&plant=${plant || ''}`, config),\n      {\n        headers,\n        data\n      } = response,\n      blob = new Blob([data], {\n        type\n      }),\n      filename = getFilename(headers) || 'BySpaceDate.xlsx';\n    downloadFile(blob, filename);\n  }\n  async labourReport(year) {\n    const config = getConfig(),\n      response = await axios.get(`/reports/labour-hours?year=${year}`, config),\n      {\n        headers,\n        data\n      } = response,\n      blob = new Blob([data], {\n        type\n      }),\n      filename = getFilename(headers) || 'LabourHours.xlsx';\n    downloadFile(blob, filename);\n  }\n}\nfunction getConfig() {\n  const userInfo = getUserInfo(),\n    name = (userInfo === null || userInfo === void 0 ? void 0 : userInfo.name) || '',\n    password = (userInfo === null || userInfo === void 0 ? void 0 : userInfo.password) || '',\n    auth = getBasicAuth(name, password),\n    config = {\n      headers: {\n        Authorization: auth\n      },\n      responseType\n    };\n  return config;\n}\nfunction getFilename(headers) {\n  const contentDisposition = headers['content-disposition'] || '',\n    dispositionParts = contentDisposition.split(';'),\n    filenamePart = dispositionParts.find(a => a.indexOf('filename') !== -1) || '',\n    filename = filenamePart.replace(' filename=', '');\n  return filename;\n}\nfunction downloadFile(blob, filename) {\n  const a = document.createElement('a');\n  a.download = filename;\n  a.style.display = 'none';\n  a.href = URL.createObjectURL(blob);\n  document.body.appendChild(a);\n  a.click();\n  URL.revokeObjectURL(a.href);\n  document.body.removeChild(a);\n}\nexport const reportsApi = new ReportsService();", "map": {"version": 3, "names": ["getBasicAuth", "axios", "getUserInfo", "type", "responseType", "ReportsService", "orderSummary", "start", "end", "plant", "config", "getConfig", "response", "get", "headers", "data", "blob", "Blob", "filename", "getFilename", "downloadFile", "ordersByStickDate", "ordersByPinchDate", "ordersByFlowerDate", "ordersBySpaceDate", "labourReport", "year", "userInfo", "name", "password", "auth", "Authorization", "contentDisposition", "dispositionParts", "split", "filenamePart", "find", "a", "indexOf", "replace", "document", "createElement", "download", "style", "display", "href", "URL", "createObjectURL", "body", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "reportsApi"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/api/reports-service.ts"], "sourcesContent": ["import { getBasicAuth } from './auth-service';\r\nimport { axios } from 'boot/axios';\r\nimport { getUserInfo } from 'features/auth/auth-provider';\r\nimport { AxiosResponseHeaders, ResponseType } from 'axios';\r\n\r\nconst type =\r\n    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\r\n  responseType: ResponseType = 'blob';\r\n\r\nclass ReportsService {\r\n  async orderSummary(start: string, end: string, plant: string | null) {\r\n    const config = getConfig(),\r\n      response = await axios.get(\r\n        `/reports/orders/summary?start=${start}&end=${end}&plant=${\r\n          plant || ''\r\n        }`,\r\n        config\r\n      ),\r\n      { headers, data } = response,\r\n      blob = new Blob([data], { type }),\r\n      filename = getFilename(headers as AxiosResponseHeaders) || 'Orders.xlsx';\r\n\r\n    downloadFile(blob, filename);\r\n  }\r\n\r\n  async ordersByStickDate(start: string, end: string, plant: string | null) {\r\n    const config = getConfig(),\r\n      response = await axios.get(\r\n        `/reports/orders/by-stick-date?start=${start}&end=${end}&plant=${\r\n          plant || ''\r\n        }`,\r\n        config\r\n      ),\r\n      { headers, data } = response,\r\n      blob = new Blob([data], { type }),\r\n      filename =\r\n        getFilename(headers as AxiosResponseHeaders) || 'ByStickDate.xlsx';\r\n\r\n    downloadFile(blob, filename);\r\n  }\r\n\r\n  async ordersByPinchDate(start: string, end: string, plant: string | null) {\r\n    const config = getConfig(),\r\n      response = await axios.get(\r\n        `/reports/orders/by-pinch-date?start=${start}&end=${end}&plant=${\r\n          plant || ''\r\n        }`,\r\n        config\r\n      ),\r\n      { headers, data } = response,\r\n      blob = new Blob([data], { type }),\r\n      filename =\r\n        getFilename(headers as AxiosResponseHeaders) || 'ByPinchDate.xlsx';\r\n\r\n    downloadFile(blob, filename);\r\n  }\r\n\r\n  async ordersByFlowerDate(start: string, end: string, plant: string | null) {\r\n    const config = getConfig(),\r\n      response = await axios.get(\r\n        `/reports/orders/by-flower-date?start=${start}&end=${end}&plant=${\r\n          plant || ''\r\n        }`,\r\n        config\r\n      ),\r\n      { headers, data } = response,\r\n      blob = new Blob([data], { type }),\r\n      filename =\r\n        getFilename(headers as AxiosResponseHeaders) || 'ByFlowerDate.xlsx';\r\n\r\n    downloadFile(blob, filename);\r\n  }\r\n\r\n  async ordersBySpaceDate(start: string, end: string, plant: string | null) {\r\n    const config = getConfig(),\r\n      response = await axios.get(\r\n        `/reports/orders/by-space-date?start=${start}&end=${end}&plant=${\r\n          plant || ''\r\n        }`,\r\n        config\r\n      ),\r\n      { headers, data } = response,\r\n      blob = new Blob([data], { type }),\r\n      filename =\r\n        getFilename(headers as AxiosResponseHeaders) || 'BySpaceDate.xlsx';\r\n\r\n    downloadFile(blob, filename);\r\n  }\r\n\r\n  async labourReport(year: number) {\r\n    const config = getConfig(),\r\n      response = await axios.get(`/reports/labour-hours?year=${year}`, config),\r\n      { headers, data } = response,\r\n      blob = new Blob([data], { type }),\r\n      filename =\r\n        getFilename(headers as AxiosResponseHeaders) || 'LabourHours.xlsx';\r\n\r\n    downloadFile(blob, filename);\r\n  }\r\n}\r\n\r\nfunction getConfig() {\r\n  const userInfo = getUserInfo(),\r\n    name = userInfo?.name || '',\r\n    password = userInfo?.password || '',\r\n    auth = getBasicAuth(name, password),\r\n    config = { headers: { Authorization: auth }, responseType };\r\n  return config;\r\n}\r\n\r\nfunction getFilename(headers: AxiosResponseHeaders) {\r\n  const contentDisposition = headers['content-disposition'] || '',\r\n    dispositionParts = contentDisposition.split(';'),\r\n    filenamePart =\r\n      dispositionParts.find((a) => a.indexOf('filename') !== -1) || '',\r\n    filename = filenamePart.replace(' filename=', '');\r\n\r\n  return filename;\r\n}\r\n\r\nfunction downloadFile(blob: Blob, filename: string) {\r\n  const a = document.createElement('a');\r\n  a.download = filename;\r\n  a.style.display = 'none';\r\n  a.href = URL.createObjectURL(blob);\r\n  document.body.appendChild(a);\r\n  a.click();\r\n  URL.revokeObjectURL(a.href);\r\n  document.body.removeChild(a);\r\n}\r\n\r\nexport const reportsApi = new ReportsService();\r\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,KAAK,QAAQ,YAAY;AAClC,SAASC,WAAW,QAAQ,6BAA6B;AAGzD,MAAMC,IAAI,GACN,mEAAmE;EACrEC,YAA0B,GAAG,MAAM;AAErC,MAAMC,cAAc,CAAC;EACnB,MAAMC,YAAY,CAACC,KAAa,EAAEC,GAAW,EAAEC,KAAoB,EAAE;IACnE,MAAMC,MAAM,GAAGC,SAAS,EAAE;MACxBC,QAAQ,GAAG,MAAMX,KAAK,CAACY,GAAG,CACvB,iCAAgCN,KAAM,QAAOC,GAAI,UAChDC,KAAK,IAAI,EACV,EAAC,EACFC,MAAM,CACP;MACD;QAAEI,OAAO;QAAEC;MAAK,CAAC,GAAGH,QAAQ;MAC5BI,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACF,IAAI,CAAC,EAAE;QAAEZ;MAAK,CAAC,CAAC;MACjCe,QAAQ,GAAGC,WAAW,CAACL,OAAO,CAAyB,IAAI,aAAa;IAE1EM,YAAY,CAACJ,IAAI,EAAEE,QAAQ,CAAC;EAC9B;EAEA,MAAMG,iBAAiB,CAACd,KAAa,EAAEC,GAAW,EAAEC,KAAoB,EAAE;IACxE,MAAMC,MAAM,GAAGC,SAAS,EAAE;MACxBC,QAAQ,GAAG,MAAMX,KAAK,CAACY,GAAG,CACvB,uCAAsCN,KAAM,QAAOC,GAAI,UACtDC,KAAK,IAAI,EACV,EAAC,EACFC,MAAM,CACP;MACD;QAAEI,OAAO;QAAEC;MAAK,CAAC,GAAGH,QAAQ;MAC5BI,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACF,IAAI,CAAC,EAAE;QAAEZ;MAAK,CAAC,CAAC;MACjCe,QAAQ,GACNC,WAAW,CAACL,OAAO,CAAyB,IAAI,kBAAkB;IAEtEM,YAAY,CAACJ,IAAI,EAAEE,QAAQ,CAAC;EAC9B;EAEA,MAAMI,iBAAiB,CAACf,KAAa,EAAEC,GAAW,EAAEC,KAAoB,EAAE;IACxE,MAAMC,MAAM,GAAGC,SAAS,EAAE;MACxBC,QAAQ,GAAG,MAAMX,KAAK,CAACY,GAAG,CACvB,uCAAsCN,KAAM,QAAOC,GAAI,UACtDC,KAAK,IAAI,EACV,EAAC,EACFC,MAAM,CACP;MACD;QAAEI,OAAO;QAAEC;MAAK,CAAC,GAAGH,QAAQ;MAC5BI,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACF,IAAI,CAAC,EAAE;QAAEZ;MAAK,CAAC,CAAC;MACjCe,QAAQ,GACNC,WAAW,CAACL,OAAO,CAAyB,IAAI,kBAAkB;IAEtEM,YAAY,CAACJ,IAAI,EAAEE,QAAQ,CAAC;EAC9B;EAEA,MAAMK,kBAAkB,CAAChB,KAAa,EAAEC,GAAW,EAAEC,KAAoB,EAAE;IACzE,MAAMC,MAAM,GAAGC,SAAS,EAAE;MACxBC,QAAQ,GAAG,MAAMX,KAAK,CAACY,GAAG,CACvB,wCAAuCN,KAAM,QAAOC,GAAI,UACvDC,KAAK,IAAI,EACV,EAAC,EACFC,MAAM,CACP;MACD;QAAEI,OAAO;QAAEC;MAAK,CAAC,GAAGH,QAAQ;MAC5BI,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACF,IAAI,CAAC,EAAE;QAAEZ;MAAK,CAAC,CAAC;MACjCe,QAAQ,GACNC,WAAW,CAACL,OAAO,CAAyB,IAAI,mBAAmB;IAEvEM,YAAY,CAACJ,IAAI,EAAEE,QAAQ,CAAC;EAC9B;EAEA,MAAMM,iBAAiB,CAACjB,KAAa,EAAEC,GAAW,EAAEC,KAAoB,EAAE;IACxE,MAAMC,MAAM,GAAGC,SAAS,EAAE;MACxBC,QAAQ,GAAG,MAAMX,KAAK,CAACY,GAAG,CACvB,uCAAsCN,KAAM,QAAOC,GAAI,UACtDC,KAAK,IAAI,EACV,EAAC,EACFC,MAAM,CACP;MACD;QAAEI,OAAO;QAAEC;MAAK,CAAC,GAAGH,QAAQ;MAC5BI,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACF,IAAI,CAAC,EAAE;QAAEZ;MAAK,CAAC,CAAC;MACjCe,QAAQ,GACNC,WAAW,CAACL,OAAO,CAAyB,IAAI,kBAAkB;IAEtEM,YAAY,CAACJ,IAAI,EAAEE,QAAQ,CAAC;EAC9B;EAEA,MAAMO,YAAY,CAACC,IAAY,EAAE;IAC/B,MAAMhB,MAAM,GAAGC,SAAS,EAAE;MACxBC,QAAQ,GAAG,MAAMX,KAAK,CAACY,GAAG,CAAE,8BAA6Ba,IAAK,EAAC,EAAEhB,MAAM,CAAC;MACxE;QAAEI,OAAO;QAAEC;MAAK,CAAC,GAAGH,QAAQ;MAC5BI,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACF,IAAI,CAAC,EAAE;QAAEZ;MAAK,CAAC,CAAC;MACjCe,QAAQ,GACNC,WAAW,CAACL,OAAO,CAAyB,IAAI,kBAAkB;IAEtEM,YAAY,CAACJ,IAAI,EAAEE,QAAQ,CAAC;EAC9B;AACF;AAEA,SAASP,SAAS,GAAG;EACnB,MAAMgB,QAAQ,GAAGzB,WAAW,EAAE;IAC5B0B,IAAI,GAAG,CAAAD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEC,IAAI,KAAI,EAAE;IAC3BC,QAAQ,GAAG,CAAAF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,QAAQ,KAAI,EAAE;IACnCC,IAAI,GAAG9B,YAAY,CAAC4B,IAAI,EAAEC,QAAQ,CAAC;IACnCnB,MAAM,GAAG;MAAEI,OAAO,EAAE;QAAEiB,aAAa,EAAED;MAAK,CAAC;MAAE1B;IAAa,CAAC;EAC7D,OAAOM,MAAM;AACf;AAEA,SAASS,WAAW,CAACL,OAA6B,EAAE;EAClD,MAAMkB,kBAAkB,GAAGlB,OAAO,CAAC,qBAAqB,CAAC,IAAI,EAAE;IAC7DmB,gBAAgB,GAAGD,kBAAkB,CAACE,KAAK,CAAC,GAAG,CAAC;IAChDC,YAAY,GACVF,gBAAgB,CAACG,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;IAClEpB,QAAQ,GAAGiB,YAAY,CAACI,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;EAEnD,OAAOrB,QAAQ;AACjB;AAEA,SAASE,YAAY,CAACJ,IAAU,EAAEE,QAAgB,EAAE;EAClD,MAAMmB,CAAC,GAAGG,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;EACrCJ,CAAC,CAACK,QAAQ,GAAGxB,QAAQ;EACrBmB,CAAC,CAACM,KAAK,CAACC,OAAO,GAAG,MAAM;EACxBP,CAAC,CAACQ,IAAI,GAAGC,GAAG,CAACC,eAAe,CAAC/B,IAAI,CAAC;EAClCwB,QAAQ,CAACQ,IAAI,CAACC,WAAW,CAACZ,CAAC,CAAC;EAC5BA,CAAC,CAACa,KAAK,EAAE;EACTJ,GAAG,CAACK,eAAe,CAACd,CAAC,CAACQ,IAAI,CAAC;EAC3BL,QAAQ,CAACQ,IAAI,CAACI,WAAW,CAACf,CAAC,CAAC;AAC9B;AAEA,OAAO,MAAMgB,UAAU,GAAG,IAAIhD,cAAc,EAAE"}, "metadata": {}, "sourceType": "module"}