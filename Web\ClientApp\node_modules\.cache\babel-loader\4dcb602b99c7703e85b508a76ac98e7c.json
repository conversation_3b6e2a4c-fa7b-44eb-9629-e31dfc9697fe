{"ast": null, "code": "import { configureStore } from '@reduxjs/toolkit';\nimport orders from 'features/orders/orders-slice';\nimport orderDetail from 'features/orders/detail-slice';\nimport plants from 'features/plants/plants-slice';\nimport plantDetail from 'features/plants/detail-slice';\nimport zones from 'features/zones/zones-slice';\nimport zoneDetail from 'features/zones/detail-slice';\nimport customers from 'features/customers/customers-slice';\nimport customerDetail from 'features/customers/detail-slice';\nimport users from 'features/users/users-slice';\nimport driverTaskList from 'features/driver-tasks/driver-task-slice';\nexport const store = configureStore({\n  reducer: {\n    orders,\n    orderDetail,\n    plants,\n    plantDetail,\n    zones,\n    zoneDetail,\n    customers,\n    customerDetail,\n    users,\n    driverTaskList\n  }\n});", "map": {"version": 3, "names": ["configureStore", "orders", "orderDetail", "plants", "plantDetail", "zones", "zoneDetail", "customers", "customerDetail", "users", "driverTaskList", "store", "reducer"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/app/store.ts"], "sourcesContent": ["import { configureStore, ThunkAction, Action } from '@reduxjs/toolkit';\r\nimport orders from 'features/orders/orders-slice';\r\nimport orderDetail from 'features/orders/detail-slice';\r\nimport plants from 'features/plants/plants-slice';\r\nimport plantDetail from 'features/plants/detail-slice';\r\nimport zones from 'features/zones/zones-slice';\r\nimport zoneDetail from 'features/zones/detail-slice';\r\nimport customers from 'features/customers/customers-slice';\r\nimport customerDetail from 'features/customers/detail-slice';\r\nimport users from 'features/users/users-slice';\r\nimport driverTaskList from 'features/driver-tasks/driver-task-slice';\r\n\r\nexport const store = configureStore({\r\n  reducer: {\r\n    orders,\r\n    orderDetail,\r\n    plants,\r\n    plantDetail,\r\n    zones,\r\n    zoneDetail,\r\n    customers,\r\n    customerDetail,\r\n    users,\r\n    driverTaskList,\r\n  },\r\n});\r\n\r\nexport type RootState = ReturnType<typeof store.getState>;\r\nexport type AppThunk<ReturnType = void> = ThunkAction<\r\n  ReturnType,\r\n  RootState,\r\n  unknown,\r\n  Action<string>\r\n>;\r\n"], "mappings": "AAAA,SAASA,cAAc,QAA6B,kBAAkB;AACtE,OAAOC,MAAM,MAAM,8BAA8B;AACjD,OAAOC,WAAW,MAAM,8BAA8B;AACtD,OAAOC,MAAM,MAAM,8BAA8B;AACjD,OAAOC,WAAW,MAAM,8BAA8B;AACtD,OAAOC,KAAK,MAAM,4BAA4B;AAC9C,OAAOC,UAAU,MAAM,6BAA6B;AACpD,OAAOC,SAAS,MAAM,oCAAoC;AAC1D,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,KAAK,MAAM,4BAA4B;AAC9C,OAAOC,cAAc,MAAM,yCAAyC;AAEpE,OAAO,MAAMC,KAAK,GAAGX,cAAc,CAAC;EAClCY,OAAO,EAAE;IACPX,MAAM;IACNC,WAAW;IACXC,MAAM;IACNC,WAAW;IACXC,KAAK;IACLC,UAAU;IACVC,SAAS;IACTC,cAAc;IACdC,KAAK;IACLC;EACF;AACF,CAAC,CAAC"}, "metadata": {}, "sourceType": "module"}