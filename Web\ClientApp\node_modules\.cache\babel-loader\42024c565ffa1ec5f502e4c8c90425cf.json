{"ast": null, "code": "var _s = $RefreshSig$();\nimport React from 'react';\nimport { AuthContext } from './auth-context';\nexport function useAuth() {\n  _s();\n  return React.useContext(AuthContext);\n}\n_s(useAuth, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");", "map": {"version": 3, "names": ["React", "AuthContext", "useAuth", "useContext"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/auth/use-auth.ts"], "sourcesContent": ["import React from 'react';\r\nimport { AuthContext } from './auth-context'\r\n\r\nexport function useAuth() {\r\n  return React.useContext(AuthContext);\r\n}\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,OAAO,SAASC,OAAO,GAAG;EAAA;EACxB,OAAOF,KAAK,CAACG,UAAU,CAACF,WAAW,CAAC;AACtC;AAAC,GAFeC,OAAO"}, "metadata": {}, "sourceType": "module"}