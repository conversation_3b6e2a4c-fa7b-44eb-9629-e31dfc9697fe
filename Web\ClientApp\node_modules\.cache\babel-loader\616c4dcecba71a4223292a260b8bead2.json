{"ast": null, "code": "import * as React from 'react';\nimport { ManagerReferenceNodeContext } from './Manager';\nimport { unwrapArray, setRef } from './utils';\nimport { usePopper } from './usePopper';\nvar NOOP = function NOOP() {\n  return void 0;\n};\nvar NOOP_PROMISE = function NOOP_PROMISE() {\n  return Promise.resolve(null);\n};\nvar EMPTY_MODIFIERS = [];\nexport function Popper(_ref) {\n  var _ref$placement = _ref.placement,\n    placement = _ref$placement === void 0 ? 'bottom' : _ref$placement,\n    _ref$strategy = _ref.strategy,\n    strategy = _ref$strategy === void 0 ? 'absolute' : _ref$strategy,\n    _ref$modifiers = _ref.modifiers,\n    modifiers = _ref$modifiers === void 0 ? EMPTY_MODIFIERS : _ref$modifiers,\n    referenceElement = _ref.referenceElement,\n    onFirstUpdate = _ref.onFirstUpdate,\n    innerRef = _ref.innerRef,\n    children = _ref.children;\n  var referenceNode = React.useContext(ManagerReferenceNodeContext);\n  var _React$useState = React.useState(null),\n    popperElement = _React$useState[0],\n    setPopperElement = _React$useState[1];\n  var _React$useState2 = React.useState(null),\n    arrowElement = _React$useState2[0],\n    setArrowElement = _React$useState2[1];\n  React.useEffect(function () {\n    setRef(innerRef, popperElement);\n  }, [innerRef, popperElement]);\n  var options = React.useMemo(function () {\n    return {\n      placement: placement,\n      strategy: strategy,\n      onFirstUpdate: onFirstUpdate,\n      modifiers: [].concat(modifiers, [{\n        name: 'arrow',\n        enabled: arrowElement != null,\n        options: {\n          element: arrowElement\n        }\n      }])\n    };\n  }, [placement, strategy, onFirstUpdate, modifiers, arrowElement]);\n  var _usePopper = usePopper(referenceElement || referenceNode, popperElement, options),\n    state = _usePopper.state,\n    styles = _usePopper.styles,\n    forceUpdate = _usePopper.forceUpdate,\n    update = _usePopper.update;\n  var childrenProps = React.useMemo(function () {\n    return {\n      ref: setPopperElement,\n      style: styles.popper,\n      placement: state ? state.placement : placement,\n      hasPopperEscaped: state && state.modifiersData.hide ? state.modifiersData.hide.hasPopperEscaped : null,\n      isReferenceHidden: state && state.modifiersData.hide ? state.modifiersData.hide.isReferenceHidden : null,\n      arrowProps: {\n        style: styles.arrow,\n        ref: setArrowElement\n      },\n      forceUpdate: forceUpdate || NOOP,\n      update: update || NOOP_PROMISE\n    };\n  }, [setPopperElement, setArrowElement, placement, state, styles, update, forceUpdate]);\n  return unwrapArray(children)(childrenProps);\n}", "map": {"version": 3, "names": ["React", "ManagerReferenceNodeContext", "unwrapArray", "setRef", "usePopper", "NOOP", "NOOP_PROMISE", "Promise", "resolve", "EMPTY_MODIFIERS", "<PERSON><PERSON>", "_ref", "_ref$placement", "placement", "_ref$strategy", "strategy", "_ref$modifiers", "modifiers", "referenceElement", "onFirstUpdate", "innerRef", "children", "referenceNode", "useContext", "_React$useState", "useState", "popper<PERSON>lement", "setPopperElement", "_React$useState2", "arrowElement", "setArrowElement", "useEffect", "options", "useMemo", "concat", "name", "enabled", "element", "_usePopper", "state", "styles", "forceUpdate", "update", "childrenProps", "ref", "style", "popper", "hasPopperEscaped", "modifiersData", "hide", "isReferenceHidden", "arrowProps", "arrow"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/node_modules/react-popper/lib/esm/Popper.js"], "sourcesContent": ["import * as React from 'react';\nimport { ManagerReferenceNodeContext } from './Manager';\nimport { unwrapArray, setRef } from './utils';\nimport { usePopper } from './usePopper';\n\nvar NOOP = function NOOP() {\n  return void 0;\n};\n\nvar NOOP_PROMISE = function NOOP_PROMISE() {\n  return Promise.resolve(null);\n};\n\nvar EMPTY_MODIFIERS = [];\nexport function Popper(_ref) {\n  var _ref$placement = _ref.placement,\n      placement = _ref$placement === void 0 ? 'bottom' : _ref$placement,\n      _ref$strategy = _ref.strategy,\n      strategy = _ref$strategy === void 0 ? 'absolute' : _ref$strategy,\n      _ref$modifiers = _ref.modifiers,\n      modifiers = _ref$modifiers === void 0 ? EMPTY_MODIFIERS : _ref$modifiers,\n      referenceElement = _ref.referenceElement,\n      onFirstUpdate = _ref.onFirstUpdate,\n      innerRef = _ref.innerRef,\n      children = _ref.children;\n  var referenceNode = React.useContext(ManagerReferenceNodeContext);\n\n  var _React$useState = React.useState(null),\n      popperElement = _React$useState[0],\n      setPopperElement = _React$useState[1];\n\n  var _React$useState2 = React.useState(null),\n      arrowElement = _React$useState2[0],\n      setArrowElement = _React$useState2[1];\n\n  React.useEffect(function () {\n    setRef(innerRef, popperElement);\n  }, [innerRef, popperElement]);\n  var options = React.useMemo(function () {\n    return {\n      placement: placement,\n      strategy: strategy,\n      onFirstUpdate: onFirstUpdate,\n      modifiers: [].concat(modifiers, [{\n        name: 'arrow',\n        enabled: arrowElement != null,\n        options: {\n          element: arrowElement\n        }\n      }])\n    };\n  }, [placement, strategy, onFirstUpdate, modifiers, arrowElement]);\n\n  var _usePopper = usePopper(referenceElement || referenceNode, popperElement, options),\n      state = _usePopper.state,\n      styles = _usePopper.styles,\n      forceUpdate = _usePopper.forceUpdate,\n      update = _usePopper.update;\n\n  var childrenProps = React.useMemo(function () {\n    return {\n      ref: setPopperElement,\n      style: styles.popper,\n      placement: state ? state.placement : placement,\n      hasPopperEscaped: state && state.modifiersData.hide ? state.modifiersData.hide.hasPopperEscaped : null,\n      isReferenceHidden: state && state.modifiersData.hide ? state.modifiersData.hide.isReferenceHidden : null,\n      arrowProps: {\n        style: styles.arrow,\n        ref: setArrowElement\n      },\n      forceUpdate: forceUpdate || NOOP,\n      update: update || NOOP_PROMISE\n    };\n  }, [setPopperElement, setArrowElement, placement, state, styles, update, forceUpdate]);\n  return unwrapArray(children)(childrenProps);\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,2BAA2B,QAAQ,WAAW;AACvD,SAASC,WAAW,EAAEC,MAAM,QAAQ,SAAS;AAC7C,SAASC,SAAS,QAAQ,aAAa;AAEvC,IAAIC,IAAI,GAAG,SAASA,IAAI,GAAG;EACzB,OAAO,KAAK,CAAC;AACf,CAAC;AAED,IAAIC,YAAY,GAAG,SAASA,YAAY,GAAG;EACzC,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC;AAC9B,CAAC;AAED,IAAIC,eAAe,GAAG,EAAE;AACxB,OAAO,SAASC,MAAM,CAACC,IAAI,EAAE;EAC3B,IAAIC,cAAc,GAAGD,IAAI,CAACE,SAAS;IAC/BA,SAAS,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAGA,cAAc;IACjEE,aAAa,GAAGH,IAAI,CAACI,QAAQ;IAC7BA,QAAQ,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAG,UAAU,GAAGA,aAAa;IAChEE,cAAc,GAAGL,IAAI,CAACM,SAAS;IAC/BA,SAAS,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAGP,eAAe,GAAGO,cAAc;IACxEE,gBAAgB,GAAGP,IAAI,CAACO,gBAAgB;IACxCC,aAAa,GAAGR,IAAI,CAACQ,aAAa;IAClCC,QAAQ,GAAGT,IAAI,CAACS,QAAQ;IACxBC,QAAQ,GAAGV,IAAI,CAACU,QAAQ;EAC5B,IAAIC,aAAa,GAAGtB,KAAK,CAACuB,UAAU,CAACtB,2BAA2B,CAAC;EAEjE,IAAIuB,eAAe,GAAGxB,KAAK,CAACyB,QAAQ,CAAC,IAAI,CAAC;IACtCC,aAAa,GAAGF,eAAe,CAAC,CAAC,CAAC;IAClCG,gBAAgB,GAAGH,eAAe,CAAC,CAAC,CAAC;EAEzC,IAAII,gBAAgB,GAAG5B,KAAK,CAACyB,QAAQ,CAAC,IAAI,CAAC;IACvCI,YAAY,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAClCE,eAAe,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAEzC5B,KAAK,CAAC+B,SAAS,CAAC,YAAY;IAC1B5B,MAAM,CAACiB,QAAQ,EAAEM,aAAa,CAAC;EACjC,CAAC,EAAE,CAACN,QAAQ,EAAEM,aAAa,CAAC,CAAC;EAC7B,IAAIM,OAAO,GAAGhC,KAAK,CAACiC,OAAO,CAAC,YAAY;IACtC,OAAO;MACLpB,SAAS,EAAEA,SAAS;MACpBE,QAAQ,EAAEA,QAAQ;MAClBI,aAAa,EAAEA,aAAa;MAC5BF,SAAS,EAAE,EAAE,CAACiB,MAAM,CAACjB,SAAS,EAAE,CAAC;QAC/BkB,IAAI,EAAE,OAAO;QACbC,OAAO,EAAEP,YAAY,IAAI,IAAI;QAC7BG,OAAO,EAAE;UACPK,OAAO,EAAER;QACX;MACF,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAE,CAAChB,SAAS,EAAEE,QAAQ,EAAEI,aAAa,EAAEF,SAAS,EAAEY,YAAY,CAAC,CAAC;EAEjE,IAAIS,UAAU,GAAGlC,SAAS,CAACc,gBAAgB,IAAII,aAAa,EAAEI,aAAa,EAAEM,OAAO,CAAC;IACjFO,KAAK,GAAGD,UAAU,CAACC,KAAK;IACxBC,MAAM,GAAGF,UAAU,CAACE,MAAM;IAC1BC,WAAW,GAAGH,UAAU,CAACG,WAAW;IACpCC,MAAM,GAAGJ,UAAU,CAACI,MAAM;EAE9B,IAAIC,aAAa,GAAG3C,KAAK,CAACiC,OAAO,CAAC,YAAY;IAC5C,OAAO;MACLW,GAAG,EAAEjB,gBAAgB;MACrBkB,KAAK,EAAEL,MAAM,CAACM,MAAM;MACpBjC,SAAS,EAAE0B,KAAK,GAAGA,KAAK,CAAC1B,SAAS,GAAGA,SAAS;MAC9CkC,gBAAgB,EAAER,KAAK,IAAIA,KAAK,CAACS,aAAa,CAACC,IAAI,GAAGV,KAAK,CAACS,aAAa,CAACC,IAAI,CAACF,gBAAgB,GAAG,IAAI;MACtGG,iBAAiB,EAAEX,KAAK,IAAIA,KAAK,CAACS,aAAa,CAACC,IAAI,GAAGV,KAAK,CAACS,aAAa,CAACC,IAAI,CAACC,iBAAiB,GAAG,IAAI;MACxGC,UAAU,EAAE;QACVN,KAAK,EAAEL,MAAM,CAACY,KAAK;QACnBR,GAAG,EAAEd;MACP,CAAC;MACDW,WAAW,EAAEA,WAAW,IAAIpC,IAAI;MAChCqC,MAAM,EAAEA,MAAM,IAAIpC;IACpB,CAAC;EACH,CAAC,EAAE,CAACqB,gBAAgB,EAAEG,eAAe,EAAEjB,SAAS,EAAE0B,KAAK,EAAEC,MAAM,EAAEE,MAAM,EAAED,WAAW,CAAC,CAAC;EACtF,OAAOvC,WAAW,CAACmB,QAAQ,CAAC,CAACsB,aAAa,CAAC;AAC7C"}, "metadata": {}, "sourceType": "module"}