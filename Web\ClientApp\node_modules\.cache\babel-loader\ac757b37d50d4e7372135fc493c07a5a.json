{"ast": null, "code": "import { parse, icon } from '@fortawesome/fontawesome-svg-core';\nimport PropTypes from 'prop-types';\nimport React from 'react';\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\n// Get CSS class list from a props object\nfunction classList(props) {\n  var _classes;\n  var beat = props.beat,\n    fade = props.fade,\n    beatFade = props.beatFade,\n    bounce = props.bounce,\n    shake = props.shake,\n    flash = props.flash,\n    spin = props.spin,\n    spinPulse = props.spinPulse,\n    spinReverse = props.spinReverse,\n    pulse = props.pulse,\n    fixedWidth = props.fixedWidth,\n    inverse = props.inverse,\n    border = props.border,\n    listItem = props.listItem,\n    flip = props.flip,\n    size = props.size,\n    rotation = props.rotation,\n    pull = props.pull; // map of CSS class names to properties\n\n  var classes = (_classes = {\n    'fa-beat': beat,\n    'fa-fade': fade,\n    'fa-beat-fade': beatFade,\n    'fa-bounce': bounce,\n    'fa-shake': shake,\n    'fa-flash': flash,\n    'fa-spin': spin,\n    'fa-spin-reverse': spinReverse,\n    'fa-spin-pulse': spinPulse,\n    'fa-pulse': pulse,\n    'fa-fw': fixedWidth,\n    'fa-inverse': inverse,\n    'fa-border': border,\n    'fa-li': listItem,\n    'fa-flip': flip === true,\n    'fa-flip-horizontal': flip === 'horizontal' || flip === 'both',\n    'fa-flip-vertical': flip === 'vertical' || flip === 'both'\n  }, _defineProperty(_classes, \"fa-\".concat(size), typeof size !== 'undefined' && size !== null), _defineProperty(_classes, \"fa-rotate-\".concat(rotation), typeof rotation !== 'undefined' && rotation !== null && rotation !== 0), _defineProperty(_classes, \"fa-pull-\".concat(pull), typeof pull !== 'undefined' && pull !== null), _defineProperty(_classes, 'fa-swap-opacity', props.swapOpacity), _classes); // map over all the keys in the classes object\n  // return an array of the keys where the value for the key is not null\n\n  return Object.keys(classes).map(function (key) {\n    return classes[key] ? key : null;\n  }).filter(function (key) {\n    return key;\n  });\n}\n\n// Camelize taken from humps\n// humps is copyright © 2012+ Dom Christie\n// Released under the MIT license.\n// Performant way to determine if object coerces to a number\nfunction _isNumerical(obj) {\n  obj = obj - 0; // eslint-disable-next-line no-self-compare\n\n  return obj === obj;\n}\nfunction camelize(string) {\n  if (_isNumerical(string)) {\n    return string;\n  } // eslint-disable-next-line no-useless-escape\n\n  string = string.replace(/[\\-_\\s]+(.)?/g, function (match, chr) {\n    return chr ? chr.toUpperCase() : '';\n  }); // Ensure 1st char is always lowercase\n\n  return string.substr(0, 1).toLowerCase() + string.substr(1);\n}\nvar _excluded = [\"style\"];\nfunction capitalize(val) {\n  return val.charAt(0).toUpperCase() + val.slice(1);\n}\nfunction styleToObject(style) {\n  return style.split(';').map(function (s) {\n    return s.trim();\n  }).filter(function (s) {\n    return s;\n  }).reduce(function (acc, pair) {\n    var i = pair.indexOf(':');\n    var prop = camelize(pair.slice(0, i));\n    var value = pair.slice(i + 1).trim();\n    prop.startsWith('webkit') ? acc[capitalize(prop)] = value : acc[prop] = value;\n    return acc;\n  }, {});\n}\nfunction convert(createElement, element) {\n  var extraProps = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  if (typeof element === 'string') {\n    return element;\n  }\n  var children = (element.children || []).map(function (child) {\n    return convert(createElement, child);\n  });\n  /* eslint-disable dot-notation */\n\n  var mixins = Object.keys(element.attributes || {}).reduce(function (acc, key) {\n    var val = element.attributes[key];\n    switch (key) {\n      case 'class':\n        acc.attrs['className'] = val;\n        delete element.attributes['class'];\n        break;\n      case 'style':\n        acc.attrs['style'] = styleToObject(val);\n        break;\n      default:\n        if (key.indexOf('aria-') === 0 || key.indexOf('data-') === 0) {\n          acc.attrs[key.toLowerCase()] = val;\n        } else {\n          acc.attrs[camelize(key)] = val;\n        }\n    }\n    return acc;\n  }, {\n    attrs: {}\n  });\n  var _extraProps$style = extraProps.style,\n    existingStyle = _extraProps$style === void 0 ? {} : _extraProps$style,\n    remaining = _objectWithoutProperties(extraProps, _excluded);\n  mixins.attrs['style'] = _objectSpread2(_objectSpread2({}, mixins.attrs['style']), existingStyle);\n  /* eslint-enable */\n\n  return createElement.apply(void 0, [element.tag, _objectSpread2(_objectSpread2({}, mixins.attrs), remaining)].concat(_toConsumableArray(children)));\n}\nvar PRODUCTION = false;\ntry {\n  PRODUCTION = process.env.NODE_ENV === 'production';\n} catch (e) {}\nfunction log() {\n  if (!PRODUCTION && console && typeof console.error === 'function') {\n    var _console;\n    (_console = console).error.apply(_console, arguments);\n  }\n}\nfunction normalizeIconArgs(icon) {\n  // this has everything that it needs to be rendered which means it was probably imported\n  // directly from an icon svg package\n  if (icon && _typeof(icon) === 'object' && icon.prefix && icon.iconName && icon.icon) {\n    return icon;\n  }\n  if (parse.icon) {\n    return parse.icon(icon);\n  } // if the icon is null, there's nothing to do\n\n  if (icon === null) {\n    return null;\n  } // if the icon is an object and has a prefix and an icon name, return it\n\n  if (icon && _typeof(icon) === 'object' && icon.prefix && icon.iconName) {\n    return icon;\n  } // if it's an array with length of two\n\n  if (Array.isArray(icon) && icon.length === 2) {\n    // use the first item as prefix, second as icon name\n    return {\n      prefix: icon[0],\n      iconName: icon[1]\n    };\n  } // if it's a string, use it as the icon name\n\n  if (typeof icon === 'string') {\n    return {\n      prefix: 'fas',\n      iconName: icon\n    };\n  }\n}\n\n// creates an object with a key of key\n// and a value of value\n// if certain conditions are met\nfunction objectWithKey(key, value) {\n  // if the value is a non-empty array\n  // or it's not an array but it is truthy\n  // then create the object with the key and the value\n  // if not, return an empty array\n  return Array.isArray(value) && value.length > 0 || !Array.isArray(value) && value ? _defineProperty({}, key, value) : {};\n}\nvar defaultProps = {\n  border: false,\n  className: '',\n  mask: null,\n  maskId: null,\n  fixedWidth: false,\n  inverse: false,\n  flip: false,\n  icon: null,\n  listItem: false,\n  pull: null,\n  pulse: false,\n  rotation: null,\n  size: null,\n  spin: false,\n  spinPulse: false,\n  spinReverse: false,\n  beat: false,\n  fade: false,\n  beatFade: false,\n  bounce: false,\n  shake: false,\n  symbol: false,\n  title: '',\n  titleId: null,\n  transform: null,\n  swapOpacity: false\n};\nvar FontAwesomeIcon = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var allProps = _objectSpread2(_objectSpread2({}, defaultProps), props);\n  var iconArgs = allProps.icon,\n    maskArgs = allProps.mask,\n    symbol = allProps.symbol,\n    className = allProps.className,\n    title = allProps.title,\n    titleId = allProps.titleId,\n    maskId = allProps.maskId;\n  var iconLookup = normalizeIconArgs(iconArgs);\n  var classes = objectWithKey('classes', [].concat(_toConsumableArray(classList(allProps)), _toConsumableArray((className || '').split(' '))));\n  var transform = objectWithKey('transform', typeof allProps.transform === 'string' ? parse.transform(allProps.transform) : allProps.transform);\n  var mask = objectWithKey('mask', normalizeIconArgs(maskArgs));\n  var renderedIcon = icon(iconLookup, _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({}, classes), transform), mask), {}, {\n    symbol: symbol,\n    title: title,\n    titleId: titleId,\n    maskId: maskId\n  }));\n  if (!renderedIcon) {\n    log('Could not find icon', iconLookup);\n    return null;\n  }\n  var abstract = renderedIcon.abstract;\n  var extraProps = {\n    ref: ref\n  };\n  Object.keys(allProps).forEach(function (key) {\n    // eslint-disable-next-line no-prototype-builtins\n    if (!defaultProps.hasOwnProperty(key)) {\n      extraProps[key] = allProps[key];\n    }\n  });\n  return convertCurry(abstract[0], extraProps);\n});\nFontAwesomeIcon.displayName = 'FontAwesomeIcon';\nFontAwesomeIcon.propTypes = {\n  beat: PropTypes.bool,\n  border: PropTypes.bool,\n  beatFade: PropTypes.bool,\n  bounce: PropTypes.bool,\n  className: PropTypes.string,\n  fade: PropTypes.bool,\n  flash: PropTypes.bool,\n  mask: PropTypes.oneOfType([PropTypes.object, PropTypes.array, PropTypes.string]),\n  maskId: PropTypes.string,\n  fixedWidth: PropTypes.bool,\n  inverse: PropTypes.bool,\n  flip: PropTypes.oneOf([true, false, 'horizontal', 'vertical', 'both']),\n  icon: PropTypes.oneOfType([PropTypes.object, PropTypes.array, PropTypes.string]),\n  listItem: PropTypes.bool,\n  pull: PropTypes.oneOf(['right', 'left']),\n  pulse: PropTypes.bool,\n  rotation: PropTypes.oneOf([0, 90, 180, 270]),\n  shake: PropTypes.bool,\n  size: PropTypes.oneOf(['2xs', 'xs', 'sm', 'lg', 'xl', '2xl', '1x', '2x', '3x', '4x', '5x', '6x', '7x', '8x', '9x', '10x']),\n  spin: PropTypes.bool,\n  spinPulse: PropTypes.bool,\n  spinReverse: PropTypes.bool,\n  symbol: PropTypes.oneOfType([PropTypes.bool, PropTypes.string]),\n  title: PropTypes.string,\n  titleId: PropTypes.string,\n  transform: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),\n  swapOpacity: PropTypes.bool\n};\nvar convertCurry = convert.bind(null, React.createElement);\nexport { FontAwesomeIcon };", "map": {"version": 3, "names": ["parse", "icon", "PropTypes", "React", "ownKeys", "object", "enumerableOnly", "keys", "Object", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread2", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "key", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "value", "configurable", "writable", "_objectWithoutPropertiesLoose", "excluded", "sourceKeys", "indexOf", "_objectWithoutProperties", "sourceSymbolKeys", "propertyIsEnumerable", "call", "_toConsumableArray", "arr", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "Array", "isArray", "_arrayLikeToArray", "iter", "from", "o", "minLen", "n", "toString", "slice", "name", "test", "len", "arr2", "TypeError", "classList", "props", "_classes", "beat", "fade", "beatFade", "bounce", "shake", "flash", "spin", "spinPulse", "spinReverse", "pulse", "fixedWidth", "inverse", "border", "listItem", "flip", "size", "rotation", "pull", "classes", "concat", "swapOpacity", "map", "_isNumerical", "camelize", "string", "replace", "match", "chr", "toUpperCase", "substr", "toLowerCase", "_excluded", "capitalize", "val", "char<PERSON>t", "styleToObject", "style", "split", "s", "trim", "reduce", "acc", "pair", "prop", "startsWith", "convert", "createElement", "element", "extraProps", "undefined", "children", "child", "mixins", "attributes", "attrs", "_extraProps$style", "existingStyle", "remaining", "tag", "PRODUCTION", "process", "env", "NODE_ENV", "e", "log", "console", "error", "_console", "normalizeIconArgs", "prefix", "iconName", "objectWithKey", "defaultProps", "className", "mask", "maskId", "symbol", "title", "titleId", "transform", "FontAwesomeIcon", "forwardRef", "ref", "allProps", "iconArgs", "<PERSON><PERSON><PERSON><PERSON>", "iconLookup", "renderedIcon", "abstract", "hasOwnProperty", "convertCurry", "displayName", "propTypes", "bool", "oneOfType", "array", "oneOf", "bind"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/node_modules/@fortawesome/react-fontawesome/index.es.js"], "sourcesContent": ["import { parse, icon } from '@fortawesome/fontawesome-svg-core';\nimport PropTypes from 'prop-types';\nimport React from 'react';\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n\n  return target;\n}\n\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n\n  var key, i;\n\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}\n\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\n\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\n\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\n// Get CSS class list from a props object\nfunction classList(props) {\n  var _classes;\n\n  var beat = props.beat,\n      fade = props.fade,\n      beatFade = props.beatFade,\n      bounce = props.bounce,\n      shake = props.shake,\n      flash = props.flash,\n      spin = props.spin,\n      spinPulse = props.spinPulse,\n      spinReverse = props.spinReverse,\n      pulse = props.pulse,\n      fixedWidth = props.fixedWidth,\n      inverse = props.inverse,\n      border = props.border,\n      listItem = props.listItem,\n      flip = props.flip,\n      size = props.size,\n      rotation = props.rotation,\n      pull = props.pull; // map of CSS class names to properties\n\n  var classes = (_classes = {\n    'fa-beat': beat,\n    'fa-fade': fade,\n    'fa-beat-fade': beatFade,\n    'fa-bounce': bounce,\n    'fa-shake': shake,\n    'fa-flash': flash,\n    'fa-spin': spin,\n    'fa-spin-reverse': spinReverse,\n    'fa-spin-pulse': spinPulse,\n    'fa-pulse': pulse,\n    'fa-fw': fixedWidth,\n    'fa-inverse': inverse,\n    'fa-border': border,\n    'fa-li': listItem,\n    'fa-flip': flip === true,\n    'fa-flip-horizontal': flip === 'horizontal' || flip === 'both',\n    'fa-flip-vertical': flip === 'vertical' || flip === 'both'\n  }, _defineProperty(_classes, \"fa-\".concat(size), typeof size !== 'undefined' && size !== null), _defineProperty(_classes, \"fa-rotate-\".concat(rotation), typeof rotation !== 'undefined' && rotation !== null && rotation !== 0), _defineProperty(_classes, \"fa-pull-\".concat(pull), typeof pull !== 'undefined' && pull !== null), _defineProperty(_classes, 'fa-swap-opacity', props.swapOpacity), _classes); // map over all the keys in the classes object\n  // return an array of the keys where the value for the key is not null\n\n  return Object.keys(classes).map(function (key) {\n    return classes[key] ? key : null;\n  }).filter(function (key) {\n    return key;\n  });\n}\n\n// Camelize taken from humps\n// humps is copyright © 2012+ Dom Christie\n// Released under the MIT license.\n// Performant way to determine if object coerces to a number\nfunction _isNumerical(obj) {\n  obj = obj - 0; // eslint-disable-next-line no-self-compare\n\n  return obj === obj;\n}\n\nfunction camelize(string) {\n  if (_isNumerical(string)) {\n    return string;\n  } // eslint-disable-next-line no-useless-escape\n\n\n  string = string.replace(/[\\-_\\s]+(.)?/g, function (match, chr) {\n    return chr ? chr.toUpperCase() : '';\n  }); // Ensure 1st char is always lowercase\n\n  return string.substr(0, 1).toLowerCase() + string.substr(1);\n}\n\nvar _excluded = [\"style\"];\n\nfunction capitalize(val) {\n  return val.charAt(0).toUpperCase() + val.slice(1);\n}\n\nfunction styleToObject(style) {\n  return style.split(';').map(function (s) {\n    return s.trim();\n  }).filter(function (s) {\n    return s;\n  }).reduce(function (acc, pair) {\n    var i = pair.indexOf(':');\n    var prop = camelize(pair.slice(0, i));\n    var value = pair.slice(i + 1).trim();\n    prop.startsWith('webkit') ? acc[capitalize(prop)] = value : acc[prop] = value;\n    return acc;\n  }, {});\n}\n\nfunction convert(createElement, element) {\n  var extraProps = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n\n  if (typeof element === 'string') {\n    return element;\n  }\n\n  var children = (element.children || []).map(function (child) {\n    return convert(createElement, child);\n  });\n  /* eslint-disable dot-notation */\n\n  var mixins = Object.keys(element.attributes || {}).reduce(function (acc, key) {\n    var val = element.attributes[key];\n\n    switch (key) {\n      case 'class':\n        acc.attrs['className'] = val;\n        delete element.attributes['class'];\n        break;\n\n      case 'style':\n        acc.attrs['style'] = styleToObject(val);\n        break;\n\n      default:\n        if (key.indexOf('aria-') === 0 || key.indexOf('data-') === 0) {\n          acc.attrs[key.toLowerCase()] = val;\n        } else {\n          acc.attrs[camelize(key)] = val;\n        }\n\n    }\n\n    return acc;\n  }, {\n    attrs: {}\n  });\n\n  var _extraProps$style = extraProps.style,\n      existingStyle = _extraProps$style === void 0 ? {} : _extraProps$style,\n      remaining = _objectWithoutProperties(extraProps, _excluded);\n\n  mixins.attrs['style'] = _objectSpread2(_objectSpread2({}, mixins.attrs['style']), existingStyle);\n  /* eslint-enable */\n\n  return createElement.apply(void 0, [element.tag, _objectSpread2(_objectSpread2({}, mixins.attrs), remaining)].concat(_toConsumableArray(children)));\n}\n\nvar PRODUCTION = false;\n\ntry {\n  PRODUCTION = process.env.NODE_ENV === 'production';\n} catch (e) {}\n\nfunction log () {\n  if (!PRODUCTION && console && typeof console.error === 'function') {\n    var _console;\n\n    (_console = console).error.apply(_console, arguments);\n  }\n}\n\nfunction normalizeIconArgs(icon) {\n  // this has everything that it needs to be rendered which means it was probably imported\n  // directly from an icon svg package\n  if (icon && _typeof(icon) === 'object' && icon.prefix && icon.iconName && icon.icon) {\n    return icon;\n  }\n\n  if (parse.icon) {\n    return parse.icon(icon);\n  } // if the icon is null, there's nothing to do\n\n\n  if (icon === null) {\n    return null;\n  } // if the icon is an object and has a prefix and an icon name, return it\n\n\n  if (icon && _typeof(icon) === 'object' && icon.prefix && icon.iconName) {\n    return icon;\n  } // if it's an array with length of two\n\n\n  if (Array.isArray(icon) && icon.length === 2) {\n    // use the first item as prefix, second as icon name\n    return {\n      prefix: icon[0],\n      iconName: icon[1]\n    };\n  } // if it's a string, use it as the icon name\n\n\n  if (typeof icon === 'string') {\n    return {\n      prefix: 'fas',\n      iconName: icon\n    };\n  }\n}\n\n// creates an object with a key of key\n// and a value of value\n// if certain conditions are met\nfunction objectWithKey(key, value) {\n  // if the value is a non-empty array\n  // or it's not an array but it is truthy\n  // then create the object with the key and the value\n  // if not, return an empty array\n  return Array.isArray(value) && value.length > 0 || !Array.isArray(value) && value ? _defineProperty({}, key, value) : {};\n}\n\nvar defaultProps = {\n  border: false,\n  className: '',\n  mask: null,\n  maskId: null,\n  fixedWidth: false,\n  inverse: false,\n  flip: false,\n  icon: null,\n  listItem: false,\n  pull: null,\n  pulse: false,\n  rotation: null,\n  size: null,\n  spin: false,\n  spinPulse: false,\n  spinReverse: false,\n  beat: false,\n  fade: false,\n  beatFade: false,\n  bounce: false,\n  shake: false,\n  symbol: false,\n  title: '',\n  titleId: null,\n  transform: null,\n  swapOpacity: false\n};\nvar FontAwesomeIcon = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var allProps = _objectSpread2(_objectSpread2({}, defaultProps), props);\n\n  var iconArgs = allProps.icon,\n      maskArgs = allProps.mask,\n      symbol = allProps.symbol,\n      className = allProps.className,\n      title = allProps.title,\n      titleId = allProps.titleId,\n      maskId = allProps.maskId;\n  var iconLookup = normalizeIconArgs(iconArgs);\n  var classes = objectWithKey('classes', [].concat(_toConsumableArray(classList(allProps)), _toConsumableArray((className || '').split(' '))));\n  var transform = objectWithKey('transform', typeof allProps.transform === 'string' ? parse.transform(allProps.transform) : allProps.transform);\n  var mask = objectWithKey('mask', normalizeIconArgs(maskArgs));\n  var renderedIcon = icon(iconLookup, _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({}, classes), transform), mask), {}, {\n    symbol: symbol,\n    title: title,\n    titleId: titleId,\n    maskId: maskId\n  }));\n\n  if (!renderedIcon) {\n    log('Could not find icon', iconLookup);\n    return null;\n  }\n\n  var abstract = renderedIcon.abstract;\n  var extraProps = {\n    ref: ref\n  };\n  Object.keys(allProps).forEach(function (key) {\n    // eslint-disable-next-line no-prototype-builtins\n    if (!defaultProps.hasOwnProperty(key)) {\n      extraProps[key] = allProps[key];\n    }\n  });\n  return convertCurry(abstract[0], extraProps);\n});\nFontAwesomeIcon.displayName = 'FontAwesomeIcon';\nFontAwesomeIcon.propTypes = {\n  beat: PropTypes.bool,\n  border: PropTypes.bool,\n  beatFade: PropTypes.bool,\n  bounce: PropTypes.bool,\n  className: PropTypes.string,\n  fade: PropTypes.bool,\n  flash: PropTypes.bool,\n  mask: PropTypes.oneOfType([PropTypes.object, PropTypes.array, PropTypes.string]),\n  maskId: PropTypes.string,\n  fixedWidth: PropTypes.bool,\n  inverse: PropTypes.bool,\n  flip: PropTypes.oneOf([true, false, 'horizontal', 'vertical', 'both']),\n  icon: PropTypes.oneOfType([PropTypes.object, PropTypes.array, PropTypes.string]),\n  listItem: PropTypes.bool,\n  pull: PropTypes.oneOf(['right', 'left']),\n  pulse: PropTypes.bool,\n  rotation: PropTypes.oneOf([0, 90, 180, 270]),\n  shake: PropTypes.bool,\n  size: PropTypes.oneOf(['2xs', 'xs', 'sm', 'lg', 'xl', '2xl', '1x', '2x', '3x', '4x', '5x', '6x', '7x', '8x', '9x', '10x']),\n  spin: PropTypes.bool,\n  spinPulse: PropTypes.bool,\n  spinReverse: PropTypes.bool,\n  symbol: PropTypes.oneOfType([PropTypes.bool, PropTypes.string]),\n  title: PropTypes.string,\n  titleId: PropTypes.string,\n  transform: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),\n  swapOpacity: PropTypes.bool\n};\nvar convertCurry = convert.bind(null, React.createElement);\n\nexport { FontAwesomeIcon };\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,IAAI,QAAQ,mCAAmC;AAC/D,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,KAAK,MAAM,OAAO;AAEzB,SAASC,OAAO,CAACC,MAAM,EAAEC,cAAc,EAAE;EACvC,IAAIC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACF,MAAM,CAAC;EAE9B,IAAIG,MAAM,CAACC,qBAAqB,EAAE;IAChC,IAAIC,OAAO,GAAGF,MAAM,CAACC,qBAAqB,CAACJ,MAAM,CAAC;IAClDC,cAAc,KAAKI,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MACzD,OAAOJ,MAAM,CAACK,wBAAwB,CAACR,MAAM,EAAEO,GAAG,CAAC,CAACE,UAAU;IAChE,CAAC,CAAC,CAAC,EAAEP,IAAI,CAACQ,IAAI,CAACC,KAAK,CAACT,IAAI,EAAEG,OAAO,CAAC;EACrC;EAEA,OAAOH,IAAI;AACb;AAEA,SAASU,cAAc,CAACC,MAAM,EAAE;EAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IACzC,IAAIG,MAAM,GAAG,IAAI,IAAIF,SAAS,CAACD,CAAC,CAAC,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IACrDA,CAAC,GAAG,CAAC,GAAGf,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MACzDC,eAAe,CAACP,MAAM,EAAEM,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;IAC3C,CAAC,CAAC,GAAGhB,MAAM,CAACkB,yBAAyB,GAAGlB,MAAM,CAACmB,gBAAgB,CAACT,MAAM,EAAEV,MAAM,CAACkB,yBAAyB,CAACJ,MAAM,CAAC,CAAC,GAAGlB,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;MACjKhB,MAAM,CAACoB,cAAc,CAACV,MAAM,EAAEM,GAAG,EAAEhB,MAAM,CAACK,wBAAwB,CAACS,MAAM,EAAEE,GAAG,CAAC,CAAC;IAClF,CAAC,CAAC;EACJ;EAEA,OAAON,MAAM;AACf;AAEA,SAASW,OAAO,CAACC,GAAG,EAAE;EACpB,yBAAyB;;EAEzB,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,GAAG,EAAE;IAClG,OAAO,OAAOA,GAAG;EACnB,CAAC,GAAG,UAAUA,GAAG,EAAE;IACjB,OAAOA,GAAG,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;EAC7H,CAAC,EAAED,OAAO,CAACC,GAAG,CAAC;AACjB;AAEA,SAASL,eAAe,CAACK,GAAG,EAAEN,GAAG,EAAEW,KAAK,EAAE;EACxC,IAAIX,GAAG,IAAIM,GAAG,EAAE;IACdtB,MAAM,CAACoB,cAAc,CAACE,GAAG,EAAEN,GAAG,EAAE;MAC9BW,KAAK,EAAEA,KAAK;MACZrB,UAAU,EAAE,IAAI;MAChBsB,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,MAAM;IACLP,GAAG,CAACN,GAAG,CAAC,GAAGW,KAAK;EAClB;EAEA,OAAOL,GAAG;AACZ;AAEA,SAASQ,6BAA6B,CAAChB,MAAM,EAAEiB,QAAQ,EAAE;EACvD,IAAIjB,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAC7B,IAAIJ,MAAM,GAAG,CAAC,CAAC;EACf,IAAIsB,UAAU,GAAGhC,MAAM,CAACD,IAAI,CAACe,MAAM,CAAC;EACpC,IAAIE,GAAG,EAAEL,CAAC;EAEV,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqB,UAAU,CAACnB,MAAM,EAAEF,CAAC,EAAE,EAAE;IACtCK,GAAG,GAAGgB,UAAU,CAACrB,CAAC,CAAC;IACnB,IAAIoB,QAAQ,CAACE,OAAO,CAACjB,GAAG,CAAC,IAAI,CAAC,EAAE;IAChCN,MAAM,CAACM,GAAG,CAAC,GAAGF,MAAM,CAACE,GAAG,CAAC;EAC3B;EAEA,OAAON,MAAM;AACf;AAEA,SAASwB,wBAAwB,CAACpB,MAAM,EAAEiB,QAAQ,EAAE;EAClD,IAAIjB,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAE7B,IAAIJ,MAAM,GAAGoB,6BAA6B,CAAChB,MAAM,EAAEiB,QAAQ,CAAC;EAE5D,IAAIf,GAAG,EAAEL,CAAC;EAEV,IAAIX,MAAM,CAACC,qBAAqB,EAAE;IAChC,IAAIkC,gBAAgB,GAAGnC,MAAM,CAACC,qBAAqB,CAACa,MAAM,CAAC;IAE3D,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,gBAAgB,CAACtB,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC5CK,GAAG,GAAGmB,gBAAgB,CAACxB,CAAC,CAAC;MACzB,IAAIoB,QAAQ,CAACE,OAAO,CAACjB,GAAG,CAAC,IAAI,CAAC,EAAE;MAChC,IAAI,CAAChB,MAAM,CAAC0B,SAAS,CAACU,oBAAoB,CAACC,IAAI,CAACvB,MAAM,EAAEE,GAAG,CAAC,EAAE;MAC9DN,MAAM,CAACM,GAAG,CAAC,GAAGF,MAAM,CAACE,GAAG,CAAC;IAC3B;EACF;EAEA,OAAON,MAAM;AACf;AAEA,SAAS4B,kBAAkB,CAACC,GAAG,EAAE;EAC/B,OAAOC,kBAAkB,CAACD,GAAG,CAAC,IAAIE,gBAAgB,CAACF,GAAG,CAAC,IAAIG,2BAA2B,CAACH,GAAG,CAAC,IAAII,kBAAkB,EAAE;AACrH;AAEA,SAASH,kBAAkB,CAACD,GAAG,EAAE;EAC/B,IAAIK,KAAK,CAACC,OAAO,CAACN,GAAG,CAAC,EAAE,OAAOO,iBAAiB,CAACP,GAAG,CAAC;AACvD;AAEA,SAASE,gBAAgB,CAACM,IAAI,EAAE;EAC9B,IAAI,OAAOxB,MAAM,KAAK,WAAW,IAAIwB,IAAI,CAACxB,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,IAAIuB,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,OAAOH,KAAK,CAACI,IAAI,CAACD,IAAI,CAAC;AAC3H;AAEA,SAASL,2BAA2B,CAACO,CAAC,EAAEC,MAAM,EAAE;EAC9C,IAAI,CAACD,CAAC,EAAE;EACR,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOH,iBAAiB,CAACG,CAAC,EAAEC,MAAM,CAAC;EAC9D,IAAIC,CAAC,GAAGnD,MAAM,CAAC0B,SAAS,CAAC0B,QAAQ,CAACf,IAAI,CAACY,CAAC,CAAC,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACtD,IAAIF,CAAC,KAAK,QAAQ,IAAIF,CAAC,CAACxB,WAAW,EAAE0B,CAAC,GAAGF,CAAC,CAACxB,WAAW,CAAC6B,IAAI;EAC3D,IAAIH,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOP,KAAK,CAACI,IAAI,CAACC,CAAC,CAAC;EACpD,IAAIE,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACI,IAAI,CAACJ,CAAC,CAAC,EAAE,OAAOL,iBAAiB,CAACG,CAAC,EAAEC,MAAM,CAAC;AAClH;AAEA,SAASJ,iBAAiB,CAACP,GAAG,EAAEiB,GAAG,EAAE;EACnC,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGjB,GAAG,CAAC1B,MAAM,EAAE2C,GAAG,GAAGjB,GAAG,CAAC1B,MAAM;EAErD,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAE8C,IAAI,GAAG,IAAIb,KAAK,CAACY,GAAG,CAAC,EAAE7C,CAAC,GAAG6C,GAAG,EAAE7C,CAAC,EAAE,EAAE8C,IAAI,CAAC9C,CAAC,CAAC,GAAG4B,GAAG,CAAC5B,CAAC,CAAC;EAErE,OAAO8C,IAAI;AACb;AAEA,SAASd,kBAAkB,GAAG;EAC5B,MAAM,IAAIe,SAAS,CAAC,sIAAsI,CAAC;AAC7J;;AAEA;AACA,SAASC,SAAS,CAACC,KAAK,EAAE;EACxB,IAAIC,QAAQ;EAEZ,IAAIC,IAAI,GAAGF,KAAK,CAACE,IAAI;IACjBC,IAAI,GAAGH,KAAK,CAACG,IAAI;IACjBC,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;IACzBC,MAAM,GAAGL,KAAK,CAACK,MAAM;IACrBC,KAAK,GAAGN,KAAK,CAACM,KAAK;IACnBC,KAAK,GAAGP,KAAK,CAACO,KAAK;IACnBC,IAAI,GAAGR,KAAK,CAACQ,IAAI;IACjBC,SAAS,GAAGT,KAAK,CAACS,SAAS;IAC3BC,WAAW,GAAGV,KAAK,CAACU,WAAW;IAC/BC,KAAK,GAAGX,KAAK,CAACW,KAAK;IACnBC,UAAU,GAAGZ,KAAK,CAACY,UAAU;IAC7BC,OAAO,GAAGb,KAAK,CAACa,OAAO;IACvBC,MAAM,GAAGd,KAAK,CAACc,MAAM;IACrBC,QAAQ,GAAGf,KAAK,CAACe,QAAQ;IACzBC,IAAI,GAAGhB,KAAK,CAACgB,IAAI;IACjBC,IAAI,GAAGjB,KAAK,CAACiB,IAAI;IACjBC,QAAQ,GAAGlB,KAAK,CAACkB,QAAQ;IACzBC,IAAI,GAAGnB,KAAK,CAACmB,IAAI,CAAC,CAAC;;EAEvB,IAAIC,OAAO,IAAInB,QAAQ,GAAG;IACxB,SAAS,EAAEC,IAAI;IACf,SAAS,EAAEC,IAAI;IACf,cAAc,EAAEC,QAAQ;IACxB,WAAW,EAAEC,MAAM;IACnB,UAAU,EAAEC,KAAK;IACjB,UAAU,EAAEC,KAAK;IACjB,SAAS,EAAEC,IAAI;IACf,iBAAiB,EAAEE,WAAW;IAC9B,eAAe,EAAED,SAAS;IAC1B,UAAU,EAAEE,KAAK;IACjB,OAAO,EAAEC,UAAU;IACnB,YAAY,EAAEC,OAAO;IACrB,WAAW,EAAEC,MAAM;IACnB,OAAO,EAAEC,QAAQ;IACjB,SAAS,EAAEC,IAAI,KAAK,IAAI;IACxB,oBAAoB,EAAEA,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,MAAM;IAC9D,kBAAkB,EAAEA,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK;EACtD,CAAC,EAAE3D,eAAe,CAAC4C,QAAQ,EAAE,KAAK,CAACoB,MAAM,CAACJ,IAAI,CAAC,EAAE,OAAOA,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,IAAI,CAAC,EAAE5D,eAAe,CAAC4C,QAAQ,EAAE,YAAY,CAACoB,MAAM,CAACH,QAAQ,CAAC,EAAE,OAAOA,QAAQ,KAAK,WAAW,IAAIA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,CAAC,CAAC,EAAE7D,eAAe,CAAC4C,QAAQ,EAAE,UAAU,CAACoB,MAAM,CAACF,IAAI,CAAC,EAAE,OAAOA,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,IAAI,CAAC,EAAE9D,eAAe,CAAC4C,QAAQ,EAAE,iBAAiB,EAAED,KAAK,CAACsB,WAAW,CAAC,EAAErB,QAAQ,CAAC,CAAC,CAAC;EAChZ;;EAEA,OAAO7D,MAAM,CAACD,IAAI,CAACiF,OAAO,CAAC,CAACG,GAAG,CAAC,UAAUnE,GAAG,EAAE;IAC7C,OAAOgE,OAAO,CAAChE,GAAG,CAAC,GAAGA,GAAG,GAAG,IAAI;EAClC,CAAC,CAAC,CAACb,MAAM,CAAC,UAAUa,GAAG,EAAE;IACvB,OAAOA,GAAG;EACZ,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA,SAASoE,YAAY,CAAC9D,GAAG,EAAE;EACzBA,GAAG,GAAGA,GAAG,GAAG,CAAC,CAAC,CAAC;;EAEf,OAAOA,GAAG,KAAKA,GAAG;AACpB;AAEA,SAAS+D,QAAQ,CAACC,MAAM,EAAE;EACxB,IAAIF,YAAY,CAACE,MAAM,CAAC,EAAE;IACxB,OAAOA,MAAM;EACf,CAAC,CAAC;;EAGFA,MAAM,GAAGA,MAAM,CAACC,OAAO,CAAC,eAAe,EAAE,UAAUC,KAAK,EAAEC,GAAG,EAAE;IAC7D,OAAOA,GAAG,GAAGA,GAAG,CAACC,WAAW,EAAE,GAAG,EAAE;EACrC,CAAC,CAAC,CAAC,CAAC;;EAEJ,OAAOJ,MAAM,CAACK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGN,MAAM,CAACK,MAAM,CAAC,CAAC,CAAC;AAC7D;AAEA,IAAIE,SAAS,GAAG,CAAC,OAAO,CAAC;AAEzB,SAASC,UAAU,CAACC,GAAG,EAAE;EACvB,OAAOA,GAAG,CAACC,MAAM,CAAC,CAAC,CAAC,CAACN,WAAW,EAAE,GAAGK,GAAG,CAAC1C,KAAK,CAAC,CAAC,CAAC;AACnD;AAEA,SAAS4C,aAAa,CAACC,KAAK,EAAE;EAC5B,OAAOA,KAAK,CAACC,KAAK,CAAC,GAAG,CAAC,CAAChB,GAAG,CAAC,UAAUiB,CAAC,EAAE;IACvC,OAAOA,CAAC,CAACC,IAAI,EAAE;EACjB,CAAC,CAAC,CAAClG,MAAM,CAAC,UAAUiG,CAAC,EAAE;IACrB,OAAOA,CAAC;EACV,CAAC,CAAC,CAACE,MAAM,CAAC,UAAUC,GAAG,EAAEC,IAAI,EAAE;IAC7B,IAAI7F,CAAC,GAAG6F,IAAI,CAACvE,OAAO,CAAC,GAAG,CAAC;IACzB,IAAIwE,IAAI,GAAGpB,QAAQ,CAACmB,IAAI,CAACnD,KAAK,CAAC,CAAC,EAAE1C,CAAC,CAAC,CAAC;IACrC,IAAIgB,KAAK,GAAG6E,IAAI,CAACnD,KAAK,CAAC1C,CAAC,GAAG,CAAC,CAAC,CAAC0F,IAAI,EAAE;IACpCI,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,GAAGH,GAAG,CAACT,UAAU,CAACW,IAAI,CAAC,CAAC,GAAG9E,KAAK,GAAG4E,GAAG,CAACE,IAAI,CAAC,GAAG9E,KAAK;IAC7E,OAAO4E,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;AAEA,SAASI,OAAO,CAACC,aAAa,EAAEC,OAAO,EAAE;EACvC,IAAIC,UAAU,GAAGlG,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKmG,SAAS,GAAGnG,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAEvF,IAAI,OAAOiG,OAAO,KAAK,QAAQ,EAAE;IAC/B,OAAOA,OAAO;EAChB;EAEA,IAAIG,QAAQ,GAAG,CAACH,OAAO,CAACG,QAAQ,IAAI,EAAE,EAAE7B,GAAG,CAAC,UAAU8B,KAAK,EAAE;IAC3D,OAAON,OAAO,CAACC,aAAa,EAAEK,KAAK,CAAC;EACtC,CAAC,CAAC;EACF;;EAEA,IAAIC,MAAM,GAAGlH,MAAM,CAACD,IAAI,CAAC8G,OAAO,CAACM,UAAU,IAAI,CAAC,CAAC,CAAC,CAACb,MAAM,CAAC,UAAUC,GAAG,EAAEvF,GAAG,EAAE;IAC5E,IAAI+E,GAAG,GAAGc,OAAO,CAACM,UAAU,CAACnG,GAAG,CAAC;IAEjC,QAAQA,GAAG;MACT,KAAK,OAAO;QACVuF,GAAG,CAACa,KAAK,CAAC,WAAW,CAAC,GAAGrB,GAAG;QAC5B,OAAOc,OAAO,CAACM,UAAU,CAAC,OAAO,CAAC;QAClC;MAEF,KAAK,OAAO;QACVZ,GAAG,CAACa,KAAK,CAAC,OAAO,CAAC,GAAGnB,aAAa,CAACF,GAAG,CAAC;QACvC;MAEF;QACE,IAAI/E,GAAG,CAACiB,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,IAAIjB,GAAG,CAACiB,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;UAC5DsE,GAAG,CAACa,KAAK,CAACpG,GAAG,CAAC4E,WAAW,EAAE,CAAC,GAAGG,GAAG;QACpC,CAAC,MAAM;UACLQ,GAAG,CAACa,KAAK,CAAC/B,QAAQ,CAACrE,GAAG,CAAC,CAAC,GAAG+E,GAAG;QAChC;IAAC;IAIL,OAAOQ,GAAG;EACZ,CAAC,EAAE;IACDa,KAAK,EAAE,CAAC;EACV,CAAC,CAAC;EAEF,IAAIC,iBAAiB,GAAGP,UAAU,CAACZ,KAAK;IACpCoB,aAAa,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,iBAAiB;IACrEE,SAAS,GAAGrF,wBAAwB,CAAC4E,UAAU,EAAEjB,SAAS,CAAC;EAE/DqB,MAAM,CAACE,KAAK,CAAC,OAAO,CAAC,GAAG3G,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEyG,MAAM,CAACE,KAAK,CAAC,OAAO,CAAC,CAAC,EAAEE,aAAa,CAAC;EAChG;;EAEA,OAAOV,aAAa,CAACpG,KAAK,CAAC,KAAK,CAAC,EAAE,CAACqG,OAAO,CAACW,GAAG,EAAE/G,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEyG,MAAM,CAACE,KAAK,CAAC,EAAEG,SAAS,CAAC,CAAC,CAACtC,MAAM,CAAC3C,kBAAkB,CAAC0E,QAAQ,CAAC,CAAC,CAAC;AACrJ;AAEA,IAAIS,UAAU,GAAG,KAAK;AAEtB,IAAI;EACFA,UAAU,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY;AACpD,CAAC,CAAC,OAAOC,CAAC,EAAE,CAAC;AAEb,SAASC,GAAG,GAAI;EACd,IAAI,CAACL,UAAU,IAAIM,OAAO,IAAI,OAAOA,OAAO,CAACC,KAAK,KAAK,UAAU,EAAE;IACjE,IAAIC,QAAQ;IAEZ,CAACA,QAAQ,GAAGF,OAAO,EAAEC,KAAK,CAACxH,KAAK,CAACyH,QAAQ,EAAErH,SAAS,CAAC;EACvD;AACF;AAEA,SAASsH,iBAAiB,CAACzI,IAAI,EAAE;EAC/B;EACA;EACA,IAAIA,IAAI,IAAI4B,OAAO,CAAC5B,IAAI,CAAC,KAAK,QAAQ,IAAIA,IAAI,CAAC0I,MAAM,IAAI1I,IAAI,CAAC2I,QAAQ,IAAI3I,IAAI,CAACA,IAAI,EAAE;IACnF,OAAOA,IAAI;EACb;EAEA,IAAID,KAAK,CAACC,IAAI,EAAE;IACd,OAAOD,KAAK,CAACC,IAAI,CAACA,IAAI,CAAC;EACzB,CAAC,CAAC;;EAGF,IAAIA,IAAI,KAAK,IAAI,EAAE;IACjB,OAAO,IAAI;EACb,CAAC,CAAC;;EAGF,IAAIA,IAAI,IAAI4B,OAAO,CAAC5B,IAAI,CAAC,KAAK,QAAQ,IAAIA,IAAI,CAAC0I,MAAM,IAAI1I,IAAI,CAAC2I,QAAQ,EAAE;IACtE,OAAO3I,IAAI;EACb,CAAC,CAAC;;EAGF,IAAImD,KAAK,CAACC,OAAO,CAACpD,IAAI,CAAC,IAAIA,IAAI,CAACoB,MAAM,KAAK,CAAC,EAAE;IAC5C;IACA,OAAO;MACLsH,MAAM,EAAE1I,IAAI,CAAC,CAAC,CAAC;MACf2I,QAAQ,EAAE3I,IAAI,CAAC,CAAC;IAClB,CAAC;EACH,CAAC,CAAC;;EAGF,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5B,OAAO;MACL0I,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAE3I;IACZ,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA,SAAS4I,aAAa,CAACrH,GAAG,EAAEW,KAAK,EAAE;EACjC;EACA;EACA;EACA;EACA,OAAOiB,KAAK,CAACC,OAAO,CAAClB,KAAK,CAAC,IAAIA,KAAK,CAACd,MAAM,GAAG,CAAC,IAAI,CAAC+B,KAAK,CAACC,OAAO,CAAClB,KAAK,CAAC,IAAIA,KAAK,GAAGV,eAAe,CAAC,CAAC,CAAC,EAAED,GAAG,EAAEW,KAAK,CAAC,GAAG,CAAC,CAAC;AAC1H;AAEA,IAAI2G,YAAY,GAAG;EACjB5D,MAAM,EAAE,KAAK;EACb6D,SAAS,EAAE,EAAE;EACbC,IAAI,EAAE,IAAI;EACVC,MAAM,EAAE,IAAI;EACZjE,UAAU,EAAE,KAAK;EACjBC,OAAO,EAAE,KAAK;EACdG,IAAI,EAAE,KAAK;EACXnF,IAAI,EAAE,IAAI;EACVkF,QAAQ,EAAE,KAAK;EACfI,IAAI,EAAE,IAAI;EACVR,KAAK,EAAE,KAAK;EACZO,QAAQ,EAAE,IAAI;EACdD,IAAI,EAAE,IAAI;EACVT,IAAI,EAAE,KAAK;EACXC,SAAS,EAAE,KAAK;EAChBC,WAAW,EAAE,KAAK;EAClBR,IAAI,EAAE,KAAK;EACXC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,KAAK;EACfC,MAAM,EAAE,KAAK;EACbC,KAAK,EAAE,KAAK;EACZwE,MAAM,EAAE,KAAK;EACbC,KAAK,EAAE,EAAE;EACTC,OAAO,EAAE,IAAI;EACbC,SAAS,EAAE,IAAI;EACf3D,WAAW,EAAE;AACf,CAAC;AACD,IAAI4D,eAAe,GAAG,aAAanJ,KAAK,CAACoJ,UAAU,CAAC,UAAUnF,KAAK,EAAEoF,GAAG,EAAE;EACxE,IAAIC,QAAQ,GAAGxI,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE6H,YAAY,CAAC,EAAE1E,KAAK,CAAC;EAEtE,IAAIsF,QAAQ,GAAGD,QAAQ,CAACxJ,IAAI;IACxB0J,QAAQ,GAAGF,QAAQ,CAACT,IAAI;IACxBE,MAAM,GAAGO,QAAQ,CAACP,MAAM;IACxBH,SAAS,GAAGU,QAAQ,CAACV,SAAS;IAC9BI,KAAK,GAAGM,QAAQ,CAACN,KAAK;IACtBC,OAAO,GAAGK,QAAQ,CAACL,OAAO;IAC1BH,MAAM,GAAGQ,QAAQ,CAACR,MAAM;EAC5B,IAAIW,UAAU,GAAGlB,iBAAiB,CAACgB,QAAQ,CAAC;EAC5C,IAAIlE,OAAO,GAAGqD,aAAa,CAAC,SAAS,EAAE,EAAE,CAACpD,MAAM,CAAC3C,kBAAkB,CAACqB,SAAS,CAACsF,QAAQ,CAAC,CAAC,EAAE3G,kBAAkB,CAAC,CAACiG,SAAS,IAAI,EAAE,EAAEpC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC5I,IAAI0C,SAAS,GAAGR,aAAa,CAAC,WAAW,EAAE,OAAOY,QAAQ,CAACJ,SAAS,KAAK,QAAQ,GAAGrJ,KAAK,CAACqJ,SAAS,CAACI,QAAQ,CAACJ,SAAS,CAAC,GAAGI,QAAQ,CAACJ,SAAS,CAAC;EAC7I,IAAIL,IAAI,GAAGH,aAAa,CAAC,MAAM,EAAEH,iBAAiB,CAACiB,QAAQ,CAAC,CAAC;EAC7D,IAAIE,YAAY,GAAG5J,IAAI,CAAC2J,UAAU,EAAE3I,cAAc,CAACA,cAAc,CAACA,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEuE,OAAO,CAAC,EAAE6D,SAAS,CAAC,EAAEL,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;IACnIE,MAAM,EAAEA,MAAM;IACdC,KAAK,EAAEA,KAAK;IACZC,OAAO,EAAEA,OAAO;IAChBH,MAAM,EAAEA;EACV,CAAC,CAAC,CAAC;EAEH,IAAI,CAACY,YAAY,EAAE;IACjBvB,GAAG,CAAC,qBAAqB,EAAEsB,UAAU,CAAC;IACtC,OAAO,IAAI;EACb;EAEA,IAAIE,QAAQ,GAAGD,YAAY,CAACC,QAAQ;EACpC,IAAIxC,UAAU,GAAG;IACfkC,GAAG,EAAEA;EACP,CAAC;EACDhJ,MAAM,CAACD,IAAI,CAACkJ,QAAQ,CAAC,CAAClI,OAAO,CAAC,UAAUC,GAAG,EAAE;IAC3C;IACA,IAAI,CAACsH,YAAY,CAACiB,cAAc,CAACvI,GAAG,CAAC,EAAE;MACrC8F,UAAU,CAAC9F,GAAG,CAAC,GAAGiI,QAAQ,CAACjI,GAAG,CAAC;IACjC;EACF,CAAC,CAAC;EACF,OAAOwI,YAAY,CAACF,QAAQ,CAAC,CAAC,CAAC,EAAExC,UAAU,CAAC;AAC9C,CAAC,CAAC;AACFgC,eAAe,CAACW,WAAW,GAAG,iBAAiB;AAC/CX,eAAe,CAACY,SAAS,GAAG;EAC1B5F,IAAI,EAAEpE,SAAS,CAACiK,IAAI;EACpBjF,MAAM,EAAEhF,SAAS,CAACiK,IAAI;EACtB3F,QAAQ,EAAEtE,SAAS,CAACiK,IAAI;EACxB1F,MAAM,EAAEvE,SAAS,CAACiK,IAAI;EACtBpB,SAAS,EAAE7I,SAAS,CAAC4F,MAAM;EAC3BvB,IAAI,EAAErE,SAAS,CAACiK,IAAI;EACpBxF,KAAK,EAAEzE,SAAS,CAACiK,IAAI;EACrBnB,IAAI,EAAE9I,SAAS,CAACkK,SAAS,CAAC,CAAClK,SAAS,CAACG,MAAM,EAAEH,SAAS,CAACmK,KAAK,EAAEnK,SAAS,CAAC4F,MAAM,CAAC,CAAC;EAChFmD,MAAM,EAAE/I,SAAS,CAAC4F,MAAM;EACxBd,UAAU,EAAE9E,SAAS,CAACiK,IAAI;EAC1BlF,OAAO,EAAE/E,SAAS,CAACiK,IAAI;EACvB/E,IAAI,EAAElF,SAAS,CAACoK,KAAK,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;EACtErK,IAAI,EAAEC,SAAS,CAACkK,SAAS,CAAC,CAAClK,SAAS,CAACG,MAAM,EAAEH,SAAS,CAACmK,KAAK,EAAEnK,SAAS,CAAC4F,MAAM,CAAC,CAAC;EAChFX,QAAQ,EAAEjF,SAAS,CAACiK,IAAI;EACxB5E,IAAI,EAAErF,SAAS,CAACoK,KAAK,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;EACxCvF,KAAK,EAAE7E,SAAS,CAACiK,IAAI;EACrB7E,QAAQ,EAAEpF,SAAS,CAACoK,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;EAC5C5F,KAAK,EAAExE,SAAS,CAACiK,IAAI;EACrB9E,IAAI,EAAEnF,SAAS,CAACoK,KAAK,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;EAC1H1F,IAAI,EAAE1E,SAAS,CAACiK,IAAI;EACpBtF,SAAS,EAAE3E,SAAS,CAACiK,IAAI;EACzBrF,WAAW,EAAE5E,SAAS,CAACiK,IAAI;EAC3BjB,MAAM,EAAEhJ,SAAS,CAACkK,SAAS,CAAC,CAAClK,SAAS,CAACiK,IAAI,EAAEjK,SAAS,CAAC4F,MAAM,CAAC,CAAC;EAC/DqD,KAAK,EAAEjJ,SAAS,CAAC4F,MAAM;EACvBsD,OAAO,EAAElJ,SAAS,CAAC4F,MAAM;EACzBuD,SAAS,EAAEnJ,SAAS,CAACkK,SAAS,CAAC,CAAClK,SAAS,CAAC4F,MAAM,EAAE5F,SAAS,CAACG,MAAM,CAAC,CAAC;EACpEqF,WAAW,EAAExF,SAAS,CAACiK;AACzB,CAAC;AACD,IAAIH,YAAY,GAAG7C,OAAO,CAACoD,IAAI,CAAC,IAAI,EAAEpK,KAAK,CAACiH,aAAa,CAAC;AAE1D,SAASkC,eAAe"}, "metadata": {}, "sourceType": "module"}