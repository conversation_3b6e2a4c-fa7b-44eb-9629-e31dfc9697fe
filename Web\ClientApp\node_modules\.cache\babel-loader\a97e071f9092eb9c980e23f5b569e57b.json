{"ast": null, "code": "import * as models from './models/driver-tasks';\nimport { api } from './api-base';\nimport { ServiceBase } from './service-base';\nimport { getBasicAuth } from './auth-service';\nimport { configuration } from 'features/auth/configuration';\nimport { routes } from 'app/routes';\nclass DriverTasksService extends ServiceBase {\n  driverTasks() {\n    return this.query('filters/driver-tasks');\n  }\n  getOneDriverTask(id) {\n    return this.getOneDocument(id);\n  }\n  async getDrivers(user) {\n    const url = `${configuration.remote_server}/_users/_design/user-filters/_view/boekestyn-drivers?include_docs=true`,\n      authorization = getBasicAuth(user.name, user.password),\n      headers = {\n        Authorization: authorization\n      },\n      response = await api.get(url, {\n        headers\n      }),\n      drivers = response.rows.flatMap(row => row.doc ? [row.doc] : []).map(row => ({\n        name: row.name,\n        email: row.email || null,\n        phone: row.phone || null\n      }));\n    return drivers;\n  }\n  async createDriverTask(user, dueDate, assignedTo, priority, notes, status, fromLocation, toLocation) {\n    let sendTextNotification = arguments.length > 8 && arguments[8] !== undefined ? arguments[8] : false;\n    const driverTask = {\n      ...models.createDriverTask(user),\n      dueDate,\n      assignedTo,\n      priority,\n      notes,\n      status,\n      fromLocation,\n      toLocation\n    };\n    await this.saveDocument(driverTask);\n    if (assignedTo !== null && assignedTo !== void 0 && assignedTo.phone && sendTextNotification) {\n      const link = `${window.location.origin}${routes.driverTasks.list.path}`,\n        message = `${user.name} has assigned you a task:\\n\\n${notes}`,\n        phoneNumber = assignedTo.phone;\n      this.sendTextNotification({\n        user,\n        phoneNumber,\n        link,\n        message\n      });\n    }\n  }\n  updateDriverTask(driverTask) {\n    return this.saveDocument(driverTask);\n  }\n  sendTextNotification(_ref) {\n    let {\n      user,\n      phoneNumber,\n      link,\n      message\n    } = _ref;\n    const authorization = getBasicAuth(user.name, user.password),\n      headers = {\n        Authorization: authorization\n      },\n      model = {\n        phoneNumber,\n        link,\n        message\n      };\n    api.post('/notifications/driver-task', model, {\n      headers\n    });\n  }\n}\nexport const driverTasksApi = new DriverTasksService();", "map": {"version": 3, "names": ["models", "api", "ServiceBase", "getBasicAuth", "configuration", "routes", "DriverTasksService", "driverTasks", "query", "getOneDriverTask", "id", "getOneDocument", "getDrivers", "user", "url", "remote_server", "authorization", "name", "password", "headers", "Authorization", "response", "get", "drivers", "rows", "flatMap", "row", "doc", "map", "email", "phone", "createDriverTask", "dueDate", "assignedTo", "priority", "notes", "status", "fromLocation", "toLocation", "sendTextNotification", "driverTask", "saveDocument", "link", "window", "location", "origin", "list", "path", "message", "phoneNumber", "updateDriverTask", "model", "post", "driverTasksApi"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/api/driver-tasks-service.ts"], "sourcesContent": ["import { UserInfo, CouchUserDoc } from './models/auth';\r\nimport * as models from './models/driver-tasks';\r\nimport { api } from './api-base';\r\nimport { ServiceBase } from './service-base';\r\nimport { getBasicAuth } from './auth-service';\r\nimport { configuration } from 'features/auth/configuration';\r\nimport { routes } from 'app/routes';\r\n\r\nclass DriverTasksService extends ServiceBase {\r\n  driverTasks() {\r\n    return this.query<models.DriverTask>('filters/driver-tasks');\r\n  }\r\n\r\n  getOneDriverTask(id: string): Promise<models.DriverTask> {\r\n    return this.getOneDocument(id);\r\n  }\r\n\r\n  async getDrivers(user: UserInfo): Promise<models.Driver[]> {\r\n    const url = `${configuration.remote_server}/_users/_design/user-filters/_view/boekestyn-drivers?include_docs=true`,\r\n      authorization = getBasicAuth(user.name, user.password),\r\n      headers = { Authorization: authorization },\r\n      response = await api.get<PouchDB.Query.Response<CouchUserDoc>>(url, {\r\n        headers,\r\n      }),\r\n      drivers = response.rows\r\n        .flatMap((row) => (row.doc ? [row.doc] : []))\r\n        .map((row) => ({\r\n          name: row.name,\r\n          email: row.email || null,\r\n          phone: row.phone || null,\r\n        }));\r\n\r\n    return drivers;\r\n  }\r\n\r\n  async createDriverTask(\r\n    user: UserInfo,\r\n    dueDate: string,\r\n    assignedTo: models.Driver | null,\r\n    priority: models.Priority,\r\n    notes: string,\r\n    status: models.Status,\r\n    fromLocation: string,\r\n    toLocation: string,\r\n    sendTextNotification: boolean = false\r\n  ) {\r\n    const driverTask = {\r\n      ...models.createDriverTask(user),\r\n      dueDate,\r\n      assignedTo,\r\n      priority,\r\n      notes,\r\n      status,\r\n      fromLocation,\r\n      toLocation,\r\n    };\r\n\r\n    await this.saveDocument(driverTask);\r\n\r\n    if (assignedTo?.phone && sendTextNotification) {\r\n      const link = `${window.location.origin}${routes.driverTasks.list.path}`,\r\n        message = `${user.name} has assigned you a task:\\n\\n${notes}`,\r\n        phoneNumber = assignedTo.phone;\r\n\r\n      this.sendTextNotification({ user, phoneNumber, link, message });\r\n    }\r\n  }\r\n\r\n  updateDriverTask(driverTask: models.DriverTask) {\r\n    return this.saveDocument(driverTask);\r\n  }\r\n\r\n  sendTextNotification({\r\n    user,\r\n    phoneNumber,\r\n    link,\r\n    message,\r\n  }: TextNotificationArgs) {\r\n    const authorization = getBasicAuth(user.name, user.password),\r\n      headers = { Authorization: authorization },\r\n      model = { phoneNumber, link, message };\r\n\r\n    api.post('/notifications/driver-task', model, { headers });\r\n  }\r\n}\r\n\r\nexport interface TextNotificationArgs {\r\n  user: UserInfo;\r\n  phoneNumber: string;\r\n  link: string;\r\n  message: string;\r\n}\r\n\r\nexport const driverTasksApi = new DriverTasksService();\r\n"], "mappings": "AACA,OAAO,KAAKA,MAAM,MAAM,uBAAuB;AAC/C,SAASC,GAAG,QAAQ,YAAY;AAChC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,MAAM,QAAQ,YAAY;AAEnC,MAAMC,kBAAkB,SAASJ,WAAW,CAAC;EAC3CK,WAAW,GAAG;IACZ,OAAO,IAAI,CAACC,KAAK,CAAoB,sBAAsB,CAAC;EAC9D;EAEAC,gBAAgB,CAACC,EAAU,EAA8B;IACvD,OAAO,IAAI,CAACC,cAAc,CAACD,EAAE,CAAC;EAChC;EAEA,MAAME,UAAU,CAACC,IAAc,EAA4B;IACzD,MAAMC,GAAG,GAAI,GAAEV,aAAa,CAACW,aAAc,wEAAuE;MAChHC,aAAa,GAAGb,YAAY,CAACU,IAAI,CAACI,IAAI,EAAEJ,IAAI,CAACK,QAAQ,CAAC;MACtDC,OAAO,GAAG;QAAEC,aAAa,EAAEJ;MAAc,CAAC;MAC1CK,QAAQ,GAAG,MAAMpB,GAAG,CAACqB,GAAG,CAAuCR,GAAG,EAAE;QAClEK;MACF,CAAC,CAAC;MACFI,OAAO,GAAGF,QAAQ,CAACG,IAAI,CACpBC,OAAO,CAAEC,GAAG,IAAMA,GAAG,CAACC,GAAG,GAAG,CAACD,GAAG,CAACC,GAAG,CAAC,GAAG,EAAG,CAAC,CAC5CC,GAAG,CAAEF,GAAG,KAAM;QACbT,IAAI,EAAES,GAAG,CAACT,IAAI;QACdY,KAAK,EAAEH,GAAG,CAACG,KAAK,IAAI,IAAI;QACxBC,KAAK,EAAEJ,GAAG,CAACI,KAAK,IAAI;MACtB,CAAC,CAAC,CAAC;IAEP,OAAOP,OAAO;EAChB;EAEA,MAAMQ,gBAAgB,CACpBlB,IAAc,EACdmB,OAAe,EACfC,UAAgC,EAChCC,QAAyB,EACzBC,KAAa,EACbC,MAAqB,EACrBC,YAAoB,EACpBC,UAAkB,EAElB;IAAA,IADAC,oBAA6B,uEAAG,KAAK;IAErC,MAAMC,UAAU,GAAG;MACjB,GAAGxC,MAAM,CAAC+B,gBAAgB,CAAClB,IAAI,CAAC;MAChCmB,OAAO;MACPC,UAAU;MACVC,QAAQ;MACRC,KAAK;MACLC,MAAM;MACNC,YAAY;MACZC;IACF,CAAC;IAED,MAAM,IAAI,CAACG,YAAY,CAACD,UAAU,CAAC;IAEnC,IAAIP,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEH,KAAK,IAAIS,oBAAoB,EAAE;MAC7C,MAAMG,IAAI,GAAI,GAAEC,MAAM,CAACC,QAAQ,CAACC,MAAO,GAAExC,MAAM,CAACE,WAAW,CAACuC,IAAI,CAACC,IAAK,EAAC;QACrEC,OAAO,GAAI,GAAEnC,IAAI,CAACI,IAAK,gCAA+BkB,KAAM,EAAC;QAC7Dc,WAAW,GAAGhB,UAAU,CAACH,KAAK;MAEhC,IAAI,CAACS,oBAAoB,CAAC;QAAE1B,IAAI;QAAEoC,WAAW;QAAEP,IAAI;QAAEM;MAAQ,CAAC,CAAC;IACjE;EACF;EAEAE,gBAAgB,CAACV,UAA6B,EAAE;IAC9C,OAAO,IAAI,CAACC,YAAY,CAACD,UAAU,CAAC;EACtC;EAEAD,oBAAoB,OAKK;IAAA,IALJ;MACnB1B,IAAI;MACJoC,WAAW;MACXP,IAAI;MACJM;IACoB,CAAC;IACrB,MAAMhC,aAAa,GAAGb,YAAY,CAACU,IAAI,CAACI,IAAI,EAAEJ,IAAI,CAACK,QAAQ,CAAC;MAC1DC,OAAO,GAAG;QAAEC,aAAa,EAAEJ;MAAc,CAAC;MAC1CmC,KAAK,GAAG;QAAEF,WAAW;QAAEP,IAAI;QAAEM;MAAQ,CAAC;IAExC/C,GAAG,CAACmD,IAAI,CAAC,4BAA4B,EAAED,KAAK,EAAE;MAAEhC;IAAQ,CAAC,CAAC;EAC5D;AACF;AASA,OAAO,MAAMkC,cAAc,GAAG,IAAI/C,kBAAkB,EAAE"}, "metadata": {}, "sourceType": "module"}