{"ast": null, "code": "import moment from 'moment';\nimport { guid } from 'utils/guid';\nimport { createPlant } from './plants';\nimport { weeklyCustomer } from './customers';\nimport { createZone } from './zones';\nexport const OrderType = 'order';\nexport function createOrder(baseOrder) {\n  const order = {\n    _id: guid(),\n    type: OrderType,\n    orderNumber: '',\n    supplierPoNumber: '',\n    stickDate: moment().format('YYYY-MM-DD'),\n    hasPartialSpace: false,\n    hasSpacing: true,\n    fullSpaceDate: moment().add(1, 'week').format('YYYY-MM-DD'),\n    hasLightsOut: true,\n    lightsOutDate: moment().add(1, 'week').format('YYYY-MM-DD'),\n    hasPinching: false,\n    flowerDate: moment().add(1, 'week').format('YYYY-MM-DD'),\n    cuttings: 0,\n    pots: 0,\n    cases: 0,\n    customer: weeklyCustomer(),\n    plant: createPlant(),\n    stickZone: createZone(),\n    fullSpaceZone: createZone(),\n    tableCountTight: 1,\n    tableCountSpaced: 1,\n    notes: null,\n    season: null\n  };\n  if (baseOrder) {\n    Object.assign(order, baseOrder);\n  }\n  if (!order.orderNumber) {\n    order.orderNumber = createOrderNumber(order.plant.abbreviation, order.customer.abbreviation, order.stickDate);\n  }\n  return order;\n}\nexport function createOrderNumber(plantAbbreviation, customerAbbreviation, stickDate) {\n  const abbreviation = plantAbbreviation || '',\n    customer = customerAbbreviation || '',\n    date = moment(stickDate),\n    week = date.isoWeek(),\n    year = date.isoWeekYear(),\n    day = date.isoWeekday(),\n    orderNumber = `${abbreviation}${customer}${year}-${week}-${day}`;\n  return orderNumber;\n}", "map": {"version": 3, "names": ["moment", "guid", "createPlant", "weeklyCustomer", "createZone", "OrderType", "createOrder", "baseOrder", "order", "_id", "type", "orderNumber", "supplierPoNumber", "stickDate", "format", "hasPartialSpace", "hasSpacing", "fullSpaceDate", "add", "hasLightsOut", "lightsOutDate", "hasPinching", "flowerDate", "cuttings", "pots", "cases", "customer", "plant", "stickZone", "fullSpaceZone", "tableCountTight", "tableCountSpaced", "notes", "season", "Object", "assign", "createOrderNumber", "abbreviation", "plantAbbreviation", "customerAbbreviation", "date", "week", "isoWeek", "year", "isoWeekYear", "day", "isoWeekday"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/api/models/orders.ts"], "sourcesContent": ["import moment from 'moment';\r\nimport { guid } from 'utils/guid';\r\nimport { createPlant, Plant } from './plants';\r\nimport { Customer, weeklyCustomer } from './customers';\r\nimport { createZone, Zone } from './zones';\r\nimport { ModelBase } from './model-base';\r\n\r\nexport const OrderType = 'order';\r\n\r\nexport interface Order extends ModelBase {\r\n  type: string;\r\n  orderNumber: string;\r\n  customer: Customer;\r\n  plant: Plant;\r\n  cuttings: number;\r\n  pots: number;\r\n  cases: number;\r\n  supplierPoNumber: string;\r\n  stickDate: string;\r\n  stickZone: Zone;\r\n  hasPartialSpace: boolean;\r\n  partialSpaceDate?: string;\r\n  partialSpaceZone?: Zone;\r\n  hasSpacing: boolean;\r\n  fullSpaceDate?: string;\r\n  fullSpaceZone?: Zone;\r\n  hasLightsOut: boolean;\r\n  lightsOutDate?: string;\r\n  lightsOutZone?: Zone;\r\n  hasPinching: boolean;\r\n  pinchDate?: string;\r\n  flowerDate: string;\r\n  tableCountTight: number;\r\n  tableCountPartiallySpaced?: number;\r\n  tableCountSpaced?: number;\r\n  notes: string | null;\r\n  season: string | null;\r\n  varieties?: OrderVariety[];\r\n  salesWeeks?: SalesWeek[];\r\n}\r\n\r\nexport interface OrderVariety {\r\n  name: string;\r\n  cuttings: number;\r\n  pots: number;\r\n  cases: number;\r\n  comment: string | null;\r\n}\r\n\r\nexport interface SalesWeek {\r\n  id: string;\r\n  week: string;\r\n  cases: number;\r\n}\r\n\r\nexport function createOrder(baseOrder?: Order) {\r\n  const order: Order = {\r\n    _id: guid(),\r\n    type: OrderType,\r\n    orderNumber: '',\r\n    supplierPoNumber: '',\r\n    stickDate: moment().format('YYYY-MM-DD'),\r\n    hasPartialSpace: false,\r\n    hasSpacing: true,\r\n    fullSpaceDate: moment().add(1, 'week').format('YYYY-MM-DD'),\r\n    hasLightsOut: true,\r\n    lightsOutDate: moment().add(1, 'week').format('YYYY-MM-DD'),\r\n    hasPinching: false,\r\n    flowerDate: moment().add(1, 'week').format('YYYY-MM-DD'),\r\n    cuttings: 0,\r\n    pots: 0,\r\n    cases: 0,\r\n    customer: weeklyCustomer(),\r\n    plant: createPlant(),\r\n    stickZone: createZone(),\r\n    fullSpaceZone: createZone(),\r\n    tableCountTight: 1,\r\n    tableCountSpaced: 1,\r\n    notes: null,\r\n    season: null,\r\n  };\r\n\r\n  if (baseOrder) {\r\n    Object.assign(order, baseOrder);\r\n  }\r\n\r\n  if (!order.orderNumber) {\r\n    order.orderNumber = createOrderNumber(\r\n      order.plant.abbreviation,\r\n      order.customer.abbreviation,\r\n      order.stickDate\r\n    );\r\n  }\r\n\r\n  return order;\r\n}\r\n\r\nexport function createOrderNumber(\r\n  plantAbbreviation: string,\r\n  customerAbbreviation: string,\r\n  stickDate: string\r\n) {\r\n  const abbreviation = plantAbbreviation || '',\r\n    customer = customerAbbreviation || '',\r\n    date = moment(stickDate),\r\n    week = date.isoWeek(),\r\n    year = date.isoWeekYear(),\r\n    day = date.isoWeekday(),\r\n    orderNumber = `${abbreviation}${customer}${year}-${week}-${day}`;\r\n\r\n  return orderNumber;\r\n}\r\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,QAAQ;AAC3B,SAASC,IAAI,QAAQ,YAAY;AACjC,SAASC,WAAW,QAAe,UAAU;AAC7C,SAAmBC,cAAc,QAAQ,aAAa;AACtD,SAASC,UAAU,QAAc,SAAS;AAG1C,OAAO,MAAMC,SAAS,GAAG,OAAO;AAgDhC,OAAO,SAASC,WAAW,CAACC,SAAiB,EAAE;EAC7C,MAAMC,KAAY,GAAG;IACnBC,GAAG,EAAER,IAAI,EAAE;IACXS,IAAI,EAAEL,SAAS;IACfM,WAAW,EAAE,EAAE;IACfC,gBAAgB,EAAE,EAAE;IACpBC,SAAS,EAAEb,MAAM,EAAE,CAACc,MAAM,CAAC,YAAY,CAAC;IACxCC,eAAe,EAAE,KAAK;IACtBC,UAAU,EAAE,IAAI;IAChBC,aAAa,EAAEjB,MAAM,EAAE,CAACkB,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAACJ,MAAM,CAAC,YAAY,CAAC;IAC3DK,YAAY,EAAE,IAAI;IAClBC,aAAa,EAAEpB,MAAM,EAAE,CAACkB,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAACJ,MAAM,CAAC,YAAY,CAAC;IAC3DO,WAAW,EAAE,KAAK;IAClBC,UAAU,EAAEtB,MAAM,EAAE,CAACkB,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAACJ,MAAM,CAAC,YAAY,CAAC;IACxDS,QAAQ,EAAE,CAAC;IACXC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAEvB,cAAc,EAAE;IAC1BwB,KAAK,EAAEzB,WAAW,EAAE;IACpB0B,SAAS,EAAExB,UAAU,EAAE;IACvByB,aAAa,EAAEzB,UAAU,EAAE;IAC3B0B,eAAe,EAAE,CAAC;IAClBC,gBAAgB,EAAE,CAAC;IACnBC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE;EACV,CAAC;EAED,IAAI1B,SAAS,EAAE;IACb2B,MAAM,CAACC,MAAM,CAAC3B,KAAK,EAAED,SAAS,CAAC;EACjC;EAEA,IAAI,CAACC,KAAK,CAACG,WAAW,EAAE;IACtBH,KAAK,CAACG,WAAW,GAAGyB,iBAAiB,CACnC5B,KAAK,CAACmB,KAAK,CAACU,YAAY,EACxB7B,KAAK,CAACkB,QAAQ,CAACW,YAAY,EAC3B7B,KAAK,CAACK,SAAS,CAChB;EACH;EAEA,OAAOL,KAAK;AACd;AAEA,OAAO,SAAS4B,iBAAiB,CAC/BE,iBAAyB,EACzBC,oBAA4B,EAC5B1B,SAAiB,EACjB;EACA,MAAMwB,YAAY,GAAGC,iBAAiB,IAAI,EAAE;IAC1CZ,QAAQ,GAAGa,oBAAoB,IAAI,EAAE;IACrCC,IAAI,GAAGxC,MAAM,CAACa,SAAS,CAAC;IACxB4B,IAAI,GAAGD,IAAI,CAACE,OAAO,EAAE;IACrBC,IAAI,GAAGH,IAAI,CAACI,WAAW,EAAE;IACzBC,GAAG,GAAGL,IAAI,CAACM,UAAU,EAAE;IACvBnC,WAAW,GAAI,GAAE0B,YAAa,GAAEX,QAAS,GAAEiB,IAAK,IAAGF,IAAK,IAAGI,GAAI,EAAC;EAElE,OAAOlC,WAAW;AACpB"}, "metadata": {}, "sourceType": "module"}