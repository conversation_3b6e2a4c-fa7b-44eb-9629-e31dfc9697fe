{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\GreenRP\\\\Web\\\\ClientApp\\\\src\\\\features\\\\error\\\\Error.tsx\";\nimport { Alert } from 'reactstrap';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport function Error(_ref) {\n  let {\n    error,\n    clearError\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(Alert, {\n    color: \"neutral\",\n    className: \"text-danger\",\n    toggle: clearError,\n    isOpen: !!error,\n    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"lead text-capitalize\",\n      children: error === null || error === void 0 ? void 0 : error.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 5\n    }, this), !!(error !== null && error !== void 0 && error.detail) && /*#__PURE__*/_jsxDEV(\"p\", {\n      children: error.detail\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 10\n  }, this);\n}\n_c = Error;\nvar _c;\n$RefreshReg$(_c, \"Error\");", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Error", "error", "clearError", "title", "detail"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/error/Error.tsx"], "sourcesContent": ["import { ProblemDetails } from 'utils/problem-details';\r\nimport { Alert } from 'reactstrap';\r\n\r\ninterface ErrorProps {\r\n  error: ProblemDetails | null;\r\n  clearError?: () => void;\r\n}\r\n\r\nexport function Error({error, clearError}: ErrorProps) {\r\n  return <Alert color=\"neutral\" className=\"text-danger\" toggle={clearError} isOpen={!!error}>\r\n    <p className=\"lead text-capitalize\">{error?.title}</p>\r\n    {!!error?.detail &&\r\n      <p>{error.detail}</p>\r\n    }\r\n  </Alert>;\r\n}\r\n"], "mappings": ";AACA,SAASA,KAAK,QAAQ,YAAY;AAAC;AAOnC,OAAO,SAASC,KAAK,OAAkC;EAAA,IAAjC;IAACC,KAAK;IAAEC;EAAsB,CAAC;EACnD,oBAAO,QAAC,KAAK;IAAC,KAAK,EAAC,SAAS;IAAC,SAAS,EAAC,aAAa;IAAC,MAAM,EAAEA,UAAW;IAAC,MAAM,EAAE,CAAC,CAACD,KAAM;IAAA,wBACxF;MAAG,SAAS,EAAC,sBAAsB;MAAA,UAAEA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEE;IAAK;MAAA;MAAA;MAAA;IAAA,QAAK,EACrD,CAAC,EAACF,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEG,MAAM,kBACd;MAAA,UAAIH,KAAK,CAACG;IAAM;MAAA;MAAA;MAAA;IAAA,QAAK;EAAA;IAAA;IAAA;IAAA;EAAA,QAEjB;AACV;AAAC,KAPeJ,KAAK;AAAA;AAAA"}, "metadata": {}, "sourceType": "module"}