{"ast": null, "code": "import errorConstructors from './error-constructors.js';\nexport class NonError extends Error {\n  name = 'NonError';\n  constructor(message) {\n    super(NonError._prepareSuperMessage(message));\n  }\n  static _prepareSuperMessage(message) {\n    try {\n      return JSON.stringify(message);\n    } catch {\n      return String(message);\n    }\n  }\n}\nconst commonProperties = [{\n  property: 'name',\n  enumerable: false\n}, {\n  property: 'message',\n  enumerable: false\n}, {\n  property: 'stack',\n  enumerable: false\n}, {\n  property: 'code',\n  enumerable: true\n}, {\n  property: 'cause',\n  enumerable: false\n}];\nconst toJsonWasCalled = Symbol('.toJSON was called');\nconst toJSON = from => {\n  from[toJsonWasCalled] = true;\n  const json = from.toJSON();\n  delete from[toJsonWasCalled];\n  return json;\n};\nconst getErrorConstructor = name => errorConstructors.get(name) ?? Error;\n\n// eslint-disable-next-line complexity\nconst destroyCircular = _ref => {\n  let {\n    from,\n    seen,\n    to,\n    forceEnumerable,\n    maxDepth,\n    depth,\n    useToJSON,\n    serialize\n  } = _ref;\n  if (!to) {\n    if (Array.isArray(from)) {\n      to = [];\n    } else if (!serialize && isErrorLike(from)) {\n      const Error = getErrorConstructor(from.name);\n      to = new Error();\n    } else {\n      to = {};\n    }\n  }\n  seen.push(from);\n  if (depth >= maxDepth) {\n    return to;\n  }\n  if (useToJSON && typeof from.toJSON === 'function' && from[toJsonWasCalled] !== true) {\n    return toJSON(from);\n  }\n  const continueDestroyCircular = value => destroyCircular({\n    from: value,\n    seen: [...seen],\n    forceEnumerable,\n    maxDepth,\n    depth,\n    useToJSON,\n    serialize\n  });\n  for (const [key, value] of Object.entries(from)) {\n    // eslint-disable-next-line node/prefer-global/buffer\n    if (typeof Buffer === 'function' && Buffer.isBuffer(value)) {\n      to[key] = '[object Buffer]';\n      continue;\n    }\n\n    // TODO: Use `stream.isReadable()` when targeting Node.js 18.\n    if (value !== null && typeof value === 'object' && typeof value.pipe === 'function') {\n      to[key] = '[object Stream]';\n      continue;\n    }\n    if (typeof value === 'function') {\n      continue;\n    }\n    if (!value || typeof value !== 'object') {\n      to[key] = value;\n      continue;\n    }\n    if (!seen.includes(from[key])) {\n      depth++;\n      to[key] = continueDestroyCircular(from[key]);\n      continue;\n    }\n    to[key] = '[Circular]';\n  }\n  for (const {\n    property,\n    enumerable\n  } of commonProperties) {\n    if (typeof from[property] !== 'undefined' && from[property] !== null) {\n      Object.defineProperty(to, property, {\n        value: isErrorLike(from[property]) ? continueDestroyCircular(from[property]) : from[property],\n        enumerable: forceEnumerable ? true : enumerable,\n        configurable: true,\n        writable: true\n      });\n    }\n  }\n  return to;\n};\nexport function serializeError(value) {\n  let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const {\n    maxDepth = Number.POSITIVE_INFINITY,\n    useToJSON = true\n  } = options;\n  if (typeof value === 'object' && value !== null) {\n    return destroyCircular({\n      from: value,\n      seen: [],\n      forceEnumerable: true,\n      maxDepth,\n      depth: 0,\n      useToJSON,\n      serialize: true\n    });\n  }\n\n  // People sometimes throw things besides Error objects…\n  if (typeof value === 'function') {\n    // `JSON.stringify()` discards functions. We do too, unless a function is thrown directly.\n    return `[Function: ${value.name ?? 'anonymous'}]`;\n  }\n  return value;\n}\nexport function deserializeError(value) {\n  let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const {\n    maxDepth = Number.POSITIVE_INFINITY\n  } = options;\n  if (value instanceof Error) {\n    return value;\n  }\n  if (isMinimumViableSerializedError(value)) {\n    const Error = getErrorConstructor(value.name);\n    return destroyCircular({\n      from: value,\n      seen: [],\n      to: new Error(),\n      maxDepth,\n      depth: 0,\n      serialize: false\n    });\n  }\n  return new NonError(value);\n}\nexport function isErrorLike(value) {\n  return Boolean(value) && typeof value === 'object' && 'name' in value && 'message' in value && 'stack' in value;\n}\nfunction isMinimumViableSerializedError(value) {\n  return Boolean(value) && typeof value === 'object' && 'message' in value && !Array.isArray(value);\n}\nexport { default as errorConstructors } from './error-constructors.js';", "map": {"version": 3, "names": ["errorConstructors", "NonError", "Error", "name", "constructor", "message", "_prepareSuperMessage", "JSON", "stringify", "String", "commonProperties", "property", "enumerable", "toJsonWasCalled", "Symbol", "toJSON", "from", "json", "getErrorConstructor", "get", "destroyCircular", "seen", "to", "forceEnumerable", "max<PERSON><PERSON><PERSON>", "depth", "useToJSON", "serialize", "Array", "isArray", "isErrorLike", "push", "continueDestroyCircular", "value", "key", "Object", "entries", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "pipe", "includes", "defineProperty", "configurable", "writable", "serializeError", "options", "Number", "POSITIVE_INFINITY", "deserializeError", "isMinimumViableSerializedError", "Boolean", "default"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/node_modules/serialize-error/index.js"], "sourcesContent": ["import errorConstructors from './error-constructors.js';\n\nexport class NonError extends Error {\n\tname = 'NonError';\n\n\tconstructor(message) {\n\t\tsuper(NonError._prepareSuperMessage(message));\n\t}\n\n\tstatic _prepareSuperMessage(message) {\n\t\ttry {\n\t\t\treturn JSON.stringify(message);\n\t\t} catch {\n\t\t\treturn String(message);\n\t\t}\n\t}\n}\n\nconst commonProperties = [\n\t{\n\t\tproperty: 'name',\n\t\tenumerable: false,\n\t},\n\t{\n\t\tproperty: 'message',\n\t\tenumerable: false,\n\t},\n\t{\n\t\tproperty: 'stack',\n\t\tenumerable: false,\n\t},\n\t{\n\t\tproperty: 'code',\n\t\tenumerable: true,\n\t},\n\t{\n\t\tproperty: 'cause',\n\t\tenumerable: false,\n\t},\n];\n\nconst toJsonWasCalled = Symbol('.toJSON was called');\n\nconst toJSON = from => {\n\tfrom[toJsonWasCalled] = true;\n\tconst json = from.toJSON();\n\tdelete from[toJsonWasCalled];\n\treturn json;\n};\n\nconst getErrorConstructor = name => errorConstructors.get(name) ?? Error;\n\n// eslint-disable-next-line complexity\nconst destroyCircular = ({\n\tfrom,\n\tseen,\n\tto,\n\tforceEnumerable,\n\tmaxDepth,\n\tdepth,\n\tuseToJSON,\n\tserialize,\n}) => {\n\tif (!to) {\n\t\tif (Array.isArray(from)) {\n\t\t\tto = [];\n\t\t} else if (!serialize && isErrorLike(from)) {\n\t\t\tconst Error = getErrorConstructor(from.name);\n\t\t\tto = new Error();\n\t\t} else {\n\t\t\tto = {};\n\t\t}\n\t}\n\n\tseen.push(from);\n\n\tif (depth >= maxDepth) {\n\t\treturn to;\n\t}\n\n\tif (useToJSON && typeof from.toJSON === 'function' && from[toJsonWasCalled] !== true) {\n\t\treturn toJSON(from);\n\t}\n\n\tconst continueDestroyCircular = value => destroyCircular({\n\t\tfrom: value,\n\t\tseen: [...seen],\n\t\tforceEnumerable,\n\t\tmaxDepth,\n\t\tdepth,\n\t\tuseToJSON,\n\t\tserialize,\n\t});\n\n\tfor (const [key, value] of Object.entries(from)) {\n\t\t// eslint-disable-next-line node/prefer-global/buffer\n\t\tif (typeof Buffer === 'function' && Buffer.isBuffer(value)) {\n\t\t\tto[key] = '[object Buffer]';\n\t\t\tcontinue;\n\t\t}\n\n\t\t// TODO: Use `stream.isReadable()` when targeting Node.js 18.\n\t\tif (value !== null && typeof value === 'object' && typeof value.pipe === 'function') {\n\t\t\tto[key] = '[object Stream]';\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (typeof value === 'function') {\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (!value || typeof value !== 'object') {\n\t\t\tto[key] = value;\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (!seen.includes(from[key])) {\n\t\t\tdepth++;\n\t\t\tto[key] = continueDestroyCircular(from[key]);\n\n\t\t\tcontinue;\n\t\t}\n\n\t\tto[key] = '[Circular]';\n\t}\n\n\tfor (const {property, enumerable} of commonProperties) {\n\t\tif (typeof from[property] !== 'undefined' && from[property] !== null) {\n\t\t\tObject.defineProperty(to, property, {\n\t\t\t\tvalue: isErrorLike(from[property]) ? continueDestroyCircular(from[property]) : from[property],\n\t\t\t\tenumerable: forceEnumerable ? true : enumerable,\n\t\t\t\tconfigurable: true,\n\t\t\t\twritable: true,\n\t\t\t});\n\t\t}\n\t}\n\n\treturn to;\n};\n\nexport function serializeError(value, options = {}) {\n\tconst {\n\t\tmaxDepth = Number.POSITIVE_INFINITY,\n\t\tuseToJSON = true,\n\t} = options;\n\n\tif (typeof value === 'object' && value !== null) {\n\t\treturn destroyCircular({\n\t\t\tfrom: value,\n\t\t\tseen: [],\n\t\t\tforceEnumerable: true,\n\t\t\tmaxDepth,\n\t\t\tdepth: 0,\n\t\t\tuseToJSON,\n\t\t\tserialize: true,\n\t\t});\n\t}\n\n\t// People sometimes throw things besides Error objects…\n\tif (typeof value === 'function') {\n\t\t// `JSON.stringify()` discards functions. We do too, unless a function is thrown directly.\n\t\treturn `[Function: ${value.name ?? 'anonymous'}]`;\n\t}\n\n\treturn value;\n}\n\nexport function deserializeError(value, options = {}) {\n\tconst {maxDepth = Number.POSITIVE_INFINITY} = options;\n\n\tif (value instanceof Error) {\n\t\treturn value;\n\t}\n\n\tif (isMinimumViableSerializedError(value)) {\n\t\tconst Error = getErrorConstructor(value.name);\n\t\treturn destroyCircular({\n\t\t\tfrom: value,\n\t\t\tseen: [],\n\t\t\tto: new Error(),\n\t\t\tmaxDepth,\n\t\t\tdepth: 0,\n\t\t\tserialize: false,\n\t\t});\n\t}\n\n\treturn new NonError(value);\n}\n\nexport function isErrorLike(value) {\n\treturn Boolean(value)\n\t&& typeof value === 'object'\n\t&& 'name' in value\n\t&& 'message' in value\n\t&& 'stack' in value;\n}\n\nfunction isMinimumViableSerializedError(value) {\n\treturn Boolean(value)\n\t&& typeof value === 'object'\n\t&& 'message' in value\n\t&& !Array.isArray(value);\n}\n\nexport {default as errorConstructors} from './error-constructors.js';\n"], "mappings": "AAAA,OAAOA,iBAAiB,MAAM,yBAAyB;AAEvD,OAAO,MAAMC,QAAQ,SAASC,KAAK,CAAC;EACnCC,IAAI,GAAG,UAAU;EAEjBC,WAAW,CAACC,OAAO,EAAE;IACpB,KAAK,CAACJ,QAAQ,CAACK,oBAAoB,CAACD,OAAO,CAAC,CAAC;EAC9C;EAEA,OAAOC,oBAAoB,CAACD,OAAO,EAAE;IACpC,IAAI;MACH,OAAOE,IAAI,CAACC,SAAS,CAACH,OAAO,CAAC;IAC/B,CAAC,CAAC,MAAM;MACP,OAAOI,MAAM,CAACJ,OAAO,CAAC;IACvB;EACD;AACD;AAEA,MAAMK,gBAAgB,GAAG,CACxB;EACCC,QAAQ,EAAE,MAAM;EAChBC,UAAU,EAAE;AACb,CAAC,EACD;EACCD,QAAQ,EAAE,SAAS;EACnBC,UAAU,EAAE;AACb,CAAC,EACD;EACCD,QAAQ,EAAE,OAAO;EACjBC,UAAU,EAAE;AACb,CAAC,EACD;EACCD,QAAQ,EAAE,MAAM;EAChBC,UAAU,EAAE;AACb,CAAC,EACD;EACCD,QAAQ,EAAE,OAAO;EACjBC,UAAU,EAAE;AACb,CAAC,CACD;AAED,MAAMC,eAAe,GAAGC,MAAM,CAAC,oBAAoB,CAAC;AAEpD,MAAMC,MAAM,GAAGC,IAAI,IAAI;EACtBA,IAAI,CAACH,eAAe,CAAC,GAAG,IAAI;EAC5B,MAAMI,IAAI,GAAGD,IAAI,CAACD,MAAM,EAAE;EAC1B,OAAOC,IAAI,CAACH,eAAe,CAAC;EAC5B,OAAOI,IAAI;AACZ,CAAC;AAED,MAAMC,mBAAmB,GAAGf,IAAI,IAAIH,iBAAiB,CAACmB,GAAG,CAAChB,IAAI,CAAC,IAAID,KAAK;;AAExE;AACA,MAAMkB,eAAe,GAAG,QASlB;EAAA,IATmB;IACxBJ,IAAI;IACJK,IAAI;IACJC,EAAE;IACFC,eAAe;IACfC,QAAQ;IACRC,KAAK;IACLC,SAAS;IACTC;EACD,CAAC;EACA,IAAI,CAACL,EAAE,EAAE;IACR,IAAIM,KAAK,CAACC,OAAO,CAACb,IAAI,CAAC,EAAE;MACxBM,EAAE,GAAG,EAAE;IACR,CAAC,MAAM,IAAI,CAACK,SAAS,IAAIG,WAAW,CAACd,IAAI,CAAC,EAAE;MAC3C,MAAMd,KAAK,GAAGgB,mBAAmB,CAACF,IAAI,CAACb,IAAI,CAAC;MAC5CmB,EAAE,GAAG,IAAIpB,KAAK,EAAE;IACjB,CAAC,MAAM;MACNoB,EAAE,GAAG,CAAC,CAAC;IACR;EACD;EAEAD,IAAI,CAACU,IAAI,CAACf,IAAI,CAAC;EAEf,IAAIS,KAAK,IAAID,QAAQ,EAAE;IACtB,OAAOF,EAAE;EACV;EAEA,IAAII,SAAS,IAAI,OAAOV,IAAI,CAACD,MAAM,KAAK,UAAU,IAAIC,IAAI,CAACH,eAAe,CAAC,KAAK,IAAI,EAAE;IACrF,OAAOE,MAAM,CAACC,IAAI,CAAC;EACpB;EAEA,MAAMgB,uBAAuB,GAAGC,KAAK,IAAIb,eAAe,CAAC;IACxDJ,IAAI,EAAEiB,KAAK;IACXZ,IAAI,EAAE,CAAC,GAAGA,IAAI,CAAC;IACfE,eAAe;IACfC,QAAQ;IACRC,KAAK;IACLC,SAAS;IACTC;EACD,CAAC,CAAC;EAEF,KAAK,MAAM,CAACO,GAAG,EAAED,KAAK,CAAC,IAAIE,MAAM,CAACC,OAAO,CAACpB,IAAI,CAAC,EAAE;IAChD;IACA,IAAI,OAAOqB,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ,CAACL,KAAK,CAAC,EAAE;MAC3DX,EAAE,CAACY,GAAG,CAAC,GAAG,iBAAiB;MAC3B;IACD;;IAEA;IACA,IAAID,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,CAACM,IAAI,KAAK,UAAU,EAAE;MACpFjB,EAAE,CAACY,GAAG,CAAC,GAAG,iBAAiB;MAC3B;IACD;IAEA,IAAI,OAAOD,KAAK,KAAK,UAAU,EAAE;MAChC;IACD;IAEA,IAAI,CAACA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MACxCX,EAAE,CAACY,GAAG,CAAC,GAAGD,KAAK;MACf;IACD;IAEA,IAAI,CAACZ,IAAI,CAACmB,QAAQ,CAACxB,IAAI,CAACkB,GAAG,CAAC,CAAC,EAAE;MAC9BT,KAAK,EAAE;MACPH,EAAE,CAACY,GAAG,CAAC,GAAGF,uBAAuB,CAAChB,IAAI,CAACkB,GAAG,CAAC,CAAC;MAE5C;IACD;IAEAZ,EAAE,CAACY,GAAG,CAAC,GAAG,YAAY;EACvB;EAEA,KAAK,MAAM;IAACvB,QAAQ;IAAEC;EAAU,CAAC,IAAIF,gBAAgB,EAAE;IACtD,IAAI,OAAOM,IAAI,CAACL,QAAQ,CAAC,KAAK,WAAW,IAAIK,IAAI,CAACL,QAAQ,CAAC,KAAK,IAAI,EAAE;MACrEwB,MAAM,CAACM,cAAc,CAACnB,EAAE,EAAEX,QAAQ,EAAE;QACnCsB,KAAK,EAAEH,WAAW,CAACd,IAAI,CAACL,QAAQ,CAAC,CAAC,GAAGqB,uBAAuB,CAAChB,IAAI,CAACL,QAAQ,CAAC,CAAC,GAAGK,IAAI,CAACL,QAAQ,CAAC;QAC7FC,UAAU,EAAEW,eAAe,GAAG,IAAI,GAAGX,UAAU;QAC/C8B,YAAY,EAAE,IAAI;QAClBC,QAAQ,EAAE;MACX,CAAC,CAAC;IACH;EACD;EAEA,OAAOrB,EAAE;AACV,CAAC;AAED,OAAO,SAASsB,cAAc,CAACX,KAAK,EAAgB;EAAA,IAAdY,OAAO,uEAAG,CAAC,CAAC;EACjD,MAAM;IACLrB,QAAQ,GAAGsB,MAAM,CAACC,iBAAiB;IACnCrB,SAAS,GAAG;EACb,CAAC,GAAGmB,OAAO;EAEX,IAAI,OAAOZ,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE;IAChD,OAAOb,eAAe,CAAC;MACtBJ,IAAI,EAAEiB,KAAK;MACXZ,IAAI,EAAE,EAAE;MACRE,eAAe,EAAE,IAAI;MACrBC,QAAQ;MACRC,KAAK,EAAE,CAAC;MACRC,SAAS;MACTC,SAAS,EAAE;IACZ,CAAC,CAAC;EACH;;EAEA;EACA,IAAI,OAAOM,KAAK,KAAK,UAAU,EAAE;IAChC;IACA,OAAQ,cAAaA,KAAK,CAAC9B,IAAI,IAAI,WAAY,GAAE;EAClD;EAEA,OAAO8B,KAAK;AACb;AAEA,OAAO,SAASe,gBAAgB,CAACf,KAAK,EAAgB;EAAA,IAAdY,OAAO,uEAAG,CAAC,CAAC;EACnD,MAAM;IAACrB,QAAQ,GAAGsB,MAAM,CAACC;EAAiB,CAAC,GAAGF,OAAO;EAErD,IAAIZ,KAAK,YAAY/B,KAAK,EAAE;IAC3B,OAAO+B,KAAK;EACb;EAEA,IAAIgB,8BAA8B,CAAChB,KAAK,CAAC,EAAE;IAC1C,MAAM/B,KAAK,GAAGgB,mBAAmB,CAACe,KAAK,CAAC9B,IAAI,CAAC;IAC7C,OAAOiB,eAAe,CAAC;MACtBJ,IAAI,EAAEiB,KAAK;MACXZ,IAAI,EAAE,EAAE;MACRC,EAAE,EAAE,IAAIpB,KAAK,EAAE;MACfsB,QAAQ;MACRC,KAAK,EAAE,CAAC;MACRE,SAAS,EAAE;IACZ,CAAC,CAAC;EACH;EAEA,OAAO,IAAI1B,QAAQ,CAACgC,KAAK,CAAC;AAC3B;AAEA,OAAO,SAASH,WAAW,CAACG,KAAK,EAAE;EAClC,OAAOiB,OAAO,CAACjB,KAAK,CAAC,IAClB,OAAOA,KAAK,KAAK,QAAQ,IACzB,MAAM,IAAIA,KAAK,IACf,SAAS,IAAIA,KAAK,IAClB,OAAO,IAAIA,KAAK;AACpB;AAEA,SAASgB,8BAA8B,CAAChB,KAAK,EAAE;EAC9C,OAAOiB,OAAO,CAACjB,KAAK,CAAC,IAClB,OAAOA,KAAK,KAAK,QAAQ,IACzB,SAAS,IAAIA,KAAK,IAClB,CAACL,KAAK,CAACC,OAAO,CAACI,KAAK,CAAC;AACzB;AAEA,SAAQkB,OAAO,IAAInD,iBAAiB,QAAO,yBAAyB"}, "metadata": {}, "sourceType": "module"}