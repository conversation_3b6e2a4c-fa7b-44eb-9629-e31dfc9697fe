{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport verifyPlainObject from '../utils/verifyPlainObject';\nexport function defaultMergeProps(stateProps, dispatchProps, ownProps) {\n  return _extends({}, ownProps, stateProps, dispatchProps);\n}\nexport function wrapMergePropsFunc(mergeProps) {\n  return function initMergePropsProxy(dispatch, _ref) {\n    var displayName = _ref.displayName,\n      pure = _ref.pure,\n      areMergedPropsEqual = _ref.areMergedPropsEqual;\n    var hasRunOnce = false;\n    var mergedProps;\n    return function mergePropsProxy(stateProps, dispatchProps, ownProps) {\n      var nextMergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n      if (hasRunOnce) {\n        if (!pure || !areMergedPropsEqual(nextMergedProps, mergedProps)) mergedProps = nextMergedProps;\n      } else {\n        hasRunOnce = true;\n        mergedProps = nextMergedProps;\n        if (process.env.NODE_ENV !== 'production') verifyPlainObject(mergedProps, displayName, 'mergeProps');\n      }\n      return mergedProps;\n    };\n  };\n}\nexport function whenMergePropsIsFunction(mergeProps) {\n  return typeof mergeProps === 'function' ? wrapMergePropsFunc(mergeProps) : undefined;\n}\nexport function whenMergePropsIsOmitted(mergeProps) {\n  return !mergeProps ? function () {\n    return defaultMergeProps;\n  } : undefined;\n}\nexport default [whenMergePropsIsFunction, whenMergePropsIsOmitted];", "map": {"version": 3, "names": ["_extends", "verifyPlainObject", "defaultMergeProps", "stateProps", "dispatchProps", "ownProps", "wrapMergePropsFunc", "mergeProps", "initMergePropsProxy", "dispatch", "_ref", "displayName", "pure", "areMergedPropsEqual", "hasRunOnce", "mergedProps", "mergePropsProxy", "nextMergedProps", "process", "env", "NODE_ENV", "whenMergePropsIsFunction", "undefined", "whenMergePropsIsOmitted"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/node_modules/react-redux/es/connect/mergeProps.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport verifyPlainObject from '../utils/verifyPlainObject';\nexport function defaultMergeProps(stateProps, dispatchProps, ownProps) {\n  return _extends({}, ownProps, stateProps, dispatchProps);\n}\nexport function wrapMergePropsFunc(mergeProps) {\n  return function initMergePropsProxy(dispatch, _ref) {\n    var displayName = _ref.displayName,\n        pure = _ref.pure,\n        areMergedPropsEqual = _ref.areMergedPropsEqual;\n    var hasRunOnce = false;\n    var mergedProps;\n    return function mergePropsProxy(stateProps, dispatchProps, ownProps) {\n      var nextMergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n\n      if (hasRunOnce) {\n        if (!pure || !areMergedPropsEqual(nextMergedProps, mergedProps)) mergedProps = nextMergedProps;\n      } else {\n        hasRunOnce = true;\n        mergedProps = nextMergedProps;\n        if (process.env.NODE_ENV !== 'production') verifyPlainObject(mergedProps, displayName, 'mergeProps');\n      }\n\n      return mergedProps;\n    };\n  };\n}\nexport function whenMergePropsIsFunction(mergeProps) {\n  return typeof mergeProps === 'function' ? wrapMergePropsFunc(mergeProps) : undefined;\n}\nexport function whenMergePropsIsOmitted(mergeProps) {\n  return !mergeProps ? function () {\n    return defaultMergeProps;\n  } : undefined;\n}\nexport default [whenMergePropsIsFunction, whenMergePropsIsOmitted];"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,iBAAiB,MAAM,4BAA4B;AAC1D,OAAO,SAASC,iBAAiB,CAACC,UAAU,EAAEC,aAAa,EAAEC,QAAQ,EAAE;EACrE,OAAOL,QAAQ,CAAC,CAAC,CAAC,EAAEK,QAAQ,EAAEF,UAAU,EAAEC,aAAa,CAAC;AAC1D;AACA,OAAO,SAASE,kBAAkB,CAACC,UAAU,EAAE;EAC7C,OAAO,SAASC,mBAAmB,CAACC,QAAQ,EAAEC,IAAI,EAAE;IAClD,IAAIC,WAAW,GAAGD,IAAI,CAACC,WAAW;MAC9BC,IAAI,GAAGF,IAAI,CAACE,IAAI;MAChBC,mBAAmB,GAAGH,IAAI,CAACG,mBAAmB;IAClD,IAAIC,UAAU,GAAG,KAAK;IACtB,IAAIC,WAAW;IACf,OAAO,SAASC,eAAe,CAACb,UAAU,EAAEC,aAAa,EAAEC,QAAQ,EAAE;MACnE,IAAIY,eAAe,GAAGV,UAAU,CAACJ,UAAU,EAAEC,aAAa,EAAEC,QAAQ,CAAC;MAErE,IAAIS,UAAU,EAAE;QACd,IAAI,CAACF,IAAI,IAAI,CAACC,mBAAmB,CAACI,eAAe,EAAEF,WAAW,CAAC,EAAEA,WAAW,GAAGE,eAAe;MAChG,CAAC,MAAM;QACLH,UAAU,GAAG,IAAI;QACjBC,WAAW,GAAGE,eAAe;QAC7B,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAEnB,iBAAiB,CAACc,WAAW,EAAEJ,WAAW,EAAE,YAAY,CAAC;MACtG;MAEA,OAAOI,WAAW;IACpB,CAAC;EACH,CAAC;AACH;AACA,OAAO,SAASM,wBAAwB,CAACd,UAAU,EAAE;EACnD,OAAO,OAAOA,UAAU,KAAK,UAAU,GAAGD,kBAAkB,CAACC,UAAU,CAAC,GAAGe,SAAS;AACtF;AACA,OAAO,SAASC,uBAAuB,CAAChB,UAAU,EAAE;EAClD,OAAO,CAACA,UAAU,GAAG,YAAY;IAC/B,OAAOL,iBAAiB;EAC1B,CAAC,GAAGoB,SAAS;AACf;AACA,eAAe,CAACD,wBAAwB,EAAEE,uBAAuB,CAAC"}, "metadata": {}, "sourceType": "module"}