{"ast": null, "code": "import moment from 'moment';\nimport numeral from 'numeral';\nexport function formatDate(date) {\n  let format = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'W[-]E [(]MMM D[)]';\n  if (date == null) {\n    return '';\n  }\n  const m = moment(date);\n  if (!m.isValid()) {\n    return '';\n  }\n  return m.format(format);\n}\nexport function formatNumber(value) {\n  let format = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '0,0';\n  if (value == null) {\n    return '0';\n  }\n  return numeral(value).format(format);\n}\nexport function formatCurrency(value) {\n  let format = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '$0,0.00';\n  if (value == null) {\n    return '$0.00';\n  }\n  return numeral(value).format(format);\n}\nexport function parseWeekAndDay(weekAndDay) {\n  let shouldBeFuture = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  const regex = /(\\d{1,2})-(\\d)/;\n  if (regex.test(weekAndDay)) {\n    const match = regex.exec(weekAndDay),\n      week = parseInt((match === null || match === void 0 ? void 0 : match[1]) || ''),\n      day = parseInt((match === null || match === void 0 ? void 0 : match[2]) || '');\n    if (week && day) {\n      const m = moment().isoWeek(week).isoWeekday(day);\n      if (m.isValid()) {\n        if (shouldBeFuture && m.isBefore()) {\n          m.add(1, 'year').isoWeek(week).isoWeekday(day);\n        }\n        return m.toDate();\n      }\n    }\n  }\n  return null;\n}\nexport function toWeekAndDay(date) {\n  return formatDate(date, 'W[-]E');\n}\nexport function parseWeekAndYear(weekAndYear, endOfWeek) {\n  let shouldBeFuture = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  const regex = /(\\d{1,2})\\/(\\d{4})/;\n  if (regex.test(weekAndYear)) {\n    const match = regex.exec(weekAndYear),\n      week = parseInt((match === null || match === void 0 ? void 0 : match[1]) || ''),\n      year = parseInt((match === null || match === void 0 ? void 0 : match[2]) || '');\n    if (week && year) {\n      const m = moment().isoWeek(week).isoWeekYear(year)[endOfWeek ? 'endOf' : 'startOf']('isoWeek');\n      if (m.isValid()) {\n        if (shouldBeFuture && m.isBefore()) {\n          m.add(1, 'year').isoWeek(week).isoWeekYear(year)[endOfWeek ? 'endOf' : 'startOf']('isoWeek');\n        }\n        return m.toDate();\n      }\n    }\n  }\n  return null;\n}\nexport function toWeekAndYear(date) {\n  return formatDate(date, 'WW[/]YYYY');\n}", "map": {"version": 3, "names": ["moment", "numeral", "formatDate", "date", "format", "m", "<PERSON><PERSON><PERSON><PERSON>", "formatNumber", "value", "formatCurrency", "parseWeekAndDay", "weekAndDay", "shouldBeFuture", "regex", "test", "match", "exec", "week", "parseInt", "day", "isoWeek", "isoWeekday", "isBefore", "add", "toDate", "toWeekAndDay", "parseWeekAndYear", "weekAndYear", "endOfWeek", "year", "isoWeekYear", "toWeekAndYear"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/utils/format.ts"], "sourcesContent": ["import moment from 'moment';\r\nimport numeral from 'numeral';\r\n\r\nexport function formatDate(date: any, format = 'W[-]E [(]MMM D[)]'): string {\r\n  if(date == null) {\r\n    return '';\r\n  }\r\n\r\n  const m = moment(date);\r\n\r\n  if(!m.isValid()) {\r\n    return '';\r\n  }\r\n\r\n  return m.format(format);\r\n}\r\n\r\nexport function formatNumber(value: any, format = '0,0'): string {\r\n  if(value == null) {\r\n    return '0';\r\n  }\r\n\r\n  return numeral(value).format(format);\r\n}\r\n\r\nexport function formatCurrency(value: any, format = '$0,0.00'): string {\r\n  if(value == null) {\r\n    return '$0.00';\r\n  }\r\n\r\n  return numeral(value).format(format);\r\n}\r\n\r\nexport function parseWeekAndDay(weekAndDay: string, shouldBeFuture: boolean = false) {\r\n  const regex = /(\\d{1,2})-(\\d)/;\r\n  \r\n  if(regex.test(weekAndDay)) {\r\n    const match = regex.exec(weekAndDay),\r\n      week = parseInt(match?.[1] || ''),\r\n      day = parseInt(match?.[2] || '');\r\n\r\n    if(week && day) {\r\n      const m = moment().isoWeek(week).isoWeekday(day);\r\n      if(m.isValid()) {\r\n        if(shouldBeFuture && m.isBefore()) {\r\n          m.add(1, 'year').isoWeek(week).isoWeekday(day);\r\n        }\r\n        return m.toDate();\r\n      }\r\n    }\r\n  }\r\n\r\n  return null;\r\n}\r\n\r\nexport function toWeekAndDay(date: any) {\r\n  return formatDate(date, 'W[-]E');\r\n}\r\n\r\nexport function parseWeekAndYear(weekAndYear: string, endOfWeek?: boolean, shouldBeFuture: boolean = false) {\r\n  const regex = /(\\d{1,2})\\/(\\d{4})/;\r\n  \r\n  if(regex.test(weekAndYear)) {\r\n    const match = regex.exec(weekAndYear),\r\n      week = parseInt(match?.[1] || ''),\r\n      year = parseInt(match?.[2] || '');\r\n\r\n    if(week && year) {\r\n      const m = moment().isoWeek(week).isoWeekYear(year)[endOfWeek ? 'endOf' : 'startOf']('isoWeek');\r\n      if(m.isValid()) {\r\n        if(shouldBeFuture && m.isBefore()) {\r\n          m.add(1, 'year').isoWeek(week).isoWeekYear(year)[endOfWeek ? 'endOf' : 'startOf']('isoWeek');\r\n        }\r\n        return m.toDate();\r\n      }\r\n    }\r\n  }\r\n\r\n  return null;\r\n}\r\n\r\nexport function toWeekAndYear(date: any) {\r\n  return formatDate(date, 'WW[/]YYYY');\r\n}\r\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,QAAQ;AAC3B,OAAOC,OAAO,MAAM,SAAS;AAE7B,OAAO,SAASC,UAAU,CAACC,IAAS,EAAwC;EAAA,IAAtCC,MAAM,uEAAG,mBAAmB;EAChE,IAAGD,IAAI,IAAI,IAAI,EAAE;IACf,OAAO,EAAE;EACX;EAEA,MAAME,CAAC,GAAGL,MAAM,CAACG,IAAI,CAAC;EAEtB,IAAG,CAACE,CAAC,CAACC,OAAO,EAAE,EAAE;IACf,OAAO,EAAE;EACX;EAEA,OAAOD,CAAC,CAACD,MAAM,CAACA,MAAM,CAAC;AACzB;AAEA,OAAO,SAASG,YAAY,CAACC,KAAU,EAA0B;EAAA,IAAxBJ,MAAM,uEAAG,KAAK;EACrD,IAAGI,KAAK,IAAI,IAAI,EAAE;IAChB,OAAO,GAAG;EACZ;EAEA,OAAOP,OAAO,CAACO,KAAK,CAAC,CAACJ,MAAM,CAACA,MAAM,CAAC;AACtC;AAEA,OAAO,SAASK,cAAc,CAACD,KAAU,EAA8B;EAAA,IAA5BJ,MAAM,uEAAG,SAAS;EAC3D,IAAGI,KAAK,IAAI,IAAI,EAAE;IAChB,OAAO,OAAO;EAChB;EAEA,OAAOP,OAAO,CAACO,KAAK,CAAC,CAACJ,MAAM,CAACA,MAAM,CAAC;AACtC;AAEA,OAAO,SAASM,eAAe,CAACC,UAAkB,EAAmC;EAAA,IAAjCC,cAAuB,uEAAG,KAAK;EACjF,MAAMC,KAAK,GAAG,gBAAgB;EAE9B,IAAGA,KAAK,CAACC,IAAI,CAACH,UAAU,CAAC,EAAE;IACzB,MAAMI,KAAK,GAAGF,KAAK,CAACG,IAAI,CAACL,UAAU,CAAC;MAClCM,IAAI,GAAGC,QAAQ,CAAC,CAAAH,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAG,CAAC,CAAC,KAAI,EAAE,CAAC;MACjCI,GAAG,GAAGD,QAAQ,CAAC,CAAAH,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAG,CAAC,CAAC,KAAI,EAAE,CAAC;IAElC,IAAGE,IAAI,IAAIE,GAAG,EAAE;MACd,MAAMd,CAAC,GAAGL,MAAM,EAAE,CAACoB,OAAO,CAACH,IAAI,CAAC,CAACI,UAAU,CAACF,GAAG,CAAC;MAChD,IAAGd,CAAC,CAACC,OAAO,EAAE,EAAE;QACd,IAAGM,cAAc,IAAIP,CAAC,CAACiB,QAAQ,EAAE,EAAE;UACjCjB,CAAC,CAACkB,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAACH,OAAO,CAACH,IAAI,CAAC,CAACI,UAAU,CAACF,GAAG,CAAC;QAChD;QACA,OAAOd,CAAC,CAACmB,MAAM,EAAE;MACnB;IACF;EACF;EAEA,OAAO,IAAI;AACb;AAEA,OAAO,SAASC,YAAY,CAACtB,IAAS,EAAE;EACtC,OAAOD,UAAU,CAACC,IAAI,EAAE,OAAO,CAAC;AAClC;AAEA,OAAO,SAASuB,gBAAgB,CAACC,WAAmB,EAAEC,SAAmB,EAAmC;EAAA,IAAjChB,cAAuB,uEAAG,KAAK;EACxG,MAAMC,KAAK,GAAG,oBAAoB;EAElC,IAAGA,KAAK,CAACC,IAAI,CAACa,WAAW,CAAC,EAAE;IAC1B,MAAMZ,KAAK,GAAGF,KAAK,CAACG,IAAI,CAACW,WAAW,CAAC;MACnCV,IAAI,GAAGC,QAAQ,CAAC,CAAAH,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAG,CAAC,CAAC,KAAI,EAAE,CAAC;MACjCc,IAAI,GAAGX,QAAQ,CAAC,CAAAH,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAG,CAAC,CAAC,KAAI,EAAE,CAAC;IAEnC,IAAGE,IAAI,IAAIY,IAAI,EAAE;MACf,MAAMxB,CAAC,GAAGL,MAAM,EAAE,CAACoB,OAAO,CAACH,IAAI,CAAC,CAACa,WAAW,CAACD,IAAI,CAAC,CAACD,SAAS,GAAG,OAAO,GAAG,SAAS,CAAC,CAAC,SAAS,CAAC;MAC9F,IAAGvB,CAAC,CAACC,OAAO,EAAE,EAAE;QACd,IAAGM,cAAc,IAAIP,CAAC,CAACiB,QAAQ,EAAE,EAAE;UACjCjB,CAAC,CAACkB,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAACH,OAAO,CAACH,IAAI,CAAC,CAACa,WAAW,CAACD,IAAI,CAAC,CAACD,SAAS,GAAG,OAAO,GAAG,SAAS,CAAC,CAAC,SAAS,CAAC;QAC9F;QACA,OAAOvB,CAAC,CAACmB,MAAM,EAAE;MACnB;IACF;EACF;EAEA,OAAO,IAAI;AACb;AAEA,OAAO,SAASO,aAAa,CAAC5B,IAAS,EAAE;EACvC,OAAOD,UAAU,CAACC,IAAI,EAAE,WAAW,CAAC;AACtC"}, "metadata": {}, "sourceType": "module"}