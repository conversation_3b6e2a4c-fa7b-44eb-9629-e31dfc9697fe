{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\GreenRP\\\\Web\\\\ClientApp\\\\src\\\\index.tsx\";\nimport React from 'react';\nimport { createRoot } from 'react-dom/client';\nimport { Provider } from 'react-redux';\nimport App from './app/App';\nimport { store } from 'app/store';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst container = document.getElementById('root');\nif (container) {\n  const root = createRoot(container);\n  root.render( /*#__PURE__*/_jsxDEV(React.StrictMode, {\n    children: /*#__PURE__*/_jsxDEV(Provider, {\n      store: store,\n      children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this));\n}\n// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\n//reportWebVitals();", "map": {"version": 3, "names": ["React", "createRoot", "Provider", "App", "store", "container", "document", "getElementById", "root", "render"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/index.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { createRoot } from 'react-dom/client';\r\nimport { Provider } from 'react-redux';\r\nimport App from './app/App';\r\nimport { store } from 'app/store';\r\n\r\nconst container = document.getElementById('root');\r\nif (container) {\r\n  const root = createRoot(container);\r\n\r\n  root.render(\r\n    <React.StrictMode>\r\n      <Provider store={store}>\r\n        <App />\r\n      </Provider>\r\n    </React.StrictMode>\r\n  );\r\n}\r\n// If you want to start measuring performance in your app, pass a function\r\n// to log results (for example: reportWebVitals(console.log))\r\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\r\n//reportWebVitals();\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,QAAQ,QAAQ,aAAa;AACtC,OAAOC,GAAG,MAAM,WAAW;AAC3B,SAASC,KAAK,QAAQ,WAAW;AAAC;AAElC,MAAMC,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC;AACjD,IAAIF,SAAS,EAAE;EACb,MAAMG,IAAI,GAAGP,UAAU,CAACI,SAAS,CAAC;EAElCG,IAAI,CAACC,MAAM,eACT,QAAC,KAAK,CAAC,UAAU;IAAA,uBACf,QAAC,QAAQ;MAAC,KAAK,EAAEL,KAAM;MAAA,uBACrB,QAAC,GAAG;QAAA;QAAA;QAAA;MAAA;IAAG;MAAA;MAAA;MAAA;IAAA;EACE;IAAA;IAAA;IAAA;EAAA,QACM,CACpB;AACH;AACA;AACA;AACA;AACA"}, "metadata": {}, "sourceType": "module"}