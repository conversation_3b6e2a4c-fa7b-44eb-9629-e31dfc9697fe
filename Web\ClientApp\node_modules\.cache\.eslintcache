[{"C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\App.tsx": "2", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\store.ts": "3", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\routes.ts": "4", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\events.ts": "5", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\Layout.tsx": "6", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\customer-service.ts": "7", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\order-service.ts": "8", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\zone-service.ts": "9", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\plant-service.ts": "10", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\require-auth.tsx": "11", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\detail-slice.ts": "12", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\plants\\plants-slice.ts": "13", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\zones\\zones-slice.ts": "14", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\auth-provider.tsx": "15", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\Login.tsx": "16", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\zones\\detail-slice.ts": "17", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\orders-slice.ts": "18", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\plants\\detail-slice.ts": "19", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\customers\\customers-slice.ts": "20", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\customers\\detail-slice.ts": "21", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\users\\users-slice.ts": "22", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\driver-task-slice.ts": "23", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\zones\\Detail.tsx": "24", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\users\\Detail.tsx": "25", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\users\\List.tsx": "26", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\Detail.tsx": "27", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\List.tsx": "28", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\New.tsx": "29", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\zones\\List.tsx": "30", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\customers\\Detail.tsx": "31", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\plants\\List.tsx": "32", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\customers\\List.tsx": "33", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\plants\\Detail.tsx": "34", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\ByStickDate.tsx": "35", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\ByPinchDate.tsx": "36", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\ByFlowerDate.tsx": "37", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\Detail.tsx": "38", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\List.tsx": "39", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\service-base.ts": "40", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\NavMenu.tsx": "41", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\BySpaceDate.tsx": "42", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\colours\\Detail.tsx": "43", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\pages\\Index.tsx": "44", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\colours\\List.tsx": "45", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\auth-context.ts": "46", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\use-auth.ts": "47", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\reports-service.ts": "48", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\auth-service.ts": "49", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\driver-tasks-service.ts": "50", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\orders.ts": "51", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\plants.ts": "52", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\zones.ts": "53", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\driver-tasks.ts": "54", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\customers.ts": "55", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\sort.ts": "56", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\loading\\Loading.tsx": "57", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\problem-details.ts": "58", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\format.ts": "59", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\equals.ts": "60", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\error\\Error.tsx": "61", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\boot\\index.ts": "62", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\focus.ts": "63", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\class-names.ts": "64", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\weeks.ts": "65", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\notifications-service.ts": "66", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\ListFilters.tsx": "67", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\List-Date.tsx": "68", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\guid.ts": "69", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\OrderRow.tsx": "70", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\database.ts": "71", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\LabourReport.tsx": "72", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\SalesWeekRow.tsx": "73", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\Variety.tsx": "74", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\boot\\axios.ts": "75", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\api-base.ts": "76", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\configuration.ts": "77", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\boot\\font-awesome.ts": "78", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\List-Item.tsx": "79", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\colours\\colours-slice.ts": "80", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\colours\\detail-slice.ts": "81", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\colour.ts": "82", "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\colour-service.ts": "83"}, {"size": 675, "mtime": 1753379942630, "results": "84", "hashOfConfig": "85"}, {"size": 5818, "mtime": 1753379942615, "results": "86", "hashOfConfig": "85"}, {"size": 1194, "mtime": 1753382476769, "results": "87", "hashOfConfig": "85"}, {"size": 3150, "mtime": 1753381281451, "results": "88", "hashOfConfig": "85"}, {"size": 793, "mtime": 1753379942616, "results": "89", "hashOfConfig": "85"}, {"size": 189, "mtime": 1753379942615, "results": "90", "hashOfConfig": "85"}, {"size": 380, "mtime": 1753379942611, "results": "91", "hashOfConfig": "85"}, {"size": 882, "mtime": 1753379942614, "results": "92", "hashOfConfig": "85"}, {"size": 340, "mtime": 1753379942615, "results": "93", "hashOfConfig": "85"}, {"size": 366, "mtime": 1753379942614, "results": "94", "hashOfConfig": "85"}, {"size": 604, "mtime": 1753379942619, "results": "95", "hashOfConfig": "85"}, {"size": 3382, "mtime": 1753379942627, "results": "96", "hashOfConfig": "85"}, {"size": 864, "mtime": 1753379942628, "results": "97", "hashOfConfig": "85"}, {"size": 694, "mtime": 1753379942630, "results": "98", "hashOfConfig": "85"}, {"size": 1669, "mtime": 1753379942618, "results": "99", "hashOfConfig": "85"}, {"size": 2313, "mtime": 1753379942618, "results": "100", "hashOfConfig": "85"}, {"size": 3177, "mtime": 1753379942630, "results": "101", "hashOfConfig": "85"}, {"size": 7906, "mtime": 1753379942627, "results": "102", "hashOfConfig": "85"}, {"size": 3227, "mtime": 1753379942628, "results": "103", "hashOfConfig": "85"}, {"size": 766, "mtime": 1753379942620, "results": "104", "hashOfConfig": "85"}, {"size": 3384, "mtime": 1753379942620, "results": "105", "hashOfConfig": "85"}, {"size": 1854, "mtime": 1753379942628, "results": "106", "hashOfConfig": "85"}, {"size": 6721, "mtime": 1753379942622, "results": "107", "hashOfConfig": "85"}, {"size": 4788, "mtime": 1753379942630, "results": "108", "hashOfConfig": "85"}, {"size": 16200, "mtime": 1753379942628, "results": "109", "hashOfConfig": "85"}, {"size": 2126, "mtime": 1753379942628, "results": "110", "hashOfConfig": "85"}, {"size": 9857, "mtime": 1753379942620, "results": "111", "hashOfConfig": "85"}, {"size": 4221, "mtime": 1753379942622, "results": "112", "hashOfConfig": "85"}, {"size": 9032, "mtime": 1753379942622, "results": "113", "hashOfConfig": "85"}, {"size": 2229, "mtime": 1753379942630, "results": "114", "hashOfConfig": "85"}, {"size": 4014, "mtime": 1753379942619, "results": "115", "hashOfConfig": "85"}, {"size": 2573, "mtime": 1753379942628, "results": "116", "hashOfConfig": "85"}, {"size": 1870, "mtime": 1753379942619, "results": "117", "hashOfConfig": "85"}, {"size": 20321, "mtime": 1753379942627, "results": "118", "hashOfConfig": "85"}, {"size": 9851, "mtime": 1753379942625, "results": "119", "hashOfConfig": "85"}, {"size": 10007, "mtime": 1753379942623, "results": "120", "hashOfConfig": "85"}, {"size": 10445, "mtime": 1753379942623, "results": "121", "hashOfConfig": "85"}, {"size": 47495, "mtime": 1753379942626, "results": "122", "hashOfConfig": "85"}, {"size": 10641, "mtime": 1753379942626, "results": "123", "hashOfConfig": "85"}, {"size": 2321, "mtime": 1753379942614, "results": "124", "hashOfConfig": "85"}, {"size": 5358, "mtime": 1753382309129, "results": "125", "hashOfConfig": "85"}, {"size": 9798, "mtime": 1753379942625, "results": "126", "hashOfConfig": "85"}, {"size": 93, "mtime": 1753381578088, "results": "127", "hashOfConfig": "85"}, {"size": 506, "mtime": 1753379942630, "results": "128", "hashOfConfig": "85"}, {"size": 87, "mtime": 1753381585585, "results": "129", "hashOfConfig": "85"}, {"size": 362, "mtime": 1753379942618, "results": "130", "hashOfConfig": "85"}, {"size": 149, "mtime": 1753379942619, "results": "131", "hashOfConfig": "85"}, {"size": 4200, "mtime": 1753379942614, "results": "132", "hashOfConfig": "85"}, {"size": 2526, "mtime": 1753379942611, "results": "133", "hashOfConfig": "85"}, {"size": 2754, "mtime": 1753379942611, "results": "134", "hashOfConfig": "85"}, {"size": 2779, "mtime": 1753379942613, "results": "135", "hashOfConfig": "85"}, {"size": 1326, "mtime": 1753379942613, "results": "136", "hashOfConfig": "85"}, {"size": 494, "mtime": 1753379942613, "results": "137", "hashOfConfig": "85"}, {"size": 1320, "mtime": 1753379942612, "results": "138", "hashOfConfig": "85"}, {"size": 635, "mtime": 1753379942612, "results": "139", "hashOfConfig": "85"}, {"size": 1276, "mtime": 1753379942633, "results": "140", "hashOfConfig": "85"}, {"size": 221, "mtime": 1753379942623, "results": "141", "hashOfConfig": "85"}, {"size": 591, "mtime": 1753379942633, "results": "142", "hashOfConfig": "85"}, {"size": 2083, "mtime": 1753379942632, "results": "143", "hashOfConfig": "85"}, {"size": 355, "mtime": 1753379942632, "results": "144", "hashOfConfig": "85"}, {"size": 476, "mtime": 1753379942623, "results": "145", "hashOfConfig": "85"}, {"size": 45, "mtime": 1753379942616, "results": "146", "hashOfConfig": "85"}, {"size": 231, "mtime": 1753379942632, "results": "147", "hashOfConfig": "85"}, {"size": 97, "mtime": 1753379942632, "results": "148", "hashOfConfig": "85"}, {"size": 262, "mtime": 1753379942633, "results": "149", "hashOfConfig": "85"}, {"size": 610, "mtime": 1753379942614, "results": "150", "hashOfConfig": "85"}, {"size": 6749, "mtime": 1753379942622, "results": "151", "hashOfConfig": "85"}, {"size": 1070, "mtime": 1753379942620, "results": "152", "hashOfConfig": "85"}, {"size": 299, "mtime": 1753379942633, "results": "153", "hashOfConfig": "85"}, {"size": 3932, "mtime": 1753379942626, "results": "154", "hashOfConfig": "85"}, {"size": 5026, "mtime": 1753379942615, "results": "155", "hashOfConfig": "85"}, {"size": 2449, "mtime": 1753379942626, "results": "156", "hashOfConfig": "85"}, {"size": 3040, "mtime": 1753379942627, "results": "157", "hashOfConfig": "85"}, {"size": 5512, "mtime": 1753379942627, "results": "158", "hashOfConfig": "85"}, {"size": 68, "mtime": 1753379942616, "results": "159", "hashOfConfig": "85"}, {"size": 3831, "mtime": 1753379942610, "results": "160", "hashOfConfig": "85"}, {"size": 413, "mtime": 1753379942618, "results": "161", "hashOfConfig": "85"}, {"size": 135, "mtime": 1753379942616, "results": "162", "hashOfConfig": "85"}, {"size": 8989, "mtime": 1753379942622, "results": "163", "hashOfConfig": "85"}, {"size": 649, "mtime": 1753382456558, "results": "164", "hashOfConfig": "85"}, {"size": 3292, "mtime": 1753382709343, "results": "165", "hashOfConfig": "85"}, {"size": 372, "mtime": 1753381693125, "results": "166", "hashOfConfig": "85"}, {"size": 359, "mtime": 1753382778104, "results": "167", "hashOfConfig": "85"}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1jzqadv", {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\App.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\store.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\routes.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\events.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\Layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\customer-service.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\order-service.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\zone-service.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\plant-service.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\require-auth.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\detail-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\plants\\plants-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\zones\\zones-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\auth-provider.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\Login.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\zones\\detail-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\orders-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\plants\\detail-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\customers\\customers-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\customers\\detail-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\users\\users-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\driver-task-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\zones\\Detail.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\users\\Detail.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\users\\List.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\Detail.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\List.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\New.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\zones\\List.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\customers\\Detail.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\plants\\List.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\customers\\List.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\plants\\Detail.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\ByStickDate.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\ByPinchDate.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\ByFlowerDate.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\Detail.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\List.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\service-base.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\NavMenu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\BySpaceDate.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\colours\\Detail.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\pages\\Index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\colours\\List.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\auth-context.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\use-auth.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\reports-service.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\auth-service.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\driver-tasks-service.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\orders.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\plants.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\zones.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\driver-tasks.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\customers.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\sort.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\loading\\Loading.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\problem-details.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\format.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\equals.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\error\\Error.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\boot\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\focus.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\class-names.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\weeks.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\notifications-service.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\ListFilters.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\List-Date.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\utils\\guid.ts", [], ["417", "418"], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\OrderRow.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\app\\database.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\LabourReport.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\SalesWeekRow.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\orders\\Variety.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\boot\\axios.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\api-base.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\auth\\configuration.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\boot\\font-awesome.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\driver-tasks\\List-Item.tsx", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\colours\\colours-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\features\\colours\\detail-slice.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\models\\colour.ts", [], [], "C:\\Users\\<USER>\\Documents\\projects\\GreenRP\\Web\\ClientApp\\src\\api\\colour-service.ts", [], [], {"ruleId": "419", "severity": 1, "message": "420", "line": 5, "column": 60, "nodeType": "421", "messageId": "422", "endLine": 5, "endColumn": 61, "suppressions": "423"}, {"ruleId": "419", "severity": 1, "message": "420", "line": 5, "column": 66, "nodeType": "421", "messageId": "422", "endLine": 5, "endColumn": 67, "suppressions": "424"}, "no-mixed-operators", "Unexpected mix of '&' and '|'. Use parentheses to clarify the intended order of operations.", "BinaryExpression", "unexpectedMixedOperator", ["425"], ["426"], {"kind": "427", "justification": "428"}, {"kind": "427", "justification": "428"}, "directive", ""]