{"ast": null, "code": "var getBuiltIn = require('../internals/get-built-in');\nmodule.exports = getBuiltIn('navigator', 'userAgent') || '';", "map": {"version": 3, "names": ["getBuiltIn", "require", "module", "exports"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/node_modules/core-js-pure/internals/engine-user-agent.js"], "sourcesContent": ["var getBuiltIn = require('../internals/get-built-in');\n\nmodule.exports = getBuiltIn('navigator', 'userAgent') || '';\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AAErDC,MAAM,CAACC,OAAO,GAAGH,UAAU,CAAC,WAAW,EAAE,WAAW,CAAC,IAAI,EAAE"}, "metadata": {}, "sourceType": "script"}