{"ast": null, "code": "import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { createPopper as defaultCreatePopper } from '@popperjs/core';\nimport isEqual from 'react-fast-compare';\nimport { fromEntries, useIsomorphicLayoutEffect } from './utils';\nvar EMPTY_MODIFIERS = [];\nexport var usePopper = function usePopper(referenceElement, popperElement, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var prevOptions = React.useRef(null);\n  var optionsWithDefaults = {\n    onFirstUpdate: options.onFirstUpdate,\n    placement: options.placement || 'bottom',\n    strategy: options.strategy || 'absolute',\n    modifiers: options.modifiers || EMPTY_MODIFIERS\n  };\n  var _React$useState = React.useState({\n      styles: {\n        popper: {\n          position: optionsWithDefaults.strategy,\n          left: '0',\n          top: '0'\n        },\n        arrow: {\n          position: 'absolute'\n        }\n      },\n      attributes: {}\n    }),\n    state = _React$useState[0],\n    setState = _React$useState[1];\n  var updateStateModifier = React.useMemo(function () {\n    return {\n      name: 'updateState',\n      enabled: true,\n      phase: 'write',\n      fn: function fn(_ref) {\n        var state = _ref.state;\n        var elements = Object.keys(state.elements);\n        ReactDOM.flushSync(function () {\n          setState({\n            styles: fromEntries(elements.map(function (element) {\n              return [element, state.styles[element] || {}];\n            })),\n            attributes: fromEntries(elements.map(function (element) {\n              return [element, state.attributes[element]];\n            }))\n          });\n        });\n      },\n      requires: ['computeStyles']\n    };\n  }, []);\n  var popperOptions = React.useMemo(function () {\n    var newOptions = {\n      onFirstUpdate: optionsWithDefaults.onFirstUpdate,\n      placement: optionsWithDefaults.placement,\n      strategy: optionsWithDefaults.strategy,\n      modifiers: [].concat(optionsWithDefaults.modifiers, [updateStateModifier, {\n        name: 'applyStyles',\n        enabled: false\n      }])\n    };\n    if (isEqual(prevOptions.current, newOptions)) {\n      return prevOptions.current || newOptions;\n    } else {\n      prevOptions.current = newOptions;\n      return newOptions;\n    }\n  }, [optionsWithDefaults.onFirstUpdate, optionsWithDefaults.placement, optionsWithDefaults.strategy, optionsWithDefaults.modifiers, updateStateModifier]);\n  var popperInstanceRef = React.useRef();\n  useIsomorphicLayoutEffect(function () {\n    if (popperInstanceRef.current) {\n      popperInstanceRef.current.setOptions(popperOptions);\n    }\n  }, [popperOptions]);\n  useIsomorphicLayoutEffect(function () {\n    if (referenceElement == null || popperElement == null) {\n      return;\n    }\n    var createPopper = options.createPopper || defaultCreatePopper;\n    var popperInstance = createPopper(referenceElement, popperElement, popperOptions);\n    popperInstanceRef.current = popperInstance;\n    return function () {\n      popperInstance.destroy();\n      popperInstanceRef.current = null;\n    };\n  }, [referenceElement, popperElement, options.createPopper]);\n  return {\n    state: popperInstanceRef.current ? popperInstanceRef.current.state : null,\n    styles: state.styles,\n    attributes: state.attributes,\n    update: popperInstanceRef.current ? popperInstanceRef.current.update : null,\n    forceUpdate: popperInstanceRef.current ? popperInstanceRef.current.forceUpdate : null\n  };\n};", "map": {"version": 3, "names": ["React", "ReactDOM", "createPopper", "defaultCreatePopper", "isEqual", "fromEntries", "useIsomorphicLayoutEffect", "EMPTY_MODIFIERS", "usePopper", "referenceElement", "popper<PERSON>lement", "options", "prevOptions", "useRef", "optionsWithDefaults", "onFirstUpdate", "placement", "strategy", "modifiers", "_React$useState", "useState", "styles", "popper", "position", "left", "top", "arrow", "attributes", "state", "setState", "updateStateModifier", "useMemo", "name", "enabled", "phase", "fn", "_ref", "elements", "Object", "keys", "flushSync", "map", "element", "requires", "popperOptions", "newOptions", "concat", "current", "popperInstanceRef", "setOptions", "popperInstance", "destroy", "update", "forceUpdate"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/node_modules/react-popper/lib/esm/usePopper.js"], "sourcesContent": ["import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { createPopper as defaultCreatePopper } from '@popperjs/core';\nimport isEqual from 'react-fast-compare';\nimport { fromEntries, useIsomorphicLayoutEffect } from './utils';\nvar EMPTY_MODIFIERS = [];\nexport var usePopper = function usePopper(referenceElement, popperElement, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var prevOptions = React.useRef(null);\n  var optionsWithDefaults = {\n    onFirstUpdate: options.onFirstUpdate,\n    placement: options.placement || 'bottom',\n    strategy: options.strategy || 'absolute',\n    modifiers: options.modifiers || EMPTY_MODIFIERS\n  };\n\n  var _React$useState = React.useState({\n    styles: {\n      popper: {\n        position: optionsWithDefaults.strategy,\n        left: '0',\n        top: '0'\n      },\n      arrow: {\n        position: 'absolute'\n      }\n    },\n    attributes: {}\n  }),\n      state = _React$useState[0],\n      setState = _React$useState[1];\n\n  var updateStateModifier = React.useMemo(function () {\n    return {\n      name: 'updateState',\n      enabled: true,\n      phase: 'write',\n      fn: function fn(_ref) {\n        var state = _ref.state;\n        var elements = Object.keys(state.elements);\n        ReactDOM.flushSync(function () {\n          setState({\n            styles: fromEntries(elements.map(function (element) {\n              return [element, state.styles[element] || {}];\n            })),\n            attributes: fromEntries(elements.map(function (element) {\n              return [element, state.attributes[element]];\n            }))\n          });\n        });\n      },\n      requires: ['computeStyles']\n    };\n  }, []);\n  var popperOptions = React.useMemo(function () {\n    var newOptions = {\n      onFirstUpdate: optionsWithDefaults.onFirstUpdate,\n      placement: optionsWithDefaults.placement,\n      strategy: optionsWithDefaults.strategy,\n      modifiers: [].concat(optionsWithDefaults.modifiers, [updateStateModifier, {\n        name: 'applyStyles',\n        enabled: false\n      }])\n    };\n\n    if (isEqual(prevOptions.current, newOptions)) {\n      return prevOptions.current || newOptions;\n    } else {\n      prevOptions.current = newOptions;\n      return newOptions;\n    }\n  }, [optionsWithDefaults.onFirstUpdate, optionsWithDefaults.placement, optionsWithDefaults.strategy, optionsWithDefaults.modifiers, updateStateModifier]);\n  var popperInstanceRef = React.useRef();\n  useIsomorphicLayoutEffect(function () {\n    if (popperInstanceRef.current) {\n      popperInstanceRef.current.setOptions(popperOptions);\n    }\n  }, [popperOptions]);\n  useIsomorphicLayoutEffect(function () {\n    if (referenceElement == null || popperElement == null) {\n      return;\n    }\n\n    var createPopper = options.createPopper || defaultCreatePopper;\n    var popperInstance = createPopper(referenceElement, popperElement, popperOptions);\n    popperInstanceRef.current = popperInstance;\n    return function () {\n      popperInstance.destroy();\n      popperInstanceRef.current = null;\n    };\n  }, [referenceElement, popperElement, options.createPopper]);\n  return {\n    state: popperInstanceRef.current ? popperInstanceRef.current.state : null,\n    styles: state.styles,\n    attributes: state.attributes,\n    update: popperInstanceRef.current ? popperInstanceRef.current.update : null,\n    forceUpdate: popperInstanceRef.current ? popperInstanceRef.current.forceUpdate : null\n  };\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,QAAQ,MAAM,WAAW;AACrC,SAASC,YAAY,IAAIC,mBAAmB,QAAQ,gBAAgB;AACpE,OAAOC,OAAO,MAAM,oBAAoB;AACxC,SAASC,WAAW,EAAEC,yBAAyB,QAAQ,SAAS;AAChE,IAAIC,eAAe,GAAG,EAAE;AACxB,OAAO,IAAIC,SAAS,GAAG,SAASA,SAAS,CAACC,gBAAgB,EAAEC,aAAa,EAAEC,OAAO,EAAE;EAClF,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EAEA,IAAIC,WAAW,GAAGZ,KAAK,CAACa,MAAM,CAAC,IAAI,CAAC;EACpC,IAAIC,mBAAmB,GAAG;IACxBC,aAAa,EAAEJ,OAAO,CAACI,aAAa;IACpCC,SAAS,EAAEL,OAAO,CAACK,SAAS,IAAI,QAAQ;IACxCC,QAAQ,EAAEN,OAAO,CAACM,QAAQ,IAAI,UAAU;IACxCC,SAAS,EAAEP,OAAO,CAACO,SAAS,IAAIX;EAClC,CAAC;EAED,IAAIY,eAAe,GAAGnB,KAAK,CAACoB,QAAQ,CAAC;MACnCC,MAAM,EAAE;QACNC,MAAM,EAAE;UACNC,QAAQ,EAAET,mBAAmB,CAACG,QAAQ;UACtCO,IAAI,EAAE,GAAG;UACTC,GAAG,EAAE;QACP,CAAC;QACDC,KAAK,EAAE;UACLH,QAAQ,EAAE;QACZ;MACF,CAAC;MACDI,UAAU,EAAE,CAAC;IACf,CAAC,CAAC;IACEC,KAAK,GAAGT,eAAe,CAAC,CAAC,CAAC;IAC1BU,QAAQ,GAAGV,eAAe,CAAC,CAAC,CAAC;EAEjC,IAAIW,mBAAmB,GAAG9B,KAAK,CAAC+B,OAAO,CAAC,YAAY;IAClD,OAAO;MACLC,IAAI,EAAE,aAAa;MACnBC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,OAAO;MACdC,EAAE,EAAE,SAASA,EAAE,CAACC,IAAI,EAAE;QACpB,IAAIR,KAAK,GAAGQ,IAAI,CAACR,KAAK;QACtB,IAAIS,QAAQ,GAAGC,MAAM,CAACC,IAAI,CAACX,KAAK,CAACS,QAAQ,CAAC;QAC1CpC,QAAQ,CAACuC,SAAS,CAAC,YAAY;UAC7BX,QAAQ,CAAC;YACPR,MAAM,EAAEhB,WAAW,CAACgC,QAAQ,CAACI,GAAG,CAAC,UAAUC,OAAO,EAAE;cAClD,OAAO,CAACA,OAAO,EAAEd,KAAK,CAACP,MAAM,CAACqB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;YACHf,UAAU,EAAEtB,WAAW,CAACgC,QAAQ,CAACI,GAAG,CAAC,UAAUC,OAAO,EAAE;cACtD,OAAO,CAACA,OAAO,EAAEd,KAAK,CAACD,UAAU,CAACe,OAAO,CAAC,CAAC;YAC7C,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC;MACDC,QAAQ,EAAE,CAAC,eAAe;IAC5B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,IAAIC,aAAa,GAAG5C,KAAK,CAAC+B,OAAO,CAAC,YAAY;IAC5C,IAAIc,UAAU,GAAG;MACf9B,aAAa,EAAED,mBAAmB,CAACC,aAAa;MAChDC,SAAS,EAAEF,mBAAmB,CAACE,SAAS;MACxCC,QAAQ,EAAEH,mBAAmB,CAACG,QAAQ;MACtCC,SAAS,EAAE,EAAE,CAAC4B,MAAM,CAAChC,mBAAmB,CAACI,SAAS,EAAE,CAACY,mBAAmB,EAAE;QACxEE,IAAI,EAAE,aAAa;QACnBC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC;IAED,IAAI7B,OAAO,CAACQ,WAAW,CAACmC,OAAO,EAAEF,UAAU,CAAC,EAAE;MAC5C,OAAOjC,WAAW,CAACmC,OAAO,IAAIF,UAAU;IAC1C,CAAC,MAAM;MACLjC,WAAW,CAACmC,OAAO,GAAGF,UAAU;MAChC,OAAOA,UAAU;IACnB;EACF,CAAC,EAAE,CAAC/B,mBAAmB,CAACC,aAAa,EAAED,mBAAmB,CAACE,SAAS,EAAEF,mBAAmB,CAACG,QAAQ,EAAEH,mBAAmB,CAACI,SAAS,EAAEY,mBAAmB,CAAC,CAAC;EACxJ,IAAIkB,iBAAiB,GAAGhD,KAAK,CAACa,MAAM,EAAE;EACtCP,yBAAyB,CAAC,YAAY;IACpC,IAAI0C,iBAAiB,CAACD,OAAO,EAAE;MAC7BC,iBAAiB,CAACD,OAAO,CAACE,UAAU,CAACL,aAAa,CAAC;IACrD;EACF,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EACnBtC,yBAAyB,CAAC,YAAY;IACpC,IAAIG,gBAAgB,IAAI,IAAI,IAAIC,aAAa,IAAI,IAAI,EAAE;MACrD;IACF;IAEA,IAAIR,YAAY,GAAGS,OAAO,CAACT,YAAY,IAAIC,mBAAmB;IAC9D,IAAI+C,cAAc,GAAGhD,YAAY,CAACO,gBAAgB,EAAEC,aAAa,EAAEkC,aAAa,CAAC;IACjFI,iBAAiB,CAACD,OAAO,GAAGG,cAAc;IAC1C,OAAO,YAAY;MACjBA,cAAc,CAACC,OAAO,EAAE;MACxBH,iBAAiB,CAACD,OAAO,GAAG,IAAI;IAClC,CAAC;EACH,CAAC,EAAE,CAACtC,gBAAgB,EAAEC,aAAa,EAAEC,OAAO,CAACT,YAAY,CAAC,CAAC;EAC3D,OAAO;IACL0B,KAAK,EAAEoB,iBAAiB,CAACD,OAAO,GAAGC,iBAAiB,CAACD,OAAO,CAACnB,KAAK,GAAG,IAAI;IACzEP,MAAM,EAAEO,KAAK,CAACP,MAAM;IACpBM,UAAU,EAAEC,KAAK,CAACD,UAAU;IAC5ByB,MAAM,EAAEJ,iBAAiB,CAACD,OAAO,GAAGC,iBAAiB,CAACD,OAAO,CAACK,MAAM,GAAG,IAAI;IAC3EC,WAAW,EAAEL,iBAAiB,CAACD,OAAO,GAAGC,iBAAiB,CAACD,OAAO,CAACM,WAAW,GAAG;EACnF,CAAC;AACH,CAAC"}, "metadata": {}, "sourceType": "module"}