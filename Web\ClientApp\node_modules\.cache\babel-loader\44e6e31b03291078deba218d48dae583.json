{"ast": null, "code": "import platform from './node/index.js';\nexport { platform as default };", "map": {"version": 3, "names": ["platform", "default"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/node_modules/axios/lib/platform/index.js"], "sourcesContent": ["import platform from './node/index.js';\n\nexport {platform as default}\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,iBAAiB;AAEtC,SAAQA,QAAQ,IAAIC,OAAO"}, "metadata": {}, "sourceType": "module"}