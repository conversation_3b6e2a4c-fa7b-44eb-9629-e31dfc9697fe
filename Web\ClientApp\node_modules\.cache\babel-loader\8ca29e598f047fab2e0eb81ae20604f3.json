{"ast": null, "code": "import * as React from 'react';\n\n/**\n * Takes an argument and if it's an array, returns the first item in the array,\n * otherwise returns the argument. Used for Preact compatibility.\n */\nexport var unwrapArray = function unwrapArray(arg) {\n  return Array.isArray(arg) ? arg[0] : arg;\n};\n/**\n * Takes a maybe-undefined function and arbitrary args and invokes the function\n * only if it is defined.\n */\n\nexport var safeInvoke = function safeInvoke(fn) {\n  if (typeof fn === 'function') {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    return fn.apply(void 0, args);\n  }\n};\n/**\n * Sets a ref using either a ref callback or a ref object\n */\n\nexport var setRef = function setRef(ref, node) {\n  // if its a function call it\n  if (typeof ref === 'function') {\n    return safeInvoke(ref, node);\n  } // otherwise we should treat it as a ref object\n  else if (ref != null) {\n    ref.current = node;\n  }\n};\n/**\n * Simple ponyfill for Object.fromEntries\n */\n\nexport var fromEntries = function fromEntries(entries) {\n  return entries.reduce(function (acc, _ref) {\n    var key = _ref[0],\n      value = _ref[1];\n    acc[key] = value;\n    return acc;\n  }, {});\n};\n/**\n * Small wrapper around `useLayoutEffect` to get rid of the warning on SSR envs\n */\n\nexport var useIsomorphicLayoutEffect = typeof window !== 'undefined' && window.document && window.document.createElement ? React.useLayoutEffect : React.useEffect;", "map": {"version": 3, "names": ["React", "unwrapArray", "arg", "Array", "isArray", "safeInvoke", "fn", "_len", "arguments", "length", "args", "_key", "apply", "setRef", "ref", "node", "current", "fromEntries", "entries", "reduce", "acc", "_ref", "key", "value", "useIsomorphicLayoutEffect", "window", "document", "createElement", "useLayoutEffect", "useEffect"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/node_modules/react-popper/lib/esm/utils.js"], "sourcesContent": ["import * as React from 'react';\n\n/**\n * Takes an argument and if it's an array, returns the first item in the array,\n * otherwise returns the argument. Used for Preact compatibility.\n */\nexport var unwrapArray = function unwrapArray(arg) {\n  return Array.isArray(arg) ? arg[0] : arg;\n};\n/**\n * Takes a maybe-undefined function and arbitrary args and invokes the function\n * only if it is defined.\n */\n\nexport var safeInvoke = function safeInvoke(fn) {\n  if (typeof fn === 'function') {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n\n    return fn.apply(void 0, args);\n  }\n};\n/**\n * Sets a ref using either a ref callback or a ref object\n */\n\nexport var setRef = function setRef(ref, node) {\n  // if its a function call it\n  if (typeof ref === 'function') {\n    return safeInvoke(ref, node);\n  } // otherwise we should treat it as a ref object\n  else if (ref != null) {\n      ref.current = node;\n    }\n};\n/**\n * Simple ponyfill for Object.fromEntries\n */\n\nexport var fromEntries = function fromEntries(entries) {\n  return entries.reduce(function (acc, _ref) {\n    var key = _ref[0],\n        value = _ref[1];\n    acc[key] = value;\n    return acc;\n  }, {});\n};\n/**\n * Small wrapper around `useLayoutEffect` to get rid of the warning on SSR envs\n */\n\nexport var useIsomorphicLayoutEffect = typeof window !== 'undefined' && window.document && window.document.createElement ? React.useLayoutEffect : React.useEffect;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;;AAE9B;AACA;AACA;AACA;AACA,OAAO,IAAIC,WAAW,GAAG,SAASA,WAAW,CAACC,GAAG,EAAE;EACjD,OAAOC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG;AAC1C,CAAC;AACD;AACA;AACA;AACA;;AAEA,OAAO,IAAIG,UAAU,GAAG,SAASA,UAAU,CAACC,EAAE,EAAE;EAC9C,IAAI,OAAOA,EAAE,KAAK,UAAU,EAAE;IAC5B,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIP,KAAK,CAACI,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEI,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGJ,IAAI,EAAEI,IAAI,EAAE,EAAE;MAC1GD,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC,GAAGH,SAAS,CAACG,IAAI,CAAC;IAClC;IAEA,OAAOL,EAAE,CAACM,KAAK,CAAC,KAAK,CAAC,EAAEF,IAAI,CAAC;EAC/B;AACF,CAAC;AACD;AACA;AACA;;AAEA,OAAO,IAAIG,MAAM,GAAG,SAASA,MAAM,CAACC,GAAG,EAAEC,IAAI,EAAE;EAC7C;EACA,IAAI,OAAOD,GAAG,KAAK,UAAU,EAAE;IAC7B,OAAOT,UAAU,CAACS,GAAG,EAAEC,IAAI,CAAC;EAC9B,CAAC,CAAC;EAAA,KACG,IAAID,GAAG,IAAI,IAAI,EAAE;IAClBA,GAAG,CAACE,OAAO,GAAGD,IAAI;EACpB;AACJ,CAAC;AACD;AACA;AACA;;AAEA,OAAO,IAAIE,WAAW,GAAG,SAASA,WAAW,CAACC,OAAO,EAAE;EACrD,OAAOA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAEC,IAAI,EAAE;IACzC,IAAIC,GAAG,GAAGD,IAAI,CAAC,CAAC,CAAC;MACbE,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC;IACnBD,GAAG,CAACE,GAAG,CAAC,GAAGC,KAAK;IAChB,OAAOH,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AACR,CAAC;AACD;AACA;AACA;;AAEA,OAAO,IAAII,yBAAyB,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,QAAQ,IAAID,MAAM,CAACC,QAAQ,CAACC,aAAa,GAAG3B,KAAK,CAAC4B,eAAe,GAAG5B,KAAK,CAAC6B,SAAS"}, "metadata": {}, "sourceType": "module"}