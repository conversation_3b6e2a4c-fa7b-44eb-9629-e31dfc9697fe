{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\GreenRP\\\\Web\\\\ClientApp\\\\src\\\\features\\\\colours\\\\List.tsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport function List() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"Colour List\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 3,\n    columnNumber: 10\n  }, this);\n}\n_c = List;\nexport default List;\nvar _c;\n$RefreshReg$(_c, \"List\");", "map": {"version": 3, "names": ["List"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/colours/List.tsx"], "sourcesContent": ["\r\nexport function List() {\r\n  return <div>Colour List</div>;\r\n}\r\n\r\nexport default List;"], "mappings": ";;AACA,OAAO,SAASA,IAAI,GAAG;EACrB,oBAAO;IAAA;EAAA;IAAA;IAAA;IAAA;EAAA,QAAsB;AAC/B;AAAC,KAFeA,IAAI;AAIpB,eAAeA,IAAI;AAAC;AAAA"}, "metadata": {}, "sourceType": "module"}