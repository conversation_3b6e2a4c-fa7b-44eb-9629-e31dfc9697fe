{"ast": null, "code": "import { createAction, createAsyncThunk, createSlice } from '@reduxjs/toolkit';\nimport { createZone } from 'api/models/zones';\nimport { zoneApi } from 'api/zone-service';\nconst initialState = {\n  isLoading: false,\n  zone: createZone(),\n  error: null\n};\nexport const saveZone = createAsyncThunk('zone-detail/save-zone', async (_, _ref) => {\n  let {\n    rejectWithValue,\n    getState\n  } = _ref;\n  try {\n    const zone = getState().zoneDetail.zone,\n      doc = {\n        ...zone\n      };\n    const updated = await zoneApi.save(doc);\n    return updated;\n  } catch (e) {\n    return rejectWithValue(e);\n  }\n});\nexport const deleteZone = createAsyncThunk('zone-detail/delete-zone', async (_, _ref2) => {\n  let {\n    rejectWithValue,\n    getState\n  } = _ref2;\n  try {\n    const zone = getState().zoneDetail.zone,\n      doc = {\n        ...zone\n      };\n    const updated = await zoneApi.delete(doc);\n    return updated;\n  } catch (e) {\n    return rejectWithValue(e);\n  }\n});\nconst savePending = createAction(saveZone.pending.type),\n  saveFulfilled = createAction(saveZone.fulfilled.type),\n  saveRejected = createAction(saveZone.rejected.type),\n  deletePending = createAction(deleteZone.pending.type),\n  deleteFulfilled = createAction(deleteZone.fulfilled.type),\n  deleteRejected = createAction(deleteZone.rejected.type);\nexport const zoneDetailSlice = createSlice({\n  name: 'zone-detail',\n  initialState,\n  reducers: {\n    setZone(state, action) {\n      state.zone = action.payload;\n    }\n  },\n  extraReducers: builder => builder.addCase(savePending, state => {\n    state.isLoading = true;\n    state.error = null;\n  }).addCase(saveFulfilled, (state, action) => {\n    state.isLoading = false;\n    if (action.payload) {\n      state.zone = action.payload;\n    }\n  }).addCase(saveRejected, (state, action) => {\n    state.isLoading = false;\n    state.error = action.payload;\n  }).addCase(deletePending, state => {\n    state.isLoading = true;\n    state.error = null;\n  }).addCase(deleteFulfilled, (state, action) => {\n    state.isLoading = false;\n    state.zone = createZone();\n  }).addCase(deleteRejected, (state, action) => {\n    state.isLoading = false;\n    state.error = action.payload;\n  })\n});\nexport const {\n  setZone\n} = zoneDetailSlice.actions;\nexport const selectZone = state => state.zoneDetail.zone;\nexport const selectIsLoading = state => state.zoneDetail.isLoading;\nexport const selectError = state => state.zoneDetail.error;\nexport default zoneDetailSlice.reducer;", "map": {"version": 3, "names": ["createAction", "createAsyncThunk", "createSlice", "createZone", "zoneApi", "initialState", "isLoading", "zone", "error", "saveZone", "_", "rejectWithValue", "getState", "zoneDetail", "doc", "updated", "save", "e", "deleteZone", "delete", "savePending", "pending", "type", "saveFulfilled", "fulfilled", "saveRejected", "rejected", "deletePending", "deleteFulfilled", "deleteRejected", "zoneDetailSlice", "name", "reducers", "setZone", "state", "action", "payload", "extraReducers", "builder", "addCase", "actions", "selectZone", "selectIsLoading", "selectError", "reducer"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/zones/detail-slice.ts"], "sourcesContent": ["import { AsyncThunk, createAction, createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';\r\nimport { createZone, Zone } from 'api/models/zones';\r\nimport { zoneApi } from 'api/zone-service';\r\nimport { RootState } from 'app/store';\r\nimport { ProblemDetails } from 'utils/problem-details';\r\n\r\ninterface ZoneDetailState {\r\n  isLoading: boolean;\r\n  zone: Zone;\r\n  error: ProblemDetails | null;\r\n}\r\n\r\nconst initialState: ZoneDetailState = {\r\n  isLoading: false,\r\n  zone: createZone(),\r\n  error: null\r\n};\r\n\r\nexport const saveZone: AsyncThunk<Zone, void, {state: RootState}> = createAsyncThunk(\r\n  'zone-detail/save-zone',\r\n  async (_, {rejectWithValue, getState}) => {\r\n    try {\r\n\r\n      const zone = (getState() as RootState).zoneDetail.zone,\r\n        doc = {...zone};\r\n      \r\n      const updated = await zoneApi.save(doc);\r\n      return updated;\r\n\r\n    } catch(e) {\r\n      return rejectWithValue(e as ProblemDetails);\r\n    }\r\n  }\r\n);\r\n\r\nexport const deleteZone: AsyncThunk<void, void, {state: RootState}> = createAsyncThunk(\r\n  'zone-detail/delete-zone',\r\n  async (_, {rejectWithValue, getState}) => {\r\n    try {\r\n\r\n      const zone = (getState() as RootState).zoneDetail.zone,\r\n        doc = {...zone};\r\n      \r\n      const updated = await zoneApi.delete(doc);\r\n      return updated;\r\n\r\n    } catch(e) {\r\n      return rejectWithValue(e as ProblemDetails);\r\n    }\r\n  }\r\n);\r\n\r\nconst savePending = createAction(saveZone.pending.type),\r\n  saveFulfilled = createAction<Zone | undefined>(saveZone.fulfilled.type),\r\n  saveRejected = createAction<ProblemDetails>(saveZone.rejected.type),\r\n  deletePending = createAction(deleteZone.pending.type),\r\n  deleteFulfilled = createAction(deleteZone.fulfilled.type),\r\n  deleteRejected = createAction<ProblemDetails>(deleteZone.rejected.type);\r\n\r\nexport const zoneDetailSlice = createSlice({\r\n  name: 'zone-detail',\r\n  initialState,\r\n  reducers: {\r\n    setZone(state, action: PayloadAction<Zone>) {\r\n      state.zone = action.payload;\r\n    }\r\n  },\r\n  extraReducers: builder =>\r\n    builder\r\n      .addCase(savePending, state => {\r\n        state.isLoading = true;\r\n        state.error = null;\r\n      })\r\n      .addCase(saveFulfilled, (state, action) => {\r\n        state.isLoading = false;\r\n        if(action.payload) {\r\n          state.zone = action.payload;\r\n        }\r\n      })\r\n      .addCase(saveRejected, (state, action) => {\r\n        state.isLoading = false;\r\n        state.error = action.payload;\r\n      })\r\n      .addCase(deletePending, state => {\r\n        state.isLoading = true;\r\n        state.error = null;\r\n      })\r\n      .addCase(deleteFulfilled, (state, action) => {\r\n        state.isLoading = false;\r\n        state.zone = createZone();\r\n      })\r\n      .addCase(deleteRejected, (state, action) => {\r\n        state.isLoading = false;\r\n        state.error = action.payload;\r\n      })\r\n});\r\n\r\nexport const { setZone } = zoneDetailSlice.actions;\r\n\r\nexport const selectZone = (state: RootState) => state.zoneDetail.zone;\r\nexport const selectIsLoading = (state: RootState) => state.zoneDetail.isLoading;\r\nexport const selectError = (state: RootState) => state.zoneDetail.error;\r\n\r\nexport default zoneDetailSlice.reducer;\r\n"], "mappings": "AAAA,SAAqBA,YAAY,EAAEC,gBAAgB,EAAEC,WAAW,QAAuB,kBAAkB;AACzG,SAASC,UAAU,QAAc,kBAAkB;AACnD,SAASC,OAAO,QAAQ,kBAAkB;AAU1C,MAAMC,YAA6B,GAAG;EACpCC,SAAS,EAAE,KAAK;EAChBC,IAAI,EAAEJ,UAAU,EAAE;EAClBK,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,QAAoD,GAAGR,gBAAgB,CAClF,uBAAuB,EACvB,OAAOS,CAAC,WAAkC;EAAA,IAAhC;IAACC,eAAe;IAAEC;EAAQ,CAAC;EACnC,IAAI;IAEF,MAAML,IAAI,GAAIK,QAAQ,EAAE,CAAeC,UAAU,CAACN,IAAI;MACpDO,GAAG,GAAG;QAAC,GAAGP;MAAI,CAAC;IAEjB,MAAMQ,OAAO,GAAG,MAAMX,OAAO,CAACY,IAAI,CAACF,GAAG,CAAC;IACvC,OAAOC,OAAO;EAEhB,CAAC,CAAC,OAAME,CAAC,EAAE;IACT,OAAON,eAAe,CAACM,CAAC,CAAmB;EAC7C;AACF,CAAC,CACF;AAED,OAAO,MAAMC,UAAsD,GAAGjB,gBAAgB,CACpF,yBAAyB,EACzB,OAAOS,CAAC,YAAkC;EAAA,IAAhC;IAACC,eAAe;IAAEC;EAAQ,CAAC;EACnC,IAAI;IAEF,MAAML,IAAI,GAAIK,QAAQ,EAAE,CAAeC,UAAU,CAACN,IAAI;MACpDO,GAAG,GAAG;QAAC,GAAGP;MAAI,CAAC;IAEjB,MAAMQ,OAAO,GAAG,MAAMX,OAAO,CAACe,MAAM,CAACL,GAAG,CAAC;IACzC,OAAOC,OAAO;EAEhB,CAAC,CAAC,OAAME,CAAC,EAAE;IACT,OAAON,eAAe,CAACM,CAAC,CAAmB;EAC7C;AACF,CAAC,CACF;AAED,MAAMG,WAAW,GAAGpB,YAAY,CAACS,QAAQ,CAACY,OAAO,CAACC,IAAI,CAAC;EACrDC,aAAa,GAAGvB,YAAY,CAAmBS,QAAQ,CAACe,SAAS,CAACF,IAAI,CAAC;EACvEG,YAAY,GAAGzB,YAAY,CAAiBS,QAAQ,CAACiB,QAAQ,CAACJ,IAAI,CAAC;EACnEK,aAAa,GAAG3B,YAAY,CAACkB,UAAU,CAACG,OAAO,CAACC,IAAI,CAAC;EACrDM,eAAe,GAAG5B,YAAY,CAACkB,UAAU,CAACM,SAAS,CAACF,IAAI,CAAC;EACzDO,cAAc,GAAG7B,YAAY,CAAiBkB,UAAU,CAACQ,QAAQ,CAACJ,IAAI,CAAC;AAEzE,OAAO,MAAMQ,eAAe,GAAG5B,WAAW,CAAC;EACzC6B,IAAI,EAAE,aAAa;EACnB1B,YAAY;EACZ2B,QAAQ,EAAE;IACRC,OAAO,CAACC,KAAK,EAAEC,MAA2B,EAAE;MAC1CD,KAAK,CAAC3B,IAAI,GAAG4B,MAAM,CAACC,OAAO;IAC7B;EACF,CAAC;EACDC,aAAa,EAAEC,OAAO,IACpBA,OAAO,CACJC,OAAO,CAACnB,WAAW,EAAEc,KAAK,IAAI;IAC7BA,KAAK,CAAC5B,SAAS,GAAG,IAAI;IACtB4B,KAAK,CAAC1B,KAAK,GAAG,IAAI;EACpB,CAAC,CAAC,CACD+B,OAAO,CAAChB,aAAa,EAAE,CAACW,KAAK,EAAEC,MAAM,KAAK;IACzCD,KAAK,CAAC5B,SAAS,GAAG,KAAK;IACvB,IAAG6B,MAAM,CAACC,OAAO,EAAE;MACjBF,KAAK,CAAC3B,IAAI,GAAG4B,MAAM,CAACC,OAAO;IAC7B;EACF,CAAC,CAAC,CACDG,OAAO,CAACd,YAAY,EAAE,CAACS,KAAK,EAAEC,MAAM,KAAK;IACxCD,KAAK,CAAC5B,SAAS,GAAG,KAAK;IACvB4B,KAAK,CAAC1B,KAAK,GAAG2B,MAAM,CAACC,OAAO;EAC9B,CAAC,CAAC,CACDG,OAAO,CAACZ,aAAa,EAAEO,KAAK,IAAI;IAC/BA,KAAK,CAAC5B,SAAS,GAAG,IAAI;IACtB4B,KAAK,CAAC1B,KAAK,GAAG,IAAI;EACpB,CAAC,CAAC,CACD+B,OAAO,CAACX,eAAe,EAAE,CAACM,KAAK,EAAEC,MAAM,KAAK;IAC3CD,KAAK,CAAC5B,SAAS,GAAG,KAAK;IACvB4B,KAAK,CAAC3B,IAAI,GAAGJ,UAAU,EAAE;EAC3B,CAAC,CAAC,CACDoC,OAAO,CAACV,cAAc,EAAE,CAACK,KAAK,EAAEC,MAAM,KAAK;IAC1CD,KAAK,CAAC5B,SAAS,GAAG,KAAK;IACvB4B,KAAK,CAAC1B,KAAK,GAAG2B,MAAM,CAACC,OAAO;EAC9B,CAAC;AACP,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEH;AAAQ,CAAC,GAAGH,eAAe,CAACU,OAAO;AAElD,OAAO,MAAMC,UAAU,GAAIP,KAAgB,IAAKA,KAAK,CAACrB,UAAU,CAACN,IAAI;AACrE,OAAO,MAAMmC,eAAe,GAAIR,KAAgB,IAAKA,KAAK,CAACrB,UAAU,CAACP,SAAS;AAC/E,OAAO,MAAMqC,WAAW,GAAIT,KAAgB,IAAKA,KAAK,CAACrB,UAAU,CAACL,KAAK;AAEvE,eAAesB,eAAe,CAACc,OAAO"}, "metadata": {}, "sourceType": "module"}