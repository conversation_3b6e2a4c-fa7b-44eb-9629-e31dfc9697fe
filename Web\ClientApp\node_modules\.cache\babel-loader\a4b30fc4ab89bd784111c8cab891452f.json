{"ast": null, "code": "import { ServiceBase } from './service-base';\nclass CustomerService extends ServiceBase {\n  getAll() {\n    return this.query('filters/customers');\n  }\n  save(customer) {\n    return this.saveDocument(customer);\n  }\n}\nexport const customerApi = new CustomerService();", "map": {"version": 3, "names": ["ServiceBase", "CustomerService", "getAll", "query", "save", "customer", "saveDocument", "customerApi"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/api/customer-service.ts"], "sourcesContent": ["import * as models from './models/customers';\r\nimport { ServiceBase } from './service-base';\r\n\r\nclass CustomerService extends ServiceBase {\r\n  getAll() {\r\n    return this.query<models.Customer>('filters/customers');\r\n  }\r\n\r\n  save(customer: models.Customer) {\r\n    return this.saveDocument<models.Customer>(customer);\r\n  }\r\n}\r\n\r\nexport const customerApi = new CustomerService();\r\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,gBAAgB;AAE5C,MAAMC,eAAe,SAASD,WAAW,CAAC;EACxCE,MAAM,GAAG;IACP,OAAO,IAAI,CAACC,KAAK,CAAkB,mBAAmB,CAAC;EACzD;EAEAC,IAAI,CAACC,QAAyB,EAAE;IAC9B,OAAO,IAAI,CAACC,YAAY,CAAkBD,QAAQ,CAAC;EACrD;AACF;AAEA,OAAO,MAAME,WAAW,GAAG,IAAIN,eAAe,EAAE"}, "metadata": {}, "sourceType": "module"}