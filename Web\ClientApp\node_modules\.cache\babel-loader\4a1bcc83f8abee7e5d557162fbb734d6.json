{"ast": null, "code": "/******/(function () {\n  // webpackBootstrap\n  /******/\n  \"use strict\";\n\n  /******/\n  var __webpack_modules__ = {\n    /***/\"./node_modules/strip-ansi/index.js\":\n    /*!******************************************!*\\\n      !*** ./node_modules/strip-ansi/index.js ***!\n      \\******************************************/\n    /***/\n    function (__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {\n      __webpack_require__.r(__webpack_exports__);\n      /* harmony export */\n      __webpack_require__.d(__webpack_exports__, {\n        /* harmony export */\"default\": function () {\n          return (/* binding */stripAnsi\n          );\n        }\n        /* harmony export */\n      });\n      /* harmony import */\n      var ansi_regex__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__( /*! ansi-regex */\"./node_modules/strip-ansi/node_modules/ansi-regex/index.js\");\n      function stripAnsi(string) {\n        if (typeof string !== 'string') {\n          throw new TypeError(\"Expected a `string`, got `\".concat(typeof string, \"`\"));\n        }\n        return string.replace((0, ansi_regex__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(), '');\n      }\n\n      /***/\n    },\n\n    /***/\"./node_modules/strip-ansi/node_modules/ansi-regex/index.js\":\n    /*!******************************************************************!*\\\n      !*** ./node_modules/strip-ansi/node_modules/ansi-regex/index.js ***!\n      \\******************************************************************/\n    /***/\n    function (__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {\n      __webpack_require__.r(__webpack_exports__);\n      /* harmony export */\n      __webpack_require__.d(__webpack_exports__, {\n        /* harmony export */\"default\": function () {\n          return (/* binding */ansiRegex\n          );\n        }\n        /* harmony export */\n      });\n      function ansiRegex() {\n        var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n          _ref$onlyFirst = _ref.onlyFirst,\n          onlyFirst = _ref$onlyFirst === void 0 ? false : _ref$onlyFirst;\n        var pattern = [\"[\\\\u001B\\\\u009B][[\\\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]+)*|[a-zA-Z\\\\d]+(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]*)*)?\\\\u0007)\", '(?:(?:\\\\d{1,4}(?:;\\\\d{0,4})*)?[\\\\dA-PR-TZcf-ntqry=><~]))'].join('|');\n        return new RegExp(pattern, onlyFirst ? undefined : 'g');\n      }\n\n      /***/\n    }\n\n    /******/\n  };\n  /************************************************************************/\n  /******/ // The module cache\n  /******/\n  var __webpack_module_cache__ = {};\n  /******/\n  /******/ // The require function\n  /******/\n  function __webpack_require__(moduleId) {\n    /******/ // Check if module is in cache\n    /******/var cachedModule = __webpack_module_cache__[moduleId];\n    /******/\n    if (cachedModule !== undefined) {\n      /******/return cachedModule.exports;\n      /******/\n    }\n    /******/ // Create a new module (and put it into the cache)\n    /******/\n    var module = __webpack_module_cache__[moduleId] = {\n      /******/ // no module.id needed\n      /******/ // no module.loaded needed\n      /******/exports: {}\n      /******/\n    };\n    /******/\n    /******/ // Execute the module function\n    /******/\n    __webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n    /******/\n    /******/ // Return the exports of the module\n    /******/\n    return module.exports;\n    /******/\n  }\n  /******/\n  /************************************************************************/\n  /******/ /* webpack/runtime/define property getters */\n  /******/\n  !function () {\n    /******/ // define getter functions for harmony exports\n    /******/__webpack_require__.d = function (exports, definition) {\n      /******/for (var key in definition) {\n        /******/if (__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n          /******/Object.defineProperty(exports, key, {\n            enumerable: true,\n            get: definition[key]\n          });\n          /******/\n        }\n        /******/\n      }\n      /******/\n    };\n    /******/\n  }();\n  /******/\n  /******/ /* webpack/runtime/hasOwnProperty shorthand */\n  /******/\n  !function () {\n    /******/__webpack_require__.o = function (obj, prop) {\n      return Object.prototype.hasOwnProperty.call(obj, prop);\n    };\n    /******/\n  }();\n  /******/\n  /******/ /* webpack/runtime/make namespace object */\n  /******/\n  !function () {\n    /******/ // define __esModule on exports\n    /******/__webpack_require__.r = function (exports) {\n      /******/if (typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n        /******/Object.defineProperty(exports, Symbol.toStringTag, {\n          value: 'Module'\n        });\n        /******/\n      }\n      /******/\n      Object.defineProperty(exports, '__esModule', {\n        value: true\n      });\n      /******/\n    };\n    /******/\n  }();\n  /******/\n  /************************************************************************/\n  var __webpack_exports__ = {};\n  // This entry need to be wrapped in an IIFE because it need to be isolated against other modules in the chunk.\n  !function () {\n    /*!************************************************!*\\\n      !*** ./client-src/modules/strip-ansi/index.js ***!\n      \\************************************************/\n    __webpack_require__.r(__webpack_exports__);\n    /* harmony import */\n    var strip_ansi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__( /*! strip-ansi */\"./node_modules/strip-ansi/index.js\");\n\n    /* harmony default export */\n    __webpack_exports__[\"default\"] = strip_ansi__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\n  }();\n  var __webpack_export_target__ = exports;\n  for (var i in __webpack_exports__) __webpack_export_target__[i] = __webpack_exports__[i];\n  if (__webpack_exports__.__esModule) Object.defineProperty(__webpack_export_target__, \"__esModule\", {\n    value: true\n  });\n  /******/\n})();", "map": {"version": 3, "names": ["__webpack_modules__", "__unused_webpack___webpack_module__", "__webpack_exports__", "__webpack_require__", "r", "d", "stripAnsi", "ansi_regex__WEBPACK_IMPORTED_MODULE_0__", "string", "TypeError", "concat", "replace", "ansiRegex", "_ref", "arguments", "length", "undefined", "_ref$onlyFirst", "<PERSON><PERSON><PERSON><PERSON>", "pattern", "join", "RegExp", "__webpack_module_cache__", "moduleId", "cachedModule", "exports", "module", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "Symbol", "toStringTag", "value", "strip_ansi__WEBPACK_IMPORTED_MODULE_0__", "__webpack_export_target__", "i", "__esModule"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/node_modules/webpack-dev-server/client/modules/strip-ansi/index.js"], "sourcesContent": ["/******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ \"./node_modules/strip-ansi/index.js\":\n/*!******************************************!*\\\n  !*** ./node_modules/strip-ansi/index.js ***!\n  \\******************************************/\n/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {\n\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ stripAnsi; }\n/* harmony export */ });\n/* harmony import */ var ansi_regex__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ansi-regex */ \"./node_modules/strip-ansi/node_modules/ansi-regex/index.js\");\n\nfunction stripAnsi(string) {\n  if (typeof string !== 'string') {\n    throw new TypeError(\"Expected a `string`, got `\".concat(typeof string, \"`\"));\n  }\n\n  return string.replace((0,ansi_regex__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(), '');\n}\n\n/***/ }),\n\n/***/ \"./node_modules/strip-ansi/node_modules/ansi-regex/index.js\":\n/*!******************************************************************!*\\\n  !*** ./node_modules/strip-ansi/node_modules/ansi-regex/index.js ***!\n  \\******************************************************************/\n/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {\n\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ansiRegex; }\n/* harmony export */ });\nfunction ansiRegex() {\n  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n      _ref$onlyFirst = _ref.onlyFirst,\n      onlyFirst = _ref$onlyFirst === void 0 ? false : _ref$onlyFirst;\n\n  var pattern = [\"[\\\\u001B\\\\u009B][[\\\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]+)*|[a-zA-Z\\\\d]+(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]*)*)?\\\\u0007)\", '(?:(?:\\\\d{1,4}(?:;\\\\d{0,4})*)?[\\\\dA-PR-TZcf-ntqry=><~]))'].join('|');\n  return new RegExp(pattern, onlyFirst ? undefined : 'g');\n}\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/make namespace object */\n/******/ \t!function() {\n/******/ \t\t// define __esModule on exports\n/******/ \t\t__webpack_require__.r = function(exports) {\n/******/ \t\t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n/******/ \t\t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n/******/ \t\t\t}\n/******/ \t\t\tObject.defineProperty(exports, '__esModule', { value: true });\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n// This entry need to be wrapped in an IIFE because it need to be isolated against other modules in the chunk.\n!function() {\n/*!************************************************!*\\\n  !*** ./client-src/modules/strip-ansi/index.js ***!\n  \\************************************************/\n__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var strip_ansi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! strip-ansi */ \"./node_modules/strip-ansi/index.js\");\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (strip_ansi__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n}();\nvar __webpack_export_target__ = exports;\nfor(var i in __webpack_exports__) __webpack_export_target__[i] = __webpack_exports__[i];\nif(__webpack_exports__.__esModule) Object.defineProperty(__webpack_export_target__, \"__esModule\", { value: true });\n/******/ })()\n;"], "mappings": "AAAA,QAAS,CAAC,YAAW;EAAE;EACvB;EAAU,YAAY;;EACtB;EAAU,IAAIA,mBAAmB,GAAI;IAErC,KAAM,oCAAoC;IAC1C;AACA;AACA;IACA;IAAO,UAASC,mCAAmC,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAE;MAE/FA,mBAAmB,CAACC,CAAC,CAACF,mBAAmB,CAAC;MAC1C;MAAqBC,mBAAmB,CAACE,CAAC,CAACH,mBAAmB,EAAE;QAChE,oBAAuB,SAAS,EAAE,YAAW;UAAE,OAAO,cAAcI;UAAS;QAAE;QAC/E;MAAqB,CAAC,CAAC;MACvB;MAAqB,IAAIC,uCAAuC,GAAGJ,mBAAmB,EAAC,iBAAkB,4DAA4D,CAAC;MAEtK,SAASG,SAAS,CAACE,MAAM,EAAE;QACzB,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;UAC9B,MAAM,IAAIC,SAAS,CAAC,4BAA4B,CAACC,MAAM,CAAC,OAAOF,MAAM,EAAE,GAAG,CAAC,CAAC;QAC9E;QAEA,OAAOA,MAAM,CAACG,OAAO,CAAC,CAAC,CAAC,EAACJ,uCAAuC,CAAC,SAAS,CAAC,GAAG,EAAE,EAAE,CAAC;MACrF;;MAEA;IAAM,CAAE;;IAER,KAAM,4DAA4D;IAClE;AACA;AACA;IACA;IAAO,UAASN,mCAAmC,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAE;MAE/FA,mBAAmB,CAACC,CAAC,CAACF,mBAAmB,CAAC;MAC1C;MAAqBC,mBAAmB,CAACE,CAAC,CAACH,mBAAmB,EAAE;QAChE,oBAAuB,SAAS,EAAE,YAAW;UAAE,OAAO,cAAcU;UAAS;QAAE;QAC/E;MAAqB,CAAC,CAAC;MACvB,SAASA,SAAS,GAAG;QACnB,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;UAC7EG,cAAc,GAAGJ,IAAI,CAACK,SAAS;UAC/BA,SAAS,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,cAAc;QAElE,IAAIE,OAAO,GAAG,CAAC,8HAA8H,EAAE,0DAA0D,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;QACpN,OAAO,IAAIC,MAAM,CAACF,OAAO,EAAED,SAAS,GAAGF,SAAS,GAAG,GAAG,CAAC;MACzD;;MAEA;IAAM;;IAEN;EAAU,CAAE;EACZ;EACA,SAAU;EACV;EAAU,IAAIM,wBAAwB,GAAG,CAAC,CAAC;EAC3C;EACA,SAAU;EACV;EAAU,SAASnB,mBAAmB,CAACoB,QAAQ,EAAE;IACjD,SAAW;IACX,QAAW,IAAIC,YAAY,GAAGF,wBAAwB,CAACC,QAAQ,CAAC;IAChE;IAAW,IAAIC,YAAY,KAAKR,SAAS,EAAE;MAC3C,QAAY,OAAOQ,YAAY,CAACC,OAAO;MACvC;IAAW;IACX,SAAW;IACX;IAAW,IAAIC,MAAM,GAAGJ,wBAAwB,CAACC,QAAQ,CAAC,GAAG;MAC7D,SAAY;MACZ,SAAY;MACZ,QAAYE,OAAO,EAAE,CAAC;MACtB;IAAW,CAAC;IACZ;IACA,SAAW;IACX;IAAWzB,mBAAmB,CAACuB,QAAQ,CAAC,CAACG,MAAM,EAAEA,MAAM,CAACD,OAAO,EAAEtB,mBAAmB,CAAC;IACrF;IACA,SAAW;IACX;IAAW,OAAOuB,MAAM,CAACD,OAAO;IAChC;EAAU;EACV;EACA;EACA,SAAU;EACV;EAAU,CAAC,YAAW;IACtB,SAAW;IACX,QAAWtB,mBAAmB,CAACE,CAAC,GAAG,UAASoB,OAAO,EAAEE,UAAU,EAAE;MACjE,QAAY,KAAI,IAAIC,GAAG,IAAID,UAAU,EAAE;QACvC,QAAa,IAAGxB,mBAAmB,CAAC0B,CAAC,CAACF,UAAU,EAAEC,GAAG,CAAC,IAAI,CAACzB,mBAAmB,CAAC0B,CAAC,CAACJ,OAAO,EAAEG,GAAG,CAAC,EAAE;UAChG,QAAcE,MAAM,CAACC,cAAc,CAACN,OAAO,EAAEG,GAAG,EAAE;YAAEI,UAAU,EAAE,IAAI;YAAEC,GAAG,EAAEN,UAAU,CAACC,GAAG;UAAE,CAAC,CAAC;UAC7F;QAAa;QACb;MAAY;MACZ;IAAW,CAAC;IACZ;EAAU,CAAC,EAAE;EACb;EACA,SAAU;EACV;EAAU,CAAC,YAAW;IACtB,QAAWzB,mBAAmB,CAAC0B,CAAC,GAAG,UAASK,GAAG,EAAEC,IAAI,EAAE;MAAE,OAAOL,MAAM,CAACM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,GAAG,EAAEC,IAAI,CAAC;IAAE,CAAC;IAClH;EAAU,CAAC,EAAE;EACb;EACA,SAAU;EACV;EAAU,CAAC,YAAW;IACtB,SAAW;IACX,QAAWhC,mBAAmB,CAACC,CAAC,GAAG,UAASqB,OAAO,EAAE;MACrD,QAAY,IAAG,OAAOc,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,WAAW,EAAE;QACpE,QAAaV,MAAM,CAACC,cAAc,CAACN,OAAO,EAAEc,MAAM,CAACC,WAAW,EAAE;UAAEC,KAAK,EAAE;QAAS,CAAC,CAAC;QACpF;MAAY;MACZ;MAAYX,MAAM,CAACC,cAAc,CAACN,OAAO,EAAE,YAAY,EAAE;QAAEgB,KAAK,EAAE;MAAK,CAAC,CAAC;MACzE;IAAW,CAAC;IACZ;EAAU,CAAC,EAAE;EACb;EACA;EACA,IAAIvC,mBAAmB,GAAG,CAAC,CAAC;EAC5B;EACA,CAAC,YAAW;IACZ;AACA;AACA;IACAC,mBAAmB,CAACC,CAAC,CAACF,mBAAmB,CAAC;IAC1C;IAAqB,IAAIwC,uCAAuC,GAAGvC,mBAAmB,EAAC,iBAAkB,oCAAoC,CAAC;;IAE9I;IAA6BD,mBAAmB,CAAC,SAAS,CAAC,GAAIwC,uCAAuC,CAAC,SAAS,CAAE;EAClH,CAAC,EAAE;EACH,IAAIC,yBAAyB,GAAGlB,OAAO;EACvC,KAAI,IAAImB,CAAC,IAAI1C,mBAAmB,EAAEyC,yBAAyB,CAACC,CAAC,CAAC,GAAG1C,mBAAmB,CAAC0C,CAAC,CAAC;EACvF,IAAG1C,mBAAmB,CAAC2C,UAAU,EAAEf,MAAM,CAACC,cAAc,CAACY,yBAAyB,EAAE,YAAY,EAAE;IAAEF,KAAK,EAAE;EAAK,CAAC,CAAC;EAClH;AAAS,CAAC,GAAG"}, "metadata": {}, "sourceType": "script"}