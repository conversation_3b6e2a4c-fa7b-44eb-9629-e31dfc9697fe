{"ast": null, "code": "/* global __resourceQuery, __webpack_hash__ */\n/// <reference types=\"webpack/module\" />\nimport webpackHotLog from \"webpack/hot/log.js\";\nimport stripAnsi from \"./modules/strip-ansi/index.js\";\nimport parseURL from \"./utils/parseURL.js\";\nimport socket from \"./socket.js\";\nimport { formatProblem, show, hide } from \"./overlay.js\";\nimport { log, setLogLevel } from \"./utils/log.js\";\nimport sendMessage from \"./utils/sendMessage.js\";\nimport reloadApp from \"./utils/reloadApp.js\";\nimport createSocketURL from \"./utils/createSocketURL.js\";\n/**\n * @typedef {Object} Options\n * @property {boolean} hot\n * @property {boolean} liveReload\n * @property {boolean} progress\n * @property {boolean | { warnings?: boolean, errors?: boolean }} overlay\n * @property {string} [logging]\n * @property {number} [reconnect]\n */\n\n/**\n * @typedef {Object} Status\n * @property {boolean} isUnloading\n * @property {string} currentHash\n * @property {string} [previousHash]\n */\n\n/**\n * @type {Status}\n */\n\nvar status = {\n  isUnloading: false,\n  // TODO Workaround for webpack v4, `__webpack_hash__` is not replaced without HotModuleReplacement\n  // eslint-disable-next-line camelcase\n  currentHash: typeof __webpack_hash__ !== \"undefined\" ? __webpack_hash__ : \"\"\n};\n/** @type {Options} */\n\nvar options = {\n  hot: false,\n  liveReload: false,\n  progress: false,\n  overlay: false\n};\nvar parsedResourceQuery = parseURL(__resourceQuery);\nif (parsedResourceQuery.hot === \"true\") {\n  options.hot = true;\n  log.info(\"Hot Module Replacement enabled.\");\n}\nif (parsedResourceQuery[\"live-reload\"] === \"true\") {\n  options.liveReload = true;\n  log.info(\"Live Reloading enabled.\");\n}\nif (parsedResourceQuery.logging) {\n  options.logging = parsedResourceQuery.logging;\n}\nif (typeof parsedResourceQuery.reconnect !== \"undefined\") {\n  options.reconnect = Number(parsedResourceQuery.reconnect);\n}\n/**\n * @param {string} level\n */\n\nfunction setAllLogLevel(level) {\n  // This is needed because the HMR logger operate separately from dev server logger\n  webpackHotLog.setLogLevel(level === \"verbose\" || level === \"log\" ? \"info\" : level);\n  setLogLevel(level);\n}\nif (options.logging) {\n  setAllLogLevel(options.logging);\n}\nself.addEventListener(\"beforeunload\", function () {\n  status.isUnloading = true;\n});\nvar onSocketMessage = {\n  hot: function hot() {\n    if (parsedResourceQuery.hot === \"false\") {\n      return;\n    }\n    options.hot = true;\n    log.info(\"Hot Module Replacement enabled.\");\n  },\n  liveReload: function liveReload() {\n    if (parsedResourceQuery[\"live-reload\"] === \"false\") {\n      return;\n    }\n    options.liveReload = true;\n    log.info(\"Live Reloading enabled.\");\n  },\n  invalid: function invalid() {\n    log.info(\"App updated. Recompiling...\"); // Fixes #1042. overlay doesn't clear if errors are fixed but warnings remain.\n\n    if (options.overlay) {\n      hide();\n    }\n    sendMessage(\"Invalid\");\n  },\n  /**\n   * @param {string} hash\n   */\n  hash: function hash(_hash) {\n    status.previousHash = status.currentHash;\n    status.currentHash = _hash;\n  },\n  logging: setAllLogLevel,\n  /**\n   * @param {boolean} value\n   */\n  overlay: function overlay(value) {\n    if (typeof document === \"undefined\") {\n      return;\n    }\n    options.overlay = value;\n  },\n  /**\n   * @param {number} value\n   */\n  reconnect: function reconnect(value) {\n    if (parsedResourceQuery.reconnect === \"false\") {\n      return;\n    }\n    options.reconnect = value;\n  },\n  /**\n   * @param {boolean} value\n   */\n  progress: function progress(value) {\n    options.progress = value;\n  },\n  /**\n   * @param {{ pluginName?: string, percent: number, msg: string }} data\n   */\n  \"progress-update\": function progressUpdate(data) {\n    if (options.progress) {\n      log.info(\"\".concat(data.pluginName ? \"[\".concat(data.pluginName, \"] \") : \"\").concat(data.percent, \"% - \").concat(data.msg, \".\"));\n    }\n    sendMessage(\"Progress\", data);\n  },\n  \"still-ok\": function stillOk() {\n    log.info(\"Nothing changed.\");\n    if (options.overlay) {\n      hide();\n    }\n    sendMessage(\"StillOk\");\n  },\n  ok: function ok() {\n    sendMessage(\"Ok\");\n    if (options.overlay) {\n      hide();\n    }\n    reloadApp(options, status);\n  },\n  // TODO: remove in v5 in favor of 'static-changed'\n\n  /**\n   * @param {string} file\n   */\n  \"content-changed\": function contentChanged(file) {\n    log.info(\"\".concat(file ? \"\\\"\".concat(file, \"\\\"\") : \"Content\", \" from static directory was changed. Reloading...\"));\n    self.location.reload();\n  },\n  /**\n   * @param {string} file\n   */\n  \"static-changed\": function staticChanged(file) {\n    log.info(\"\".concat(file ? \"\\\"\".concat(file, \"\\\"\") : \"Content\", \" from static directory was changed. Reloading...\"));\n    self.location.reload();\n  },\n  /**\n   * @param {Error[]} warnings\n   * @param {any} params\n   */\n  warnings: function warnings(_warnings, params) {\n    log.warn(\"Warnings while compiling.\");\n    var printableWarnings = _warnings.map(function (error) {\n      var _formatProblem = formatProblem(\"warning\", error),\n        header = _formatProblem.header,\n        body = _formatProblem.body;\n      return \"\".concat(header, \"\\n\").concat(stripAnsi(body));\n    });\n    sendMessage(\"Warnings\", printableWarnings);\n    for (var i = 0; i < printableWarnings.length; i++) {\n      log.warn(printableWarnings[i]);\n    }\n    var needShowOverlayForWarnings = typeof options.overlay === \"boolean\" ? options.overlay : options.overlay && options.overlay.warnings;\n    if (needShowOverlayForWarnings) {\n      show(\"warning\", _warnings);\n    }\n    if (params && params.preventReloading) {\n      return;\n    }\n    reloadApp(options, status);\n  },\n  /**\n   * @param {Error[]} errors\n   */\n  errors: function errors(_errors) {\n    log.error(\"Errors while compiling. Reload prevented.\");\n    var printableErrors = _errors.map(function (error) {\n      var _formatProblem2 = formatProblem(\"error\", error),\n        header = _formatProblem2.header,\n        body = _formatProblem2.body;\n      return \"\".concat(header, \"\\n\").concat(stripAnsi(body));\n    });\n    sendMessage(\"Errors\", printableErrors);\n    for (var i = 0; i < printableErrors.length; i++) {\n      log.error(printableErrors[i]);\n    }\n    var needShowOverlayForErrors = typeof options.overlay === \"boolean\" ? options.overlay : options.overlay && options.overlay.errors;\n    if (needShowOverlayForErrors) {\n      show(\"error\", _errors);\n    }\n  },\n  /**\n   * @param {Error} error\n   */\n  error: function error(_error) {\n    log.error(_error);\n  },\n  close: function close() {\n    log.info(\"Disconnected!\");\n    if (options.overlay) {\n      hide();\n    }\n    sendMessage(\"Close\");\n  }\n};\nvar socketURL = createSocketURL(parsedResourceQuery);\nsocket(socketURL, onSocketMessage, options.reconnect);", "map": {"version": 3, "names": ["webpackHotLog", "stripAnsi", "parseURL", "socket", "formatProblem", "show", "hide", "log", "setLogLevel", "sendMessage", "reloadApp", "createSocketURL", "status", "isUnloading", "currentHash", "__webpack_hash__", "options", "hot", "liveReload", "progress", "overlay", "parsedResourceQuery", "__resourceQuery", "info", "logging", "reconnect", "Number", "setAllLogLevel", "level", "self", "addEventListener", "onSocketMessage", "invalid", "hash", "_hash", "previousHash", "value", "document", "progressUpdate", "data", "concat", "pluginName", "percent", "msg", "stillOk", "ok", "contentChanged", "file", "location", "reload", "staticChanged", "warnings", "_warnings", "params", "warn", "printableWarnings", "map", "error", "_formatProblem", "header", "body", "i", "length", "needShowOverlayForWarnings", "preventReloading", "errors", "_errors", "printableErrors", "_formatProblem2", "needShowOverlayForErrors", "_error", "close", "socketURL"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/node_modules/webpack-dev-server/client/index.js"], "sourcesContent": ["/* global __resourceQuery, __webpack_hash__ */\n/// <reference types=\"webpack/module\" />\nimport webpackHotLog from \"webpack/hot/log.js\";\nimport stripAnsi from \"./modules/strip-ansi/index.js\";\nimport parseURL from \"./utils/parseURL.js\";\nimport socket from \"./socket.js\";\nimport { formatProblem, show, hide } from \"./overlay.js\";\nimport { log, setLogLevel } from \"./utils/log.js\";\nimport sendMessage from \"./utils/sendMessage.js\";\nimport reloadApp from \"./utils/reloadApp.js\";\nimport createSocketURL from \"./utils/createSocketURL.js\";\n/**\n * @typedef {Object} Options\n * @property {boolean} hot\n * @property {boolean} liveReload\n * @property {boolean} progress\n * @property {boolean | { warnings?: boolean, errors?: boolean }} overlay\n * @property {string} [logging]\n * @property {number} [reconnect]\n */\n\n/**\n * @typedef {Object} Status\n * @property {boolean} isUnloading\n * @property {string} currentHash\n * @property {string} [previousHash]\n */\n\n/**\n * @type {Status}\n */\n\nvar status = {\n  isUnloading: false,\n  // TODO Workaround for webpack v4, `__webpack_hash__` is not replaced without HotModuleReplacement\n  // eslint-disable-next-line camelcase\n  currentHash: typeof __webpack_hash__ !== \"undefined\" ? __webpack_hash__ : \"\"\n};\n/** @type {Options} */\n\nvar options = {\n  hot: false,\n  liveReload: false,\n  progress: false,\n  overlay: false\n};\nvar parsedResourceQuery = parseURL(__resourceQuery);\n\nif (parsedResourceQuery.hot === \"true\") {\n  options.hot = true;\n  log.info(\"Hot Module Replacement enabled.\");\n}\n\nif (parsedResourceQuery[\"live-reload\"] === \"true\") {\n  options.liveReload = true;\n  log.info(\"Live Reloading enabled.\");\n}\n\nif (parsedResourceQuery.logging) {\n  options.logging = parsedResourceQuery.logging;\n}\n\nif (typeof parsedResourceQuery.reconnect !== \"undefined\") {\n  options.reconnect = Number(parsedResourceQuery.reconnect);\n}\n/**\n * @param {string} level\n */\n\n\nfunction setAllLogLevel(level) {\n  // This is needed because the HMR logger operate separately from dev server logger\n  webpackHotLog.setLogLevel(level === \"verbose\" || level === \"log\" ? \"info\" : level);\n  setLogLevel(level);\n}\n\nif (options.logging) {\n  setAllLogLevel(options.logging);\n}\n\nself.addEventListener(\"beforeunload\", function () {\n  status.isUnloading = true;\n});\nvar onSocketMessage = {\n  hot: function hot() {\n    if (parsedResourceQuery.hot === \"false\") {\n      return;\n    }\n\n    options.hot = true;\n    log.info(\"Hot Module Replacement enabled.\");\n  },\n  liveReload: function liveReload() {\n    if (parsedResourceQuery[\"live-reload\"] === \"false\") {\n      return;\n    }\n\n    options.liveReload = true;\n    log.info(\"Live Reloading enabled.\");\n  },\n  invalid: function invalid() {\n    log.info(\"App updated. Recompiling...\"); // Fixes #1042. overlay doesn't clear if errors are fixed but warnings remain.\n\n    if (options.overlay) {\n      hide();\n    }\n\n    sendMessage(\"Invalid\");\n  },\n\n  /**\n   * @param {string} hash\n   */\n  hash: function hash(_hash) {\n    status.previousHash = status.currentHash;\n    status.currentHash = _hash;\n  },\n  logging: setAllLogLevel,\n\n  /**\n   * @param {boolean} value\n   */\n  overlay: function overlay(value) {\n    if (typeof document === \"undefined\") {\n      return;\n    }\n\n    options.overlay = value;\n  },\n\n  /**\n   * @param {number} value\n   */\n  reconnect: function reconnect(value) {\n    if (parsedResourceQuery.reconnect === \"false\") {\n      return;\n    }\n\n    options.reconnect = value;\n  },\n\n  /**\n   * @param {boolean} value\n   */\n  progress: function progress(value) {\n    options.progress = value;\n  },\n\n  /**\n   * @param {{ pluginName?: string, percent: number, msg: string }} data\n   */\n  \"progress-update\": function progressUpdate(data) {\n    if (options.progress) {\n      log.info(\"\".concat(data.pluginName ? \"[\".concat(data.pluginName, \"] \") : \"\").concat(data.percent, \"% - \").concat(data.msg, \".\"));\n    }\n\n    sendMessage(\"Progress\", data);\n  },\n  \"still-ok\": function stillOk() {\n    log.info(\"Nothing changed.\");\n\n    if (options.overlay) {\n      hide();\n    }\n\n    sendMessage(\"StillOk\");\n  },\n  ok: function ok() {\n    sendMessage(\"Ok\");\n\n    if (options.overlay) {\n      hide();\n    }\n\n    reloadApp(options, status);\n  },\n  // TODO: remove in v5 in favor of 'static-changed'\n\n  /**\n   * @param {string} file\n   */\n  \"content-changed\": function contentChanged(file) {\n    log.info(\"\".concat(file ? \"\\\"\".concat(file, \"\\\"\") : \"Content\", \" from static directory was changed. Reloading...\"));\n    self.location.reload();\n  },\n\n  /**\n   * @param {string} file\n   */\n  \"static-changed\": function staticChanged(file) {\n    log.info(\"\".concat(file ? \"\\\"\".concat(file, \"\\\"\") : \"Content\", \" from static directory was changed. Reloading...\"));\n    self.location.reload();\n  },\n\n  /**\n   * @param {Error[]} warnings\n   * @param {any} params\n   */\n  warnings: function warnings(_warnings, params) {\n    log.warn(\"Warnings while compiling.\");\n\n    var printableWarnings = _warnings.map(function (error) {\n      var _formatProblem = formatProblem(\"warning\", error),\n          header = _formatProblem.header,\n          body = _formatProblem.body;\n\n      return \"\".concat(header, \"\\n\").concat(stripAnsi(body));\n    });\n\n    sendMessage(\"Warnings\", printableWarnings);\n\n    for (var i = 0; i < printableWarnings.length; i++) {\n      log.warn(printableWarnings[i]);\n    }\n\n    var needShowOverlayForWarnings = typeof options.overlay === \"boolean\" ? options.overlay : options.overlay && options.overlay.warnings;\n\n    if (needShowOverlayForWarnings) {\n      show(\"warning\", _warnings);\n    }\n\n    if (params && params.preventReloading) {\n      return;\n    }\n\n    reloadApp(options, status);\n  },\n\n  /**\n   * @param {Error[]} errors\n   */\n  errors: function errors(_errors) {\n    log.error(\"Errors while compiling. Reload prevented.\");\n\n    var printableErrors = _errors.map(function (error) {\n      var _formatProblem2 = formatProblem(\"error\", error),\n          header = _formatProblem2.header,\n          body = _formatProblem2.body;\n\n      return \"\".concat(header, \"\\n\").concat(stripAnsi(body));\n    });\n\n    sendMessage(\"Errors\", printableErrors);\n\n    for (var i = 0; i < printableErrors.length; i++) {\n      log.error(printableErrors[i]);\n    }\n\n    var needShowOverlayForErrors = typeof options.overlay === \"boolean\" ? options.overlay : options.overlay && options.overlay.errors;\n\n    if (needShowOverlayForErrors) {\n      show(\"error\", _errors);\n    }\n  },\n\n  /**\n   * @param {Error} error\n   */\n  error: function error(_error) {\n    log.error(_error);\n  },\n  close: function close() {\n    log.info(\"Disconnected!\");\n\n    if (options.overlay) {\n      hide();\n    }\n\n    sendMessage(\"Close\");\n  }\n};\nvar socketURL = createSocketURL(parsedResourceQuery);\nsocket(socketURL, onSocketMessage, options.reconnect);"], "mappings": "AAAA;AACA;AACA,OAAOA,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,SAAS,MAAM,+BAA+B;AACrD,OAAOC,QAAQ,MAAM,qBAAqB;AAC1C,OAAOC,MAAM,MAAM,aAAa;AAChC,SAASC,aAAa,EAAEC,IAAI,EAAEC,IAAI,QAAQ,cAAc;AACxD,SAASC,GAAG,EAAEC,WAAW,QAAQ,gBAAgB;AACjD,OAAOC,WAAW,MAAM,wBAAwB;AAChD,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,eAAe,MAAM,4BAA4B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,IAAIC,MAAM,GAAG;EACXC,WAAW,EAAE,KAAK;EAClB;EACA;EACAC,WAAW,EAAE,OAAOC,gBAAgB,KAAK,WAAW,GAAGA,gBAAgB,GAAG;AAC5E,CAAC;AACD;;AAEA,IAAIC,OAAO,GAAG;EACZC,GAAG,EAAE,KAAK;EACVC,UAAU,EAAE,KAAK;EACjBC,QAAQ,EAAE,KAAK;EACfC,OAAO,EAAE;AACX,CAAC;AACD,IAAIC,mBAAmB,GAAGnB,QAAQ,CAACoB,eAAe,CAAC;AAEnD,IAAID,mBAAmB,CAACJ,GAAG,KAAK,MAAM,EAAE;EACtCD,OAAO,CAACC,GAAG,GAAG,IAAI;EAClBV,GAAG,CAACgB,IAAI,CAAC,iCAAiC,CAAC;AAC7C;AAEA,IAAIF,mBAAmB,CAAC,aAAa,CAAC,KAAK,MAAM,EAAE;EACjDL,OAAO,CAACE,UAAU,GAAG,IAAI;EACzBX,GAAG,CAACgB,IAAI,CAAC,yBAAyB,CAAC;AACrC;AAEA,IAAIF,mBAAmB,CAACG,OAAO,EAAE;EAC/BR,OAAO,CAACQ,OAAO,GAAGH,mBAAmB,CAACG,OAAO;AAC/C;AAEA,IAAI,OAAOH,mBAAmB,CAACI,SAAS,KAAK,WAAW,EAAE;EACxDT,OAAO,CAACS,SAAS,GAAGC,MAAM,CAACL,mBAAmB,CAACI,SAAS,CAAC;AAC3D;AACA;AACA;AACA;;AAGA,SAASE,cAAc,CAACC,KAAK,EAAE;EAC7B;EACA5B,aAAa,CAACQ,WAAW,CAACoB,KAAK,KAAK,SAAS,IAAIA,KAAK,KAAK,KAAK,GAAG,MAAM,GAAGA,KAAK,CAAC;EAClFpB,WAAW,CAACoB,KAAK,CAAC;AACpB;AAEA,IAAIZ,OAAO,CAACQ,OAAO,EAAE;EACnBG,cAAc,CAACX,OAAO,CAACQ,OAAO,CAAC;AACjC;AAEAK,IAAI,CAACC,gBAAgB,CAAC,cAAc,EAAE,YAAY;EAChDlB,MAAM,CAACC,WAAW,GAAG,IAAI;AAC3B,CAAC,CAAC;AACF,IAAIkB,eAAe,GAAG;EACpBd,GAAG,EAAE,SAASA,GAAG,GAAG;IAClB,IAAII,mBAAmB,CAACJ,GAAG,KAAK,OAAO,EAAE;MACvC;IACF;IAEAD,OAAO,CAACC,GAAG,GAAG,IAAI;IAClBV,GAAG,CAACgB,IAAI,CAAC,iCAAiC,CAAC;EAC7C,CAAC;EACDL,UAAU,EAAE,SAASA,UAAU,GAAG;IAChC,IAAIG,mBAAmB,CAAC,aAAa,CAAC,KAAK,OAAO,EAAE;MAClD;IACF;IAEAL,OAAO,CAACE,UAAU,GAAG,IAAI;IACzBX,GAAG,CAACgB,IAAI,CAAC,yBAAyB,CAAC;EACrC,CAAC;EACDS,OAAO,EAAE,SAASA,OAAO,GAAG;IAC1BzB,GAAG,CAACgB,IAAI,CAAC,6BAA6B,CAAC,CAAC,CAAC;;IAEzC,IAAIP,OAAO,CAACI,OAAO,EAAE;MACnBd,IAAI,EAAE;IACR;IAEAG,WAAW,CAAC,SAAS,CAAC;EACxB,CAAC;EAED;AACF;AACA;EACEwB,IAAI,EAAE,SAASA,IAAI,CAACC,KAAK,EAAE;IACzBtB,MAAM,CAACuB,YAAY,GAAGvB,MAAM,CAACE,WAAW;IACxCF,MAAM,CAACE,WAAW,GAAGoB,KAAK;EAC5B,CAAC;EACDV,OAAO,EAAEG,cAAc;EAEvB;AACF;AACA;EACEP,OAAO,EAAE,SAASA,OAAO,CAACgB,KAAK,EAAE;IAC/B,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;MACnC;IACF;IAEArB,OAAO,CAACI,OAAO,GAAGgB,KAAK;EACzB,CAAC;EAED;AACF;AACA;EACEX,SAAS,EAAE,SAASA,SAAS,CAACW,KAAK,EAAE;IACnC,IAAIf,mBAAmB,CAACI,SAAS,KAAK,OAAO,EAAE;MAC7C;IACF;IAEAT,OAAO,CAACS,SAAS,GAAGW,KAAK;EAC3B,CAAC;EAED;AACF;AACA;EACEjB,QAAQ,EAAE,SAASA,QAAQ,CAACiB,KAAK,EAAE;IACjCpB,OAAO,CAACG,QAAQ,GAAGiB,KAAK;EAC1B,CAAC;EAED;AACF;AACA;EACE,iBAAiB,EAAE,SAASE,cAAc,CAACC,IAAI,EAAE;IAC/C,IAAIvB,OAAO,CAACG,QAAQ,EAAE;MACpBZ,GAAG,CAACgB,IAAI,CAAC,EAAE,CAACiB,MAAM,CAACD,IAAI,CAACE,UAAU,GAAG,GAAG,CAACD,MAAM,CAACD,IAAI,CAACE,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAACD,MAAM,CAACD,IAAI,CAACG,OAAO,EAAE,MAAM,CAAC,CAACF,MAAM,CAACD,IAAI,CAACI,GAAG,EAAE,GAAG,CAAC,CAAC;IAClI;IAEAlC,WAAW,CAAC,UAAU,EAAE8B,IAAI,CAAC;EAC/B,CAAC;EACD,UAAU,EAAE,SAASK,OAAO,GAAG;IAC7BrC,GAAG,CAACgB,IAAI,CAAC,kBAAkB,CAAC;IAE5B,IAAIP,OAAO,CAACI,OAAO,EAAE;MACnBd,IAAI,EAAE;IACR;IAEAG,WAAW,CAAC,SAAS,CAAC;EACxB,CAAC;EACDoC,EAAE,EAAE,SAASA,EAAE,GAAG;IAChBpC,WAAW,CAAC,IAAI,CAAC;IAEjB,IAAIO,OAAO,CAACI,OAAO,EAAE;MACnBd,IAAI,EAAE;IACR;IAEAI,SAAS,CAACM,OAAO,EAAEJ,MAAM,CAAC;EAC5B,CAAC;EACD;;EAEA;AACF;AACA;EACE,iBAAiB,EAAE,SAASkC,cAAc,CAACC,IAAI,EAAE;IAC/CxC,GAAG,CAACgB,IAAI,CAAC,EAAE,CAACiB,MAAM,CAACO,IAAI,GAAG,IAAI,CAACP,MAAM,CAACO,IAAI,EAAE,IAAI,CAAC,GAAG,SAAS,EAAE,kDAAkD,CAAC,CAAC;IACnHlB,IAAI,CAACmB,QAAQ,CAACC,MAAM,EAAE;EACxB,CAAC;EAED;AACF;AACA;EACE,gBAAgB,EAAE,SAASC,aAAa,CAACH,IAAI,EAAE;IAC7CxC,GAAG,CAACgB,IAAI,CAAC,EAAE,CAACiB,MAAM,CAACO,IAAI,GAAG,IAAI,CAACP,MAAM,CAACO,IAAI,EAAE,IAAI,CAAC,GAAG,SAAS,EAAE,kDAAkD,CAAC,CAAC;IACnHlB,IAAI,CAACmB,QAAQ,CAACC,MAAM,EAAE;EACxB,CAAC;EAED;AACF;AACA;AACA;EACEE,QAAQ,EAAE,SAASA,QAAQ,CAACC,SAAS,EAAEC,MAAM,EAAE;IAC7C9C,GAAG,CAAC+C,IAAI,CAAC,2BAA2B,CAAC;IAErC,IAAIC,iBAAiB,GAAGH,SAAS,CAACI,GAAG,CAAC,UAAUC,KAAK,EAAE;MACrD,IAAIC,cAAc,GAAGtD,aAAa,CAAC,SAAS,EAAEqD,KAAK,CAAC;QAChDE,MAAM,GAAGD,cAAc,CAACC,MAAM;QAC9BC,IAAI,GAAGF,cAAc,CAACE,IAAI;MAE9B,OAAO,EAAE,CAACpB,MAAM,CAACmB,MAAM,EAAE,IAAI,CAAC,CAACnB,MAAM,CAACvC,SAAS,CAAC2D,IAAI,CAAC,CAAC;IACxD,CAAC,CAAC;IAEFnD,WAAW,CAAC,UAAU,EAAE8C,iBAAiB,CAAC;IAE1C,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,iBAAiB,CAACO,MAAM,EAAED,CAAC,EAAE,EAAE;MACjDtD,GAAG,CAAC+C,IAAI,CAACC,iBAAiB,CAACM,CAAC,CAAC,CAAC;IAChC;IAEA,IAAIE,0BAA0B,GAAG,OAAO/C,OAAO,CAACI,OAAO,KAAK,SAAS,GAAGJ,OAAO,CAACI,OAAO,GAAGJ,OAAO,CAACI,OAAO,IAAIJ,OAAO,CAACI,OAAO,CAAC+B,QAAQ;IAErI,IAAIY,0BAA0B,EAAE;MAC9B1D,IAAI,CAAC,SAAS,EAAE+C,SAAS,CAAC;IAC5B;IAEA,IAAIC,MAAM,IAAIA,MAAM,CAACW,gBAAgB,EAAE;MACrC;IACF;IAEAtD,SAAS,CAACM,OAAO,EAAEJ,MAAM,CAAC;EAC5B,CAAC;EAED;AACF;AACA;EACEqD,MAAM,EAAE,SAASA,MAAM,CAACC,OAAO,EAAE;IAC/B3D,GAAG,CAACkD,KAAK,CAAC,2CAA2C,CAAC;IAEtD,IAAIU,eAAe,GAAGD,OAAO,CAACV,GAAG,CAAC,UAAUC,KAAK,EAAE;MACjD,IAAIW,eAAe,GAAGhE,aAAa,CAAC,OAAO,EAAEqD,KAAK,CAAC;QAC/CE,MAAM,GAAGS,eAAe,CAACT,MAAM;QAC/BC,IAAI,GAAGQ,eAAe,CAACR,IAAI;MAE/B,OAAO,EAAE,CAACpB,MAAM,CAACmB,MAAM,EAAE,IAAI,CAAC,CAACnB,MAAM,CAACvC,SAAS,CAAC2D,IAAI,CAAC,CAAC;IACxD,CAAC,CAAC;IAEFnD,WAAW,CAAC,QAAQ,EAAE0D,eAAe,CAAC;IAEtC,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,eAAe,CAACL,MAAM,EAAED,CAAC,EAAE,EAAE;MAC/CtD,GAAG,CAACkD,KAAK,CAACU,eAAe,CAACN,CAAC,CAAC,CAAC;IAC/B;IAEA,IAAIQ,wBAAwB,GAAG,OAAOrD,OAAO,CAACI,OAAO,KAAK,SAAS,GAAGJ,OAAO,CAACI,OAAO,GAAGJ,OAAO,CAACI,OAAO,IAAIJ,OAAO,CAACI,OAAO,CAAC6C,MAAM;IAEjI,IAAII,wBAAwB,EAAE;MAC5BhE,IAAI,CAAC,OAAO,EAAE6D,OAAO,CAAC;IACxB;EACF,CAAC;EAED;AACF;AACA;EACET,KAAK,EAAE,SAASA,KAAK,CAACa,MAAM,EAAE;IAC5B/D,GAAG,CAACkD,KAAK,CAACa,MAAM,CAAC;EACnB,CAAC;EACDC,KAAK,EAAE,SAASA,KAAK,GAAG;IACtBhE,GAAG,CAACgB,IAAI,CAAC,eAAe,CAAC;IAEzB,IAAIP,OAAO,CAACI,OAAO,EAAE;MACnBd,IAAI,EAAE;IACR;IAEAG,WAAW,CAAC,OAAO,CAAC;EACtB;AACF,CAAC;AACD,IAAI+D,SAAS,GAAG7D,eAAe,CAACU,mBAAmB,CAAC;AACpDlB,MAAM,CAACqE,SAAS,EAAEzC,eAAe,EAAEf,OAAO,CAACS,SAAS,CAAC"}, "metadata": {}, "sourceType": "module"}